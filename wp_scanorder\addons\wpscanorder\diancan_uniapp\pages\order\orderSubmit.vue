<template>
	<view class="page">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="nav-bar">
				<image 
					class="back-icon" 
					src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
					@tap="handleBack"
				/>
				<text class="title">确认订单</text>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<scroll-view class="content" scroll-y>
			<!-- 商家信息卡片 -->
			<view class="section-card store-section">
				<view class="store-info">
					<image class="store-icon" src="/static/order_submit/7cf83e4f8b5fdfea873cd11d4b01d044.png" />
					<view class="store-detail">
						<text class="store-name">{{storeInfo.name || '加载中...'}}</text>
						<text class="pickup-time">{{storeInfo.pickupTime || '尽快取餐'}}</text>
					</view>
				</view>
				
				<!-- 取餐方式选择 -->
				<view class="pickup-type">
					<view 
						class="type-item" 
						:class="{'active': pickupType === 'dine-in'}"
						@tap="switchPickupType('dine-in')"
					>
						<image src="/static/order_submit/bbdacb2185a84d001e76b2cfde875314.png" />
						<view class="type-info">
							<text class="type-name">堂食</text>
							<text class="type-desc">店内就餐</text>
						</view>
					</view>
					<view 
						class="type-item"
						:class="{'active': pickupType === 'takeout'}"
						@tap="switchPickupType('takeout')"
					>
						<image src="/static/order_submit/42647fbb92d04591f3631ba857a2035f.png" />
						<view class="type-info">
							<text class="type-name">外带</text>
							<text class="type-desc">打包带走</text>
						</view>
					</view>
				</view>

				<!-- 取餐时间选择 -->
				<view class="delivery-options" v-if="pickupType === 'takeout'">
					<!-- 取餐时间选择器 -->
					<view class="option-item" @tap="showTimeSelector">
						<view class="option-left">
							<image src="/static/order_submit/time.png" class="option-icon" />
							<text class="option-label">取餐时间</text>
						</view>
						<view class="option-right">
							<view class="value-container">
								<text class="option-value">{{ selectedTime ? selectedTime : '立即取餐' }}</text>
							</view>
							<u-icon name="arrow-right" color="#ccc" size="24"></u-icon>
						</view>
					</view>
					
					<!-- 收货地址选择 -->
					<view class="option-item" @tap="selectAddress">
						<view class="option-left">
							<image src="/static/order_submit/address.png" class="option-icon" />
							<text class="option-label">收货地址</text>
						</view>
						<view class="option-right">
							<view class="value-container">
								<text class="option-value" v-if="selectedAddress">
									{{selectedAddress.name}} {{selectedAddress.phone}}
								</text>
								<text class="option-placeholder" v-else>请选择收货地址</text>
							</view>
							<u-icon name="arrow-right" color="#ccc" size="24"></u-icon>
						</view>
					</view>
					<view class="address-detail" v-if="selectedAddress">
						<text>{{formatAddress(selectedAddress)}}</text>
					</view>
				</view>
			</view>

			<!-- 订单商品列表 -->
			<view class="section-card">
				<view class="section-header">
					<text class="section-title">订单商品</text>
					<view class="add-btn" @tap="goToMenu">
						<text>继续点单</text>
					</view>
				</view>
				
				<view class="product-list">
					<view 
						class="product-item" 
						v-for="(item, index) in cartList" 
						:key="index"
					>
						<image class="product-image" :src="item.image" mode="aspectFill" />
						<view class="product-info">
							<text class="product-name">{{item.name}}</text>
							<text class="product-spec" v-if="item.props_text">{{item.props_text}}</text>
							<view class="price-info">
								<text class="price">¥{{item.price}}</text>
							</view>
						</view>
						<view class="product-count">
							<text class="count">×{{item.count}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 优惠券和备注 -->
			<view class="section-card">
				<view class="option-item" @tap="selectCoupon">
					<view class="option-left">
						<image src="/static/order/coupon.png" class="option-icon" />
						<text class="option-label">优惠券</text>
					</view>
					<view class="option-right">
						<text class="discount" v-if="selectedCoupon">-¥{{discountAmount}}</text>
						<view class="value-container">
							<text class="option-value" v-if="selectedCoupon">{{selectedCoupon.name}}</text>
							<text class="option-placeholder" v-else>未使用优惠券</text>
						</view>
						<u-icon name="arrow-right" color="#ccc" size="24"></u-icon>
					</view>
				</view>
				<view class="option-item" @tap="addRemark">
					<view class="option-left">
						<image src="/static/order/remark.png" class="option-icon" />
						<text class="option-label">备注</text>
					</view>
					<view class="option-right">
						<view class="value-container">
							<text class="option-value" v-if="remark">{{remark}}</text>
							<text class="option-placeholder" v-else>添加备注</text>
						</view>
						<u-icon name="arrow-right" color="#ccc" size="24"></u-icon>
					</view>
				</view>
			</view>

			<!-- 支付方式 -->
			<view class="section-card">
				<view class="section-header">
					<text class="section-title">支付方式</text>
				</view>
				<view class="payment-list">
					<view 
						class="payment-item"
						:class="{'active': paymentMethod === 'wechat'}"
						@tap="switchPayment('wechat')"
					>
						<view class="payment-info">
							<image class="payment-icon" src="/static/order/wx.png" />
							<text class="payment-name">微信支付</text>
						</view>
						<view class="radio" :class="{'active': paymentMethod === 'wechat'}"></view>
					</view>
					<view 
						class="payment-item"
						:class="{'active': paymentMethod === 'balance'}"
						@tap="switchPayment('balance')"
					>
						<view class="payment-info">
							<image class="payment-icon" src="/static/order/yuer.png" />
							<text class="payment-name">余额支付</text>
							<text class="balance-info">当前余额: ¥{{(userBalance || 0).toFixed(2)}}</text>
						</view>
						<view class="radio" :class="{'active': paymentMethod === 'balance'}"></view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 底部空白区域，防止内容被底部支付栏遮挡 -->
		<view style="height: 180rpx;"></view>

		<!-- 底部结算栏 -->
		<view class="bottom-bar">
			<view class="price-info">
				<view class="price-details">
					<text class="total-text">合计</text>
					<text class="total-price">¥{{(totalPrice - discountAmount).toFixed(2)}}</text>
				</view>
				<text class="discount-text" v-if="discountAmount > 0">(已优惠¥{{discountAmount}})</text>
			</view>
			<view class="submit-btn" :class="{'disabled': isSubmitting}" @tap="submitOrder">
				<text>{{isSubmitting ? '处理中...' : '立即支付'}}</text>
			</view>
		</view>

		<!-- 时间选择弹窗 -->
		<u-popup 
			:show="showTimePicker"
			@close="closeTimeSelector"
			mode="bottom"
			border-radius="20"
		>
			<view class="time-selector">
				<view class="selector-header">
					<text class="title">选择取餐时间</text>
					<view class="close" @tap="closeTimeSelector">
						<u-icon name="close" color="#999" size="32"></u-icon>
					</view>
				</view>
				<scroll-view class="time-list" scroll-y>
					<!-- 立即取餐选项 -->
					<view 
						class="time-option immediate"
						:class="{'active': tempSelectedTime === ''}"
						@tap="selectImmediatePickup"
					>
						<view class="time-content">
							<text class="time">立即取餐</text>
							<text class="desc">预计15分钟内可取</text>
						</view>
						<view class="radio" :class="{'active': tempSelectedTime === ''}"></view>
					</view>
					
					<!-- 其他时间选项 -->
					<view 
						class="time-option"
						v-for="(item, index) in timeSlots"
						:key="index"
						:class="{'active': tempSelectedTime === item.time}"
						@tap="selectTime(item)"
					>
						<view class="time-content">
							<text class="time">{{item.time}}</text>
							<text class="desc">{{item.desc}}</text>
						</view>
						<view class="radio" :class="{'active': tempSelectedTime === item.time}"></view>
					</view>
				</scroll-view>
				<view class="selector-footer">
					<view class="confirm-btn" @tap="confirmTimeSelect">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 在底部添加备注弹窗 -->
		<u-popup 
			:show="showRemarkPopup"
			@close="closeRemarkPopup"
			mode="bottom"
			border-radius="20"
		>
			<view class="remark-editor">
				<view class="editor-header">
					<text class="title">添加备注</text>
					<view class="close" @tap="closeRemarkPopup">
						<u-icon name="close" color="#999" size="32"></u-icon>
					</view>
				</view>
				<view class="editor-content">
					<textarea
						v-model="tempRemark"
						class="remark-input"
						placeholder="请输入备注信息，比如：少糖、少冰等"
						:maxlength="100"
						auto-height
					/>
					<view class="word-count">{{tempRemark.length}}/100</view>
				</view>
				<view class="editor-footer">
					<view class="confirm-btn" @tap="confirmRemark">
						<text>确定</text>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { addOrder, payOrderWithBalance, getWxPayParams, queryWxPayResult } from '@/api/order.js'
import { getUserBalance } from '@/api/user.js'
import { getDefaultAddress } from '@/api/address.js'
import { getMerchantInfo } from '@/api/merchant.js'

export default {
	data() {
		return {
			statusBarHeight: 0,
			cartList: [], // 购物车商品列表
			totalPrice: 0, // 总价
			discountAmount: 0, // 优惠金额
			finalPrice: 0, // 最终支付金额
			pickupType: 'dine-in', // 取餐方式：dine-in(堂食) / takeout(外带)
			remark: '', // 备注
			paymentMethod: 'wechat', // 支付方式：wechat(微信) / balance(余额)
			storeInfo: {
				name: '',
				pickupTime: '尽快取餐'
			},
			selectedTime: '', // 选中的预约时间
			timeSlots: [], // 可选时间段列表
			showTimePicker: false, // 控制时间选择弹窗显示
			tempSelectedTime: '', // 临时存储选中的时间
			showRemarkPopup: false, // 控制备注弹窗显示
			tempRemark: '', // 临时存储备注信息
			selectedAddress: null, // 选中的收货地址
			userBalance: 0, // 用户余额
			selectedCoupon: null, // 选中的优惠券
			isSubmitting: false, // 是否正在提交订单，防止重复点击
		}
	},
	computed: {
		// 计算最终支付金额
		finalAmount() {
			return (this.totalPrice - this.discountAmount).toFixed(2)
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight
		
		// 获取购物车数据
		const cartData = uni.getStorageSync('cartData')
		if (cartData) {
			this.cartList = cartData.list
			this.totalPrice = cartData.price
			this.finalPrice = cartData.price
		}
		
		// 获取用餐方式
		const diningType = uni.getStorageSync('diningType')
		if (diningType) {
			this.pickupType = diningType
			// 如果是外卖模式，生成时间段列表
			if (diningType === 'takeout') {
				this.generateTimeSlots()
			}
		}
		
		// 恢复暂存的订单数据
		const tempOrderData = uni.getStorageSync('tempOrderData')
		if (tempOrderData) {
			if (tempOrderData.selectedAddress) {
				this.selectedAddress = tempOrderData.selectedAddress
			}
			if (tempOrderData.selectedCoupon) {
				this.selectedCoupon = tempOrderData.selectedCoupon
				this.calculateDiscount()
			}
		}
		
		// 监听地址选择结果
		uni.$on('addressSelected', (address) => {
			this.selectedAddress = address;
		})
		
		// 监听优惠券选择结果
		uni.$on('couponSelected', (coupon) => {
			console.log('选择的优惠券:', coupon)
			this.selectedCoupon = coupon
			this.calculateDiscount()
		})
		
		// 获取用户余额信息
		this.getUserBalanceInfo()
		
		// 获取商户信息
		this.getMerchantDetail()
	},
	onUnload() {
		// 移除事件监听
		uni.$off('addressSelected');
		uni.$off('couponSelected');
	},
	methods: {
		// 返回上一页
		handleBack() {
			uni.navigateBack()
		},
		
		// 继续点单
		goToMenu() {
			// 保存当前订单信息到缓存
			uni.setStorageSync('tempOrderData', {
				pickupType: this.pickupType,
				remark: this.remark,
				paymentMethod: this.paymentMethod
			})
			
			// 保存当前的用餐方式到菜单页面使用
			uni.setStorageSync('diningType', this.pickupType)
			
			// 跳转到点餐页面
			uni.switchTab({
				url: '/pages/menu/menu'
			})
		},
		
		// 切换取餐方式
		switchPickupType(type) {
			this.pickupType = type
			if (type === 'takeout') {
				this.generateTimeSlots()
			}
		},
		
		// 显示时间选择器
		showTimeSelector() {
			this.generateTimeSlots()
			this.tempSelectedTime = this.selectedTime
			this.showTimePicker = true
		},
		
		// 关闭时间选择器
		closeTimeSelector() {
			this.showTimePicker = false
			this.tempSelectedTime = this.selectedTime
		},
		
		// 选择立即取餐
		selectImmediatePickup() {
			this.tempSelectedTime = ''
		},
		
		// 选择时间
		selectTime(item) {
			this.tempSelectedTime = item.time
		},
		
		// 确认时间选择
		confirmTimeSelect() {
			// 如果是立即取餐，则清空selectedTime
			if (this.tempSelectedTime === '') {
				this.selectedTime = ''
			} else if (this.tempSelectedTime) {
				this.selectedTime = this.tempSelectedTime
			} else {
				uni.showToast({
					title: '请选择取餐时间',
					icon: 'none'
				})
				return
			}
			
			this.showTimePicker = false
		},
		
		// 生成时间段列表
		generateTimeSlots() {
			const slots = []
			const now = new Date()
			const startTime = new Date(now.setMinutes(now.getMinutes() + 15))
			
			// 生成从现在开始到晚上10点的时间段
			for (let i = 0; i < 40; i++) {
				const time = new Date(startTime.getTime() + i * 15 * 60000)
				const hours = time.getHours()
				
				// 只显示营业时间内的时间段（假设营业时间到22:00）
				if (hours < 22) {
					slots.push({
						time: this.formatTime(time),
						desc: i === 0 ? '最早可取餐时间' : ''
					})
				}
			}
			
			this.timeSlots = slots
		},
		
		// 格式化时间
		formatTime(date) {
			const hours = date.getHours().toString().padStart(2, '0')
			const minutes = date.getMinutes().toString().padStart(2, '0')
			return `${hours}:${minutes}`
		},
		
		// 选择优惠券
		selectCoupon() {
			// 保存当前订单信息到缓存
			uni.setStorageSync('tempOrderData', {
				pickupType: this.pickupType,
				remark: this.remark,
				paymentMethod: this.paymentMethod,
				selectedCoupon: this.selectedCoupon,
				selectedAddress: this.selectedAddress
			})
			
			// 跳转到优惠券选择页面，并传递订单金额
			uni.navigateTo({
				url: `/pages/coupon/coupon?amount=${this.totalPrice}`,
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		},
		
		// 计算优惠金额
		calculateDiscount() {
			if (!this.selectedCoupon) {
				this.discountAmount = 0
			} else {
				// 获取优惠券金额和限制金额
				const couponAmount = parseFloat(this.selectedCoupon.amount || 0)
				const couponLimit = parseFloat(this.selectedCoupon.limit || 0)
				
				// 检查订单金额是否满足优惠券使用条件
				if (this.totalPrice >= couponLimit) {
					this.discountAmount = couponAmount
				} else {
					// 如果不满足条件，提示用户并清除优惠券选择
					uni.showToast({
						title: `订单金额需满${couponLimit}元才能使用此优惠券`,
						icon: 'none',
						duration: 2000
					})
					this.selectedCoupon = null
					this.discountAmount = 0
				}
			}
			
			// 计算最终价格
			this.finalPrice = Math.max(0, this.totalPrice - this.discountAmount).toFixed(2)
		},
		
		// 添加备注
		addRemark() {
			this.showRemarkPopup = true
		},
		
		// 切换支付方式
		switchPayment(method) {
			this.paymentMethod = method
		},
		
		// 选择地址
		selectAddress() {
			// 保存当前订单信息到缓存
			uni.setStorageSync('tempOrderData', {
				pickupType: this.pickupType,
				remark: this.remark,
				paymentMethod: this.paymentMethod,
				selectedTime: this.selectedTime,
				selectedAddress: this.selectedAddress
			})
			
			// 跳转到地址选择页面
			uni.navigateTo({
				url: '/pages/address/address'
			})
		},
		
		// 提交订单
		async submitOrder() {
			// 防止重复提交订单
			if (this.isSubmitting) {
				console.log('订单正在提交中，请勿重复点击');
				uni.showToast({
					title: '请勿重复提交',
					icon: 'none'
				});
				return;
			}
			
			// 设置提交状态为true
			this.isSubmitting = true;
			
			try {
				// 如果是外带方式且未选择地址，提示选择地址
				if (this.pickupType === 'takeout' && !this.selectedAddress) {
					this.isSubmitting = false; // 重置提交状态
					return uni.showToast({
						title: '请选择收货地址',
						icon: 'none'
					})
				}
				
				// 检查并确保购物车中的商品都有规格信息
				this.cartList.forEach(item => {
					if (!item.props_text && item.specs) {
						item.props_text = item.specs;
						console.log('提交前修复商品规格信息:', item.name, item.props_text);
					} else if (!item.props_text) {
						item.props_text = '默认规格';
						item.specs = '默认规格';
						console.log('提交前设置商品默认规格:', item.name);
					}
				});
				
				// 构建订单数据
				const orderData = {
					products: this.cartList.map(item => {
						console.log('商品规格信息:', item.name, 'props_text:', item.props_text, 'specs:', item.specs);
						return {
							id: item.id,
							name: item.name,
							price: item.price,
							count: item.count,
							totalPrice: item.totalPrice,
							specs: item.props_text || item.specs || '默认规格',
							image: item.image || '',
							spec_type: item.spec_type || 'single',
							props_text: item.props_text || item.specs || '默认规格'
						};
					}),
					totalPrice: this.totalPrice,
					finalPrice: this.finalPrice,
					discountAmount: this.discountAmount,
					diningType: this.pickupType,
					remark: this.remark,
					address: this.selectedAddress ? {
						name: this.selectedAddress.name || '',
						phone: this.selectedAddress.phone || '',
						address: this.formatAddress(this.selectedAddress)
					} : null, // 添加地址信息
					phone: this.selectedAddress ? this.selectedAddress.phone : '',
					payment_method: this.paymentMethod, // 添加支付方式
					coupon: this.selectedCoupon ? {
						id: this.selectedCoupon.id,
						coupon_id: this.selectedCoupon.coupon_id || this.selectedCoupon.id,
						amount: this.selectedCoupon.amount,
						name: this.selectedCoupon.name
					} : null // 添加优惠券信息
				}
				
				// 调用添加订单接口
				console.log('提交订单数据:', JSON.stringify(orderData));
				const res = await addOrder(orderData);
				console.log('订单提交响应:', JSON.stringify(res));
				
				// 检查响应中是否包含订单号
				let orderNo;
				
				// 尝试从不同位置获取订单号
				if (res.data && res.data.order_no) {
					orderNo = res.data.order_no;
				} else if (res.data && res.data.orderNo) {
					orderNo = res.data.orderNo;
				} else if (res.order_no) {
					orderNo = res.order_no;
				} else if (res.orderNo) {
					orderNo = res.orderNo;
				} else if (res.data && res.data.id) {
					// 有些API可能使用id作为订单号
					orderNo = res.data.id;
				} else {
					// 如果找不到订单号，使用时间戳作为临时订单号
					orderNo = 'temp_' + new Date().getTime();
					console.warn('未找到有效订单号，使用临时ID:', orderNo);
				}
				
				// 获取正确的订单号
				console.log('最终使用的订单号:', orderNo);
				console.log('获取到的订单号:', orderNo);
				
				// 保存订单到本地存储，用于订单详情页显示
				const orderList = uni.getStorageSync('orderList') || [];
				const newOrder = {
					...orderData,
					order_no: orderNo, // 使用后端API返回的订单号
					orderNo: orderNo, // 保留兼容性
					createTime: new Date().toLocaleString(),
					status: 'processing',
					pay_status: 0, // 初始支付状态为未支付
					type: this.pickupType,
					dining_type: this.pickupType, // 添加与API一致的字段
					products: orderData.products.map(item => ({
						...item,
						specs: item.props_text || ''
					}))
				};
				orderList.unshift(newOrder);
				uni.setStorageSync('orderList', orderList);
				
				// 如果用户选择了余额支付，调用余额支付API
				if (this.paymentMethod === 'balance') {
					// 显示加载提示
					uni.showLoading({
						title: '余额支付中...'
					});
					
					try {
						// 调用余额支付API
						console.log('调用余额支付API，订单号:', orderNo);
						const payRes = await payOrderWithBalance(orderNo);
						console.log('余额支付响应:', JSON.stringify(payRes));
						
						// 关闭加载提示
						uni.hideLoading();
						
						// 检查支付结果 - 支持多种响应格式
						const isSuccess = payRes.code === 1;
						
						if (isSuccess) {
							// 支付成功
							this.handlePaymentSuccess(newOrder, orderList);
							
							// 刷新用户余额
							await this.getUserBalanceInfo();
						} else {
							// 如果为2 跳转充值页面
							if (payRes.code === 2) {
								// 显示确认对话框，让用户选择是否跳转到充值页面
								uni.showModal({
									title: '余额不足',
									content: '您的余额不足，是否前往充值？',
									confirmText: '去充值',
									cancelText: '取消',
									success: (res) => {
										if (res.confirm) {
											// 用户点击确认，跳转到充值页面
											uni.navigateTo({
												url: '/pages/my/recharge'
											});
										}
									}
								});
							}
							// 支付失败
							uni.showToast({
								title: payRes.msg || payRes.message || '余额支付失败',
								icon: 'none'
							});
						}
					} catch (payError) {
						// 关闭加载提示
						uni.hideLoading();
						
						console.error('余额支付过程中出错:', payError);
						uni.showToast({
							title: payError.message || payError.msg || '余额支付失败',
							icon: 'none'
						});
						
						// 出错后，仍然跳转到订单详情页
						setTimeout(() => {
							uni.redirectTo({
								url: `/pages/order/detail?order_no=${orderNo}`
							});
						}, 1500);
					}
				} else {
					// 微信支付处理
					this.handleWxPay(orderNo, newOrder, orderList);
				}
				
			} catch (e) {
				console.error('提交订单失败:', e);
				uni.showToast({
					title: e.message || e.msg || '下单失败',
					icon: 'none'
				});
			} finally {
				// 无论成功或失败，都要重置提交状态
				setTimeout(() => {
					this.isSubmitting = false;
					console.log('重置订单提交状态');
				}, 2000); // 延迟2秒重置，防止快速连续点击
			}
		},
		
		// 关闭备注弹窗
		closeRemarkPopup() {
			this.showRemarkPopup = false
		},
		
		// 确认备注
		confirmRemark() {
			if (this.tempRemark) {
				this.remark = this.tempRemark
				this.showRemarkPopup = false
				uni.showToast({
					title: '备注已添加',
					icon: 'none'
				})
			}
		},
		
		// 添加获取用户余额的方法
		async getUserBalanceInfo() {
			try {
				const res = await getUserBalance()
				console.log('获取余额返回数据:', JSON.stringify(res))
				
				// 根据接口返回格式解析余额数据
				// {"code":1,"msg":"Get balance successful","time":"1750841022","data":"1000.00"}
				if (res.code === 1 && res.data) {
					// data直接是余额字符串
					if (typeof res.data === 'string') {
						this.userBalance = parseFloat(res.data)
						console.log('从字符串中解析余额:', this.userBalance)
					} else if (typeof res.data === 'number') {
						this.userBalance = res.data
						console.log('获取到数字余额:', this.userBalance)
					} else if (typeof res.data === 'object' && res.data.balance) {
						this.userBalance = parseFloat(res.data.balance)
						console.log('从对象中解析余额:', this.userBalance)
					}
				} else {
					console.warn('API返回格式不符合预期:', res)
					this.userBalance = 0
				}
				
				// 确保余额是有效数字
				if (isNaN(this.userBalance)) {
					this.userBalance = 0
				}
				
				console.log('最终用户余额:', this.userBalance)
			} catch (e) {
				console.error('获取用户余额失败:', e)
				this.userBalance = 0
			}
		},
		// 格式化地址
		formatAddress(address) {
			if (!address) return '';
			
			// 构建省市区字符串
			const region = [
				address.province || '',
				address.city || '',
				address.district || ''
			].filter(Boolean).join(' ');
			
			// 添加详细地址
			return `${region} ${address.address || ''}`.trim();
		},
		// 微信支付处理
		async handleWxPay(orderNo, newOrder, orderList) {
			try {
				// 调用获取微信支付参数的API
				const payParamsRes = await getWxPayParams(orderNo);
				console.log('获取微信支付参数返回:', JSON.stringify(payParamsRes));
				
				// 检查API返回是否成功
				if (payParamsRes.code !== 1 || !payParamsRes.data) {
					throw new Error(payParamsRes.msg || '获取支付参数失败');
				}
				
				// 提取支付参数
				const payParams = payParamsRes.data;
				
				// 发起微信支付
				uni.requestPayment({
					provider: 'wxpay',
					timeStamp: payParams.timeStamp,
					nonceStr: payParams.nonceStr,
					package: payParams.package,
					signType: payParams.signType,
					paySign: payParams.paySign,
					success: async (res) => {
						console.log('微信支付成功:', JSON.stringify(res));
						
						// 查询支付结果（有些系统可能需要后端确认支付状态）
						try {
							uni.showLoading({
								title: '确认支付结果...'
							});
							
							const queryRes = await queryWxPayResult(orderNo);
							
							uni.hideLoading();
							
							// 根据pay_status判断支付状态，0=未支付，1=已支付
							const isPaid = queryRes.code === 1 && 
										  queryRes.data && 
										  (queryRes.data.paid === true || 
										   (queryRes.data.order && queryRes.data.order.pay_status === 1) ||
										   queryRes.data.pay_status === 1);
							
							if (isPaid) {
								// 支付成功处理
								this.handlePaymentSuccess(newOrder, orderList);
							} else {
								// 虽然微信返回成功，但后端查询失败的情况
								console.warn('支付确认失败:', JSON.stringify(queryRes));
								// 仍然按支付成功处理，等待后端异步通知
								this.handlePaymentSuccess(newOrder, orderList);
							}
						} catch (queryError) {
							uni.hideLoading();
							console.error('查询支付结果失败:', queryError);
							// 仍然按支付成功处理，等待后端异步通知
							this.handlePaymentSuccess(newOrder, orderList);
						}
					},
					fail: (err) => {
						console.error('微信支付失败:', JSON.stringify(err));
						
						// 处理用户取消支付的情况
						if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
							uni.showToast({
								title: '支付已取消',
								icon: 'none'
							});
						} else {
							uni.showToast({
								title: '支付失败: ' + (err.errMsg || '未知错误'),
								icon: 'none',
								duration: 2000
							});
						}
						
						// 支付失败后，仍然跳转到订单详情页
						setTimeout(() => {
							uni.redirectTo({
								url: `/pages/order/detail?order_no=${orderNo}`
							});
						}, 1500);
					},
					complete: () => {
						// 支付流程完成
						console.log('微信支付流程完成');
					}
				});
			} catch (e) {
				uni.hideLoading();
				console.error('微信支付处理异常:', e);
				uni.showToast({
					title: e.message || '支付失败，请稍后再试',
					icon: 'none',
					duration: 2000
				});
				
				// 异常情况下，也跳转到订单详情页
				setTimeout(() => {
					uni.redirectTo({
						url: `/pages/order/detail?order_no=${orderNo}`
					});
				}, 1500);
			}
		},
		// 处理支付成功逻辑
		handlePaymentSuccess(newOrder, orderList) {
			// 显示支付成功提示
			uni.showToast({
				title: '支付成功',
				icon: 'none'
			});
			
			// 更新订单状态
			newOrder.status = 'paid';
			newOrder.pay_status = 1; // 设置支付状态为已支付
			orderList[0] = newOrder;
			uni.setStorageSync('orderList', orderList);
			
			// 清空购物车数据
			uni.removeStorageSync('cartData');
			uni.removeStorageSync('cartList');
			uni.removeStorageSync('cartTotal');
			uni.removeStorageSync('diningType');
			uni.removeStorageSync('tempOrderData');
			
			// 通知菜单页面清空购物车
			uni.$emit('clearCart');
			
			// 跳转到订单详情页
			setTimeout(() => {
				uni.redirectTo({
					url: `/pages/order/detail?order_no=${newOrder.order_no}`
				});
			}, 1500);
		},
		// 获取商户信息
		async getMerchantDetail() {
			try {
				const res = await getMerchantInfo()
				console.log('获取商户信息返回:', JSON.stringify(res))
				
				if (res.code === 1 && res.data) {
					// 更新商户信息
					this.storeInfo = {
						name: res.data.name || res.data.store_name || res.data.title || '',
						address: res.data.address || '',
						phone: res.data.phone || res.data.tel || '',
						pickupTime: '尽快取餐'
					}
				} else {
					console.warn('商户信息获取失败:', res)
				}
			} catch (e) {
				console.error('获取商户信息异常:', e)
				// 设置默认值，防止页面显示异常
				this.storeInfo = {
					name: 'KKmall京基店2楼188号',
					pickupTime: '尽快取餐'
				}
			}
		},
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background-color: #f8f8f8;
	width: 100%;
	overflow-x: hidden; /* 防止水平溢出 */
	box-sizing: border-box;
	
	.header {
		background: #fff;
		padding-top: 88rpx;
		width: 100%;
		
		.nav-bar {
			position: relative;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				position: absolute;
				left: 30rpx;
				width: 40rpx;
				height: 40rpx;
				padding: 10rpx;
			}
			
			.title {
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
			}
		}
	}
	
	.content {
		padding: 20rpx;
		padding-bottom: 180rpx;
		width: 100%;
		box-sizing: border-box;
		
		.section-card {
			background: #fff;
			border-radius: 16rpx;
			padding: 30rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
			box-sizing: border-box;
			width: 100%;
		}
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			.section-title {
				font-size: 30rpx;
				color: #333;
				font-weight: bold;
			}
			
			.add-btn {
				padding: 12rpx 24rpx;
				border-radius: 100rpx;
				border: 2rpx solid #8cd548;
				
				text {
					font-size: 26rpx;
					color: #8cd548;
				}
			}
		}
		
		.store-section {
			border-top: 4rpx solid #fff;
			
			.store-info {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;
				
				.store-icon {
					width: 40rpx;
					height: 40rpx;
					margin-right: 20rpx;
				}
				
				.store-detail {
					.store-name {
						font-size: 30rpx;
						color: #333;
						font-weight: 500;
						margin-bottom: 8rpx;
					}
					
					.pickup-time {
						font-size: 26rpx;
						color: #999;
					}
				}
			}
			
			.pickup-type {
				display: flex;
				gap: 20rpx;
				margin-bottom: 30rpx;
				
				.type-item {
					flex: 1;
					display: flex;
					align-items: center;
					padding: 20rpx;
					border-radius: 16rpx;
					border: 2rpx solid #eee;
					transition: all 0.3s;
					
					image {
						width: 40rpx;
						height: 40rpx;
						margin-right: 16rpx;
					}
					
					.type-info {
						.type-name {
							font-size: 28rpx;
							color: #333;
							margin-bottom: 4rpx;
						}
						
						.type-desc {
							font-size: 24rpx;
							color: #999;
						}
					}
					
					&.active {
						border-color: #8cd548;
						background: rgba(140, 213, 72, 0.08);
					}
				}
			}
			
			.delivery-options {
				.option-item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 24rpx 0;
					border-bottom: 1rpx solid #f5f5f5;
					
					.option-left {
						display: flex;
						align-items: center;
						
						.option-icon {
							width: 40rpx;
							height: 40rpx;
							margin-right: 16rpx;
						}
						
						.option-label {
							font-size: 28rpx;
							color: #333;
						}
					}
					
					.option-right {
						display: flex;
						align-items: center;
						flex-shrink: 1;
						min-width: 0; /* 确保可以缩小 */
						
						.discount {
							font-size: 28rpx;
							color: #ff4444;
							margin-right: 8rpx;
							flex-shrink: 0;
						}
						
						.value-container {
							flex: 1;
							min-width: 0;
							margin-right: 16rpx;
							overflow: hidden;
						}
						
						.option-value {
							font-size: 28rpx;
							color: #333;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							display: block;
						}
						
						.option-placeholder {
							font-size: 28rpx;
							color: #999;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							display: block;
						}
					}
				}
				
				.address-detail {
					padding: 16rpx 0 0 52rpx;
					font-size: 26rpx;
					color: #666;
					line-height: 1.5;
				}
			}
		}
		
		.product-list {
			.product-item {
				display: flex;
				padding: 20rpx 0;
				position: relative;
				
				&:not(:last-child) {
					border-bottom: 1rpx solid #f5f5f5;
				}
				
				.product-image {
					width: 120rpx;
					height: 120rpx;
					border-radius: 12rpx;
					margin-right: 20rpx;
					flex-shrink: 0; /* 防止图片缩小 */
				}
				
				.product-info {
					flex: 1;
					display: flex;
					flex-direction: column;
					padding-right: 80rpx; /* 为数量留出更多空间 */
					min-width: 0; /* 确保flex项可以正确缩小 */
					
					.product-name {
						font-size: 28rpx;
						color: #333;
						margin-bottom: 8rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					
					.product-spec {
						font-size: 24rpx;
						color: #999;
						margin-bottom: 16rpx;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
					
					.price-info {
						display: flex;
						align-items: center;
						
						.price {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}
					}
				}
				
				.product-count {
					position: absolute;
					right: 0;
					bottom: 20rpx;
					width: 60rpx; /* 固定宽度 */
					text-align: right;
					
					.count {
						font-size: 26rpx;
						color: #999;
					}
				}
			}
		}
		
		.option-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24rpx 0;
			
			&:not(:last-child) {
				border-bottom: 1rpx solid #f5f5f5;
			}
			
			.option-left {
				display: flex;
				align-items: center;
				
				.option-icon {
					width: 40rpx;
					height: 40rpx;
					margin-right: 16rpx;
				}
				
				.option-label {
					font-size: 28rpx;
					color: #333;
				}
			}
			
			.option-right {
				display: flex;
				align-items: center;
				flex-shrink: 1;
				min-width: 0; /* 确保可以缩小 */
				
				.discount {
					font-size: 28rpx;
					color: #ff4444;
					margin-right: 8rpx;
					flex-shrink: 0;
				}
				
				.value-container {
					flex: 1;
					min-width: 0;
					margin-right: 16rpx;
					overflow: hidden;
				}
				
				.option-value {
					font-size: 28rpx;
					color: #333;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					display: block;
				}
				
				.option-placeholder {
					font-size: 28rpx;
					color: #999;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					display: block;
				}
			}
		}
		
		.payment-list {
			.payment-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;
				
				&:not(:last-child) {
					border-bottom: 1rpx solid #f5f5f5;
				}
				
				.payment-info {
					display: flex;
					align-items: center;
					flex: 1;
					overflow: hidden;
					
					.payment-icon {
						width: 40rpx;
						height: 40rpx;
						margin-right: 16rpx;
						flex-shrink: 0;
					}
					
					.payment-name {
						font-size: 28rpx;
						color: #333;
						margin-right: 12rpx;
						flex-shrink: 0;
					}
					
					.balance-info {
						font-size: 24rpx;
						color: #999;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}
				
				.radio {
					width: 36rpx;
					height: 36rpx;
					border-radius: 50%;
					border: 2rpx solid #ddd;
					position: relative;
					
					&.active {
						border-color: #8cd548;
						background: #fff;
						
						&::after {
							content: '';
							position: absolute;
							width: 20rpx;
							height: 20rpx;
							background: #8cd548;
							border-radius: 50%;
							left: 50%;
							top: 50%;
							transform: translate(-50%, -50%);
						}
					}
				}
				
				&.active {
					.payment-name {
						color: #333;
						font-weight: 500;
					}
				}
			}
		}
	}
	
	.bottom-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		width: 100%;
		box-sizing: border-box;
		
		.price-info {
			display: flex;
			flex-direction: column;
			
			.price-details {
				display: flex;
				align-items: center;
				
				.total-text {
					font-size: 28rpx;
					color: #333;
					margin-right: 8rpx;
				}
				
				.total-price {
					font-size: 38rpx;
					color: #ff4d4f;
					font-weight: bold;
				}
			}
			
			.discount-text {
				font-size: 24rpx;
				color: #999;
				margin-top: 4rpx;
			}
		}
		
		.submit-btn {
			padding: 0 60rpx;
			height: 88rpx;
			background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
			border-radius: 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: 0 6rpx 12rpx rgba(140, 213, 72, 0.2);
			
			text {
				font-size: 32rpx;
				color: #fff;
				font-weight: 500;
			}
			
			&:active {
				transform: scale(0.98);
			}
			
			&.disabled {
				background: #cccccc;
				opacity: 0.8;
				box-shadow: none;
				
				&:active {
					transform: none;
				}
			}
		}
	}
}

.time-selector {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	
	.selector-header {
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #f5f5f5;
		
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
		}
		
		.close {
			padding: 10rpx;
		}
	}
	
	.time-list {
		max-height: 600rpx;
		padding: 20rpx 30rpx;
		
		.time-option {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			background: #f8f8f8;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			
			.time-content {
				flex: 1;
				
				.time {
					font-size: 32rpx;
					color: #333;
					display: block;
					margin-bottom: 6rpx;
				}
				
				.desc {
					font-size: 24rpx;
					color: #999;
				}
			}
			
			.radio {
				width: 36rpx;
				height: 36rpx;
				border-radius: 50%;
				border: 2rpx solid #ddd;
				position: relative;
				
				&.active {
					border-color: #8cd548;
					background: #fff;
					
					&::after {
						content: '';
						position: absolute;
						width: 20rpx;
						height: 20rpx;
						background: #8cd548;
						border-radius: 50%;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
				}
			}
			
			&.active {
				background: rgba(140, 213, 72, 0.1);
				
				.time {
					color: #8cd548;
					font-weight: 500;
				}
			}
			
			&.immediate {
				background: #e6f7d9;
				border: 1rpx solid #8cd548;
			}
		}
	}
	
	.selector-footer {
		padding: 30rpx;
		border-top: 1rpx solid #f5f5f5;
		
		.confirm-btn {
			height: 88rpx;
			background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
			border-radius: 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			text {
				font-size: 32rpx;
				color: #fff;
				font-weight: 500;
			}
			
			&:active {
				transform: scale(0.98);
			}
		}
	}
}

.remark-editor {
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	
	.editor-header {
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #f5f5f5;
		
		.title {
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
		}
		
		.close {
			padding: 10rpx;
		}
	}
	
	.editor-content {
		padding: 30rpx;
		
		.remark-input {
			width: 100%;
			height: 200rpx;
			border: 1rpx solid #ddd;
			border-radius: 16rpx;
			padding: 20rpx;
			font-size: 28rpx;
			color: #333;
			line-height: 1.5;
			resize: none;
		}
		
		.word-count {
			text-align: right;
			font-size: 24rpx;
			color: #999;
			margin-top: 10rpx;
		}
	}
	
	.editor-footer {
		padding: 30rpx;
		border-top: 1rpx solid #f5f5f5;
		
		.confirm-btn {
			height: 88rpx;
			background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
			border-radius: 44rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			
			text {
				font-size: 32rpx;
				color: #fff;
				font-weight: 500;
			}
			
			&:active {
				transform: scale(0.98);
			}
		}
	}
}
</style>