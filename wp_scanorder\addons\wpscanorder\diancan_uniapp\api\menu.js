import request from '@/utils/request';

// 获取分类列表
export const getCategories = () => {
    return request({
        url: '/category/index',
        method: 'GET'
    });
};

// 获取商品列表
export const getProducts = (params) => {
    return request({
        url: '/product/index',
        method: 'GET',
        params: params
    });
};

// 获取商品详情
export const getProductDetail = (id) => {
    return request({
        url: '/product/detail',
        method: 'GET',
        params: { id }
    });
}; 