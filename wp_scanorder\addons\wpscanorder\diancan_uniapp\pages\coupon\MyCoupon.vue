<template>
  <view class="wp_-flex-col page">
    <view class="wp_-flex-col">
      <!-- 顶部导航栏 -->
      <nav-bar title="我的优惠券" @back="handleBack"></nav-bar>

      <!-- 状态切换 -->
      <view class="wp_-flex-row wp_-justify-between wp_-relative section_4">
        <view 
          v-for="(tab, index) in tabs" 
          :key="index"
          :class="['wp_-flex-row wp_-justify-start wp_-items-center text-wrapper', {'active': currentTab === index}]"
          @tap="switchTab(index)"
        >
          <text class="font" :class="{'active-text': currentTab === index}">{{tab.name}}</text>
        </view>
        <view class="section_5 pos_3" :style="{left: indicatorLeft}"></view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <view class="loading-circle"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 错误提示 -->
      <view class="error-container" v-else-if="error">
        <text class="error-text">{{errorMsg}}</text>
        <view class="retry-btn" @tap="fetchCoupons">
          <text class="retry-text">重试</text>
        </view>
      </view>

      <!-- 优惠券列表 -->
      <view class="wp_-flex-col list" v-else>
        <view 
          class="wp_-flex-col wp_-mt-10 section_6 list-item" 
          v-for="(item, index) in filteredCouponList" 
          :key="index"
        >
          <view class="wp_-flex-row wp_-justify-between group">
            <view class="wp_-flex-row wp_-items-baseline wp_-self-start group_2">
              <text class="font_4 text_7">￥</text>
              <text class="font_3 text_6">{{item.amount}}</text>
            </view>
            <view class="wp_-flex-col wp_-items-center">
              <text class="font_2 text_5">{{item.name}}</text>
              <text class="font_5 text_8 mt-17">有效期至{{item.expireDate}}</text>
            </view>
            <view 
              class="wp_-flex-col wp_-justify-start wp_-items-center wp_-self-start text-wrapper_3 view"
              :class="{'disabled': item.status === 'expired'}"
              @tap="item.status !== 'expired' ? useCoupon(item) : ''"
            >
              <text class="font_5 text_9">{{item.status === 'expired' ? '已过期' : '使用'}}</text>
            </view>
          </view>
          <view class="wp_-flex-col mt-3">
            <view class="wp_-flex-row wp_-self-stretch">
              <text class="font_6 text_10">优惠券</text>
              <text class="font_6 ml-1">（元）</text>
            </view>
            <text class="wp_-self-center font_5 text_11">{{item.condition}}</text>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="filteredCouponList.length === 0">
          <image class="empty-image" src="/static/order/e186e04e8774da64b58c96a8bb479840.png" />
          <text class="empty-text">暂无优惠券</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getMyCoupons } from '@/api/user.js'

export default {
  name: 'MyCoupon',
  
  data() {
    return {
      currentTab: 0,
      tabs: [
        { name: '全部' },
        { name: '未使用' }, 
        { name: '已过期' }
      ],
      couponList: [],
      loading: false,
      error: false,
      errorMsg: '加载失败，请重试'
    }
  },

  computed: {
    indicatorLeft() {
      return 82 + (this.currentTab * 120) + 'rpx'
    },
    
    // 根据当前选中的标签筛选优惠券列表
    filteredCouponList() {
      if (this.currentTab === 0) {
        return this.couponList
      } else if (this.currentTab === 1) {
        return this.couponList.filter(item => item.status === 'valid')
      } else {
        return this.couponList.filter(item => item.status === 'expired')
      }
    }
  },
  
  onLoad() {
    this.fetchCoupons()
  },

  methods: {
    handleBack() {
      uni.navigateBack()
    },

    switchTab(index) {
      this.currentTab = index
    },
    
    // 获取优惠券列表
    async fetchCoupons() {
      this.loading = true
      this.error = false
      
      try {
        const res = await getMyCoupons()
        if (res.code === 1) {
          // 处理后台返回的数据，添加状态标识
          this.couponList = res.data.map(item => {
            // 检查优惠券是否已过期
            const isExpired = new Date(item.expireDate) < new Date()
            return {
              ...item,
              status: isExpired ? 'expired' : 'valid'
            }
          })
        } else {
          this.error = true
          this.errorMsg = res.msg || '获取优惠券失败'
        }
      } catch (error) {
        console.error('获取优惠券列表失败:', error)
        this.error = true
        this.errorMsg = '网络异常，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    useCoupon(coupon) {
      if (coupon.status === 'expired') return
      
      // TODO: 使用优惠券逻辑
      this.$emit('use-coupon', coupon)
      
      // 关闭当前页面，将选中的优惠券传回上一页
      uni.navigateBack({
        success: () => {
          // 通过事件总线传递数据
          uni.$emit('selected-coupon', coupon)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  background-color: #f8fafb;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;

  .section_4 {
    padding: 28rpx 56rpx 36rpx;
    background-color: #ffffff;
    position: relative;

    .text-wrapper {
      width: 90rpx;
      
      &.active .font {
        color: #77d431;
      }
    }

    .font {
      font-size: 30rpx;
      font-family: MiSans;
      line-height: 27.6rpx;
      color: #a2a2a2;
      
      &.active-text {
        color: #77d431;
      }
    }

    .section_5 {
      background-color: #8cd548;
      border-radius: 322rpx;
      width: 40rpx;
      height: 4rpx;
      position: absolute;
      bottom: 20rpx;
      transition: left 0.3s;
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
    
    .loading-circle {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #8cd548;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20rpx;
    }
    
    .loading-text {
      font-size: 28rpx;
      color: #999;
    }
  }
  
  .error-container {
    padding: 100rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .error-text {
      font-size: 28rpx;
      color: #ff5b5b;
      margin-bottom: 30rpx;
    }
    
    .retry-btn {
      padding: 16rpx 60rpx;
      background-color: #8cd548;
      border-radius: 100rpx;
      
      .retry-text {
        font-size: 28rpx;
        color: #ffffff;
      }
    }
  }

  .empty-state {
    padding: 100rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .empty-image {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 30rpx;
    }
    
    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .list {
    padding: 30rpx 12rpx 0;

    .section_6 {
      margin: 10rpx 8rpx 0;
      padding: 36rpx 12rpx 24rpx 48rpx;
      background-color: #ffffff;
      border-radius: 20rpx;

      .group {
        padding-left: 16rpx;

        .group_2 {
          margin-top: 16rpx;
          
          .font_3 {
            font-size: 40rpx;
            color: #000000;
          }

          .font_4 {
            font-size: 24rpx;
            color: #000000;
          }
        }

        .font_2 {
          font-size: 32rpx;
          color: #000000;
        }

        .text-wrapper_3 {
          margin-top: 52rpx;
          padding: 8rpx 0;
          background-color: #77d431;
          border-radius: 6rpx;
          width: 100rpx;
          height: 40rpx;
          text-align: center;

          .text_9 {
            color: #ffffff;
          }
          
          &.disabled {
            background-color: #cccccc;
          }
        }
      }

      .font_5 {
        font-size: 28rpx;
        color: #b3b3b3;
      }

      .font_6 {
        font-size: 28rpx;
        color: #000000;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.mt-17 {
  margin-top: 34rpx;
}

.mt-3 {
  margin-top: 6rpx;
}

.ml-1 {
  margin-left: 2rpx;
}
</style>