<template>
	<view class="wp_-flex-col page">
		<!-- 使用原生swiper组件替代uView的u-swiper -->
		<swiper 
			v-if="bannerList && bannerList.length > 0"
			class="swiper" 
			circular 
			autoplay 
			indicator-dots 
			indicator-color="rgba(255, 255, 255, 0.6)"
			indicator-active-color="#ffffff"
			:interval="3000" 
			:duration="500"
			style="height: 600rpx; width: 100%;"
		>
			<swiper-item v-for="(item, index) in bannerList" :key="index" class="swiper-item">
				<image 
					:src="item.image" 
					mode="aspectFill" 
					class="swiper-image"
				></image>
			</swiper-item>
		</swiper>
		
		<!-- 会员卡片 -->
		<view class="member-card">
			<view class="member-info" v-if="isLogin">
				<view class="info-content">
					<view class="user-info">
						<view class="avatar-wrap">
							<image class="avatar" :src="userInfo.avatarUrl || '/static/home/<USER>'" mode="aspectFill" />
							<view class="vip-badge">
								<u-icon name="star-fill" size="16" color="#fff"></u-icon>
							</view>
						</view>
						<view class="user-detail">
							<text class="name">{{userInfo.nickName || ''}}</text>
							<text class="level">默认会员</text>
						</view>
					</view>
					<view class="asset-info">
						<view class="asset-item" @tap="handlePointsMall">
							<text class="value">{{points}}</text>
							<text class="label">积分</text>
						</view>
						<view class="divider"></view>
						<view class="asset-item" @tap="goToRecharge">
							<text class="value">{{balance}}</text>
							<text class="label">余额 (元)</text>
						</view>
					</view>
				</view>
				<view class="qr-code" @tap="handleMemberCode" v-if="userInfo.is_member == 1">
					<view class="code-wrap">
						<image class="code-icon" src="/static/home/<USER>" mode="aspectFit" />
						<view class="code-light"></view>
					</view>
					<text>邀请好友</text>
				</view>
			</view>
			<!-- 未登录状态显示登录按钮 -->
			<view class="member-info not-login" v-else>
				<view class="info-content">
					<view class="user-info">
						<view class="avatar-wrap">
							<image class="avatar" src="/static/my/default-avatar.png" mode="aspectFill" />
						</view>
						<view class="user-detail">
							<text class="name">立即登录</text>
							<text class="login-tip">登录后享受更多会员权益</text>
						</view>
					</view>
				</view>
				<view class="login-btn" @tap="goToLogin">
					<text>立即登录</text>
				</view>
			</view>
		</view>

		<!-- 统一白色背景区域 -->
		<view class="unified-white-box">
			<!-- 点餐方式 -->
			<view class="order-section">
				<view class="order-item" @tap="handleStoreOrder">
					<image class="order-icon" :src="iconsConfig.icon_dine_in || '/static/home/<USER>'" mode="aspectFit"/>
					<view class="order-content">
						<text class="title">堂食点餐</text>
						<text class="desc">小程序下单免排队</text>
					</view>
				</view>
				<view class="order-item" @tap="handleDeliveryOrder">
					<image class="order-icon" :src="iconsConfig.icon_takeout || '/static/home/<USER>'" mode="aspectFit"/>
					<view class="order-content">
						<text class="title">外卖配送</text>
						<text class="desc">美味送到家</text>
					</view>
				</view>
			</view>

			<!-- 分隔线 -->
			<view class="divider-line"></view>

			<!-- 功能区域 -->
			<view class="feature-section">
				<view class="feature-item" @tap="goToCoupon">
					<image class="feature-icon" :src="iconsConfig.icon_my_coupon || '/static/home/<USER>'" mode="aspectFit" />
					<text>我的优惠</text>
				</view>
				<view class="feature-item" @tap="goToRecharge">
					<image class="feature-icon" :src="iconsConfig.icon_member_recharge || '/static/home/<USER>'" mode="aspectFit" />
					<text>会员充值</text>
				</view>
				<view class="feature-item" @tap="handlePointsMall">
					<image class="feature-icon" :src="iconsConfig.icon_points_mall || '/static/home/<USER>'" mode="aspectFit" />
					<text>积分商城</text>
				</view>
			</view>
		</view>

		<!-- 热销商品区域 -->
		<view class="hot-selling-section">
			<view class="section-header">
				<text class="section-title">大家都在喝</text>
			</view>
			<scroll-view scroll-x class="product-scroll" enable-flex show-scrollbar="false">
				<view class="product-card" v-for="(item, index) in topSellingProducts" :key="index" @tap="goToProductDetail(item)">
					<view class="product-tag" v-if="item.is_new"><text>新</text></view>
					<image class="product-image" :src="item.image || '/static/home/<USER>'" mode="aspectFill"></image>
					<view class="product-info">
						<text class="product-name">{{item.name}}</text>
						<view class="product-price-row">
							<text class="product-price">¥ {{item.price}}</text>
							<view class="add-btn" @tap.stop="addToCart(item)">
								<text class="add-icon">+</text>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
import request from '@/utils/request.js'
import { getUserProfile, getUserAssets, getUserPoints, getUserBalance } from '@/api/user.js'
import { getBanners, getTopSellingProducts } from '@/api/home.js'
import { getIconsConfig } from '@/api/config.js'

export default {
	data() {
		return {
			bannerList: [
				{
					image: '/static/home/<USER>'
				}
			], // 不设置默认值，只从API获取
			userInfo: {},
			points: 0,
			balance: '0.00',
			isLogin: false, // 登录状态
			statusBarHeight: 20, // 默认状态栏高度
			topSellingProducts: [], // 热销商品数据
			iconsConfig: {} // 图标配置
		}
	},
	onLoad() {
		// 获取系统信息设置状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight
		
		// 页面加载时获取本地缓存数据
		const userInfo = uni.getStorageSync('userInfo') || {}
		const storedPoints = uni.getStorageSync('points')
		const storedBalance = uni.getStorageSync('balance')
		
		this.userInfo = userInfo
		if (storedPoints) this.points = storedPoints
		if (storedBalance) this.balance = storedBalance
		
		console.log('onLoad 初始化轮播图数据:', this.bannerList)
		
		// 获取热销商品数据
		this.getTopSellingProducts()
		
		// 获取图标配置
		this.getIconsConfig()
	},
	methods: {
		// 获取图标配置
		async getIconsConfig() {
			try {
				// 如果缓存中没有，则从API获取
				const res = await getIconsConfig()
				console.log('API返回图标数据:', JSON.stringify(res))
				
				if (res && res.code === 1 && res.data) {
					// 将图标配置存入本地缓存
					this.iconsConfig = res.data
				} else {
					console.log('获取图标配置失败或数据为空')
				}
			} catch (e) {
				console.error('获取图标配置出错:', e)
			}
		},

		// 预下载单个图片
		preloadImage(key, url) {
			console.log(`开始预下载图片[${key}]: ${url}`)
			uni.downloadFile({
				url: url,
				success: (res) => {
					if (res.statusCode === 200) {
						console.log(`图片[${key}]下载成功，临时路径: ${res.tempFilePath}`)
						// 将远程图片URL替换为本地临时路径
						this.iconsConfig[key] = res.tempFilePath
						// 更新缓存
						uni.setStorageSync('iconsConfig', JSON.stringify(this.iconsConfig))
					} else {
						console.error(`图片[${key}]下载失败，状态码: ${res.statusCode}`)
					}
				},
				fail: (err) => {
					console.error(`图片[${key}]下载出错:`, err)
				}
			})
		},
		// 获取轮播图数据
		async getBannerList() {
			try {
				// 使用专门的API函数获取轮播图
				const res = await getBanners()
				
				// 增加数据验证
				let bannerData = []
				
				if (res && res.code === 1 && res.list && Array.isArray(res.list)) {
					bannerData = res.list
				} else if (res && res.data && Array.isArray(res.data.list)) {
					bannerData = res.data.list
				} else if (res && Array.isArray(res.data)) {
					bannerData = res.data
				}
				
				if (bannerData.length > 0) {
					// 确保格式符合swiper要求 - 需要包含image字段
					this.bannerList = bannerData.map(item => {
						// 如果已经是正确格式的对象
						if (item && typeof item === 'object' && item.image) {
							return {
								image: item.image
							}
						}
						// 如果是字符串URL
						else if (typeof item === 'string') {
							return {
								image: item
							}
						}
						// 其他情况，尝试提取image属性
						else if (item && typeof item === 'object') {
							// 尝试查找可能的图片URL字段
							const imageUrl = item.image || item.img || item.url || item.src || item.imageUrl || item.imgUrl || item.path || ''
							// 检查URL是否为相对路径，如果是则添加域名
							if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {
								return {
									image: '/static/home/<USER>' // 如果是相对路径，添加静态文件夹前缀
								}
							}
							return {
								image: imageUrl
							}
						}
						return null
					}).filter(item => item && item.image)
				}
				
				// 如果API没有返回数据，则保持轮播图为空，而不是显示默认图片
			} catch(e) {
				console.error('获取轮播图失败:', e)
				// 出错时也不设置默认图片，保持轮播图为空
			}
		},
		
		// 获取用户信息和资产
		async getUserData() {
			// 检查是否登录
			const token = uni.getStorageSync('token')
			if (!token) {
				return
			}
			
			try {
				// 使用统一接口获取用户完整信息
				const profileRes = await getUserProfile()
				if (profileRes && profileRes.code === 1 && profileRes.data) {
					const userData = profileRes.data
					
					// 更新用户信息
					this.userInfo = {
						id: userData.id,
						userId: userData.user_id,
						username: userData.username,
						nickName: userData.nickname,
						avatarUrl: userData.avatar,
						phone: userData.mobile || ''
					}
					
					// 更新资产信息
					this.points = parseInt(userData.score || 0)
					this.balance = parseFloat(userData.money || 0).toFixed(2)
					
					// 更新本地存储
					uni.setStorageSync('userInfo', this.userInfo)
					uni.setStorageSync('points', this.points)
					uni.setStorageSync('balance', this.balance)
					
					console.log('用户数据获取成功:', userData.nickname)
					return
				}
				
				// 如果统一接口失败，尝试分别获取
				this.getFallbackUserData()
			} catch (error) {
				console.error('获取用户数据失败:', error)
				this.getFallbackUserData()
			}
		},
		
		// 备用方案：分别获取用户信息和资产
		async getFallbackUserData() {
			try {
				// 获取资产信息
				const assetsRes = await getUserAssets()
				if (assetsRes && assetsRes.code === 1 && assetsRes.data) {
					this.points = parseInt(assetsRes.data.points || 0)
					this.balance = parseFloat(assetsRes.data.balance || 0).toFixed(2)
					
					uni.setStorageSync('points', this.points)
					uni.setStorageSync('balance', this.balance)
				} else {
					// 如果获取资产信息失败，分别获取积分和余额
					this.getUserPointsAndBalance()
				}
			} catch (e) {
				console.error('获取资产信息失败，尝试分别获取:', e)
				this.getUserPointsAndBalance()
			}
		},
		
		// 分别获取积分和余额
		async getUserPointsAndBalance() {
			try {
				// 获取积分
				const pointsRes = await getUserPoints()
				if (pointsRes && pointsRes.code === 1) {
					this.points = parseInt(pointsRes.data.points || 0)
					uni.setStorageSync('points', this.points)
				}
				
				// 获取余额
				const balanceRes = await getUserBalance()
				if (balanceRes && balanceRes.code === 1) {
					this.balance = parseFloat(balanceRes.data.balance || 0).toFixed(2)
					uni.setStorageSync('balance', this.balance)
				}
			} catch (e) {
				console.error('获取积分或余额失败:', e)
			}
		},
		
		handleBannerChange(index) {
			console.log('当前轮播图索引：', index)
		},
		handleBannerClick(index) {
			console.log('点击了第' + (index + 1) + '张轮播图')
		},
		handleMemberCode() {
			uni.navigateTo({
				url: '/pages/invite/poster',
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		},
		handleStoreOrder() {
			// 保持堂食的选择状态
			uni.setStorageSync('diningType', 'dine-in')
			
			uni.switchTab({
				url: '/pages/menu/menu'
			})
		},
		handleDeliveryOrder() {
			// 保存外卖选择状态
			uni.setStorageSync('diningType', 'takeout')
			
			// 跳转到点餐页面
			uni.switchTab({
				url: '/pages/menu/menu'
			})
		},
		handleGroupBuy() {
			uni.showToast({
				title: '团购功能开发中',
				icon: 'none'
			})
		},
		
		goToRecharge() {
			uni.navigateTo({
				url: '/pages/my/recharge',
			})
		},
		handlePointsMall() {
			uni.navigateTo({
				url: '/pages/points/mall',
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		},
		goToCoupon() {
			uni.navigateTo({
				url: '/pages/coupon/coupon',
			})
		},
		// 跳转到登录页面
		goToLogin() {
			uni.redirectTo({
				url: '/pages/login/login'
			})
		},
		goToProductDetail(item) {
			// 跳转到菜单页面，并传递分类ID
			if (item.category_id) {
				// 储存要滚动到的商品ID
				uni.setStorageSync('scrollToProductId', item.id)
				
				// 跳转到菜单页面并传递分类ID
				uni.switchTab({
					url: '/pages/menu/menu',
					success: () => {
						// 通过事件总线触发菜单页面切换到对应分类
						uni.$emit('switchCategory', {
							categoryId: item.category_id,
							productId: item.id
						})
					}
				})
			} else {
				uni.switchTab({
					url: '/pages/menu/menu'
				})
			}
		},
		addToCart(item) {
			// 判断是否为单规格商品
			if (item.spec_type === 'single') {
				// 单规格商品直接加入购物车
				const price = parseFloat(item.price || 0).toFixed(2);
				
				// 构建购物车商品数据
				const cartItem = {
					id: item.id,
					name: item.name,
					price: price,
					image: item.image,
					count: 1,
					totalPrice: price,
					spec_type: 'single',
					specs: '默认规格',
					props_text: '默认规格'
				}
				
				// 获取当前购物车数据
				const cartData = uni.getStorageSync('cartData') || {
					list: [],
					total: 0,
					price: 0
				}
				
				// 检查购物车是否已有相同商品
				const existingItemIndex = cartData.list.findIndex(i => i.id === item.id)
				
				if (existingItemIndex > -1) {
					// 已存在则数量+1
					cartData.list[existingItemIndex].count += 1
					cartData.list[existingItemIndex].totalPrice = Number(cartData.list[existingItemIndex].price) * cartData.list[existingItemIndex].count
				} else {
					// 不存在则添加
					cartData.list.push(cartItem)
				}
				
				// 重新计算总数和总价
				cartData.total = cartData.list.reduce((sum, item) => sum + item.count, 0)
				cartData.price = cartData.list.reduce((sum, item) => sum + Number(item.totalPrice), 0)
				
				// 更新购物车数据
				uni.setStorageSync('cartData', cartData)
				
				// 显示添加成功提示
				uni.showToast({
					title: '已加入购物车',
					icon: 'none'
				})
			} else {
				// 多规格商品，跳转到商品详情让用户选择规格
				this.goToProductDetail(item)
			}
		},
		// 获取热销商品数据
		async getTopSellingProducts() {
			try {
				const res = await getTopSellingProducts({ limit: 10 })
				console.log('获取热销商品数据:', res)
				
				let productsData = []
				if (res && res.code === 1) {
					if (res.data && Array.isArray(res.data.list)) {
						productsData = res.data.list
					} else if (Array.isArray(res.data)) {
						productsData = res.data
					} else if (Array.isArray(res.list)) {
						productsData = res.list
					}
				}
				
				// 处理数据并设置is_new标识
				this.topSellingProducts = productsData.map(item => {
					// 确保价格为字符串
					const price = typeof item.price === 'number' ? item.price.toFixed(2) : item.price
					
					// 随机设置20%的商品为新品
					const isNew = Math.random() > 0.8

					// 确保图片路径正确
					let image = item.image || ''
					if (image && !image.startsWith('http') && !image.startsWith('/')) {
						image = '/' + image
					}

					return {
						...item,
						price,
						is_new: isNew,
						image
					}
				})
			} catch (error) {
				console.error('获取热销商品失败:', error)
			}
		}
	},
	async onShow() {
		// 检查登录状态
		const token = uni.getStorageSync('token')
		this.isLogin = !!token
		
		// 获取本地存储的用户信息
		const userInfo = uni.getStorageSync('userInfo') || {}
		this.userInfo = userInfo
		
		// 从本地存储获取最近的积分和余额数据（用于快速显示）
		const storedPoints = uni.getStorageSync('points')
		const storedBalance = uni.getStorageSync('balance')
		
		if (storedPoints) this.points = storedPoints
		if (storedBalance) this.balance = storedBalance
		
		// 获取轮播图数据
		console.log('onShow 开始获取轮播图')
		this.getBannerList()
		
		// 获取热销商品数据
		this.getTopSellingProducts()
		
		// 获取最新的用户数据
		if (this.isLogin) {
			this.getUserData()
		}
		
		// 重新获取图标配置
		this.getIconsConfig()
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background-color: #f8f8f8;
	padding-bottom: env(safe-area-inset-bottom);
	
	.status-bar-placeholder {
		display: none; /* 隐藏额外空间 */
	}
	
	.swiper {
		width: 100%;
		height: 380rpx; /* 增加轮播图高度 */
		margin-top: 0; /* 移除轮播图顶部外边距 */
		margin-bottom: 10rpx; /* 轮播图底部外边距 */
		padding-top: env(safe-area-inset-top); /* 添加安全区域适配 */
		
		.swiper-item {
			width: 100%;
			height: 100%;
			display: block;
		}
		
		.swiper-image {
			width: 100%;
			height: 100%;
			display: block;
			object-fit: cover; /* 确保图片填充整个容器且保持纵横比 */
		}
	}
	
	.banner {
		width: 100%;
		height: 320rpx;
		
		::v-deep .u-swiper__wrapper {
			height: 320rpx !important;
		}
		
		::v-deep .u-swiper__wrapper__item__wrapper__image {
			width: 100%;
			height: 100%;
			border-radius: 0;
		}
	}
	
	.member-card {
		margin: 20rpx 20rpx 0rpx; /* 增加上下边距 */
		
		.member-info {
			background: #fff;
			border-radius: 20rpx;
			padding: 30rpx;
			display: flex;
			justify-content: space-between;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
			
			&.not-login {
				align-items: center;
				
				.login-tip {
					font-size: 24rpx;
					color: #666;
					margin-top: 6rpx;
				}
				
				.login-btn {
					background-color: #78c238;
					padding: 16rpx 40rpx;
					border-radius: 40rpx;
					margin-left: 20rpx;
					
					text {
						color: #fff;
						font-size: 28rpx;
					}
				}
			}
			
			.info-content {
				flex: 1;
				
				.user-info {
					display: flex;
					align-items: center;
					margin-bottom: 30rpx;
					
					.avatar-wrap {
						position: relative;
						margin-right: 20rpx;
						
						.avatar {
							width: 100rpx;
							height: 100rpx;
							border-radius: 50%;
						}
						
						.vip-badge {
							position: absolute;
							right: -6rpx;
							bottom: -6rpx;
							width: 32rpx;
							height: 32rpx;
							background: #8cd548;
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}
					
					.user-detail {
						.name {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
							margin-bottom: 8rpx;
							display: block;
						}
						
						.level {
							font-size: 24rpx;
							color: #8cd548;
							background: rgba(140, 213, 72, 0.1);
							padding: 4rpx 12rpx;
							border-radius: 8rpx;
						}
					}
				}
				
				.asset-info {
					display: flex;
					align-items: center;
					
					.asset-item {
						flex: 1;
						text-align: center;
						
						.value {
							font-size: 36rpx;
							color: #333;
							font-weight: bold;
							display: block;
							margin-bottom: 8rpx;
						}
						
						.label {
							font-size: 24rpx;
							color: #999;
						}
					}
					
					.divider {
						width: 1rpx;
						height: 36rpx;
						background: #eee;
						margin: 0 30rpx;
					}
				}
			}
			
			.qr-code {
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				
				.code-wrap {
					width: 80rpx;
					height: 80rpx;
					margin-bottom: 8rpx;
					
					.code-icon {
						width: 100%;
						height: 100%;
					}
				}
				
				text {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.unified-white-box {
		margin: 20rpx 20rpx 0rpx; /* 增加上下边距 */
		background: #fff;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		
		.order-section {
			margin-bottom: 30rpx;
			display: flex;
			gap: 15rpx;
			
			.order-item {
				flex: 1;
				min-width: 0;
				border-radius: 16rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				
				.order-icon {
					width: 300rpx;
					height: 220rpx;
				}
				
				.order-content {
					width: 100%;
					text-align: center;
					display: flex;
					flex-direction: column;
					align-items: center;
					
					.title {
						font-size: 28rpx;
						color: #333;
						font-weight: bold;
						margin-bottom: 8rpx;
					}
					
					.desc {
						font-size: 24rpx;
						color: #999;
						white-space: normal;
						text-align: center;
						line-height: 1.4;
					}
				}
				
				&:active {
					transform: scale(0.98);
				}
			}
		}
		
		.divider-line {
			height: 1rpx;
			background: #eee;
			margin: 30rpx 0;
		}
		
		.feature-section {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 20rpx;
			
			.feature-item {
				background: transparent;
				padding: 20rpx 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				
				.feature-icon {
					width: 120rpx;
					height: 120rpx;
					margin-bottom: 12rpx;
				}
				
				text {
					font-size: 26rpx;
					color: #333;
					width: 100%;
					text-align: center;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}
	
	.hot-selling-section {
		margin: 20rpx 20rpx 40rpx; /* 增加底部边距 */
		padding: 20rpx;
		background: #fff;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		
		.section-header {
			margin-bottom: 20rpx;
			padding: 0 10rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			.section-title {
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
				position: relative;
				padding-left: 16rpx;
				
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 6rpx;
					height: 32rpx;
					background: linear-gradient(to bottom, #8cd548, #6ab52e);
					border-radius: 3rpx;
				}
			}
		}
		
		.product-scroll {
			white-space: nowrap;
			width: 100%;
			padding: 20rpx;
			/* 隐藏滚动条但保留滚动功能 */
			&::-webkit-scrollbar {
				display: none;
			}
			
			.product-card {
				display: inline-block;
				width: 200rpx;
				margin-right: 50rpx;
				border-radius: 16rpx;
				overflow: hidden;
				position: relative;
				background: #fff;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
				vertical-align: top;
				
				&:last-child {
					margin-right: 0;
				}
				
				.product-tag {
					position: absolute;
					top: 10rpx;
					left: 10rpx;
					background: #ff5722;
					color: #fff;
					font-size: 20rpx;
					padding: 4rpx 12rpx;
					border-radius: 20rpx;
					z-index: 2;
				}
				
				.product-image {
					width: 100%;
					height: 180rpx;
					object-fit: cover;
				}
				
				.product-info {
					padding: 10rpx;
					background: #fff;
					
					.product-name {
						font-size: 24rpx;
						color: #333;
						margin-bottom: 10rpx;
						white-space: normal;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 1;
						-webkit-box-orient: vertical;
						height: 34rpx;
						line-height: 1.4;
					}
					
					.product-price-row {
						display: flex;
						align-items: center;
						justify-content: space-between;
						
						.product-price {
							font-size: 24rpx;
							color: #333;
							font-weight: bold;
						}
						
						.add-btn {
							width: 48rpx;
							height: 48rpx;
							background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							box-shadow: 0 4rpx 8rpx rgba(140, 213, 72, 0.2);
							
							.add-icon {
								color: #fff;
								font-size: 32rpx;
								line-height: 1;
								font-weight: bold;
							}
							
							&:active {
								transform: scale(0.95);
								opacity: 0.9;
							}
						}
					}
				}
				
				&:active {
					transform: scale(0.98);
				}
			}
		}
	}
}

@keyframes shine {
	0% {
		transform: translate(-50%, -50%) rotate(0deg);
	}
	100% {
		transform: translate(50%, 50%) rotate(0deg);
	}
}
</style>