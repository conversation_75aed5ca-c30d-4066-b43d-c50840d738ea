<template>
  <view class="order-list">
    <view 
      v-for="(item, index) in processedOrderList" 
      :key="index" 
      class="order-card"
      @tap="goToDetail(item)"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="left">
          <text class="order-no">订单号: {{item.order_no || item.orderNo}}</text>
          <text class="order-time">{{item.createtime_text || item.createTime}}</text>
        </view>
        <view class="status-tag" :class="{
          'status-pending': item.status === 'pending',
          'status-paid': item.status === 'paid',
          'status-cooking': item.status === 'cooking',
          'status-cooked': item.status === 'cooked',
          'status-delivering': item.status === 'delivering',
          'status-processing': item.status === 'processing',
          'status-completed': item.status === 'completed',
          'status-cancelled': item.status === 'cancelled',
          'status-default': !['pending', 'paid', 'cooking', 'cooked', 'delivering', 'processing', 'completed', 'cancelled'].includes(item.status)
        }">
          {{item.status_text || getStatusText(item.status)}}
        </view>
      </view>

      <!-- 商品列表 -->
      <view class="product-list">
        <view class="product-item" v-for="(product, pIndex) in item.products" :key="pIndex">
          <view class="product-info">
          <text class="product-name">{{product.name}}</text>
            <text class="product-specs" v-if="product.specs">{{product.specs}}</text>
          </view>
          <view class="product-price">
            <text class="price">¥{{product.price}}</text>
            <text class="count">×{{product.count}}</text>
          </view>
        </view>
      </view>

      <!-- 订单底部 -->
      <view class="order-footer">
        <view class="total">
          <text class="count-text">共{{getTotalCount(item.products)}}件商品</text>
          <text class="price">实付 <text class="price-value">¥{{item.final_price || item.totalPrice}}</text></text>
        </view>
        <view class="actions">
          <view 
            class="action-btn outline" 
            @tap.stop="reorder(item)"
          >再来一单</view>
          
          <view 
            class="action-btn outline" 
            v-if="['pending', 'paid'].includes(item.status)" 
            @tap.stop="cancelOrder(item)"
          >取消订单</view>
          
          <view 
            class="action-btn primary" 
            v-if="item.status === 'pending'" 
            @tap.stop="payOrder(item)"
          >去支付</view>
          
          <view 
            class="action-btn primary" 
            v-if="item.status === 'paid' || item.status === 'cooking' || item.status === 'cooked' || item.status === 'delivering'" 
            @tap.stop="checkOrder(item)"
          >查看进度</view>
          
          <view 
            class="action-btn primary" 
            v-if="item.status === 'completed'" v-show="false"
            @tap.stop="goToComment(item)"
          >评价订单</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getWxPayParams, queryWxPayResult, payOrderWithBalance, cancelOrder } from '@/api/order';

export default {
  props: {
    orderList: {
      type: Array,
      default: () => []
    },
    currentTab: {
      type: Number,
      default: 0
    }
  },
  
  computed: {
    processedOrderList() {
        return this.orderList.map(order => {
        return {
          ...order,
          products: order.products || [],
          status:  order.status,
          orderNo: order.orderNo || '',
          createTime: order.createTime || '',
          totalPrice: order.totalPrice || 0
        };
      });
    }
  },
  
  methods: {
    getTotalCount(products) {
      if (!products || !Array.isArray(products)) {
        return 0;
      }
      return products.reduce((sum, item) => sum + (item.count || 0), 0);
    },
    
    getStatusText(status) {
      const statusMap = {
        'pending': '待支付',
        'paid': '已支付',
        'cooking': '制作中',
        'cooked': '制作完成',
        'delivering': '配送中',
        'processing': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      
      return statusMap[status] || status || '未知状态';
    },
    
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/order/detail?id=${item.order_no || item.orderNo}`
      });
    },
    
    reorder(item) {
      // 实现再来一单功能
      uni.showLoading({
        title: '加载中...'
      });
      
      setTimeout(() => {
        uni.hideLoading();
        uni.switchTab({
          url: '/pages/menu/menu'
        });
      }, 1000);
    },
    
    checkOrder(item) {
      this.goToDetail(item);
    },
    
    // 评价订单
    goToComment(item) {
      uni.showToast({
        title: '评价功能开发中',
        icon: 'none'
      });
      
      // 可以跳转到评价页面，这里先用提示代替
      // uni.navigateTo({
      //   url: `/pages/order/comment?id=${item.order_no || item.orderNo}`
      // });
    },
    
    // 支付订单
    async payOrder(item) {
      const orderNo = item.order_no || item.orderNo;
      
      // 弹出支付方式选择
      uni.showActionSheet({
        itemList: ['微信支付', '余额支付'],
        success: async (res) => {
          const payType = res.tapIndex;
          
          if (payType === 0) {
            // 微信支付
            this.wxPay(orderNo);
          } else if (payType === 1) {
            // 余额支付
            this.balancePay(orderNo);
          }
        }
      });
    },
    
    // 微信支付
    async wxPay(orderNo) {
      try {
        // 获取微信支付参数
        const payResult = await getWxPayParams(orderNo);
        
        if (payResult.code !== 1) {
          uni.showToast({
            title: payResult.msg || '获取支付参数失败',
            icon: 'none'
          });
          return;
        }
        
        // 调用微信支付
        uni.requestPayment({
          ...payResult.data,
          success: () => {
            this.checkPaymentResult(orderNo);
          },
          fail: (err) => {
            console.log('支付失败', err);
            uni.showToast({
              title: '支付已取消',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '支付异常，请稍后再试',
          icon: 'none'
        });
        console.error('支付出错', error);
      }
    },
    
    // 余额支付
    async balancePay(orderNo) {
      try {
        uni.showLoading({ title: '处理中...' });
        
        // 调用余额支付API
        const payResult = await payOrderWithBalance(orderNo);
        uni.hideLoading();
        
        if (payResult.code === 1) {
          uni.showToast({
            title: '支付成功',
            icon: 'none'
          });
          // 通知父组件刷新订单列表
          this.$emit('refresh');
        } else {
          // 如果为2 跳转充值页面
          if (payResult.code === 2) {
            // 显示确认对话框，让用户选择是否跳转到充值页面
            uni.showModal({
              title: '余额不足',
              content: '您的余额不足，是否前往充值？',
              confirmText: '去充值',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  // 用户点击确认，跳转到充值页面
                  uni.navigateTo({
                    url: '/pages/my/recharge'
                  });
                }
              }
            });
          }
          uni.showToast({
            title: payResult.msg || '余额不足',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '支付失败，请稍后再试',
          icon: 'none'
        });
        console.error('余额支付出错', error);
      }
    },
    
    // 检查支付结果
    async checkPaymentResult(orderNo) {
      try {
        uni.showLoading({ title: '正在查询支付结果...' });
        
        // 查询支付结果
        const queryResult = await queryWxPayResult(orderNo);
        uni.hideLoading();
        
        if (queryResult.code === 1 && queryResult.data && queryResult.data.pay_status === 1) {
          uni.showToast({
            title: '支付成功',
            icon: 'none'
          });
          
          // 通知父组件刷新订单列表
          this.$emit('refresh');
        } else {
          uni.showToast({
            title: queryResult.msg || '支付结果查询失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '查询支付结果失败',
          icon: 'none'
        });
        console.error('查询支付结果出错', error);
      }
    },
    
    // 取消订单
    cancelOrder(item) {
      const orderNo = item.order_no || item.orderNo;
      
      uni.showModal({
        title: '取消订单',
        content: '确定要取消该订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({ title: '处理中...' });
              
              const result = await cancelOrder(orderNo);
              uni.hideLoading();
              
              if (result.code === 1) {
                uni.showToast({
                  title: '订单已取消',
                  icon: 'none'
                });
                
                // 通知父组件刷新订单列表
                this.$emit('refresh');
              } else {
                uni.showToast({
                  title: result.msg || '取消失败，请稍后再试',
                  icon: 'none'
                });
              }
            } catch (error) {
              uni.hideLoading();
              uni.showToast({
                title: '取消订单失败',
                icon: 'none'
              });
              console.error('取消订单出错', error);
            }
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
@import '@/styles/theme.scss';

.order-list {
  padding: 0 20rpx;
  
  .order-card {
    margin-bottom: 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
    
    .left {
      display: flex;
      flex-direction: column;
      
      .order-no {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 8rpx;
      }
      
      .order-time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .status-tag {
      padding: 6rpx 16rpx;
      border-radius: 24rpx;
      font-size: 24rpx;
      font-weight: 500;
      
      &.status-pending {
        background-color: #fff8e6;
        color: #ff9500;
      }
      
      &.status-paid {
        background-color: #e6edff;
        color: #4c84ff;
      }
      
      &.status-cooking {
        background-color: #ffebeb;
        color: #ff6b6b;
      }
      
      &.status-cooked {
        background-color: #fff0e0;
        color: #ffa64d;
      }
      
      &.status-delivering {
        background-color: #e8efff;
        color: #5e7ce0;
      }
      
      &.status-processing {
        background-color: #e6f7ff;
        color: #0076ff;
      }
      
      &.status-completed {
        background-color: #e6fff0;
        color: #52c41a;
      }
      
      &.status-cancelled {
        background-color: #f5f5f5;
        color: #999;
      }
      
      &.status-default {
        background-color: #f5f5f5;
        color: #666;
      }
    }
  }
  
  .product-list {
    padding: 20rpx 30rpx;
    
    .product-item {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 16rpx 0;
      
      &:not(:last-child) {
        border-bottom: 1rpx solid #f5f5f5;
      }
      
      .product-info {
        flex: 1;
        margin-right: 20rpx;
        
        .product-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 4rpx;
      }
      
      .product-specs {
          font-size: 24rpx;
          color: #999;
          display: block;
          margin-top: 6rpx;
        }
      }
      
      .product-price {
        display: flex;
        align-items: center;
        
        .price {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
        }
        
        .count {
          margin-left: 12rpx;
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
  
  .order-footer {
    padding: 24rpx 30rpx;
    
    .total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      
      .count-text {
        font-size: 24rpx;
        color: #666;
      }
      
      .price {
        font-size: 24rpx;
        color: #333;
        
        .price-value {
          font-size: 32rpx;
        font-weight: bold;
          color: #ff4d4f;
        }
      }
    }
    
    .actions {
      display: flex;
      justify-content: flex-end;
      
      .action-btn {
        padding: 12rpx 24rpx;
        border-radius: 30rpx;
        font-size: 26rpx;
        margin-left: 20rpx;
        
        &.outline {
          border: 1rpx solid #ddd;
          color: #666;
        }
        
        &.primary {
          background-color: #8cd548;
          color: #fff;
        }
        
        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>