<view class="page data-v-5c101a1c"><view class="header data-v-5c101a1c"><view class="nav-bar data-v-5c101a1c"><image class="back-icon data-v-5c101a1c" src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" bindtap="__e"></image><text class="title data-v-5c101a1c">确认订单</text></view></view><scroll-view class="content data-v-5c101a1c" scroll-y="{{true}}"><view class="section-card store-section data-v-5c101a1c"><view class="store-info data-v-5c101a1c"><image class="store-icon data-v-5c101a1c" src="/static/order_submit/7cf83e4f8b5fdfea873cd11d4b01d044.png"></image><view class="store-detail data-v-5c101a1c"><text class="store-name data-v-5c101a1c">{{storeInfo.name||'加载中...'}}</text><text class="pickup-time data-v-5c101a1c">{{storeInfo.pickupTime||'尽快取餐'}}</text></view></view><view class="pickup-type data-v-5c101a1c"><view data-event-opts="{{[['tap',[['switchPickupType',['dine-in']]]]]}}" class="{{['type-item','data-v-5c101a1c',(pickupType==='dine-in')?'active':'']}}" bindtap="__e"><image src="/static/order_submit/bbdacb2185a84d001e76b2cfde875314.png" class="data-v-5c101a1c"></image><view class="type-info data-v-5c101a1c"><text class="type-name data-v-5c101a1c">堂食</text><text class="type-desc data-v-5c101a1c">店内就餐</text></view></view><view data-event-opts="{{[['tap',[['switchPickupType',['takeout']]]]]}}" class="{{['type-item','data-v-5c101a1c',(pickupType==='takeout')?'active':'']}}" bindtap="__e"><image src="/static/order_submit/42647fbb92d04591f3631ba857a2035f.png" class="data-v-5c101a1c"></image><view class="type-info data-v-5c101a1c"><text class="type-name data-v-5c101a1c">外带</text><text class="type-desc data-v-5c101a1c">打包带走</text></view></view></view><block wx:if="{{pickupType==='takeout'}}"><view class="delivery-options data-v-5c101a1c"><view data-event-opts="{{[['tap',[['showTimeSelector',['$event']]]]]}}" class="option-item data-v-5c101a1c" bindtap="__e"><view class="option-left data-v-5c101a1c"><image class="option-icon data-v-5c101a1c" src="/static/order_submit/time.png"></image><text class="option-label data-v-5c101a1c">取餐时间</text></view><view class="option-right data-v-5c101a1c"><view class="value-container data-v-5c101a1c"><text class="option-value data-v-5c101a1c">{{selectedTime?selectedTime:'立即取餐'}}</text></view><u-icon vue-id="25f3aa6a-1" name="arrow-right" color="#ccc" size="24" class="data-v-5c101a1c" bind:__l="__l"></u-icon></view></view><view data-event-opts="{{[['tap',[['selectAddress',['$event']]]]]}}" class="option-item data-v-5c101a1c" bindtap="__e"><view class="option-left data-v-5c101a1c"><image class="option-icon data-v-5c101a1c" src="/static/order_submit/address.png"></image><text class="option-label data-v-5c101a1c">收货地址</text></view><view class="option-right data-v-5c101a1c"><view class="value-container data-v-5c101a1c"><block wx:if="{{selectedAddress}}"><text class="option-value data-v-5c101a1c">{{''+selectedAddress.name+" "+selectedAddress.phone+''}}</text></block><block wx:else><text class="option-placeholder data-v-5c101a1c">请选择收货地址</text></block></view><u-icon vue-id="25f3aa6a-2" name="arrow-right" color="#ccc" size="24" class="data-v-5c101a1c" bind:__l="__l"></u-icon></view></view><block wx:if="{{selectedAddress}}"><view class="address-detail data-v-5c101a1c"><text class="data-v-5c101a1c">{{$root.m0}}</text></view></block></view></block></view><view class="section-card data-v-5c101a1c"><view class="section-header data-v-5c101a1c"><text class="section-title data-v-5c101a1c">订单商品</text><view data-event-opts="{{[['tap',[['goToMenu',['$event']]]]]}}" class="add-btn data-v-5c101a1c" bindtap="__e"><text class="data-v-5c101a1c">继续点单</text></view></view><view class="product-list data-v-5c101a1c"><block wx:for="{{cartList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="product-item data-v-5c101a1c"><image class="product-image data-v-5c101a1c" src="{{item.image}}" mode="aspectFill"></image><view class="product-info data-v-5c101a1c"><text class="product-name data-v-5c101a1c">{{item.name}}</text><block wx:if="{{item.props_text}}"><text class="product-spec data-v-5c101a1c">{{item.props_text}}</text></block><view class="price-info data-v-5c101a1c"><text class="price data-v-5c101a1c">{{"¥"+item.price}}</text></view></view><view class="product-count data-v-5c101a1c"><text class="count data-v-5c101a1c">{{"×"+item.count}}</text></view></view></block></view></view><view class="section-card data-v-5c101a1c"><view data-event-opts="{{[['tap',[['selectCoupon',['$event']]]]]}}" class="option-item data-v-5c101a1c" bindtap="__e"><view class="option-left data-v-5c101a1c"><image class="option-icon data-v-5c101a1c" src="/static/order/coupon.png"></image><text class="option-label data-v-5c101a1c">优惠券</text></view><view class="option-right data-v-5c101a1c"><block wx:if="{{selectedCoupon}}"><text class="discount data-v-5c101a1c">{{"-¥"+discountAmount}}</text></block><view class="value-container data-v-5c101a1c"><block wx:if="{{selectedCoupon}}"><text class="option-value data-v-5c101a1c">{{selectedCoupon.name}}</text></block><block wx:else><text class="option-placeholder data-v-5c101a1c">未使用优惠券</text></block></view><u-icon vue-id="25f3aa6a-3" name="arrow-right" color="#ccc" size="24" class="data-v-5c101a1c" bind:__l="__l"></u-icon></view></view><view data-event-opts="{{[['tap',[['addRemark',['$event']]]]]}}" class="option-item data-v-5c101a1c" bindtap="__e"><view class="option-left data-v-5c101a1c"><image class="option-icon data-v-5c101a1c" src="/static/order/remark.png"></image><text class="option-label data-v-5c101a1c">备注</text></view><view class="option-right data-v-5c101a1c"><view class="value-container data-v-5c101a1c"><block wx:if="{{remark}}"><text class="option-value data-v-5c101a1c">{{remark}}</text></block><block wx:else><text class="option-placeholder data-v-5c101a1c">添加备注</text></block></view><u-icon vue-id="25f3aa6a-4" name="arrow-right" color="#ccc" size="24" class="data-v-5c101a1c" bind:__l="__l"></u-icon></view></view></view><view class="section-card data-v-5c101a1c"><view class="section-header data-v-5c101a1c"><text class="section-title data-v-5c101a1c">支付方式</text></view><view class="payment-list data-v-5c101a1c"><view data-event-opts="{{[['tap',[['switchPayment',['wechat']]]]]}}" class="{{['payment-item','data-v-5c101a1c',(paymentMethod==='wechat')?'active':'']}}" bindtap="__e"><view class="payment-info data-v-5c101a1c"><image class="payment-icon data-v-5c101a1c" src="/static/order/wx.png"></image><text class="payment-name data-v-5c101a1c">微信支付</text></view><view class="{{['radio','data-v-5c101a1c',(paymentMethod==='wechat')?'active':'']}}"></view></view><view data-event-opts="{{[['tap',[['switchPayment',['balance']]]]]}}" class="{{['payment-item','data-v-5c101a1c',(paymentMethod==='balance')?'active':'']}}" bindtap="__e"><view class="payment-info data-v-5c101a1c"><image class="payment-icon data-v-5c101a1c" src="/static/order/yuer.png"></image><text class="payment-name data-v-5c101a1c">余额支付</text><text class="balance-info data-v-5c101a1c">{{"当前余额: ¥"+$root.g0}}</text></view><view class="{{['radio','data-v-5c101a1c',(paymentMethod==='balance')?'active':'']}}"></view></view></view></view></scroll-view><view style="height:180rpx;" class="data-v-5c101a1c"></view><view class="bottom-bar data-v-5c101a1c"><view class="price-info data-v-5c101a1c"><view class="price-details data-v-5c101a1c"><text class="total-text data-v-5c101a1c">合计</text><text class="total-price data-v-5c101a1c">{{"¥"+$root.g1}}</text></view><block wx:if="{{discountAmount>0}}"><text class="discount-text data-v-5c101a1c">{{"(已优惠¥"+discountAmount+")"}}</text></block></view><view data-event-opts="{{[['tap',[['submitOrder',['$event']]]]]}}" class="{{['submit-btn','data-v-5c101a1c',(isSubmitting)?'disabled':'']}}" bindtap="__e"><text class="data-v-5c101a1c">{{isSubmitting?'处理中...':'立即支付'}}</text></view></view><u-popup vue-id="25f3aa6a-5" show="{{showTimePicker}}" mode="bottom" border-radius="20" data-event-opts="{{[['^close',[['closeTimeSelector']]]]}}" bind:close="__e" class="data-v-5c101a1c" bind:__l="__l" vue-slots="{{['default']}}"><view class="time-selector data-v-5c101a1c"><view class="selector-header data-v-5c101a1c"><text class="title data-v-5c101a1c">选择取餐时间</text><view data-event-opts="{{[['tap',[['closeTimeSelector',['$event']]]]]}}" class="close data-v-5c101a1c" bindtap="__e"><u-icon vue-id="{{('25f3aa6a-6')+','+('25f3aa6a-5')}}" name="close" color="#999" size="32" class="data-v-5c101a1c" bind:__l="__l"></u-icon></view></view><scroll-view class="time-list data-v-5c101a1c" scroll-y="{{true}}"><view data-event-opts="{{[['tap',[['selectImmediatePickup',['$event']]]]]}}" class="{{['time-option','immediate','data-v-5c101a1c',(tempSelectedTime==='')?'active':'']}}" bindtap="__e"><view class="time-content data-v-5c101a1c"><text class="time data-v-5c101a1c">立即取餐</text><text class="desc data-v-5c101a1c">预计15分钟内可取</text></view><view class="{{['radio','data-v-5c101a1c',(tempSelectedTime==='')?'active':'']}}"></view></view><block wx:for="{{timeSlots}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectTime',['$0'],[[['timeSlots','',index]]]]]]]}}" class="{{['time-option','data-v-5c101a1c',(tempSelectedTime===item.time)?'active':'']}}" bindtap="__e"><view class="time-content data-v-5c101a1c"><text class="time data-v-5c101a1c">{{item.time}}</text><text class="desc data-v-5c101a1c">{{item.desc}}</text></view><view class="{{['radio','data-v-5c101a1c',(tempSelectedTime===item.time)?'active':'']}}"></view></view></block></scroll-view><view class="selector-footer data-v-5c101a1c"><view data-event-opts="{{[['tap',[['confirmTimeSelect',['$event']]]]]}}" class="confirm-btn data-v-5c101a1c" bindtap="__e"><text class="data-v-5c101a1c">确定</text></view></view></view></u-popup><u-popup vue-id="25f3aa6a-7" show="{{showRemarkPopup}}" mode="bottom" border-radius="20" data-event-opts="{{[['^close',[['closeRemarkPopup']]]]}}" bind:close="__e" class="data-v-5c101a1c" bind:__l="__l" vue-slots="{{['default']}}"><view class="remark-editor data-v-5c101a1c"><view class="editor-header data-v-5c101a1c"><text class="title data-v-5c101a1c">添加备注</text><view data-event-opts="{{[['tap',[['closeRemarkPopup',['$event']]]]]}}" class="close data-v-5c101a1c" bindtap="__e"><u-icon vue-id="{{('25f3aa6a-8')+','+('25f3aa6a-7')}}" name="close" color="#999" size="32" class="data-v-5c101a1c" bind:__l="__l"></u-icon></view></view><view class="editor-content data-v-5c101a1c"><textarea class="remark-input data-v-5c101a1c" placeholder="请输入备注信息，比如：少糖、少冰等" maxlength="{{100}}" auto-height="{{true}}" data-event-opts="{{[['input',[['__set_model',['','tempRemark','$event',[]]]]]]}}" value="{{tempRemark}}" bindinput="__e"></textarea><view class="word-count data-v-5c101a1c">{{$root.g2+"/100"}}</view></view><view class="editor-footer data-v-5c101a1c"><view data-event-opts="{{[['tap',[['confirmRemark',['$event']]]]]}}" class="confirm-btn data-v-5c101a1c" bindtap="__e"><text class="data-v-5c101a1c">确定</text></view></view></view></u-popup></view>