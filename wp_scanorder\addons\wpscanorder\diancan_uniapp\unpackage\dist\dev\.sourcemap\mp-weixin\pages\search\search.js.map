{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/search/search.vue?ec8d", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/search/search.vue?f4df", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/search/search.vue?8214", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/search/search.vue?b4b9", "uni-app:///pages/search/search.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/search/search.vue?f61c", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/search/search.vue?c08f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MenuOpen", "data", "keyword", "searchHistory", "hotSearchList", "searchList", "productList", "name", "desc", "price", "image", "spec", "description", "showSpecSelector", "selectedProduct", "onLoad", "methods", "handleBack", "uni", "handleInput", "handleSearch", "clearKeyword", "useHistory", "clearHistory", "title", "content", "success", "saveHistory", "searchProducts", "item", "goToDetail", "selectSpec", "product", "totalPrice", "number", "property", "values", "value", "is_default", "closeSpecSelector", "handleAddToCart", "count", "specSelected", "list", "total", "cartItem", "existingItem", "cartList", "icon", "handleCancel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eC+FnrB;EACAC;IACAC;EACA;EAEAC;IACA;MACAC;MACAC;MACAC,gBACA,OACA,MACA,MACA,MACA,MACA,QACA,OACA,MACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAL;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;MACA;MACAC;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MAAA;MACAL;QACAM;QACAC;QACAC;UACA;YACA;YACAR;UACA;QACA;MACA;IACA;IAEAS;MACA;QACA;QACA;UACA;QACA;QACAT;MACA;IACA;IAEAU;MAAA;MACA;QACA;QACA;MACA;;MAEA;MACA;QAAA,OACAC;MAAA,EACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;MACA,uDACAC;QACAC;QAAA;QACAC;QAAA;QACAC,WACA;UACA5B;UACA6B,SACA;YACAC;YACA5B;YACA6B;UACA;QAEA,GACA;UACA/B;UACA6B,SACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA;QAEA,GACA;UACA/B;UACA6B,SACA;YAAAC;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA,GACA;YAAAD;YAAAC;UAAA;QAEA;MACA,EACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA,+CACAX;QACAY;QACAR;QACAS;MAAA,EACA;MAEA;QAAAC;QAAAC;QAAAnC;MAAA;MACA;;MAEA;MACA;QAAA,OACAoC,+BACAA;MAAA,EACA;MAEA;QACAC;QACAA;MACA;QACAC;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;;MAEA;MACA;QACAJ;QACAC;QACAnC;MACA;MACAS;;MAEA;MACA;;MAEA;MACAA;QACAM;QACAwB;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzTA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/search/search.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/search/search.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"\nvar renderjs\nimport script from \"./search.vue?vue&type=script&lang=js&\"\nexport * from \"./search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4cedc0c6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/search/search.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=template&id=4cedc0c6&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.keyword ? _vm.searchHistory.length : null\n  var g1 = !!_vm.keyword ? _vm.searchList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<!-- 顶部搜索栏 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"nav-bar\">\n\t\t\t\t<image \n\t\t\t\t\tclass=\"back-icon\" \n\t\t\t\t\tsrc=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \n\t\t\t\t\t@tap=\"handleBack\"\n\t\t\t\t/>\n\t\t\t\t<view class=\"search-bar\">\n\t\t\t\t\t<view class=\"search-box\">\n\t\t\t\t\t\t<u-icon name=\"search\" size=\"20\" color=\"#999\"></u-icon>\n\t\t\t\t\t\t<input class=\"search-input\" v-model=\"keyword\" placeholder=\"搜索商品\" placeholder-class=\"placeholder\"\n\t\t\t\t\t\t\tconfirm-type=\"search\" @confirm=\"handleSearch\" @input=\"handleInput\" focus />\n\t\t\t\t\t\t<u-icon v-if=\"keyword\" name=\"close\" size=\"15\" color=\"#999\" @click=\"clearKeyword\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"cancel-btn\" @tap=\"handleCancel\">取消</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 搜索历史 -->\n\t\t<scroll-view class=\"content\" scroll-y>\n\t\t\t<block v-if=\"!keyword\">\n\t\t\t\t<view class=\"history\" v-if=\"searchHistory.length > 0\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<text class=\"title\">搜索历史</text>\n\t\t\t\t\t\t<view class=\"clear\" @tap=\"clearHistory\">\n\t\t\t\t\t\t\t<u-icon name=\"trash\" size=\"32\" color=\"#999\"></u-icon>\n\t\t\t\t\t\t\t<text>清空</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"history-list\">\n\t\t\t\t\t\t<view class=\"history-item\" v-for=\"(item, index) in searchHistory\" :key=\"index\" @tap=\"useHistory(item)\">\n\t\t\t\t\t\t\t<text>{{item}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 热门搜索 -->\n\t\t\t\t<view class=\"hot-search\">\n\t\t\t\t\t<view class=\"section-header\">\n\t\t\t\t\t\t<text class=\"title\">热门搜索</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"hot-list\">\n\t\t\t\t\t\t<view class=\"hot-item\" v-for=\"(item, index) in hotSearchList\" :key=\"index\" @tap=\"useHistory(item)\">\n\t\t\t\t\t\t\t<text :class=\"{'hot': index < 3}\">{{item}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</block>\n\n\t\t\t<!-- 搜索结 -->\n\t\t\t<block v-else>\n\t\t\t\t<view class=\"search-result\" v-if=\"searchList.length > 0\">\n\t\t\t\t\t<view class=\"result-item\" v-for=\"(item, index) in searchList\" :key=\"index\" @tap=\"goToDetail(item)\">\n\t\t\t\t\t\t<image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\" />\n\t\t\t\t\t\t<view class=\"item-info\">\n\t\t\t\t\t\t\t<text class=\"name\">{{item.name}}</text>\n\t\t\t\t\t\t\t<text class=\"desc\">{{item.desc}}</text>\n\t\t\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t\t\t<view class=\"price-left\">\n\t\t\t\t\t\t\t\t\t<text class=\"symbol\">¥</text>\n\t\t\t\t\t\t\t\t\t<text class=\"value\">{{item.price}}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"select-btn\">\n\t\t\t\t\t\t\t\t\t<text>选规格</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 空状态 -->\n\t\t\t\t<view class=\"empty-state\" v-else>\n\t\t\t\t\t<image class=\"empty-image\" src=\"/static/search/empty.png\" />\n\t\t\t\t\t<text class=\"empty-text\">暂无相关商品</text>\n\t\t\t\t</view>\n\t\t\t</block>\n\t\t</scroll-view>\n\n\t\t<!-- 规格选择弹窗 -->\n\t\t<menu-open \n\t\t\tv-if=\"showSpecSelector\" \n\t\t\t:product=\"selectedProduct\"\n\t\t\t@close=\"closeSpecSelector\"\n\t\t\t@add-to-cart=\"handleAddToCart\"\n\t\t></menu-open>\n\t</view>\n</template>\n\n<script>\nimport MenuOpen from '../menu/menu_open.vue'\n\nexport default {\n\tcomponents: {\n\t\tMenuOpen\n\t},\n\t\n\tdata() {\n\t\treturn {\n\t\t\tkeyword: '',\n\t\t\tsearchHistory: [],\n\t\t\thotSearchList: [\n\t\t\t\t'柠檬茶',\n\t\t\t\t'奶茶',\n\t\t\t\t'果茶',\n\t\t\t\t'咖啡',\n\t\t\t\t'小吃',\n\t\t\t\t'招牌奶茶',\n\t\t\t\t'芒果茶',\n\t\t\t\t'鸭屎香'\n\t\t\t],\n\t\t\tsearchList: [],\n\t\t\tproductList: [{\n\t\t\t\tname: '招牌柠檬茶',\n\t\t\t\tdesc: '精选柠檬片，清爽解腻',\n\t\t\t\tprice: 18,\n\t\t\t\timage: '/static/menu/595f1342438e860b2989fb7a6b4614bc.png',\n\t\t\t\tspec: '600ml',\n\t\t\t\tdescription: '精选柠檬片，清爽解腻'\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: '芒果果茶',\n\t\t\t\tdesc: '新鲜芒果，香甜可口',\n\t\t\t\tprice: 15,\n\t\t\t\timage: '/static/menu/51a6a8b5890fe982ce6e69b5bcc9499e.png',\n\t\t\t\tspec: '600ml',\n\t\t\t\tdescription: '新鲜芒果，香甜可口'\n\t\t\t}],\n\t\t\tshowSpecSelector: false, // 控制规格选择器显示\n\t\t\tselectedProduct: null, // 当前选中的商品\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\t// 获取搜索历史\n\t\tthis.searchHistory = uni.getStorageSync('searchHistory') || []\n\t},\n\t\n\tmethods: {\n\t\thandleBack() {\n\t\t\t// 清除搜索框内容\n\t\t\tthis.keyword = ''\n\t\t\tthis.searchList = []\n\t\t\t// 返回上一页\n\t\t\tuni.navigateBack()\n\t\t},\n\t\t\n\t\thandleInput() {\n\t\t\t// 实时搜索\n\t\t\tthis.searchProducts()\n\t\t},\n\t\t\n\t\thandleSearch() {\n\t\t\tif (!this.keyword) return\n\t\t\t\n\t\t\t// 保存搜索历史\n\t\t\tthis.saveHistory()\n\t\t\t// 搜索商品\n\t\t\tthis.searchProducts()\n\t\t},\n\t\t\n\t\tclearKeyword() {\n\t\t\tthis.keyword = ''\n\t\t\tthis.searchList = []\n\t\t},\n\t\t\n\t\tuseHistory(keyword) {\n\t\t\tthis.keyword = keyword\n\t\t\tthis.handleSearch()\n\t\t},\n\t\t\n\t\tclearHistory() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '确定要清空搜索历史吗？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.searchHistory = []\n\t\t\t\t\t\tuni.removeStorageSync('searchHistory')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\t\n\t\tsaveHistory() {\n\t\t\tif (!this.searchHistory.includes(this.keyword)) {\n\t\t\t\tthis.searchHistory.unshift(this.keyword)\n\t\t\t\tif (this.searchHistory.length > 10) {\n\t\t\t\t\tthis.searchHistory.pop()\n\t\t\t\t}\n\t\t\t\tuni.setStorageSync('searchHistory', this.searchHistory)\n\t\t\t}\n\t\t},\n\t\t\n\t\tsearchProducts() {\n\t\t\tif (!this.keyword) {\n\t\t\t\tthis.searchList = []\n\t\t\t\treturn\n\t\t\t}\n\t\t\t\n\t\t\t// 模拟搜索\n\t\t\tthis.searchList = this.productList.filter(item =>\n\t\t\t\titem.name.toLowerCase().includes(this.keyword.toLowerCase())\n\t\t\t)\n\t\t},\n\t\t\n\t\tgoToDetail(item) {\n\t\t\tthis.selectSpec(item)\n\t\t},\n\t\t\n\t\t// 选择规格\n\t\tselectSpec(product) {\n\t\t\t// 构建完整的商品数据\n\t\t\tthis.selectedProduct = {\n\t\t\t\t...product,\n\t\t\t\ttotalPrice: product.price, // 添加总价字段\n\t\t\t\tnumber: 1, // 添加数量字段，改为number而非count\n\t\t\t\tproperty: [\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '规格',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tvalue: product.spec || '常规',\n\t\t\t\t\t\t\t\tprice: product.price,\n\t\t\t\t\t\t\t\tis_default: true\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '温度',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{ value: '常温', is_default: true },\n\t\t\t\t\t\t\t{ value: '热', is_default: false },\n\t\t\t\t\t\t\t{ value: '冰', is_default: false }\n\t\t\t\t\t\t]\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tname: '糖度',\n\t\t\t\t\t\tvalues: [\n\t\t\t\t\t\t\t{ value: '标准糖', is_default: true },\n\t\t\t\t\t\t\t{ value: '少糖', is_default: false },\n\t\t\t\t\t\t\t{ value: '无糖', is_default: false }\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t]\n\t\t\t}\n\t\t\tthis.showSpecSelector = true\n\t\t},\n\t\t\n\t\t// 关闭规格选择器\n\t\tcloseSpecSelector() {\n\t\t\tthis.showSpecSelector = false\n\t\t\tthis.selectedProduct = null\n\t\t},\n\t\t\n\t\t// 处理添加购物车\n\t\thandleAddToCart(item) {\n\t\t\t// 构建购物车商品数据\n\t\t\tconst cartItem = {\n\t\t\t\t...item,\n\t\t\t\tcount: item.count || item.number || 1,\n\t\t\t\ttotalPrice: (item.price || 0) * (item.count || item.number || 1),\n\t\t\t\tspecSelected: true\n\t\t\t}\n\t\t\t\n\t\t\tlet cartData = uni.getStorageSync('cartData') || { list: [], total: 0, price: 0 }\n\t\t\tlet cartList = cartData.list || []\n\t\t\t\n\t\t\t// 检查购物车是否已有相同商品（包括规格）\n\t\t\tconst existingItem = cartList.find(cartItem => \n\t\t\t\tcartItem.name === item.name && \n\t\t\t\tcartItem.props_text === item.props_text\n\t\t\t)\n\t\t\t\n\t\t\tif (existingItem) {\n\t\t\t\texistingItem.count += (item.count || item.number || 1)\n\t\t\t\texistingItem.totalPrice = existingItem.price * existingItem.count\n\t\t\t} else {\n\t\t\t\tcartList.push(cartItem)\n\t\t\t}\n\t\t\t\n\t\t\t// 计算总数和总价\n\t\t\tconst total = cartList.reduce((sum, item) => sum + (item.count || 0), 0)\n\t\t\tconst price = cartList.reduce((sum, item) => sum + (item.totalPrice || 0), 0)\n\t\t\t\n\t\t\t// 更新购物车数据\n\t\t\tconst newCartData = {\n\t\t\t\tlist: cartList,\n\t\t\t\ttotal,\n\t\t\t\tprice\n\t\t\t}\n\t\t\tuni.setStorageSync('cartData', newCartData)\n\t\t\t\n\t\t\t// 关闭规格选择器\n\t\t\tthis.closeSpecSelector()\n\t\t\t\n\t\t\t// 显示添加成功提示\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已加入购物车',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t},\n\t\t\n\t\t// 修改取消按钮点击事件\n\t\thandleCancel() {\n\t\t\t// 只清除搜索框内容和搜索结果\n\t\t\tthis.keyword = ''\n\t\t\tthis.searchList = []\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.page {\n\t\tmin-height: 100vh;\n\t\tbackground: #f8f8f8;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.header {\n\t\tbackground: #fff;\n\t\tpadding-top: 120rpx;\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 100;\n\t\t\n\t\t.nav-bar {\n\t\t\tposition: relative;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx 30rpx;\n\t\t\t\n\t\t\t.back-icon {\n\t\t\t\twidth: 48rpx;\n\t\t\t\theight: 48rpx;\n\t\t\t\tpadding: 10rpx;\n\t\t\t\tmargin-right: 10rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.search-bar {\n\t\t\t\tflex: 1;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\t\n\t\t\t\t.search-box {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 72rpx;\n\t\t\t\t\tbackground: #f5f5f5;\n\t\t\t\t\tborder-radius: 36rpx;\n\t\t\t\t\tpadding: 0 24rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\t\n\t\t\t\t\t.search-input {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\theight: 100%;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\tmargin: 0 16rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.cancel-btn {\n\t\t\t\t\tpadding: 20rpx 30rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.content {\n\t\tflex: 1;\n\t\tmargin-top: 280rpx;\n\t\theight: calc(100vh - 280rpx);\n\t}\n\n\t.history {\n\t\tpadding: 30rpx;\n\t\t\n\t\t.section-header {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\t\n\t\t\t.title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t\t\n\t\t\t.clear {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 10rpx;\n\t\t\t\t\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\tmargin-left: 8rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.history-list {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tgap: 20rpx;\n\t\t\t\n\t\t\t.history-item {\n\t\t\t\tpadding: 12rpx 32rpx;\n\t\t\t\tbackground: #f5f5f5;\n\t\t\t\tborder-radius: 100rpx;\n\t\t\t\t\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.hot-search {\n\t\tpadding: 30rpx;\n\t\t\n\t\t.section-header {\n\t\t\tmargin-bottom: 20rpx;\n\t\t\t\n\t\t\t.title {\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.hot-list {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tgap: 20rpx;\n\t\t\t\n\t\t\t.hot-item {\n\t\t\t\tpadding: 12rpx 32rpx;\n\t\t\t\tbackground: #f5f5f5;\n\t\t\t\tborder-radius: 100rpx;\n\t\t\t\t\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\n\t\t\t\t\t&.hot {\n\t\t\t\t\t\tcolor: #ff4444;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.search-result {\n\t\tpadding: 20rpx;\n\t\t\n\t\t.result-item {\n\t\t\tbackground: #fff;\n\t\t\tborder-radius: 16rpx;\n\t\t\tpadding: 20rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tdisplay: flex;\n\t\t\t\n\t\t\t.item-image {\n\t\t\t\twidth: 160rpx;\n\t\t\t\theight: 160rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.item-info {\n\t\t\t\tflex: 1;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\t\n\t\t\t\t.name {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.desc {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.price {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\t\n\t\t\t\t\t.price-left {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: baseline;\n\t\t\t\t\t\t\n\t\t\t\t\t\t.symbol {\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: #ff4444;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.value {\n\t\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\t\tcolor: #ff4444;\n\t\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.select-btn {\n\t\t\t\t\t\tpadding: 8rpx 24rpx;\n\t\t\t\t\t\tbackground: #8cd548;\n\t\t\t\t\t\tborder-radius: 100rpx;\n\t\t\t\t\t\t\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.empty-state {\n\t\tpadding-top: 200rpx;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\t\n\t\t.empty-image {\n\t\t\twidth: 280rpx;\n\t\t\theight: 280rpx;\n\t\t\tmargin-bottom: 30rpx;\n\t\t}\n\t\t\n\t\t.empty-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999;\n\t\t}\n\t}\n\n\t.placeholder {\n\t\tcolor: #999;\n\t}\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./search.vue?vue&type=style&index=0&id=4cedc0c6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309786\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}