{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/demo/icon-guide.vue?907a", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/demo/icon-guide.vue?d68c", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/demo/icon-guide.vue?fe26", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/demo/icon-guide.vue?32b5", "uni-app:///pages/demo/icon-guide.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/demo/icon-guide.vue?fe78", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/demo/icon-guide.vue?5152"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "commonIcons", "customIcons", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4FvrB;EACAC;IACA;MACAC;MACAC,cACA,QACA,SACA,SACA,QACA,aACA,QACA,SACA,SACA,aACA,UACA,WACA,QACA,QACA,SACA,cACA,iBACA,SACA,QACA,cACA,eACA,YACA,cACA,OACA,YACA,OACA;MACAC,cACA,QACA,UACA,UACA,UACA,YACA;IAEA;EACA;EACAC;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAA8vC,CAAgB,mmCAAG,EAAC,C;;;;;;;;;;;ACAlxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/demo/icon-guide.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/demo/icon-guide.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./icon-guide.vue?vue&type=template&id=202e7a2a&\"\nvar renderjs\nimport script from \"./icon-guide.vue?vue&type=script&lang=js&\"\nexport * from \"./icon-guide.vue?vue&type=script&lang=js&\"\nimport style0 from \"./icon-guide.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/demo/icon-guide.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./icon-guide.vue?vue&type=template&id=202e7a2a&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./icon-guide.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./icon-guide.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 使用通用导航栏 -->\r\n    <nav-bar \r\n      title=\"图标使用指南\" \r\n      :isTabbar=\"false\" \r\n      background=\"primary\"\r\n      textColor=\"#fff\"\r\n    ></nav-bar>\r\n    \r\n    <!-- 导航栏占位 -->\r\n    <view class=\"navbar-placeholder\" :style=\"{ height: statusBarHeight + 88 + 'px' }\"></view>\r\n\r\n    <!-- 内容区域 -->\r\n    <scroll-view class=\"content-container\" scroll-y>\r\n      <!-- 基本图标说明 -->\r\n      <ui-card title=\"图标使用说明\">\r\n        <view class=\"description\">\r\n          <text>应用使用两种图标系统：</text>\r\n          <view class=\"list-item\">\r\n            <text>1. uView 内置图标库（推荐使用）</text>\r\n          </view>\r\n          <view class=\"list-item\">\r\n            <text>2. 自定义图标（位于 static/icons 目录）</text>\r\n          </view>\r\n          <text class=\"tip\">使用统一图标组件 custom-icon 可以同时支持这两种图标</text>\r\n        </view>\r\n      </ui-card>\r\n      \r\n      <!-- uView图标演示 -->\r\n      <ui-card title=\"uView 内置图标（常用）\">\r\n        <view class=\"icon-grid\">\r\n          <view class=\"icon-grid-item\" v-for=\"(icon, index) in commonIcons\" :key=\"index\">\r\n            <custom-icon :name=\"icon\" :size=\"40\" color=\"#333\"></custom-icon>\r\n            <text class=\"icon-text\">{{icon}}</text>\r\n          </view>\r\n        </view>\r\n      </ui-card>\r\n      \r\n      <!-- 自定义图标演示 -->\r\n      <ui-card title=\"自定义图标\">\r\n        <view class=\"icon-grid\">\r\n          <view class=\"icon-grid-item\" v-for=\"(icon, index) in customIcons\" :key=\"index\">\r\n            <custom-icon :name=\"icon\" :size=\"40\" :custom=\"true\"></custom-icon>\r\n            <text class=\"icon-text\">{{icon}}</text>\r\n          </view>\r\n        </view>\r\n      </ui-card>\r\n      \r\n      <!-- 使用示例 -->\r\n      <ui-card title=\"图标使用示例\">\r\n        <view class=\"example-section\">\r\n          <view class=\"example-title\">基本使用</view>\r\n          <view class=\"example-content\">\r\n            <custom-icon name=\"home\" size=\"32\" color=\"#8cd548\"></custom-icon>\r\n            <view class=\"code-block\">\r\n              <text class=\"code\">&lt;custom-icon name=\"home\" size=\"32\" color=\"#8cd548\"&gt;&lt;/custom-icon&gt;</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"example-section\">\r\n          <view class=\"example-title\">图标 + 文字</view>\r\n          <view class=\"example-content\">\r\n            <view class=\"icon-text\">\r\n              <custom-icon name=\"home\" size=\"28\" color=\"#8cd548\" class=\"icon\"></custom-icon>\r\n              <text class=\"text\">首页</text>\r\n            </view>\r\n            <view class=\"code-block\">\r\n              <text class=\"code\">&lt;view class=\"icon-text\"&gt;\r\n  &lt;custom-icon name=\"home\" size=\"28\" color=\"#8cd548\" class=\"icon\"&gt;&lt;/custom-icon&gt;\r\n  &lt;text class=\"text\"&gt;首页&lt;/text&gt;\r\n&lt;/view&gt;</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"example-section\">\r\n          <view class=\"example-title\">按钮中的图标</view>\r\n          <view class=\"example-content\">\r\n            <custom-button type=\"primary\" icon=\"plus\">添加</custom-button>\r\n            <view class=\"code-block\">\r\n              <text class=\"code\">&lt;custom-button type=\"primary\" icon=\"plus\"&gt;添加&lt;/custom-button&gt;</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </ui-card>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20,\r\n      commonIcons: [\r\n        'home',\r\n        'photo',\r\n        'level',\r\n        'star',\r\n        'star-fill',\r\n        'plus',\r\n        'minus',\r\n        'close',\r\n        'checkmark',\r\n        'search',\r\n        'setting',\r\n        'bell',\r\n        'chat',\r\n        'heart',\r\n        'heart-fill',\r\n        'shopping-cart',\r\n        'trash',\r\n        'edit',\r\n        'arrow-left',\r\n        'arrow-right',\r\n        'arrow-up',\r\n        'arrow-down',\r\n        'map',\r\n        'calendar',\r\n        'time'\r\n      ],\r\n      customIcons: [\r\n        'logo',\r\n        'coupon',\r\n        'member',\r\n        'wallet',\r\n        'location',\r\n        'order'\r\n      ]\r\n    };\r\n  },\r\n  onLoad() {\r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight;\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n@import '@/styles/icons.scss';\r\n\r\n.description {\r\n  .list-item {\r\n    margin: $spacing-xs 0;\r\n    padding-left: $spacing-md;\r\n  }\r\n  \r\n  .tip {\r\n    display: block;\r\n    margin-top: $spacing-md;\r\n    color: $primary-color;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.example-section {\r\n  margin-bottom: $spacing-lg;\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n  \r\n  .example-title {\r\n    font-size: $font-size-md;\r\n    color: $text-color-primary;\r\n    margin-bottom: $spacing-sm;\r\n  }\r\n  \r\n  .example-content {\r\n    .code-block {\r\n      margin-top: $spacing-sm;\r\n      background-color: #f7f7f7;\r\n      padding: $spacing-sm;\r\n      border-radius: $border-radius-sm;\r\n      \r\n      .code {\r\n        font-family: monospace;\r\n        font-size: $font-size-xs;\r\n        color: $text-color-secondary;\r\n        word-break: break-all;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./icon-guide.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./icon-guide.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309852\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}