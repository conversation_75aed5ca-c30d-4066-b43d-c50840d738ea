@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.cart-popup.data-v-41ea784f {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.cart-popup .mask.data-v-41ea784f {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  -webkit-animation: fadeIn-data-v-41ea784f 0.3s ease-out;
          animation: fadeIn-data-v-41ea784f 0.3s ease-out;
}
.cart-popup .content.data-v-41ea784f {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
  -webkit-animation: slideUp-data-v-41ea784f 0.3s ease-out;
          animation: slideUp-data-v-41ea784f 0.3s ease-out;
  width: 100%;
  box-sizing: border-box;
}
.cart-popup .content .header.data-v-41ea784f {
  position: relative;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f2f2f2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to bottom, #f9f9f9, #ffffff);
  border-radius: 32rpx 32rpx 0 0;
}
.cart-popup .content .header.data-v-41ea784f::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
}
.cart-popup .content .header .title.data-v-41ea784f {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  position: relative;
}
.cart-popup .content .header .title.data-v-41ea784f::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(to right, #8cd548, #6ab52e);
  border-radius: 2rpx;
}
.cart-popup .content .header .clear-btn.data-v-41ea784f {
  position: absolute;
  left: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
}
.cart-popup .content .header .clear-btn .clear-icon.data-v-41ea784f {
  width: 24rpx;
  height: 24rpx;
  margin-right: 6rpx;
  opacity: 0.6;
}
.cart-popup .content .header .clear-btn text.data-v-41ea784f {
  font-size: 24rpx;
  color: #999;
  transition: all 0.2s;
}
.cart-popup .content .header .clear-btn:active text.data-v-41ea784f {
  color: #ff5722;
}
.cart-popup .content .header .clear-btn:active .clear-icon.data-v-41ea784f {
  opacity: 0.8;
}
.cart-popup .content .header .close-btn.data-v-41ea784f {
  position: absolute;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}
.cart-popup .content .header .close-btn .close-icon.data-v-41ea784f {
  font-size: 40rpx;
  color: #666;
  line-height: 1;
}
.cart-popup .content .header .close-btn.data-v-41ea784f:active {
  background: rgba(0, 0, 0, 0.1);
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.cart-popup .content .cart-list.data-v-41ea784f {
  padding: 20rpx 20rpx;
}
.cart-popup .content .cart-list .empty-cart.data-v-41ea784f {
  padding: 80rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.cart-popup .content .cart-list .empty-cart .empty-icon.data-v-41ea784f {
  width: 140rpx;
  height: 140rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}
.cart-popup .content .cart-list .empty-cart .empty-text.data-v-41ea784f {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 16rpx;
}
.cart-popup .content .cart-list .empty-cart .empty-tip.data-v-41ea784f {
  font-size: 28rpx;
  color: #999;
}
.cart-popup .content .cart-list .cart-item.data-v-41ea784f {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.cart-popup .content .cart-list .cart-item.data-v-41ea784f:last-child {
  border-bottom: none;
}
.cart-popup .content .cart-list .cart-item.data-v-41ea784f::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.03), rgba(0, 0, 0, 0.01));
}
.cart-popup .content .cart-list .cart-item .item-info.data-v-41ea784f {
  display: flex;
  align-items: flex-start;
  flex: 1;
  max-width: 55%;
  overflow: hidden;
  padding-right: 10rpx;
}
.cart-popup .content .cart-list .cart-item .item-info .item-image.data-v-41ea784f {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  object-fit: cover;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #fff;
  flex-shrink: 0;
}
.cart-popup .content .cart-list .cart-item .item-info .item-details.data-v-41ea784f {
  margin-left: 16rpx;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.cart-popup .content .cart-list .cart-item .item-info .item-details .item-name.data-v-41ea784f {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 6rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.cart-popup .content .cart-list .cart-item .item-info .item-details .item-spec.data-v-41ea784f {
  font-size: 18rpx;
  color: #999;
  margin-bottom: 6rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 2rpx 8rpx;
  border-radius: 20rpx;
  display: inline-block;
  max-width: 100%;
}
.cart-popup .content .cart-list .cart-item .item-info .item-details .item-price.data-v-41ea784f {
  display: flex;
  align-items: baseline;
  margin-top: 6rpx;
}
.cart-popup .content .cart-list .cart-item .item-info .item-details .item-price .price-symbol.data-v-41ea784f {
  font-size: 18rpx;
  color: #ff5722;
  font-weight: bold;
}
.cart-popup .content .cart-list .cart-item .item-info .item-details .item-price .price-value.data-v-41ea784f {
  font-size: 24rpx;
  color: #ff5722;
  font-weight: bold;
}
.cart-popup .content .cart-list .cart-item .quantity-control.data-v-41ea784f {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100rpx;
  padding: 2rpx;
  margin-left: auto;
  width: 110rpx;
  justify-content: space-between;
  margin-right: 40rpx;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.data-v-41ea784f {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn text.data-v-41ea784f {
  font-size: 18rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.data-v-41ea784f:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.8;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.minus-btn text.data-v-41ea784f {
  color: #999;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.minus-btn.delete-state.data-v-41ea784f {
  background: #ffebee;
  border: 1rpx solid #ffcdd2;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.minus-btn.delete-state text.data-v-41ea784f {
  color: #f44336;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.minus-btn.delete-state.data-v-41ea784f:active {
  background: #ffcdd2;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.plus-btn.data-v-41ea784f {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.plus-btn text.data-v-41ea784f {
  color: #fff;
}
.cart-popup .content .cart-list .cart-item .quantity-control .quantity-btn.plus-btn.data-v-41ea784f:active {
  background: linear-gradient(135deg, #7bc53a 0%, #5aa41e 100%);
}
.cart-popup .content .cart-list .cart-item .quantity-control .number.data-v-41ea784f {
  font-size: 18rpx;
  font-weight: bold;
  min-width: 20rpx;
  text-align: center;
}
.cart-popup .content .footer.data-v-41ea784f {
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1rpx solid #f2f2f2;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
  position: relative;
}
.cart-popup .content .footer.data-v-41ea784f::before {
  content: '';
  position: absolute;
  top: 0;
  left: 30rpx;
  right: 30rpx;
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
}
.cart-popup .content .footer .total-info.data-v-41ea784f {
  display: flex;
  align-items: baseline;
}
.cart-popup .content .footer .total-info .total-label.data-v-41ea784f {
  font-size: 30rpx;
  color: #333;
  margin-right: 10rpx;
  font-weight: 500;
}
.cart-popup .content .footer .total-info .total-price.data-v-41ea784f {
  display: flex;
  align-items: baseline;
}
.cart-popup .content .footer .total-info .total-price .price-symbol.data-v-41ea784f {
  font-size: 26rpx;
  color: #ff5722;
  font-weight: bold;
}
.cart-popup .content .footer .total-info .total-price .price-value.data-v-41ea784f {
  font-size: 40rpx;
  color: #ff5722;
  font-weight: bold;
}
.cart-popup .content .footer .checkout-btn.data-v-41ea784f {
  padding: 0 50rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);
  position: relative;
  overflow: hidden;
}
.cart-popup .content .footer .checkout-btn.data-v-41ea784f::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
  -webkit-transform: skewX(-25deg);
          transform: skewX(-25deg);
  -webkit-animation: shine-data-v-41ea784f 3s infinite;
          animation: shine-data-v-41ea784f 3s infinite;
}
.cart-popup .content .footer .checkout-btn text.data-v-41ea784f {
  font-size: 34rpx;
  color: #fff;
  font-weight: bold;
  position: relative;
  z-index: 2;
}
.cart-popup .content .footer .checkout-btn.data-v-41ea784f:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  opacity: 0.9;
  background: linear-gradient(135deg, #7bc53a 0%, #5aa41e 100%);
}
@-webkit-keyframes fadeIn-data-v-41ea784f {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeIn-data-v-41ea784f {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@-webkit-keyframes slideUp-data-v-41ea784f {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp-data-v-41ea784f {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@-webkit-keyframes shine-data-v-41ea784f {
0% {
    left: -100%;
}
20% {
    left: 100%;
}
100% {
    left: 100%;
}
}
@keyframes shine-data-v-41ea784f {
0% {
    left: -100%;
}
20% {
    left: 100%;
}
100% {
    left: 100%;
}
}

