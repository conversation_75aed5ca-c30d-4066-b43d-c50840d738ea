(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/menu/menu"],{

/***/ 180:
/*!*************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/main.js?{"page":"pages%2Fmenu%2Fmenu"} ***!
  \*************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _menu = _interopRequireDefault(__webpack_require__(/*! ./pages/menu/menu.vue */ 181));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_menu.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 181:
/*!******************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue ***!
  \******************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./menu.vue?vue&type=template&id=368aef34&scoped=true& */ 182);
/* harmony import */ var _menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./menu.vue?vue&type=script&lang=js& */ 184);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _menu_vue_vue_type_style_index_0_id_368aef34_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./menu.vue?vue&type=style&index=0&id=368aef34&scoped=true&lang=scss& */ 187);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "368aef34",
  null,
  false,
  _menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/menu/menu.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 182:
/*!*************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?vue&type=template&id=368aef34&scoped=true& ***!
  \*************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=template&id=368aef34&scoped=true& */ 183);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_template_id_368aef34_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 183:
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?vue&type=template&id=368aef34&scoped=true& ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.cartTotal > 0 ? _vm.cartPrice.toFixed(2) : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 184:
/*!*******************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?vue&type=script&lang=js& ***!
  \*******************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=script&lang=js& */ 185);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 185:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 55));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 57));
var _menu = __webpack_require__(/*! @/api/menu */ 186);
var _merchant = __webpack_require__(/*! @/api/merchant */ 177);
var _methods;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var MenuOpen = function MenuOpen() {
  __webpack_require__.e(/*! require.ensure | pages/menu/menu_open */ "pages/menu/menu_open").then((function () {
    return resolve(__webpack_require__(/*! ../menu/menu_open.vue */ 381));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var ShoppingCart = function ShoppingCart() {
  __webpack_require__.e(/*! require.ensure | pages/menu/ShoppingCart */ "pages/menu/ShoppingCart").then((function () {
    return resolve(__webpack_require__(/*! ./ShoppingCart.vue */ 388));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    MenuOpen: MenuOpen,
    ShoppingCart: ShoppingCart
  },
  data: function data() {
    return {
      showSpecSelector: false,
      // 控制规格选择器显示
      selectedProduct: null,
      // 当前选中的商品
      productList: [{
        name: '暴打凤梨鸭屎香柠檬茶',
        spec: '600ml',
        price: 18,
        image: '/static/menu/595f1342438e860b2989fb7a6b4614bc.png'
      }, {
        name: '招牌暴打渣男柠檬茶',
        spec: '600ml',
        price: 18,
        image: '/static/menu/ade3ff4d605f82d824aedfaef156d804.png'
      }, {
        name: '芒果柠檬茶',
        spec: '600ml',
        price: 15,
        image: '/static/menu/51a6a8b5890fe982ce6e69b5bcc9499e.png'
      }],
      storeInfo: {
        name: 'KKmall京基店2楼188号',
        distance: '300m'
      },
      currentCategory: 0,
      // 当前选中的分类
      categories: [],
      // 分类列表
      diningType: 'dine-in',
      // 用餐方式：dine-in(堂食) / takeout(外卖)
      cartTotal: 0,
      // 购物车商品总数
      cartPrice: 0,
      // 购物车总价
      cartList: [],
      // 购物车商品列表
      showCart: false,
      // 控制物弹窗显示
      currentProducts: [],
      // 当前分类的商品列表
      loading: false,
      // 加载状态
      merchant: {
        name: '',
        logo_image: '',
        address: '',
        phone: '',
        monthly_sales: 0,
        business_hours: '',
        rating: 5.0,
        announcement: ''
      },
      pendingProductInfo: null,
      // 存储待打开的商品信息
      allProducts: [] // 所有分类的商品缓存
    };
  },
  onLoad: function onLoad() {
    // 监听清空购物车事件
    uni.$on('clearCart', this.handleClearCart);

    // 监听从首页跳转过来时切换分类的事件
    uni.$on('switchCategory', this.handleSwitchCategory);

    // 初始化时就获取商户信息和分类数据
    this.getMerchantInfo();
    this.getCategories();
  },
  onUnload: function onUnload() {
    // 移除事件监听
    uni.$off('clearCart', this.handleClearCart);
    uni.$off('switchCategory', this.handleSwitchCategory);
  },
  methods: (_methods = {
    // 获取分类列表
    getCategories: function getCategories() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var res, categoriesData, categoryIndex;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.prev = 0;
                _context.next = 3;
                return (0, _menu.getCategories)();
              case 3:
                res = _context.sent;
                // 根据接口返回的实际数据结构调整取值路径
                categoriesData = [];
                if (res && res.code === 1) {
                  if (res.data && Array.isArray(res.data.list)) {
                    categoriesData = res.data.list;
                  } else if (res.data && Array.isArray(res.data)) {
                    categoriesData = res.data;
                  } else if (Array.isArray(res.list)) {
                    categoriesData = res.list;
                  }
                }
                _this.categories = categoriesData;
                console.log('获取到的分类数据:', _this.categories);

                // 如果有待处理的商品信息，检查对应的分类
                if (_this.pendingProductInfo && _this.pendingProductInfo.categoryId) {
                  categoryIndex = _this.categories.findIndex(function (cat) {
                    return cat.id === _this.pendingProductInfo.categoryId;
                  });
                  if (categoryIndex > -1 && categoryIndex !== _this.currentCategory) {
                    console.log('处理待打开商品的分类切换:', categoryIndex);
                    _this.currentCategory = categoryIndex;
                  }
                }

                // 如果有分类数据，自动获取第一个分类的商品
                if (!(_this.categories.length > 0)) {
                  _context.next = 12;
                  break;
                }
                _context.next = 12;
                return _this.getProductList();
              case 12:
                _context.next = 18;
                break;
              case 14:
                _context.prev = 14;
                _context.t0 = _context["catch"](0);
                console.error('获取分类失败:', _context.t0);
                uni.showToast({
                  title: '获取分类失败',
                  icon: 'none'
                });
              case 18:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[0, 14]]);
      }))();
    },
    // 搜索商品
    onSearch: function onSearch(keyword) {
      console.log('搜索:', keyword);
    },
    // 切换堂食/外卖
    switchDiningType: function switchDiningType(type) {
      this.diningType = type;
      // 将当前选择的用餐方式保存到本地存储
      uni.setStorageSync('diningType', type);
    },
    // 选择商品规格
    selectSpec: function selectSpec(product) {
      if (product.spec_type === 'single') {
        // 单规格商品直接加入购物车
        var price = parseFloat(product.price || 0);
        // 确保规格信息正确
        var specText = product.spec || product.props_text || '默认规格';
        console.log('单规格商品添加购物车，规格信息:', specText);
        this.handleAddToCart({
          id: product.id,
          name: product.name,
          price: price,
          image: product.image,
          count: 1,
          totalPrice: price,
          spec_type: 'single',
          specs: specText,
          props_text: specText
        });
      } else {
        // 多规格商品，打开选择器并传递规格数据
        var specConfig = product.spec_config || {};

        // 构建规格属性数据
        var properties = [];

        // 添加规格选项
        if (specConfig.规格 && (Array.isArray(specConfig.规格) ? specConfig.规格.length > 0 : true)) {
          var specs = Array.isArray(specConfig.规格) ? specConfig.规格 : [specConfig.规格];
          properties.push({
            name: '规格',
            values: specs.map(function (item) {
              return {
                value: item,
                is_default: false,
                price: specConfig.价格 && specConfig.价格[item] ? parseFloat(specConfig.价格[item]) : parseFloat(product.price || 0)
              };
            })
          });
        }

        // 添加温度选项
        if (specConfig.温度 && (Array.isArray(specConfig.温度) ? specConfig.温度.length > 0 : true)) {
          var temps = Array.isArray(specConfig.温度) ? specConfig.温度 : [specConfig.温度];
          properties.push({
            name: '温度',
            values: temps.map(function (item) {
              return {
                value: item,
                is_default: false
              };
            })
          });
        }

        // 添加甜度选项
        if (specConfig.甜度 && (Array.isArray(specConfig.甜度) ? specConfig.甜度.length > 0 : true)) {
          var sweetness = Array.isArray(specConfig.甜度) ? specConfig.甜度 : [specConfig.甜度];
          properties.push({
            name: '甜度',
            values: sweetness.map(function (item) {
              return {
                value: item,
                is_default: false
              };
            })
          });
        }

        // 设置选中商品数据
        this.selectedProduct = _objectSpread(_objectSpread({}, product), {}, {
          number: 1,
          property: properties,
          prices: specConfig.价格 || {}
        });

        // 设置每个属性的第一个选项为默认选中
        this.selectedProduct.property.forEach(function (prop) {
          if (prop.values.length > 0) {
            prop.values[0].is_default = true;
          }
        });

        // 打开规格选择器
        this.showSpecSelector = true;
      }
    },
    // 关闭规格择器
    closeSpecSelector: function closeSpecSelector() {
      this.showSpecSelector = false;
      this.selectedProduct = null;
    },
    // 切换分类
    switchCategory: function switchCategory(index) {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                if (!(_this2.currentCategory === index)) {
                  _context3.next = 2;
                  break;
                }
                return _context3.abrupt("return");
              case 2:
                _this2.currentCategory = index;

                // 清空当前商品列表，显示加载中
                _this2.currentProducts = [];
                uni.showLoading({
                  title: '加载中...',
                  mask: true
                });
                _context3.prev = 5;
                _context3.next = 8;
                return _this2.getProductList();
              case 8:
                _context3.next = 14;
                break;
              case 10:
                _context3.prev = 10;
                _context3.t0 = _context3["catch"](5);
                console.error('切换分类加载商品失败:', _context3.t0);
                // 如果失败，尝试重新加载一次
                setTimeout( /*#__PURE__*/(0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
                  return _regenerator.default.wrap(function _callee2$(_context2) {
                    while (1) {
                      switch (_context2.prev = _context2.next) {
                        case 0:
                          _context2.prev = 0;
                          _context2.next = 3;
                          return _this2.getProductList();
                        case 3:
                          _context2.next = 8;
                          break;
                        case 5:
                          _context2.prev = 5;
                          _context2.t0 = _context2["catch"](0);
                          console.error('重试加载商品失败:', _context2.t0);
                        case 8:
                        case "end":
                          return _context2.stop();
                      }
                    }
                  }, _callee2, null, [[0, 5]]);
                })), 1000);
              case 14:
                _context3.prev = 14;
                uni.hideLoading();
                return _context3.finish(14);
              case 17:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[5, 10, 14, 17]]);
      }))();
    },
    // 打开购物车
    openCart: function openCart() {
      this.showCart = true;
    },
    // 关闭购物车
    closeCart: function closeCart() {
      this.showCart = false;
    },
    // 更新购物车
    updateCart: function updateCart(data) {
      this.cartList = data.list;
      this.cartTotal = data.total;
      this.cartPrice = data.price;
      // 同步到本地存储
      uni.setStorageSync('cartData', data);
    },
    // 添加到购物车
    addToCart: function addToCart(product, spec) {
      // 添加商品到购物车
      var item = _objectSpread(_objectSpread({}, product), {}, {
        spec: spec,
        count: 1
      });
      this.cartList.push(item);
      this.cartTotal++;
      this.cartPrice += Number(product.price);
    },
    // 去结算
    goToCheckout: function goToCheckout() {
      if (this.cartTotal > 0) {
        // 保存购车数据
        uni.setStorageSync('cartData', {
          list: this.cartList,
          total: this.cartTotal,
          price: this.cartPrice
        });

        // 保存当前的用餐方式
        uni.setStorageSync('diningType', this.diningType);

        // 跳转到订单提交页面
        uni.navigateTo({
          url: '/pages/order/orderSubmit'
        });
      }
    },
    // 处理添加购物车
    handleAddToCart: function handleAddToCart(item) {
      // 检查购物车是否已有相同商品（包括规格）
      var existingItem = this.cartList.find(function (cartItem) {
        if (cartItem.id !== item.id) return false;
        if (item.spec_type === 'single') return true;
        return cartItem.props_text === item.props_text;
      });
      if (existingItem) {
        // 如果已存在相同规格的商品，增加数量
        existingItem.count += item.count;
        existingItem.totalPrice = Number(existingItem.price) * existingItem.count;
      } else {
        // 如果不存在，添加新商品
        this.cartList.push(item);
      }

      // 计算总数和总价
      this.updateCartTotal();
    },
    // 跳转到搜索页面
    goToSearch: function goToSearch() {
      uni.navigateTo({
        url: '/pages/search/search',
        animationType: 'slide-in-right',
        animationDuration: 300
      });
    },
    // 加载购物车数据
    loadCartData: function loadCartData() {
      var cartData = uni.getStorageSync('cartData');
      if (cartData) {
        console.log('从本地加载购物车数据:', JSON.stringify(cartData));
        this.cartList = cartData.list || [];
        this.cartTotal = cartData.total || 0;
        this.cartPrice = cartData.price || 0;

        // 检查并修复从本地加载的购物车数据中的规格信息
        if (this.cartList.length > 0) {
          var hasFixed = false;
          this.cartList.forEach(function (item) {
            if (!item.props_text && item.specs) {
              item.props_text = item.specs;
              console.log('修复加载的购物车商品规格:', item.name, item.props_text);
              hasFixed = true;
            } else if (!item.props_text) {
              item.props_text = '默认规格';
              item.specs = '默认规格';
              console.log('为加载的购物车商品设置默认规格:', item.name);
              hasFixed = true;
            }
          });

          // 如果有修复，更新到本地存储
          if (hasFixed) {
            console.log('更新修复后的购物车数据到本地存储');
            uni.setStorageSync('cartData', {
              list: this.cartList,
              total: this.cartTotal,
              price: this.cartPrice
            });
          }
        }
      }
    },
    // 添加跳转到店铺详情的方法
    goToStoreDetail: function goToStoreDetail() {
      uni.navigateTo({
        url: "/pages/store/detail?id=".concat(this.merchant.id),
        animationType: 'slide-in-right',
        animationDuration: 300
      });
    },
    // 获取商品列表
    getProductList: function getProductList() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var _this3$categories$_th, category_id, categoryIdNum, res, productsData;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                _this3.loading = true;
                category_id = (_this3$categories$_th = _this3.categories[_this3.currentCategory]) === null || _this3$categories$_th === void 0 ? void 0 : _this3$categories$_th.id;
                if (category_id) {
                  _context4.next = 5;
                  break;
                }
                return _context4.abrupt("return");
              case 5:
                // 确保category_id是整数类型，与后端接口要求匹配
                categoryIdNum = parseInt(category_id) || 0;
                console.log('获取商品列表，原始分类ID:', category_id, '类型:', (0, _typeof2.default)(category_id));
                console.log('获取商品列表，转换后分类ID:', categoryIdNum, '类型:', (0, _typeof2.default)(categoryIdNum));
                _context4.next = 10;
                return (0, _menu.getProducts)({
                  category_id: categoryIdNum
                });
              case 10:
                res = _context4.sent;
                // 根据接口返回的实际数据结构调整取值路径
                productsData = [];
                if (res && res.code === 1) {
                  if (res.data && Array.isArray(res.data.list)) {
                    productsData = res.data.list;
                  } else if (res.data && Array.isArray(res.data)) {
                    productsData = res.data;
                  } else if (Array.isArray(res.list)) {
                    productsData = res.list;
                  }
                }
                if (!(productsData.length === 0)) {
                  _context4.next = 17;
                  break;
                }
                console.log('当前分类没有商品数据');
                _this3.currentProducts = [];
                return _context4.abrupt("return");
              case 17:
                // 处理商品数据，只处理价格信息
                _this3.currentProducts = productsData.map(function (product) {
                  // 确保价格为数字
                  var price = parseFloat(product.price || 0).toFixed(2);

                  // 保留规格信息但不显示
                  var spec = '';
                  if (product.spec) {
                    spec = product.spec;
                  } else if (product.props_text) {
                    spec = product.props_text;
                  } else if (product.spec_config && product.spec_config.规格) {
                    spec = Array.isArray(product.spec_config.规格) ? product.spec_config.规格[0] : product.spec_config.规格;
                  } else {
                    spec = '默认规格';
                  }
                  return _objectSpread(_objectSpread({}, product), {}, {
                    spec: spec,
                    price: price
                  });
                });
                console.log('处理后的商品数据:', _this3.currentProducts);

                // 存储到所有商品缓存中
                _this3.allProducts = [].concat((0, _toConsumableArray2.default)(_this3.allProducts.filter(function (p) {
                  return p.category_id !== categoryIdNum;
                })), (0, _toConsumableArray2.default)(_this3.currentProducts));

                // 如果有待打开的商品，检查是否在当前分类中
                _this3.tryOpenPendingProduct();
                _context4.next = 28;
                break;
              case 23:
                _context4.prev = 23;
                _context4.t0 = _context4["catch"](0);
                console.error('获取商品列表失败:', _context4.t0);
                uni.showToast({
                  title: '获取商品列表失败',
                  icon: 'none'
                });
                // 网络错误时清空商品列表
                _this3.currentProducts = [];
              case 28:
                _context4.prev = 28;
                _this3.loading = false;
                return _context4.finish(28);
              case 31:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 23, 28, 31]]);
      }))();
    },
    // 尝试打开待处理的商品
    tryOpenPendingProduct: function tryOpenPendingProduct() {
      var _this4 = this;
      if (!this.pendingProductInfo || !this.pendingProductInfo.productId) return;

      // 当前分类的商品中查找
      var productToOpen = this.currentProducts.find(function (p) {
        return p.id == _this4.pendingProductInfo.productId;
      });
      if (productToOpen) {
        console.log('在当前分类中找到待打开的商品:', productToOpen.name);

        // 确保购物车弹窗已关闭
        this.showCart = false;

        // 延迟一点打开商品详情，确保UI渲染完成
        setTimeout(function () {
          // 打开商品详情
          _this4.selectSpec(productToOpen);
          // 清空待处理信息
          _this4.pendingProductInfo = null;
        }, 200);
      } else {
        console.log('当前分类未找到商品ID:', this.pendingProductInfo.productId);

        // 尝试在所有已加载的商品中查找
        productToOpen = this.allProducts.find(function (p) {
          return p.id == _this4.pendingProductInfo.productId;
        });
        if (productToOpen) {
          console.log('在其他分类中找到待打开的商品:', productToOpen.name);
          // 切换到对应分类
          var categoryIndex = this.categories.findIndex(function (cat) {
            return cat.id === productToOpen.category_id;
          });
          if (categoryIndex > -1 && categoryIndex !== this.currentCategory) {
            console.log('切换到商品所在分类:', categoryIndex);
            this.currentCategory = categoryIndex;
            // 获取分类商品并在回调中打开商品详情
            this.getProductList().then(function () {
              setTimeout(function () {
                // 确保购物车弹窗关闭
                _this4.showCart = false;
                // 打开商品详情
                _this4.selectSpec(productToOpen);
                // 清空待处理信息
                _this4.pendingProductInfo = null;
              }, 200);
            });
          }
        }
      }
    },
    // 修改模态框添加到购物车的方法
    handleAddToCartInModal: function handleAddToCartInModal() {
      var _selectedProps$find;
      // 获取选中的规格
      var selectedProps = this.selectedProduct.property.map(function (prop) {
        var selected = prop.values.find(function (v) {
          return v.is_default;
        });
        return {
          name: prop.name,
          value: selected ? selected.value : ''
        };
      });

      // 获取规格对应的价格
      var size = (_selectedProps$find = selectedProps.find(function (p) {
        return p.name === '规格';
      })) === null || _selectedProps$find === void 0 ? void 0 : _selectedProps$find.value;
      var price = this.selectedProduct.prices[size] || this.selectedProduct.price;

      // 构建购物车商品数据
      var cartItem = {
        id: this.selectedProduct.id,
        name: this.selectedProduct.name,
        price: Number(price),
        image: this.selectedProduct.image,
        count: this.selectedProduct.number,
        totalPrice: Number(price) * this.selectedProduct.number,
        spec_type: 'multi',
        specs: selectedProps.map(function (p) {
          return "".concat(p.name, ":").concat(p.value);
        }).join('，'),
        props_text: selectedProps.map(function (p) {
          return "".concat(p.name, ":").concat(p.value);
        }).join('，')
      };
      this.handleAddToCart(cartItem);
      this.closeSpecSelector();
    }
  }, (0, _defineProperty2.default)(_methods, "handleAddToCart", function handleAddToCart(item) {
    // 确保规格信息正确设置
    console.log('添加购物车，商品数据:', JSON.stringify(item));

    // 确保props_text字段存在
    if (!item.props_text && item.specs) {
      item.props_text = item.specs;
      console.log('补充props_text字段:', item.props_text);
    }

    // 检查购物车是否已有相同商品（包括规格）
    var existingItem = this.cartList.find(function (cartItem) {
      if (cartItem.id !== item.id) return false;
      if (item.spec_type === 'single') return true;
      return cartItem.props_text === item.props_text;
    });
    if (existingItem) {
      // 如果已存在相同规格的商品，增加数量
      existingItem.count += item.count;
      existingItem.totalPrice = Number(existingItem.price) * existingItem.count;
      // 确保existingItem的props_text字段
      if (!existingItem.props_text && existingItem.specs) {
        existingItem.props_text = existingItem.specs;
      }
      console.log('更新购物车已有商品:', JSON.stringify(existingItem));
    } else {
      // 如果不存在，添加新商品
      this.cartList.push(item);
      console.log('添加新商品到购物车:', JSON.stringify(item));
    }

    // 计算总数和总价
    this.updateCartTotal();
  }), (0, _defineProperty2.default)(_methods, "updateCartTotal", function updateCartTotal() {
    // 先检查并修复购物车中的每个商品是否都有props_text字段
    this.cartList.forEach(function (item) {
      if (!item.props_text && item.specs) {
        item.props_text = item.specs;
        console.log('修复购物车商品缺失的props_text字段:', item.id, item.props_text);
      } else if (!item.props_text) {
        // 如果没有规格信息，设置为默认值
        item.props_text = '默认规格';
        item.specs = '默认规格';
        console.log('设置商品默认规格:', item.id);
      }
    });

    // 计算总数和总价
    var total = this.cartList.reduce(function (sum, item) {
      return sum + item.count;
    }, 0);
    var price = this.cartList.reduce(function (sum, item) {
      return sum + item.totalPrice;
    }, 0);

    // 更新购物车数据
    var newCartData = {
      list: this.cartList,
      total: total,
      price: price
    };

    // 输出完整的购物车数据用于调试
    console.log('保存到本地存储的购物车数据:', JSON.stringify(newCartData));

    // 更新本地存储
    uni.setStorageSync('cartData', newCartData);

    // 更新页面数据
    this.cartTotal = total;
    this.cartPrice = price;

    // 显示添加成功提示
    uni.showToast({
      title: '已加入购物车',
      icon: 'none'
    });
  }), (0, _defineProperty2.default)(_methods, "getMerchantInfo", function getMerchantInfo() {
    var _this5 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
      var res, merchantData;
      return _regenerator.default.wrap(function _callee5$(_context5) {
        while (1) {
          switch (_context5.prev = _context5.next) {
            case 0:
              _context5.prev = 0;
              _context5.next = 3;
              return (0, _merchant.getMerchantInfo)();
            case 3:
              res = _context5.sent;
              console.log('获取到的商户信息:', res);

              // 检查API返回的数据结构
              if (!(res && res.code === 1 && res.data)) {
                _context5.next = 11;
                break;
              }
              merchantData = res.data; // 从data中提取商户信息
              _this5.merchant = {
                id: merchantData.id || 0,
                name: merchantData.name || '',
                logo_image: merchantData.logo_image || '',
                address: merchantData.address || '',
                phone: merchantData.phone || '',
                monthly_sales: merchantData.monthly_sales || 0,
                business_hours: merchantData.business_hours || '',
                rating: Number(merchantData.rating) || 5.0,
                announcement: merchantData.announcement || ''
              };
              console.log('处理后的商户信息:', _this5.merchant);
              _context5.next = 12;
              break;
            case 11:
              throw new Error('API返回数据格式错误');
            case 12:
              _context5.next = 18;
              break;
            case 14:
              _context5.prev = 14;
              _context5.t0 = _context5["catch"](0);
              console.error('获取商户信息失败:', _context5.t0);
              uni.showToast({
                title: '获取商户信息失败',
                icon: 'none'
              });
            case 18:
            case "end":
              return _context5.stop();
          }
        }
      }, _callee5, null, [[0, 14]]);
    }))();
  }), (0, _defineProperty2.default)(_methods, "handleClearCart", function handleClearCart() {
    this.cartList = [];
    this.cartTotal = 0;
    this.cartPrice = 0;
    // 更新商品数量状态
    this.productList.forEach(function (product) {
      product.number = 0;
    });
  }), (0, _defineProperty2.default)(_methods, "handleSwitchCategory", function handleSwitchCategory(data) {
    if (!data || !data.categoryId) return;
    console.log('接收到切换分类事件:', data);

    // 存储待打开的商品信息
    if (data.productId) {
      this.pendingProductInfo = {
        categoryId: data.categoryId,
        productId: data.productId
      };

      // 先关闭可能打开的购物车弹窗
      this.showCart = false;
    }

    // 找到对应分类的索引
    var categoryIndex = this.categories.findIndex(function (cat) {
      return cat.id === data.categoryId;
    });
    if (categoryIndex > -1) {
      // 切换到对应分类
      this.switchCategory(categoryIndex);
    } else {
      // 如果还没有获取到分类数据，先保存要切换的分类ID
      console.log('分类数据未加载，记录待切换的分类');
      // 在getCategories完成后会处理pendingProductInfo
    }
  }), _methods),
  onShow: function onShow() {
    var _this6 = this;
    return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
      var savedDiningType, scrollToProductId;
      return _regenerator.default.wrap(function _callee6$(_context6) {
        while (1) {
          switch (_context6.prev = _context6.next) {
            case 0:
              // 检查是否有外卖选择状态
              savedDiningType = uni.getStorageSync('diningType');
              if (savedDiningType) {
                _this6.diningType = savedDiningType;
                // 清除保存的状态
                uni.removeStorageSync('diningType');
              }

              // 加载购物车数据
              _this6.loadCartData();

              // 检查本地存储中是否有待打开的商品ID
              scrollToProductId = uni.getStorageSync('scrollToProductId');
              if (scrollToProductId) {
                console.log('从本地存储获取到待打开商品ID:', scrollToProductId);
                // 保存待打开的商品信息
                _this6.pendingProductInfo = {
                  productId: scrollToProductId
                };
                // 关闭购物车弹窗，避免冲突
                _this6.showCart = false;
                // 清除本地存储
                uni.removeStorageSync('scrollToProductId');
              }

              // 每次进入页面时都重新获取最新数据
              // 先获取商户信息
              _context6.next = 7;
              return _this6.getMerchantInfo();
            case 7:
              _context6.next = 9;
              return _this6.getCategories();
            case 9:
            case "end":
              return _context6.stop();
          }
        }
      }, _callee6);
    }))();
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 187:
/*!****************************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?vue&type=style&index=0&id=368aef34&scoped=true&lang=scss& ***!
  \****************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_style_index_0_id_368aef34_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=style&index=0&id=368aef34&scoped=true&lang=scss& */ 188);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_style_index_0_id_368aef34_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_style_index_0_id_368aef34_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_style_index_0_id_368aef34_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_style_index_0_id_368aef34_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_menu_vue_vue_type_style_index_0_id_368aef34_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 188:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?vue&type=style&index=0&id=368aef34&scoped=true&lang=scss& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[180,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/menu/menu.js.map