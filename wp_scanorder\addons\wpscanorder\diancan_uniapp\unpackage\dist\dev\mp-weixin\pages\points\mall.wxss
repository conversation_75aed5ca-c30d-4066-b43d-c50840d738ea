@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-163aafd7 {
  min-height: 100vh;
  background: #f8f8f8;
}
.header.data-v-163aafd7 {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding-top: 88rpx;
  padding-bottom: 30rpx;
  position: relative;
}
.header .nav-bar.data-v-163aafd7 {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.header .nav-bar .back-icon.data-v-163aafd7 {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-163aafd7 {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}
.header .nav-bar .empty-placeholder.data-v-163aafd7 {
  width: 48rpx;
  height: 48rpx;
}
.header .points-card.data-v-163aafd7 {
  margin: 30rpx 40rpx 10rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 30rpx;
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}
.header .points-card .points-info .label.data-v-163aafd7 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  display: block;
}
.header .points-card .points-info .value-container.data-v-163aafd7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
}
.header .points-card .points-info .value-container .value.data-v-163aafd7 {
  font-size: 52rpx;
  color: #fff;
  font-weight: bold;
}
.header .points-card .points-info .value-container .detail-btn.data-v-163aafd7 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.3);
  padding: 10rpx 20rpx;
  border-radius: 100rpx;
}
.header .points-card .points-info .value-container .detail-btn text.data-v-163aafd7 {
  font-size: 24rpx;
  color: #fff;
}
.header .points-card .points-info .value-container .detail-btn .arrow-right.data-v-163aafd7 {
  width: 14rpx;
  height: 14rpx;
  border-top: 2rpx solid #fff;
  border-right: 2rpx solid #fff;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  margin-left: 10rpx;
}
.header .points-card .points-tips.data-v-163aafd7 {
  margin-top: 20rpx;
}
.header .points-card .points-tips text.data-v-163aafd7 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.category-tabs.data-v-163aafd7 {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: -20rpx 20rpx 20rpx;
  padding: 20rpx;
  position: relative;
  z-index: 1;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.category-tabs .tab-item.data-v-163aafd7 {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  position: relative;
}
.category-tabs .tab-item text.data-v-163aafd7 {
  font-size: 28rpx;
  color: #666;
  position: relative;
  z-index: 1;
}
.category-tabs .tab-item.active text.data-v-163aafd7 {
  color: #8cd548;
  font-weight: bold;
}
.category-tabs .tab-item.active.data-v-163aafd7::after {
  content: '';
  position: absolute;
  bottom: 10rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #8cd548;
  border-radius: 6rpx;
}
.goods-list.data-v-163aafd7 {
  padding: 0 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.goods-list .goods-card.data-v-163aafd7 {
  width: calc(50% - 10rpx);
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.goods-list .goods-card .goods-image.data-v-163aafd7 {
  width: 100%;
  height: 240rpx;
  display: block;
  object-fit: cover;
}
.goods-list .goods-card .goods-info.data-v-163aafd7 {
  padding: 20rpx;
}
.goods-list .goods-card .goods-info .goods-name.data-v-163aafd7 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  height: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.goods-list .goods-card .goods-info .tags-row.data-v-163aafd7 {
  margin: 16rpx 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  min-height: 40rpx;
}
.goods-list .goods-card .goods-info .tags-row .tag.data-v-163aafd7 {
  background: rgba(255, 68, 68, 0.1);
  border-radius: 8rpx;
  padding: 0 12rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32rpx;
  box-sizing: border-box;
  min-width: 60rpx;
}
.goods-list .goods-card .goods-info .tags-row .tag text.data-v-163aafd7 {
  font-size: 20rpx;
  color: #ff4444;
  line-height: 1;
  padding: 0;
  margin: 0;
  white-space: nowrap;
}
.goods-list .goods-card .goods-info .tags-row .tag.product-tag.data-v-163aafd7 {
  background: rgba(106, 181, 46, 0.1);
}
.goods-list .goods-card .goods-info .tags-row .tag.product-tag text.data-v-163aafd7 {
  color: #6ab52e;
}
.goods-list .goods-card .goods-info .tags-row .tag.more-tag.data-v-163aafd7 {
  background: rgba(106, 181, 46, 0.1);
}
.goods-list .goods-card .goods-info .tags-row .tag.more-tag text.data-v-163aafd7 {
  color: #6ab52e;
}
.goods-list .goods-card .goods-info .category-info.data-v-163aafd7 {
  margin-top: 8rpx;
}
.goods-list .goods-card .goods-info .category-info text.data-v-163aafd7 {
  font-size: 22rpx;
  color: #6ab52e;
}
.goods-list .goods-card .goods-info .validity.data-v-163aafd7 {
  margin-top: 8rpx;
}
.goods-list .goods-card .goods-info .validity text.data-v-163aafd7 {
  font-size: 22rpx;
  color: #999;
}
.goods-list .goods-card .goods-info .bottom-row.data-v-163aafd7 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}
.goods-list .goods-card .goods-info .bottom-row .points-price.data-v-163aafd7 {
  display: flex;
  align-items: baseline;
}
.goods-list .goods-card .goods-info .bottom-row .points-price .price-value.data-v-163aafd7 {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}
.goods-list .goods-card .goods-info .bottom-row .points-price .price-unit.data-v-163aafd7 {
  font-size: 24rpx;
  color: #ff4444;
  margin-left: 4rpx;
}
.goods-list .goods-card .goods-info .bottom-row .exchange-btn.data-v-163aafd7 {
  padding: 0 20rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  box-sizing: border-box;
  min-width: 120rpx;
}
.goods-list .goods-card .goods-info .bottom-row .exchange-btn text.data-v-163aafd7 {
  font-size: 24rpx;
  color: #fff;
  line-height: 1;
  padding: 0;
  margin: 0;
}
.goods-list .goods-card .goods-info .bottom-row .exchange-btn.disabled.data-v-163aafd7 {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
}
.goods-list .goods-card .type-tag.data-v-163aafd7 {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.goods-list .goods-card .type-tag.coupon-tag.data-v-163aafd7 {
  background: #ff4444;
}
.goods-list .goods-card .type-tag.product-tag.data-v-163aafd7 {
  background: #6ab52e;
}
.goods-list .goods-card .type-tag text.data-v-163aafd7 {
  font-size: 22rpx;
  color: #fff;
  font-weight: bold;
}
.goods-list .goods-card .stock-tag.data-v-163aafd7 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 4rpx 12rpx;
  background: rgba(255, 68, 68, 0.8);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32rpx;
  box-sizing: border-box;
}
.goods-list .goods-card .stock-tag text.data-v-163aafd7 {
  font-size: 20rpx;
  color: #fff;
  line-height: 1;
  padding: 0;
  margin: 0;
  white-space: nowrap;
}
.loading-container.data-v-163aafd7 {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.loading-container .loading-spinner.data-v-163aafd7 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(140, 213, 72, 0.2);
  border-left: 4rpx solid #8cd548;
  border-radius: 50%;
  -webkit-animation: spin-data-v-163aafd7 1s linear infinite;
          animation: spin-data-v-163aafd7 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container text.data-v-163aafd7 {
  font-size: 28rpx;
  color: #999;
}
.empty-container.data-v-163aafd7 {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-container .empty-image.data-v-163aafd7 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-container text.data-v-163aafd7 {
  font-size: 30rpx;
  color: #666;
}
.empty-container text.empty-tips.data-v-163aafd7 {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.load-more.data-v-163aafd7 {
  padding: 40rpx 0 80rpx;
  text-align: center;
}
.load-more text.data-v-163aafd7 {
  font-size: 26rpx;
  color: #999;
}
@-webkit-keyframes spin-data-v-163aafd7 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-163aafd7 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}

