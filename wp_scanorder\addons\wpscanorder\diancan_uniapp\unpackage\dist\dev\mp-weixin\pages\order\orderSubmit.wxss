@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-5c101a1c {
  min-height: 100vh;
  background-color: #f8f8f8;
  width: 100%;
  overflow-x: hidden;
  /* 防止水平溢出 */
  box-sizing: border-box;
}
.page .header.data-v-5c101a1c {
  background: #fff;
  padding-top: 88rpx;
  width: 100%;
}
.page .header .nav-bar.data-v-5c101a1c {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .header .nav-bar .back-icon.data-v-5c101a1c {
  position: absolute;
  left: 30rpx;
  width: 40rpx;
  height: 40rpx;
  padding: 10rpx;
}
.page .header .nav-bar .title.data-v-5c101a1c {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.page .content.data-v-5c101a1c {
  padding: 20rpx;
  padding-bottom: 180rpx;
  width: 100%;
  box-sizing: border-box;
}
.page .content .section-card.data-v-5c101a1c {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  width: 100%;
}
.page .content .section-header.data-v-5c101a1c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.page .content .section-header .section-title.data-v-5c101a1c {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.page .content .section-header .add-btn.data-v-5c101a1c {
  padding: 12rpx 24rpx;
  border-radius: 100rpx;
  border: 2rpx solid #8cd548;
}
.page .content .section-header .add-btn text.data-v-5c101a1c {
  font-size: 26rpx;
  color: #8cd548;
}
.page .content .store-section.data-v-5c101a1c {
  border-top: 4rpx solid #fff;
}
.page .content .store-section .store-info.data-v-5c101a1c {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.page .content .store-section .store-info .store-icon.data-v-5c101a1c {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}
.page .content .store-section .store-info .store-detail .store-name.data-v-5c101a1c {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.page .content .store-section .store-info .store-detail .pickup-time.data-v-5c101a1c {
  font-size: 26rpx;
  color: #999;
}
.page .content .store-section .pickup-type.data-v-5c101a1c {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.page .content .store-section .pickup-type .type-item.data-v-5c101a1c {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 16rpx;
  border: 2rpx solid #eee;
  transition: all 0.3s;
}
.page .content .store-section .pickup-type .type-item image.data-v-5c101a1c {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
.page .content .store-section .pickup-type .type-item .type-info .type-name.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}
.page .content .store-section .pickup-type .type-item .type-info .type-desc.data-v-5c101a1c {
  font-size: 24rpx;
  color: #999;
}
.page .content .store-section .pickup-type .type-item.active.data-v-5c101a1c {
  border-color: #8cd548;
  background: rgba(140, 213, 72, 0.08);
}
.page .content .store-section .delivery-options .option-item.data-v-5c101a1c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.page .content .store-section .delivery-options .option-item .option-left.data-v-5c101a1c {
  display: flex;
  align-items: center;
}
.page .content .store-section .delivery-options .option-item .option-left .option-icon.data-v-5c101a1c {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
.page .content .store-section .delivery-options .option-item .option-left .option-label.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
}
.page .content .store-section .delivery-options .option-item .option-right.data-v-5c101a1c {
  display: flex;
  align-items: center;
  flex-shrink: 1;
  min-width: 0;
  /* 确保可以缩小 */
}
.page .content .store-section .delivery-options .option-item .option-right .discount.data-v-5c101a1c {
  font-size: 28rpx;
  color: #ff4444;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.page .content .store-section .delivery-options .option-item .option-right .value-container.data-v-5c101a1c {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
  overflow: hidden;
}
.page .content .store-section .delivery-options .option-item .option-right .option-value.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
.page .content .store-section .delivery-options .option-item .option-right .option-placeholder.data-v-5c101a1c {
  font-size: 28rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
.page .content .store-section .delivery-options .address-detail.data-v-5c101a1c {
  padding: 16rpx 0 0 52rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
.page .content .product-list .product-item.data-v-5c101a1c {
  display: flex;
  padding: 20rpx 0;
  position: relative;
}
.page .content .product-list .product-item.data-v-5c101a1c:not(:last-child) {
  border-bottom: 1rpx solid #f5f5f5;
}
.page .content .product-list .product-item .product-image.data-v-5c101a1c {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  /* 防止图片缩小 */
}
.page .content .product-list .product-item .product-info.data-v-5c101a1c {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-right: 80rpx;
  /* 为数量留出更多空间 */
  min-width: 0;
  /* 确保flex项可以正确缩小 */
}
.page .content .product-list .product-item .product-info .product-name.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.page .content .product-list .product-item .product-info .product-spec.data-v-5c101a1c {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.page .content .product-list .product-item .product-info .price-info.data-v-5c101a1c {
  display: flex;
  align-items: center;
}
.page .content .product-list .product-item .product-info .price-info .price.data-v-5c101a1c {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.page .content .product-list .product-item .product-count.data-v-5c101a1c {
  position: absolute;
  right: 0;
  bottom: 20rpx;
  width: 60rpx;
  /* 固定宽度 */
  text-align: right;
}
.page .content .product-list .product-item .product-count .count.data-v-5c101a1c {
  font-size: 26rpx;
  color: #999;
}
.page .content .option-item.data-v-5c101a1c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
}
.page .content .option-item.data-v-5c101a1c:not(:last-child) {
  border-bottom: 1rpx solid #f5f5f5;
}
.page .content .option-item .option-left.data-v-5c101a1c {
  display: flex;
  align-items: center;
}
.page .content .option-item .option-left .option-icon.data-v-5c101a1c {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
.page .content .option-item .option-left .option-label.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
}
.page .content .option-item .option-right.data-v-5c101a1c {
  display: flex;
  align-items: center;
  flex-shrink: 1;
  min-width: 0;
  /* 确保可以缩小 */
}
.page .content .option-item .option-right .discount.data-v-5c101a1c {
  font-size: 28rpx;
  color: #ff4444;
  margin-right: 8rpx;
  flex-shrink: 0;
}
.page .content .option-item .option-right .value-container.data-v-5c101a1c {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
  overflow: hidden;
}
.page .content .option-item .option-right .option-value.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
.page .content .option-item .option-right .option-placeholder.data-v-5c101a1c {
  font-size: 28rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
.page .content .payment-list .payment-item.data-v-5c101a1c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
}
.page .content .payment-list .payment-item.data-v-5c101a1c:not(:last-child) {
  border-bottom: 1rpx solid #f5f5f5;
}
.page .content .payment-list .payment-item .payment-info.data-v-5c101a1c {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}
.page .content .payment-list .payment-item .payment-info .payment-icon.data-v-5c101a1c {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.page .content .payment-list .payment-item .payment-info .payment-name.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
  margin-right: 12rpx;
  flex-shrink: 0;
}
.page .content .payment-list .payment-item .payment-info .balance-info.data-v-5c101a1c {
  font-size: 24rpx;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.page .content .payment-list .payment-item .radio.data-v-5c101a1c {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  position: relative;
}
.page .content .payment-list .payment-item .radio.active.data-v-5c101a1c {
  border-color: #8cd548;
  background: #fff;
}
.page .content .payment-list .payment-item .radio.active.data-v-5c101a1c::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #8cd548;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.page .content .payment-list .payment-item.active .payment-name.data-v-5c101a1c {
  color: #333;
  font-weight: 500;
}
.page .bottom-bar.data-v-5c101a1c {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}
.page .bottom-bar .price-info.data-v-5c101a1c {
  display: flex;
  flex-direction: column;
}
.page .bottom-bar .price-info .price-details.data-v-5c101a1c {
  display: flex;
  align-items: center;
}
.page .bottom-bar .price-info .price-details .total-text.data-v-5c101a1c {
  font-size: 28rpx;
  color: #333;
  margin-right: 8rpx;
}
.page .bottom-bar .price-info .price-details .total-price.data-v-5c101a1c {
  font-size: 38rpx;
  color: #ff4d4f;
  font-weight: bold;
}
.page .bottom-bar .price-info .discount-text.data-v-5c101a1c {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}
.page .bottom-bar .submit-btn.data-v-5c101a1c {
  padding: 0 60rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 12rpx rgba(140, 213, 72, 0.2);
}
.page .bottom-bar .submit-btn text.data-v-5c101a1c {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.page .bottom-bar .submit-btn.data-v-5c101a1c:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.page .bottom-bar .submit-btn.disabled.data-v-5c101a1c {
  background: #cccccc;
  opacity: 0.8;
  box-shadow: none;
}
.page .bottom-bar .submit-btn.disabled.data-v-5c101a1c:active {
  -webkit-transform: none;
          transform: none;
}
.time-selector.data-v-5c101a1c {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
}
.time-selector .selector-header.data-v-5c101a1c {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}
.time-selector .selector-header .title.data-v-5c101a1c {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.time-selector .selector-header .close.data-v-5c101a1c {
  padding: 10rpx;
}
.time-selector .time-list.data-v-5c101a1c {
  max-height: 600rpx;
  padding: 20rpx 30rpx;
}
.time-selector .time-list .time-option.data-v-5c101a1c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #f8f8f8;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}
.time-selector .time-list .time-option .time-content.data-v-5c101a1c {
  flex: 1;
}
.time-selector .time-list .time-option .time-content .time.data-v-5c101a1c {
  font-size: 32rpx;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
}
.time-selector .time-list .time-option .time-content .desc.data-v-5c101a1c {
  font-size: 24rpx;
  color: #999;
}
.time-selector .time-list .time-option .radio.data-v-5c101a1c {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  position: relative;
}
.time-selector .time-list .time-option .radio.active.data-v-5c101a1c {
  border-color: #8cd548;
  background: #fff;
}
.time-selector .time-list .time-option .radio.active.data-v-5c101a1c::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #8cd548;
  border-radius: 50%;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.time-selector .time-list .time-option.active.data-v-5c101a1c {
  background: rgba(140, 213, 72, 0.1);
}
.time-selector .time-list .time-option.active .time.data-v-5c101a1c {
  color: #8cd548;
  font-weight: 500;
}
.time-selector .time-list .time-option.immediate.data-v-5c101a1c {
  background: #e6f7d9;
  border: 1rpx solid #8cd548;
}
.time-selector .selector-footer.data-v-5c101a1c {
  padding: 30rpx;
  border-top: 1rpx solid #f5f5f5;
}
.time-selector .selector-footer .confirm-btn.data-v-5c101a1c {
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.time-selector .selector-footer .confirm-btn text.data-v-5c101a1c {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.time-selector .selector-footer .confirm-btn.data-v-5c101a1c:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.remark-editor.data-v-5c101a1c {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
}
.remark-editor .editor-header.data-v-5c101a1c {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
}
.remark-editor .editor-header .title.data-v-5c101a1c {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.remark-editor .editor-header .close.data-v-5c101a1c {
  padding: 10rpx;
}
.remark-editor .editor-content.data-v-5c101a1c {
  padding: 30rpx;
}
.remark-editor .editor-content .remark-input.data-v-5c101a1c {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #ddd;
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  resize: none;
}
.remark-editor .editor-content .word-count.data-v-5c101a1c {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.remark-editor .editor-footer.data-v-5c101a1c {
  padding: 30rpx;
  border-top: 1rpx solid #f5f5f5;
}
.remark-editor .editor-footer .confirm-btn.data-v-5c101a1c {
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.remark-editor .editor-footer .confirm-btn text.data-v-5c101a1c {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.remark-editor .editor-footer .confirm-btn.data-v-5c101a1c:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}

