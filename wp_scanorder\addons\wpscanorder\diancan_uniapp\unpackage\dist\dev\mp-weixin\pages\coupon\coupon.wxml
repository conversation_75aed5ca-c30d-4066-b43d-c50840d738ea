<view class="page data-v-032f11f4"><view class="header data-v-032f11f4"><view class="nav-bar data-v-032f11f4"><image class="back-icon data-v-032f11f4" src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" bindtap="__e"></image><text class="title data-v-032f11f4">我的优惠券</text></view></view><view class="tab-container data-v-032f11f4"><block wx:for="{{tabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="{{['data-v-032f11f4','tab-item',[(currentTab===index)?'active':'']]}}" bindtap="__e"><text class="tab-text data-v-032f11f4">{{tab.name}}</text></view></block></view><block wx:if="{{loading}}"><view class="loading data-v-032f11f4"><view class="loading-circle data-v-032f11f4"></view><text class="loading-text data-v-032f11f4">加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error-state data-v-032f11f4"><text class="error-text data-v-032f11f4">{{errorMsg}}</text><view data-event-opts="{{[['tap',[['refreshCoupons',['$event']]]]]}}" class="retry-btn data-v-032f11f4" bindtap="__e"><text class="data-v-032f11f4">重新加载</text></view></view></block><block wx:else><scroll-view class="coupon-scroll data-v-032f11f4" style="{{'height:'+(scrollHeight+'px')+';'}}" scroll-y="{{true}}" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" data-event-opts="{{[['scrolltolower',[['loadMore',['$event']]]],['refresherrefresh',[['refreshCoupons',['$event']]]]]}}" bindscrolltolower="__e" bindrefresherrefresh="__e"><view class="coupon-list data-v-032f11f4"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="{{['coupon-item','data-v-032f11f4',(this.orderAmount>0&&!item.$orig.isAvailable)?'disabled':'',(item.$orig.status==='expired'||item.$orig.status==='used')?'expired':'']}}" bindtap="__e"><view class="coupon-amount data-v-032f11f4"><text class="symbol data-v-032f11f4">¥</text><text class="value data-v-032f11f4">{{item.$orig.coupon?item.$orig.coupon.amount:item.$orig.amount}}</text><text class="limit data-v-032f11f4">{{"满"+(item.$orig.coupon?item.$orig.coupon.min_amount:item.$orig.limit)+"元可用"}}</text></view><view class="coupon-info data-v-032f11f4"><view class="info-top data-v-032f11f4"><text class="name data-v-032f11f4">{{item.$orig.coupon?item.$orig.coupon.name:item.$orig.name}}</text><view class="{{['status-tag','data-v-032f11f4',item.$orig.status]}}">{{''+(item.$orig.status_text||(item.$orig.status==='unused'||item.$orig.status==='valid'?'未使用':'已过期'))+''}}</view></view><text class="desc data-v-032f11f4">{{item.$orig.desc||'全场通用'}}</text><view class="info-bottom data-v-032f11f4"><text class="date data-v-032f11f4">{{"有效期至 "+(item.$orig.expire_time?item.m0:item.$orig.endDate||item.$orig.expireDate||'暂无期限')}}</text><block wx:if="{{item.$orig.status==='unused'||item.$orig.status==='valid'}}"><view class="use-btn data-v-032f11f4"><text class="data-v-032f11f4">立即使用</text></view></block></view></view><block wx:if="{{this.orderAmount>0&&!item.$orig.isAvailable&&(item.$orig.status==='unused'||item.$orig.status==='valid')}}"><view class="unavailable-tag data-v-032f11f4"><text class="data-v-032f11f4">不满足使用条件</text></view></block><block wx:if="{{item.$orig.status==='expired'||item.$orig.status==='used'}}"><view class="expired-watermark data-v-032f11f4"><text class="data-v-032f11f4">已过期</text></view></block></view></block></view><block wx:if="{{$root.g0===0}}"><view class="empty-state data-v-032f11f4"><image class="empty-image data-v-032f11f4" src="/static/order/e186e04e8774da64b58c96a8bb479840.png"></image><text class="empty-text data-v-032f11f4">暂无优惠券～</text><view data-event-opts="{{[['tap',[['goToMenu',['$event']]]]]}}" class="action-btn data-v-032f11f4" bindtap="__e"><text class="data-v-032f11f4">去领券</text></view></view></block><block wx:if="{{loadingMore}}"><view class="loading-more data-v-032f11f4"><view class="loading-dot data-v-032f11f4"></view><text class="data-v-032f11f4">加载中...</text></view></block><block wx:if="{{$root.g1}}"><view class="bottom-tip data-v-032f11f4"><text class="data-v-032f11f4">— 已经到底了 —</text></view></block></scroll-view></block></block></view>