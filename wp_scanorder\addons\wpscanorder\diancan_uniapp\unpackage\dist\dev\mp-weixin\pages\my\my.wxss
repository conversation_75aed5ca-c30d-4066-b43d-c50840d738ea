@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-0be17cc6 {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.page .header-section.data-v-0be17cc6 {
  padding: 0 30rpx 60rpx;
}
.page .header-section .status-bar.data-v-0be17cc6 {
  width: 100%;
}
.page .header-section .section.data-v-0be17cc6 {
  padding-top: 88rpx;
}
.page .header-section .section .user-info .user-content.data-v-0be17cc6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}
.page .header-section .section .user-info .user-content .user-left.data-v-0be17cc6 {
  display: flex;
  align-items: center;
}
.page .header-section .section .user-info .user-content .user-left .avatar.data-v-0be17cc6 {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.page .header-section .section .user-info .user-content .user-left .info-wrap.data-v-0be17cc6 {
  margin-left: 32rpx;
}
.page .header-section .section .user-info .user-content .user-left .info-wrap .nickname.data-v-0be17cc6 {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
  display: block;
}
.page .header-section .section .user-info .user-content .user-left .info-wrap .desc.data-v-0be17cc6 {
  font-size: 26rpx;
  color: #333;
  display: block;
}
.page .header-section .section .user-info .user-content .setting-btn.data-v-0be17cc6 {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .header-section .section .user-info .user-content .setting-btn.data-v-0be17cc6:active {
  opacity: 0.8;
}
.page .header-section .section .user-info .user-content .login-btn.data-v-0be17cc6 {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 100rpx;
  padding: 20rpx 40rpx;
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: all 0.3s;
}
.page .header-section .section .user-info .user-content .login-btn text.data-v-0be17cc6 {
  font-size: 30rpx;
  color: #fff;
  margin-right: 12rpx;
}
.page .header-section .section .user-info .user-content .login-btn.data-v-0be17cc6:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  background: rgba(255, 255, 255, 0.25);
}
.page .feature-box.data-v-0be17cc6 {
  margin: 20rpx 40rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.page .feature-box .feature-title.data-v-0be17cc6 {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}
.page .feature-box .feature-list .feature-item.data-v-0be17cc6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;
}
.page .feature-box .feature-list .feature-item .item-left.data-v-0be17cc6 {
  display: flex;
  align-items: center;
}
.page .feature-box .feature-list .feature-item .item-left .feature-name.data-v-0be17cc6 {
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
}
.page .feature-box .feature-list .feature-item.data-v-0be17cc6:active {
  background: #f8f8f8;
}
.page .feature-box .feature-list .feature-item.data-v-0be17cc6:last-child {
  border-bottom: none;
}
.page .feature-box .feature-list .contact-feature-btn.data-v-0be17cc6 {
  width: 100%;
  background-color: transparent;
  border: none;
  text-align: left;
  line-height: normal;
  border-radius: 0;
  padding: 30rpx 20rpx;
  margin: 0;
}
.page .feature-box .feature-list .contact-feature-btn.data-v-0be17cc6::after {
  border: none;
}
.page .feature-box .feature-list .contact-feature-btn.data-v-0be17cc6:active {
  background: #f8f8f8;
}
.page .assets-box.data-v-0be17cc6 {
  margin: 20rpx 40rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.page .assets-box .asset-item.data-v-0be17cc6 {
  flex: 1;
  text-align: center;
  position: relative;
  padding: 12rpx 0;
}
.page .assets-box .asset-item.data-v-0be17cc6:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 1rpx;
  height: 36rpx;
  background: #eee;
}
.page .assets-box .asset-item .asset-value.data-v-0be17cc6 {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  display: block;
  line-height: 1.4;
}
.page .assets-box .asset-item .asset-label.data-v-0be17cc6 {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
  display: block;
}
.page .assets-box .asset-item.data-v-0be17cc6:active {
  opacity: 0.8;
}

