import request from '@/utils/request';

// 获取积分商城商品列表
export const getPointMallList = (params) => {
    return request({
        url: '/point_mall/list',
        method: 'GET',
        params
    });
};

// 兑换积分商品
export const exchangePointMallItem = (data) => {
    return request({
        url: '/point_mall/exchange',
        method: 'POST',
        data
    });
};

// 获取我的兑换记录
export const getMyExchanges = (params) => {
    return request({
        url: '/point_mall/myexchanges',
        method: 'GET',
        params
    });
}; 