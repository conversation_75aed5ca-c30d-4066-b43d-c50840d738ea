<template>
	<view class="wp_-flex-col page">
		<!-- 顶部用户信息 -->
		<view class="header-section">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="section">
				<view class="user-info">
					<!-- 未登录状态 -->
					<template v-if="!isLogin">
						<view class="user-content" @tap="handleLogin">
							<view class="user-left">
								<image class="avatar" src="/static/my/default-avatar.png" mode="aspectFill" />
								<view class="info-wrap">
									<text class="nickname">点击授权登录</text>
									<text class="desc">登录后享受更多权益</text>
								</view>
							</view>
						</view>
					</template>
					
					<!-- 已登录状态 -->
					<template v-else>
						<view class="user-content">
							<view class="user-left">
								<image class="avatar" :src="userInfo.avatarUrl || '/static/my/default-avatar.png'" mode="aspectFill" />
								<view class="info-wrap">
									<text class="nickname">{{userInfo.nickName}}</text>
									<text class="desc">{{userInfo.phone}}</text>
								</view>
							</view>
							<view class="setting-btn" @tap="goToSetting">
								<custom-icon name="setting-fill" size="32" color="#a3a3a3"></custom-icon>
							</view>
						</view>
					</template>
				</view>
			</view>
		</view>

		<!-- 登录后显示资产信息 -->
		<template v-if="isLogin">
			<view class="assets-box">
				<view class="asset-item" @tap="goToPoints">
					<text class="asset-value">{{userAssets.points}}</text>
					<text class="asset-label">积分</text>
				</view>
				<view class="asset-item" @tap="goToCoupons">
					<text class="asset-value">{{userAssets.coupons}}</text>
					<text class="asset-label">优惠券</text>
				</view>
				<view class="asset-item" @tap="goToRecharge">
					<text class="asset-value">¥{{userAssets.balance}}</text>
					<text class="asset-label">余额</text>
				</view>
			</view>
		</template>

		<!-- 功能列表 -->
		<view class="feature-box">
			<view class="feature-title">我的服务</view>
			<view class="feature-list">
				<block v-for="(item, index) in features" :key="index">
					<!-- 普通功能项 -->
					<view 
						v-if="item.type !== 'service'"
						class="feature-item" 
						@tap="handleFeatureClick(item)"
					>
						<view class="item-left">
							<u-icon :name="item.icon" size="30" color="#333"></u-icon>
							<text class="feature-name">{{item.name}}</text>
						</view>
						<u-icon name="arrow-right" color="#999" size="15"></u-icon>
					</view>
					
					<!-- 客服功能项 - 使用button实现，但样式保持一致 -->
					<button 
						v-else
						open-type="contact"
						class="feature-item contact-feature-btn" 
					>
						<view class="item-left">
							<u-icon :name="item.icon" size="30" color="#333"></u-icon>
							<text class="feature-name">{{item.name}}</text>
						</view>
						<u-icon name="arrow-right" color="#999" size="15"></u-icon>
					</button>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
import { thirdLogin, thirdLoginFullPath, getUserProfile, getUserAssets, getUserPoints, getUserCoupons, getUserBalance, getMyCoupons } from '@/api/user.js';
export default {
	data() {
		return {
			statusBarHeight: 0,
			isLogin: false,
			userInfo: {},
			userAssets: {
				points: 0,
				coupons: 0,
				balance: '0.00'
			},
			features: [
				{ name: '我的订单', icon: 'order', type: 'order' },
				{ name: '收货地址', icon: 'map', type: 'address' },
				{ name: '联系客服', icon: 'phone', type: 'service' },
				{ name: '充值记录', icon: 'file-text', type: 'recharge-record' },
				// { name: '领取优惠', icon: 'gift', type: 'receive' },
				{ name: '资料设置', icon: 'account', type: 'settings' },
				{ name: '版权声明', icon: 'info-circle', type: 'copyright' }
			]
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight
	},
	onShow() {
		// 检查登录状态
		const token = uni.getStorageSync('token')
		if (!token) {
			this.isLogin = false
			this.userInfo = {}
			return
		}
		
		// 已登录则更新用户信息
		const userInfo = uni.getStorageSync('userInfo')
		if (userInfo) {
			this.isLogin = true 
			this.userInfo = userInfo
			
			// 从本地存储获取最近的资产数据（用于快速显示）
			const storedPoints = uni.getStorageSync('points')
			const storedBalance = uni.getStorageSync('balance')
			
			if (storedPoints) this.userAssets.points = storedPoints
			if (storedBalance) this.userAssets.balance = storedBalance
			
			// 获取最新用户信息和资产  
			this.getUserData()
		} else {
			this.isLogin = false
			this.userInfo = {}
		}
	},
	methods: {
		// 处理登录点击
		handleLogin() {
			// 保存当前页面路径
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			const url = currentPage ? `/${currentPage.route}` : '';
			
			if (url) {
				uni.setStorageSync('redirect_url', url);
			}
			
			// 跳转到登录页面
			uni.redirectTo({
				url: '/pages/login/login'
			});
		},
		
		// 处理登录成功后的数据
		handleLoginSuccess(data, originalUserInfo) {
			try {
				console.log('处理登录成功数据');
				
				if (!data) {
					throw new Error('返回数据为空');
				}
				
				const userData = data.userinfo || {};
				const thirdData = data.thirdinfo || {};
				
				if (!userData || !userData.token) {
					throw new Error('用户数据异常');
				}
				
				// 保存登录凭证
				uni.setStorageSync('token', userData.token);
				
				// 保存第三方登录数据（可选）
				if (thirdData && (thirdData.session_key || thirdData.openid)) {
					uni.setStorageSync('thirdData', {
						openid: thirdData.openid || '',
						session_key: thirdData.session_key || ''
					});
				}
				
				// 头像处理
				let avatar = '/static/my/default-avatar.png';
				if (userData.avatar) {
					avatar = userData.avatar;
				} else if (thirdData && thirdData.avatarUrl) {
					avatar = thirdData.avatarUrl;
				} else if (originalUserInfo && originalUserInfo.avatarUrl) {
					avatar = originalUserInfo.avatarUrl;
				}
				
				// 昵称处理
				let nickname = '用户';
				if (userData.nickname) {
					nickname = userData.nickname;
				} else if (thirdData && thirdData.nickName) {
					nickname = thirdData.nickName;
				} else if (originalUserInfo && originalUserInfo.nickName) {
					nickname = originalUserInfo.nickName;
				}
				
				// 保存用户信息 - 优先使用服务器返回数据
				const userInfoToSave = {
					avatarUrl: avatar,
					nickName: nickname,
					phone: userData.mobile || '',
					userId: userData.id || userData.user_id || '',
					createTime: userData.createtime || ''
				};
				
				uni.setStorageSync('userInfo', userInfoToSave);
				
				// 更新页面状态
				this.isLogin = true;
				this.userInfo = userInfoToSave;
				
				// 显示成功提示
				uni.showToast({
					title: '登录成功',
					icon: 'none',
					duration: 1500
				});
				
				// 获取最新用户信息和资产
				this.getUserData();
				
			} catch (error) {
				console.error('处理登录数据出错:', error.message);
				uni.showToast({
					title: '登录数据处理失败',
					icon: 'none',
					duration: 2000
				});
			}
		},
		
		// 获取用户数据（包括资产信息）
		async getUserData() {
			try {
				// 使用统一接口获取用户完整信息
				const profileRes = await getUserProfile()
				if (profileRes && profileRes.code === 1 && profileRes.data) {
					const userData = profileRes.data
					console.log('新增用户数据:', userData)

					// 更新用户信息
					this.userInfo = {
						id: userData.id,
						userId: userData.user_id,
						username: userData.username,
						nickName: userData.nickname,
						avatarUrl: userData.avatar,
						phone: userData.mobile,
						gender: userData.gender,
						birthday: userData.birthday
					}
					
					// 更新资产信息
					this.userAssets = {
						points: parseInt(userData.score || 0),
						coupons: parseInt(userData.coupons || 0), // 可能需要额外获取
						balance: parseFloat(userData.money || 0).toFixed(2)
					}
					
					console.log('新增用户:', this.userInfo.gender)
					// 更新本地存储
					uni.setStorageSync('userInfo', this.userInfo)
					uni.setStorageSync('points', this.userAssets.points)
					uni.setStorageSync('balance', this.userAssets.balance)
					
					// 单独获取优惠券数量
					this.getCouponsCount()
					
					console.log('用户数据获取成功:', userData.nickname)
					return
				}
				
				// 如果统一接口失败，使用分开的方式获取
				this.getFallbackUserData()
			} catch (error) {
				console.error('获取用户数据失败:', error)
				this.getFallbackUserData()
			}
		},
		
		// 获取优惠券数量
		async getCouponsCount() {
			try {
				const couponsRes = await getUserCoupons()
				if (couponsRes && couponsRes.code === 1) {
					this.userAssets.coupons = Array.isArray(couponsRes.data) 
						? couponsRes.data.length 
						: parseInt(couponsRes.data || 0)
				}
				
			} catch (e) {
				console.error('获取优惠券数量失败:', e)
			}
		},
		
		// 备用方案：分别获取用户资产
		async getFallbackUserData() {
			try {
				// 获取资产信息
				const assetsRes = await getUserAssets()
				if (assetsRes && assetsRes.code === 1 && assetsRes.data) {
					this.userAssets = {
						points: parseInt(assetsRes.data.points || 0),
						coupons: parseInt(assetsRes.data.coupons || 0),
						balance: parseFloat(assetsRes.data.balance || 0).toFixed(2)
					}
					
					// 更新本地存储
					uni.setStorageSync('points', this.userAssets.points)
					uni.setStorageSync('balance', this.userAssets.balance)
				} else {
					// 如果获取资产信息失败，分别获取各项资产
					this.getPointsInfo()
					this.getCouponsInfo()
					this.getBalanceInfo()
				}
			} catch (e) {
				console.error('获取资产信息失败，尝试分别获取:', e)
				this.getPointsInfo()
				this.getCouponsInfo()
				this.getBalanceInfo()
			}
		},
		
		// 获取积分信息
		async getPointsInfo() {
			try {
				const pointsRes = await getUserPoints()
				if (pointsRes && pointsRes.code === 1) {
					this.userAssets.points = parseInt(pointsRes.data.points || 0)
					uni.setStorageSync('points', this.userAssets.points)
				}
			} catch (e) {
				console.error('获取积分失败:', e.message)
			}
		},
		
		// 获取优惠券信息
		async getCouponsInfo() {
			try {
				// 使用新的getMyCoupons接口
				const couponsRes = await getMyCoupons()
				if (couponsRes && couponsRes.code === 1) {
					this.userAssets.coupons = Array.isArray(couponsRes.data) 
						? couponsRes.data.length 
						: parseInt(couponsRes.data || 0)
				} else {
					// 如果新接口失败，尝试使用旧接口
					const oldCouponsRes = await getUserCoupons()
					if (oldCouponsRes && oldCouponsRes.code === 1) {
						this.userAssets.coupons = Array.isArray(oldCouponsRes.data) 
							? oldCouponsRes.data.length 
							: parseInt(oldCouponsRes.data || 0)
					}
				}
			} catch (e) {
				console.error('获取优惠券失败:', e.message)
			}
		},
		
		// 获取余额信息
		async getBalanceInfo() {
			try {
				const balanceRes = await getUserBalance()
				if (balanceRes && balanceRes.code === 1) {
					this.userAssets.balance = parseFloat(balanceRes.data.balance || 0).toFixed(2)
					uni.setStorageSync('balance', this.userAssets.balance)
				}
			} catch (e) {
				console.error('获取余额失败:', e.message)
			}
		},
		
		goToSetting() {
			uni.navigateTo({
				url: '/pages/my/settings',
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		},
		handleFeatureClick(item) {
			switch(item.type) {
				case 'order':
					uni.switchTab({
						url: '/pages/order/order'
					})
					break
				case 'address':
					uni.navigateTo({
						url: '/pages/address/address'
					})
					break
				case 'recharge-record':
					uni.navigateTo({
						url: '/pages/my/recharge-record'
					})
					break
				case 'receive':
					uni.navigateTo({
						url: '/pages/coupon/receive'
					})
					break
				case 'settings':
					uni.navigateTo({
						url: '/pages/my/settings'
					})
					break
				case 'copyright':
					uni.navigateTo({
						url: '/pages/copyright/copyright'
					})
					break
			}
		},
		goToPoints() {
			uni.navigateTo({
				url: '/pages/points/mall',
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		},
		goToCoupons() {
			uni.navigateTo({
				url: '/pages/coupon/coupon',
				animationType: 'slide-in-right',
					animationDuration: 300
			})
		},
		goToRecharge() {
			uni.navigateTo({
				url: '/pages/my/recharge',
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background-color: #f8f8f8;
	
	.header-section {
		padding: 0 30rpx 60rpx;
		
		.status-bar {
			width: 100%;
		}
		
		.section {
			padding-top: 88rpx;
			
			.user-info {
				.user-content {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 20rpx 0;
					
					.user-left {
						display: flex;
						align-items: center;
						
						.avatar {
							width: 140rpx;
							height: 140rpx;
							border-radius: 50%;
							border: 4rpx solid rgba(255, 255, 255, 0.3);
							box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
						}
						
						.info-wrap {
							margin-left: 32rpx;
							
							.nickname {
								font-size: 36rpx;
								color: #333;
								font-weight: 600;
								margin-bottom: 12rpx;
								display: block;
							}
							
							.desc {
								font-size: 26rpx;
								color: #333;
								display: block;
							}
						}
					}
					
					.setting-btn {
						width: 88rpx;
						height: 88rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						
						&:active {
							opacity: 0.8;
						}
					}
					
					.login-btn {
						display: flex;
						align-items: center;
						background: rgba(255, 255, 255, 0.2);
						border-radius: 100rpx;
						padding: 20rpx 40rpx;
						border: 1px solid rgba(255, 255, 255, 0.25);
						transition: all 0.3s;
						
						text {
							font-size: 30rpx;
							color: #fff;
							margin-right: 12rpx;
						}
						
						&:active {
							transform: scale(0.95);
							background: rgba(255, 255, 255, 0.25);
						}
					}
				}
			}
		}
	}
	
	.feature-box {
		margin: 20rpx 40rpx;
		background: #fff;
		border-radius: 20rpx;
		padding: 24rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		
		.feature-title {
			font-size: 30rpx;
			color: #333;
			font-weight: 600;
			margin-bottom: 24rpx;
			padding: 0 8rpx;
		}
		
		.feature-list {
			.feature-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx 20rpx;
				border-bottom: 1px solid #f5f5f5;
				
				.item-left {
					display: flex;
					align-items: center;
					
					.feature-name {
						font-size: 28rpx;
						color: #333;
						margin-left: 20rpx;
					}
				}
				
				&:active {
					background: #f8f8f8;
				}
				
				&:last-child {
					border-bottom: none;
				}
			}
			
			.contact-feature-btn {
				width: 100%;
				background-color: transparent;
				border: none;
				text-align: left;
				line-height: normal;
				border-radius: 0;
				padding: 30rpx 20rpx;
				margin: 0;
				
				&::after {
					border: none;
				}
				
				&:active {
					background: #f8f8f8;
				}
			}
		}
	}
	
	.assets-box {
		margin: 20rpx 40rpx;
		background: #fff;
		border-radius: 20rpx;
		padding: 24rpx;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		
		.asset-item {
			flex: 1;
			text-align: center;
			position: relative;
			padding: 12rpx 0;
			
			&:not(:last-child)::after {
				content: '';
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 1rpx;
				height: 36rpx;
				background: #eee;
			}
			
			.asset-value {
				font-size: 32rpx;
				color: #333;
				font-weight: 600;
				display: block;
				line-height: 1.4;
			}
			
			.asset-label {
				font-size: 24rpx;
				color: #999;
				margin-top: 4rpx;
				display: block;
			}
			
			&:active {
				opacity: 0.8;
			}
		}
	}
}
</style>