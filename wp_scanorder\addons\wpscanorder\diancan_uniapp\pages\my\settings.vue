<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">个人资料</text>
      </view>
    </view>

    <!-- 主体内容区 -->
    <view class="content-container">
      <!-- 头像区域 -->
      <view class="avatar-section">
        <image class="avatar" :src="userInfo.avatarUrl || '/static/my/default-avatar.png'" mode="aspectFill" />
        <button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" class="avatar-button">
          <view class="avatar-overlay">
            <view class="overlay-icon">
              <u-icon name="camera-fill" color="#ffffff" size="24"></u-icon>
            </view>
          </view>
        </button>
      </view>
      
      <!-- 表单列表 -->
      <view class="form-list">
        <!-- 昵称 -->
        <view class="form-item">
          <view class="form-label">昵称</view>
          <view class="form-content">
            <input 
              class="form-input" 
              type="nickname"
              placeholder="请输入昵称" 
              :value="userInfo.nickName"
              @nicknamereview="onNicknameReview"
              @input="onNicknameInput"
              @blur="onNicknameBlur"
            />
            <view class="form-right-btn" v-if="!userInfo.nickName">
              <text class="btn-text">设置昵称</text>
            </view>
          </view>
        </view>
        
        <!-- 手机号 -->
        <view class="form-item">
          <view class="form-label">手机</view>
          <view class="form-content">
            <text class="form-text">{{formatPhone(userInfo.phone) || '未绑定'}}</text>
            <view class="form-right-btn" @tap="changePhone" v-show="false">
              <text class="btn-text">修改</text>
            </view>
          </view>
        </view>
        
        <!-- 性别 -->
        <view class="form-item" @tap="showGenderPicker">
          <view class="form-label">性别</view>
          <view class="form-content">
            <text class="form-text">{{userInfo.gender == '1' ? '男' : userInfo.gender == '2' ? '女' : '未设置'}}</text>
            <view class="form-right-btn">
              <text class="btn-text">修改</text>
            </view>
          </view>
        </view>
        
        <!-- 生日 -->
        <view class="form-item" @tap="changeBirthday">
          <view class="form-label">生日</view>
          <view class="form-content">
            <text class="form-text">{{userInfo.birthday || userInfo.birthdayOne || '未设置'}}</text>
            <view class="form-right-tip" v-if="userInfo.birthday">
              <text class="tip-text">(仅支持修改一次)</text>
            </view>
            <view class="form-right-btn" v-else>
              <text class="btn-text">修改</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 隐私提示 -->
      <view class="privacy-tip">
        <text class="tip-text">根据未成年人保护相关法律规定，我们不主动处理14周岁以下未成年人的个人信息。如果您为14周岁以下的用户，请勿填写您的个人资料。</text>
      </view>
      
      <!-- 保存按钮 -->
      <view class="save-btn" @tap="saveUserProfile">
        <text>保存</text>
      </view>
      
      <!-- 关于我们 -->
      <view class="about-container">
        <view class="about-btn" @tap="goToCopyright">
          <text>版权声明</text>
        </view>
      </view>

      <!-- 退出登录 -->
      <view class="logout-container">
        <view class="logout-btn" @tap="handleLogout">
          <text>退出登录</text>
        </view>
      </view>
    </view>
    
    <!-- 头像选择弹窗 -->
    <u-popup v-model="showAvatarPopup" mode="bottom" border-radius="16">
      <view class="avatar-popup">
        <view class="popup-title">设置头像</view>
        <view class="popup-options">
          <view class="popup-option" @tap="chooseAvatar">
            <u-icon name="photo" size="32" color="#333"></u-icon>
            <text>从相册选择</text>
          </view>
          <view class="popup-option">
            <button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" class="wx-button">
              <u-icon name="weixin-fill" size="32" color="#07c160"></u-icon>
              <text>使用微信头像</text>
            </button>
          </view>
        </view>
        <view class="popup-cancel" @tap="closeAvatarPopup">取消</view>
      </view>
    </u-popup>
    
    <!-- 生日选择器 - 垂直滚动样式 -->
    <view class="birthday-picker-modal" v-show="showBirthdayPicker" @tap.stop>
      <view class="birthday-picker-mask" @tap.stop="closeBirthdayPicker"></view>
      <view class="date-picker-content" @tap.stop>
        <view class="date-picker-header">
          <text class="date-picker-cancel" @tap="closeBirthdayPicker">取消</text>
          <text class="date-picker-title">选择生日</text>
          <text class="date-picker-confirm" @tap="confirmBirthday">确认</text>
        </view>
        
        <!-- 使用原生滚动选择器风格 -->
        <view class="picker-view-container">
          <picker-view
            class="date-picker-view"
            :value="defaultBirthdayIndex"
            @change="onPickerChange"
            :indicator-style="'height: 50px;'"
            :immediate-change="true"
            :key="Date.now()"
          >
            <!-- 年份列 -->
            <picker-view-column>
              <view class="picker-item" v-for="(year, index) in birthdayColumns[0]" :key="index">
                {{year}}
              </view>
            </picker-view-column>

            <!-- 月份列 -->
            <picker-view-column>
              <view class="picker-item" v-for="(month, index) in birthdayColumns[1]" :key="index">
                {{month}}
              </view>
            </picker-view-column>

            <!-- 日期列 -->
            <picker-view-column>
              <view class="picker-item" v-for="(day, index) in birthdayColumns[2]" :key="index">
                {{day}}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { updateUserProfile, uploadImage } from '@/api/user.js';
// 移除单独导入的uview组件，因为已经全局引入

export default {
  // 不需要单独注册组件，uView已全局注册
  data() {
    return {
      userInfo: {
        nickName: '',
        phone: '',
        avatarUrl: '',
        gender: '', // 1: 男性, 2: 女性
        birthday: '',
        birthdayOne: '',
      },
      loading: false,
      showAvatarPopup: false,
      showNicknamePopup: false,
      showBirthdayPicker: false, // 控制生日选择器显示
      tempNickname: '',
      formChanged: false, // 用于标记表单是否有修改
      birthdayColumns: [[], [], []], // 年月日三列数据
      defaultBirthdayIndex: [0, 0, 0], // 默认选中索引
      selectedYear: '', // 当前选中的年
      selectedMonth: '', // 当前选中的月
      selectedDay: '', // 当前选中的日
      lastYearIndex: 0, // 上次选择的年份索引
      lastMonthIndex: 0, // 上次选择的月份索引
      pickerKey: 0 // 用于强制重新渲染
    }
  },
  
  onLoad() {
    this.userInfo = uni.getStorageSync('userInfo') || {}
    console.log('用户信息加载完成:', this.userInfo);
    
    // 初始化临时昵称
    this.tempNickname = this.userInfo.nickName || ''
    this.initBirthdayPicker();
    this.showBirthdayPicker = false;
  },
  
  // 页面准备完成
  onReady() {
    console.log('页面准备完成');
  },
  
  // 确保页面每次显示时重新获取数据
  onShow() {
    // 刷新用户信息
    this.userInfo = uni.getStorageSync('userInfo') || {};
    console.log('onShow 刷新用户信息:', this.userInfo);
    
    // 更新临时昵称并确保弹窗关闭
    this.tempNickname = this.userInfo.nickName || '';
    this.showNicknamePopup = false;
  },
  
  // 页面卸载时清理资源
  onUnload() {
    // 清除昵称监听计时器
    if (this.nicknameTimer) {
      clearTimeout(this.nicknameTimer);
      this.nicknameTimer = null;
      console.log('页面卸载，清理昵称监听');
    }
  },
  
  methods: {
    // 跳转到版权声明页面
    goToCopyright() {
      uni.navigateTo({
        url: '/pages/copyright/copyright'
      })
    },

    // 处理返回按钮
    handleBack() {
      // 如果表单有修改且未保存，显示提示
      if (this.formChanged) {
        uni.showModal({
          title: '提示',
          content: '您有未保存的修改，确定要离开吗？',
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack()
            }
          }
        })
      } else {
        uni.navigateBack()
      }
    },
    
    // 格式化手机号码（中间部分用*替代）
    formatPhone(phone) {
      if (!phone) return '';
      if (phone.length !== 11) return phone;
      return phone.substring(0, 3) + '******' + phone.substring(9);
    },
    
    // 选择性别
    selectGender(gender) {
      if (this.userInfo.gender === gender) return;
      
      this.userInfo.gender = gender;
      this.formChanged = true;
      
      // 显示选择成功提示
      uni.showToast({
        title: gender === '1' ? '已选择男' : '已选择女',
        icon: 'success',
        duration: 1500
      });
    },
    
    // 性别选择弹窗
    showGenderPicker() {
      uni.showActionSheet({
        itemList: ['男', '女'],
        success: (res) => {
          // 选择男性
          if (res.tapIndex === 0) {
            this.selectGender('1');
          }
          // 选择女性
          else if (res.tapIndex === 1) {
            this.selectGender('2');
          }
        }
      });
    },
    
    // 修改生日
    changeBirthday() {
      console.log('触发生日修改方法');
      
      // 如果已经设置过生日，提示只能修改一次
      if (this.userInfo.birthday) {
        return uni.showToast({
          title: '生日信息只能修改一次',
          icon: 'none'
        });
      }

      // 先清空默认值，避免旧值干扰
      this.defaultBirthdayIndex = [0, 0, 0];
      
      // 延迟显示选择器
      this.showBirthdayPicker = true;
      
      // 使用setTimeout延迟初始化，确保选择器DOM已渲染
      setTimeout(() => {
        // 初始化日期数据
        this.initBirthdayPicker();
        
        // 延迟强制更新，确保索引值生效
        setTimeout(() => {
          // 强制更新视图
          this.$forceUpdate();
          console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);
          console.log('日期选择器状态:', this.showBirthdayPicker, '索引值:', this.defaultBirthdayIndex);
        }, 100);
      }, 200);
    },
    
    // 关闭生日选择器
    closeBirthdayPicker() {
      console.log('关闭日期选择器');
      this.showBirthdayPicker = false;
    },
    
    // 初始化日期选择器
    initBirthdayPicker() {
      // 清除可能的延迟任务
      if (this.pickerTimer) {
        clearTimeout(this.pickerTimer);
      }
      
      console.log('初始化日期选择器');
      const now = new Date();
      const currentYear = now.getFullYear();
      
      // 直接指定2007年为默认年份
      const defaultYear = 2007;
      
      // 年份范围：从1900年开始
      const years = [];
      for (let i = 1900; i <= currentYear; i++) {
        years.push(i + '年');
      }
      
      // 月份：1-12月
      const months = [];
      for (let i = 1; i <= 12; i++) {
        months.push((i < 10 ? '0' + i : i) + '月');
      }
      
      // 初始化日期数据（默认31天，后续会更新）
      this.selectedYear = defaultYear;
      this.selectedMonth = 1;
      this.selectedDay = 1;
      
      // 根据选中的年月计算天数
      const days = this.updateDays(this.selectedYear, this.selectedMonth);
      
      // 更新选择器数据
      this.birthdayColumns = [years, months, days];
      
      // 计算默认年份索引 - 2007与1900的差值
      const yearIndex = defaultYear - 1900;
      
      // 设置索引值 - 此处非常重要，必须使用新数组
      this.$set(this, 'defaultBirthdayIndex', [
        yearIndex,
        0, // 1月
        0  // 1日
      ]);
      
      // 输出调试信息
      console.log(`初始化日期选择器完成: 当前年份索引=${yearIndex}, 选中年份=${defaultYear}年`);
      console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);
    },
    
    // 更新日期数组
    updateDays(year, month) {
      // 计算指定年月的天数
      const daysInMonth = new Date(year, month, 0).getDate();
      console.log(`计算${year}年${month}月的天数:`, daysInMonth);
      
      // 更新日期数组
      const days = [];
      for (let i = 1; i <= daysInMonth; i++) {
        days.push((i < 10 ? '0' + i : i) + '日');
      }
      
      return days;
    },
    
    // 处理picker-view整体变化
    onPickerChange(e) {
      const values = e.detail.value;
      
      // 获取年月日索引
      const yearIndex = values[0];
      const monthIndex = values[1];
      const dayIndex = values[2];
      
      // 更新默认选中索引
      this.defaultBirthdayIndex = values;
      
      // 获取不带单位的值
      const yearText = this.birthdayColumns[0][yearIndex];
      const monthText = this.birthdayColumns[1][monthIndex];
      const dayText = this.birthdayColumns[2][dayIndex];
      
      // 获取数值
      this.selectedYear = parseInt(yearText.replace('年', ''));
      this.selectedMonth = parseInt(monthText.replace('月', ''));
      this.selectedDay = parseInt(dayText.replace('日', ''));
      
      console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);
      
      // 如果年份或月份变化了，需要更新日期数组
      if (this.lastYearIndex !== yearIndex || this.lastMonthIndex !== monthIndex) {
        // 更新日期列
        const newDays = this.updateDays(this.selectedYear, this.selectedMonth);
        this.birthdayColumns[2] = newDays;
        
        // 记录当前选中的年月索引
        this.lastYearIndex = yearIndex;
        this.lastMonthIndex = monthIndex;
      }
    },
    
    // 确认生日选择
    confirmBirthday() {
      // 拼接生日
      const year = this.selectedYear;
      const month = String(this.selectedMonth).padStart(2, '0');
      const day = String(this.selectedDay).padStart(2, '0');
      
      const birthday = `${year}-${month}-${day}`;
      
      console.log('确认选择生日:', birthday);
      
      // 设置生日
      this.userInfo.birthdayOne = birthday;
      this.formChanged = true;
      
      // 关闭选择器
      this.showBirthdayPicker = false;
      
      // 显示成功提示
      uni.showToast({
        title: '生日设置成功',
        icon: 'success'
      });
    },
    
    // 取消日期选择
    cancelBirthday() {
      this.showBirthdayPicker = false;
    },
    
    // 处理日期选择
    onDatePickerChange(e) {
      const values = e.detail.value;
      this.datePickerValue = values;
      
      // 当年份或月份变化时，需要更新日期数组
      const year = this.years[values[0]];
      const month = this.months[values[1]];
      
      // 更新日期数组
      const daysInMonth = new Date(year, month, 0).getDate();
      if (this.days.length !== daysInMonth) {
        this.days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
        
        // 如果当前选中的日期超出了这个月的最大天数，则调整
        if (values[2] >= daysInMonth) {
          this.datePickerValue[2] = daysInMonth - 1;
        }
      }
    },
    
    // 初始化日期选择器数据
    initDatePicker() {
      console.log('开始初始化日期选择器数据');
      const now = new Date();
      const currentYear = now.getFullYear();
      const defaultYear = currentYear - 18; // 默认18岁
      
      // 创建年份数组 (1900年至今)
      this.years = [];
      for (let i = 1900; i <= currentYear; i++) {
        this.years.push(i);
      }
      
      // 创建月份数组
      this.months = [];
      for (let i = 1; i <= 12; i++) {
        this.months.push(i);
      }
      
      // 默认选中的年月
      const defaultYearIndex = this.years.findIndex(y => y === defaultYear);
      const defaultMonthIndex = 0; // 默认1月
      
      // 创建日期数组 (默认31天，后续会根据选择的年月动态调整)
      this.days = [];
      const daysInMonth = new Date(defaultYear, 1, 0).getDate();
      for (let i = 1; i <= daysInMonth; i++) {
        this.days.push(i);
      }
      
      // 初始日期值
      this.datePickerValue = [
        defaultYearIndex > -1 ? defaultYearIndex : this.years.length - 18,
        defaultMonthIndex,
        0
      ];
      
      console.log('日期选择器数据初始化完成:', {
        years: this.years.length,
        months: this.months.length,
        days: this.days.length,
        defaultValues: this.datePickerValue
      });
    },
    
    // 保存用户资料
    async saveUserProfile() {
      if (!this.formChanged) {
        return uni.showToast({
          title: '未做任何修改',
          icon: 'none'
        });
      }
      
      if (this.loading) return;
      
      this.loading = true;
      uni.showLoading({
        title: '保存中...',
        mask: true
      });
      if (this.userInfo.birthdayOne) {
        this.userInfo.birthday = this.userInfo.birthdayOne;
      }
      try {
        // 构建请求数据
        const requestData = {
          nickname: this.userInfo.nickName,
          gender: this.userInfo.gender,
          birthday: this.userInfo.birthday
        };
        
        // 调用API更新用户资料
        const updateRes = await updateUserProfile(requestData);
        
        if (updateRes && updateRes.code === 1) {
          // 更新成功
          this.saveUserInfo();
          this.formChanged = false;
          
          uni.showToast({
            title: '保存成功',
            icon: 'none'
          });
        } else {
          throw new Error((updateRes && updateRes.msg) ? updateRes.msg : '保存失败');
        }
      } catch (e) {
        console.error('保存用户资料失败:', e);
        uni.showToast({
          title: e.message || '保存失败',
          icon: 'none'
        });
      } finally {
        // uni.hideLoading();
        this.loading = false;
      }
    },
    
    // 显示头像选项弹窗
    showAvatarOptions() {
      this.showAvatarPopup = true
    },
    
    // 关闭头像选项弹窗
    closeAvatarPopup() {
      this.showAvatarPopup = false
    },
    
    // 选择头像
    chooseAvatar() {
      this.showAvatarPopup = false
      
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          
          // 显示上传中提示
          uni.showLoading({
            title: '上传中...',
            mask: true
          })
          
          // 先上传图片到服务器
          this.uploadAvatar(tempFilePath)
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
          uni.showToast({
            title: '选择失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 处理微信头像选择回调
    async onChooseAvatar(e) {
      console.log('选择头像回调:', e)
      const { avatarUrl } = e.detail
      
      if (!avatarUrl) {
        return uni.showToast({
          title: '获取头像失败',
          icon: 'none'
        })
      }
      
      // 显示上传中提示
      uni.showLoading({
        title: '上传中...',
        mask: true
      })
      
      try {
        // 上传头像到服务器
        await this.uploadAvatar(avatarUrl)
        
        uni.hideLoading()
        uni.showToast({
          title: '头像更新成功',
          icon: 'none'
        })
      } catch (e) {
        uni.hideLoading()
        console.error('上传头像失败:', e)
        uni.showToast({
          title: e.message || '头像更新失败',
          icon: 'none'
        })
      }
    },
    // 处理昵称输入
    onNicknameInput(e) {
      console.log('昵称输入事件:', e)
      if (e.detail && e.detail.value) {
        const nickname = e.detail.value.trim()
        this.userInfo.nickName = nickname
        this.tempNickname = nickname
        this.formChanged = true
        
        console.log('昵称已更新:', nickname)
      }
    },
    
    // 处理昵称输入框失去焦点
    onNicknameBlur(e) {
      console.log('昵称输入框失去焦点:', e);
      // 如果有值，确保更新
      if (e.detail && e.detail.value) {
        const nickname = e.detail.value.trim();
        if (nickname && nickname !== this.userInfo.nickName) {
          this.userInfo.nickName = nickname;
          this.tempNickname = nickname;
          this.formChanged = true;
          console.log('失去焦点时更新昵称:', nickname);
        }
      }
    },
    
    // 处理微信获取头像事件后询问用户昵称
    async onGetNickname(e) {
      console.log('获取头像事件:', e);
      
      try {
        // 选择头像后，显示输入昵称对话框
        uni.showModal({
          title: '设置昵称',
          editable: true,
          placeholderText: '请输入昵称',
          content: this.userInfo.nickName || '',
          success: async (res) => {
            if (res.confirm && res.content) {
              const nickname = res.content.trim();
              
              if (!nickname) {
                return;
              }
              
              if (nickname === this.userInfo.nickName) {
                return;
              }
              
              // 设置临时昵称
              this.tempNickname = nickname;
              
              // 显示提示
              uni.showLoading({
                title: '正在保存昵称...',
                mask: true
              });
              
              // 自动保存
              await this.confirmNickname();
            }
          }
        });
      } catch (err) {
        console.error('处理昵称获取事件出错:', err);
        uni.showToast({
          title: '获取昵称失败',
          icon: 'none'
        });
      }
    },
    
    // 处理微信昵称审核事件 - 这是使用nickname组件的关键方法
    async onNicknameReview(e) {
      console.log('昵称审核事件详情:', JSON.stringify(e));
      
      try {
        // 获取微信昵称
        let wxNickname = this.tempNickname;
        
        // 若未获取到昵称，记录错误
        if (!wxNickname) {
          console.error('未能获取到微信昵称，完整事件对象:', e);
          wxNickname = "微信用户"; // 默认昵称
          console.log('未获取到微信昵称，使用默认值');
        } else {
          console.log('成功获取到微信昵称:', wxNickname);
        }
        
        // 更新昵称和表单状态
        this.userInfo.nickName = wxNickname;
        this.tempNickname = wxNickname;
        this.formChanged = true;
        
        uni.showToast({
          title: '已获取微信昵称',
          icon: 'none'
        });
      } catch (err) {
        console.error('处理昵称审核事件出错:', err);
        uni.showToast({
          title: '获取昵称失败',
          icon: 'none'
        });
      }
    },
    
    // 这个方法不再需要，我们直接在onNicknameReview中处理了
    async handleNickname(nickname) {
      console.log('处理获取到的昵称:', nickname);
      
      // 如果没有获取到昵称，则尝试使用直接输入
      if (!nickname) {
        // 如果没有获取到昵称，显示手动输入框
        return this.showManualNicknameInput();
      }
      
      // 设置临时昵称
      this.tempNickname = nickname;
      
      // 显示确认对话框
      uni.showModal({
        title: '确认修改昵称',
        content: `是否将昵称修改为"${nickname}"？`,
        success: async (res) => {
          if (res.confirm) {
            // 显示提示
            uni.showLoading({
              title: '正在保存昵称...',
              mask: true
            });
            
            // 保存昵称
            const result = await this.confirmNickname();
            console.log('保存昵称结果:', result);
          }
        }
      });
    },
    
    // 显示手动输入昵称的对话框
    showManualNicknameInput(message = '请输入您的昵称') {
      uni.showModal({
        title: '设置昵称',
        content: message,
        editable: true,
        placeholderText: '请输入昵称',
        success: async (res) => {
          if (res.confirm && res.content) {
            // 显示提示并保存用户输入的昵称
            uni.showLoading({
              title: '正在保存昵称...',
              mask: true
            });
            
            this.tempNickname = res.content.trim();
            await this.confirmNickname();
          }
        }
      });
    },
    
    // 处理点击昵称项
    handleWechatNickname() {
      // 显示昵称输入弹窗
      this.showNicknamePopup = true;
    },
    
    // 确认修改昵称
    async confirmNickname() {
      const nickname = this.tempNickname
      
      if (!nickname || nickname === this.userInfo.nickName) {
        console.log('昵称为空或未更改，不执行保存操作');
        uni.hideLoading();
        return false
      }
      
      try {
        console.log('准备提交昵称到服务器:', nickname);
        
        // 构造请求参数
        const requestData = { nickname };
        
        // 显示详细的请求参数，便于调试
        console.log('请求参数:', JSON.stringify(requestData));
        
        // 直接调用API更新昵称
        const updateRes = await updateUserProfile(requestData);
        
        // 记录响应详情
        console.log('昵称更新接口响应:', JSON.stringify(updateRes));
        
        // 检查响应状态
        if (updateRes && updateRes.code === 1) {
          // 更新本地用户信息
          this.userInfo.nickName = nickname;
          this.saveUserInfo();
          console.log('本地用户信息已更新:', JSON.stringify(this.userInfo));
          
          // 更新UI显示
          this.$forceUpdate();
          
          // 显示成功提示
          uni.showToast({
            title: '昵称更新成功',
            icon: 'success',
            duration: 2000
          });
          
          return true;
        } else {
          // 详细记录错误信息
          console.error('接口返回错误:', updateRes);
          throw new Error((updateRes && updateRes.msg) ? updateRes.msg : '昵称更新失败');
        }
      } catch (e) {
        console.error('更新昵称失败:', e);
        
        // 显示错误提示
        uni.showToast({
          title: e.message || '昵称更新失败',
          icon: 'none',
          duration: 2000
        });
        
        return false;
      } finally {
        // 确保无论成功失败都隐藏加载提示
        uni.hideLoading();
      }
    },
    
    // 显示隐私设置
    showPrivacySettings() {
      uni.showToast({
        title: '隐私设置功能开发中',
        icon: 'none'
      })
    },
    
    // 下载微信头像
    downloadWechatAvatar(avatarUrl) {
      return new Promise((resolve, reject) => {
        uni.downloadFile({
          url: avatarUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.tempFilePath)
            } else {
              reject(new Error('下载头像失败'))
            }
          },
          fail: (err) => {
            console.error('下载微信头像失败:', err)
            reject(err)
          }
        })
      })
    },
    
    // 上传头像
    async uploadAvatar(filePath, showToast = true) {
      try {
        // 上传图片获取URL
        const uploadRes = await uploadImage(filePath)
        
        if (uploadRes.code === 1 && uploadRes.data && uploadRes.data.url) {
          // 获取到图片URL后，调用修改个人信息接口
          const avatarUrl = uploadRes.data.url
          const updateRes = await updateUserProfile({ avatar: avatarUrl })
          
          if (updateRes.code === 1) {
            // 更新用户信息中的头像
            this.userInfo.avatarUrl = avatarUrl
            this.saveUserInfo()
            
            if (showToast) {
              uni.hideLoading()
              uni.showToast({
                title: '头像更新成功',
                icon: 'none'
              })
            }
            return true
          } else {
            throw new Error(updateRes.msg || '保存头像失败')
          }
        } else {
          throw new Error(uploadRes.msg || '图片上传失败')
        }
      } catch (e) {
        if (showToast) {
          uni.hideLoading()
          uni.showToast({
            title: e.msg || e.message || '上传失败',
            icon: 'none'
          })
        }
        console.error('上传头像失败:', e)
        throw e
      }
    },

    // 此方法已替换为toggleNicknameEdit
    changeName() {
      this.toggleNicknameEdit()
    },

    // 修改手机号
    changePhone() {
      uni.showModal({
        title: '修改手机号',
        editable: true,
        placeholderText: '请输入手机号',
        content: this.userInfo.phone || '',
        success: async (res) => {
          if(res.confirm && res.content) {
            // 手机号格式验证
            if(!/^1[3-9]\d{9}$/.test(res.content)) {
              return uni.showToast({
                title: '手机号格式不正确',
                icon: 'none'
              })
            }
            
            const phone = res.content
            
            // 避免重复提交
            if (this.loading) return
            if (phone === this.userInfo.phone) return
              
            this.loading = true
            uni.showLoading({
              title: '保存中...',
              mask: true
            })
            
            try {
              // 接口文档中没有明确提到手机号修改的字段，这里使用username尝试
              // 实际开发中需要根据后端接口确认正确的字段名
              const updateRes = await updateUserProfile({ username: phone })
              
              if(updateRes.code === 1) {
                // 更新本地用户信息
                this.userInfo.phone = phone
                this.saveUserInfo()
                
                uni.showToast({
                  title: '手机号修改成功',
                  icon: 'none'
                })
              } else {
                throw new Error(updateRes.msg || '手机号修改失败')
              }
            } catch(e) {
              console.error('修改手机号失败:', e)
              uni.showToast({
                title: e.msg || e.message || '修改失败',
                icon: 'none'
              })
            } finally {
              uni.hideLoading()
              this.loading = false
            }
          }
        }
      })
    },

    // 保存用户信息
    saveUserInfo() {
      uni.setStorageSync('userInfo', this.userInfo)
    },
    
    handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: (res) => {
          if(res.confirm) {
            uni.removeStorageSync('token')
            uni.removeStorageSync('userInfo')
            uni.navigateBack()
          }
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 34rpx;
      color: #000;
      font-weight: 500;
    }
  }
}

// 主体内容区域样式
.content-container {
  padding-bottom: 40rpx;
  
  // 昵称输入弹窗样式
  .nickname-popup {
    padding: 40rpx 30rpx;
    
    .popup-title {
      text-align: center;
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 30rpx;
    }
    
    .popup-content {
      margin-bottom: 40rpx;
      
      .nickname-popup-input {
        width: 100%;
        height: 80rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
      }
    }
    
    .popup-buttons {
    display: flex;
    border-top: 1px solid #f5f5f5;
    
    .popup-btn {
      flex: 1;
      height: 90rpx;
      line-height: 90rpx;
      text-align: center;
      font-size: 32rpx;
      
      &:active {
        background-color: #f9f9f9;
      }
    }
    
    .cancel-btn {
      color: #666;
      border-right: 1px solid #f5f5f5;
    }
    
    .normal-btn {
      color: #2196F3;
      font-weight: 500;
      border-right: 1px solid #f5f5f5;
    }
    
    .confirm-btn {
      color: #07c160;
      font-weight: 500;
    }
  }
  
  .popup-tip {
    font-size: 24rpx;
    color: #999;
    text-align: center;
    margin-bottom: 20rpx;
  }
}
  
  // 头像区域样式
  .avatar-section {
    position: relative;
    padding: 40rpx 0 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-bottom: 1px solid #f5f5f5;
    
    .avatar {
      width: 180rpx;
      height: 180rpx;
      border-radius: 50%;
      border: 2rpx solid #f5f5f5;
    }
    
    .avatar-button {
      position: absolute;
      bottom: 30rpx;
      right: 50%;
      transform: translateX(60rpx);
      width: 60rpx;
      height: 60rpx;
      padding: 0;
      margin: 0;
      border: none;
      background: transparent;
      
      &::after {
        border: none;
      }
    }
    
    .avatar-overlay {
      width: 60rpx;
      height: 60rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  // 表单区域样式
  .form-list {
    margin-top: 20rpx;
    background: #fff;
    
    .form-item {
      display: flex;
      padding: 30rpx 40rpx;
      border-bottom: 1px solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .form-label {
        width: 160rpx;
        font-size: 32rpx;
        color: #333;
        padding-top: 8rpx;
      }
      
      .form-content {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .form-input {
          flex: 1;
          height: 80rpx;
          font-size: 32rpx;
          color: #333;
          padding: 0 10rpx;
          
          &::placeholder {
            color: #999;
          }
        }
        
        .form-text {
          font-size: 32rpx;
          color: #333;
        }
        
        .form-right-btn {
          margin-left: 20rpx;
          
          .btn-text {
            color: #576b95;
            font-size: 32rpx;
          }
        }
        
        .form-right-tip {
          margin-left: 20rpx;
          
          .tip-text {
            color: #999;
            font-size: 26rpx;
          }
        }
        
        // 性别选择样式
        .gender-group {
          display: flex;
          flex: 1;
          
          .gender-option {
            display: flex;
            align-items: center;
            margin-right: 60rpx;
            
            .gender-radio {
              width: 40rpx;
              height: 40rpx;
              border-radius: 50%;
              border: 2rpx solid #ddd;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 10rpx;
              
              .radio-inner {
                width: 24rpx;
                height: 24rpx;
                border-radius: 50%;
                background: #07c160;
              }
            }
            
            .gender-text {
              font-size: 32rpx;
              color: #333;
            }
            
            &.active {
              .gender-radio {
                border-color: #07c160;
              }
            }
          }
        }
      }
    }
  }
  
  /* 已移动到nickname-section中 */
  
  .sync-wx-profile {
    background: #07c160;
    padding: 12rpx 30rpx;
    border-radius: 30rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
    
    text {
      font-size: 28rpx;
      color: #fff;
      margin-left: 10rpx;
    }
    
    &:active {
      opacity: 0.9;
    }
  }
}

// 表单列表样式
.form-list {
  margin: 30rpx;
  
  .form-section {
    background: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
    
    .form-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx;
      border-bottom: 1px solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-left {
        display: flex;
        align-items: center;
        
        .label {
          font-size: 30rpx;
          color: #333;
          margin-left: 16rpx;
        }
      }
      
      .item-right {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        
        .value {
          font-size: 30rpx;
          color: #999;
          margin-right: 16rpx;
        }
        
        /* 已移除昵称按钮相关样式 */
      }
      
      &:active {
        background-color: #f9f9f9;
      }
    }
  }
}

// 隐私提示
.privacy-tip {
  padding: 30rpx 40rpx;
  
  .tip-text {
    font-size: 26rpx;
    color: #999;
    line-height: 1.6;
  }
}

// 保存按钮
.save-btn {
  margin: 40rpx 30rpx;
  height: 90rpx;
  background: #e64340;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  text {
    font-size: 34rpx;
    color: #fff;
    font-weight: 500;
  }
  
  &:active {
    opacity: 0.9;
  }
}

// 关于我们容器
.about-container {
  margin-top: 40rpx;
  text-align: center;
}

// 关于我们按钮
.about-btn {
  display: inline-block;
  padding: 20rpx 40rpx;

  text {
    font-size: 28rpx;
    color: #999;
  }

  &:active {
    opacity: 0.7;
  }
}

// 退出登录容器
.logout-container {
  margin-top: 20rpx;
  text-align: center;
}

// 退出登录按钮
.logout-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  
  text {
    font-size: 32rpx;
    color: #576b95;
  }
  
  &:active {
    opacity: 0.7;
  }
}

// 头像选择弹窗
.avatar-popup {
  padding: 30rpx;
  
  .popup-title {
    text-align: center;
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 40rpx;
  }
  
  .popup-options {
    display: flex;
    justify-content: space-around;
    margin-bottom: 40rpx;
    
    .popup-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx;
      
      text {
        font-size: 28rpx;
        color: #333;
        margin-top: 16rpx;
      }
      
      .wx-button {
        background: transparent;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0;
        border: none;
        line-height: normal;
        
        &::after {
          border: none;
        }
        
        text {
          font-size: 28rpx;
          color: #333;
          margin-top: 16rpx;
        }
      }
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  .popup-cancel {
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 32rpx;
    color: #333;
    border-top: 1px solid #eee;
    
    &:active {
      background-color: #f5f5f5;
    }
  }
}

// 昵称输入弹窗样式
.nickname-popup {
  padding: 40rpx 30rpx;
  
  .popup-title {
    text-align: center;
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 30rpx;
  }
  
  .popup-content {
    margin-bottom: 40rpx;
    
    .nickname-popup-input {
      width: 100%;
      height: 80rpx;
      border: 1px solid #eee;
      border-radius: 8rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
      box-sizing: border-box;
    }
  }
}

/* 微信昵称按钮样式 */
.wx-nickname-btn {
  background: transparent;
  padding: 0;
  margin: 0 0 0 10rpx;
  line-height: normal;
  border: none;
  display: inline-block;
  
  &::after {
    border: none;
  }
}

/* 昵称弹窗样式 */
/* 昵称弹窗 - 简约风格 */
.nickname-popup-container {
  width: 580rpx;
  padding: 40rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  animation: fadeIn 0.3s ease-out;
}

/* 标题区 */
.nickname-popup-container .popup-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.nickname-popup-container .popup-header .popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 输入区 */
.nickname-popup-container .input-container {
  margin-bottom: 30rpx;
}

.nickname-popup-container .input-container .input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 4rpx 16rpx;
  margin-bottom: 12rpx;
}

.nickname-popup-container .input-container .input-wrapper .input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.nickname-popup-container .input-container .input-wrapper .nickname-edit-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}

.nickname-popup-container .input-container .input-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

/* 按钮区 */
.nickname-popup-container .action-btns {
  margin-top: 20rpx;
}

.nickname-popup-container .action-btns .cancel-btn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  color: #07c160;
  background: #f8f8f8;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.nickname-popup-container .action-btns .cancel-btn:active {
  background-color: #f2f2f2;
}

.popup-buttons {
  display: flex;
  border-top: 1px solid #f5f5f5;
  
  .popup-btn {
    flex: 1;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    font-size: 32rpx;
    
    &:active {
      background-color: #f9f9f9;
    }
  }
  
  .cancel-btn {
    color: #666;
    border-right: 1px solid #f5f5f5;
  }
  
  .confirm-btn {
    color: #07c160;
    font-weight: 500;
  }
}

/* 动画效果 */
@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  70% {
    transform: scale(1.03);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 简单的淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 日期选择器样式 */
.birthday-picker-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}

.birthday-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.date-picker-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 12rpx;
  border-top-right-radius: 12rpx;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10000;
  animation: slideUp 0.3s ease-out forwards;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  height: 88rpx;
  font-size: 32rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}

.date-picker-cancel {
  color: #888;
  font-size: 28rpx;
}

.date-picker-title {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}

.date-picker-confirm {
  color: #07c160;
  font-weight: 500;
  font-size: 28rpx;
}

.date-picker-view {
  width: 100%;
  height: 400rpx;
  text-align: center;
}

.picker-item {
  line-height: 50px;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 修正后新增的样式 */
.picker-view-container {
  width: 100%;
  height: 300rpx;
  background-color: #fff;
  margin-top: 10rpx;
}

.date-picker-view {
  width: 100%;
  height: 100%;
  text-align: center;
}

.picker-item {
  line-height: 50px;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}
</style> 