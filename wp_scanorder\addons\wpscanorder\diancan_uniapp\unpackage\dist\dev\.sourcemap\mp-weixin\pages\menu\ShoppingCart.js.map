{"version": 3, "sources": ["webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/ShoppingCart.vue?943d", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/ShoppingCart.vue?1889", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/ShoppingCart.vue?e3d4", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/ShoppingCart.vue?f12f", "uni-app:///pages/menu/ShoppingCart.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/ShoppingCart.vue?7c54", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/ShoppingCart.vue?4ac7"], "names": ["props", "cartList", "type", "default", "totalPrice", "methods", "handleClose", "handleClear", "uni", "title", "content", "success", "list", "total", "price", "icon", "decreaseQuantity", "item", "increaseQuantity", "updateCart", "handleCheckout", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClCA;AAAA;AAAA;AAAA;AAAqqB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCgFzrB;EACAA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEAC;QACAC;QACAC;QACAC;UACA;YACA;YACA;cACAC;cACAC;cACAC;YACA;;YAEA;YACAN;;YAEA;YACAA;cACAC;cACAM;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;;QAEA;QACAR;UACAC;UACAM;QACA;;QAEA;QACA;UACA;QACA;MACA;QACA;QACAE;QACAA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;;MAEA;MACAD;MACAA;;MAEA;MACA;IACA;IAEA;IACAE;MACA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;;MAEA;MACA;QACAP;QACAC;QACAC;MACA;IACA;IAEA;IACAM;MACA;QACAZ;UACAC;UACAM;QACA;QACA;MACA;;MAEA;MACAP;QACAI;QACAC;UAAA;QAAA;QACAC;MACA;;MAEA;MACA;;MAEA;MACAN;QACAa;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7MA;AAAA;AAAA;AAAA;AAAwxC,CAAgB,6nCAAG,EAAC,C;;;;;;;;;;;ACA5yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/menu/ShoppingCart.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ShoppingCart.vue?vue&type=template&id=41ea784f&scoped=true&\"\nvar renderjs\nimport script from \"./ShoppingCart.vue?vue&type=script&lang=js&\"\nexport * from \"./ShoppingCart.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ShoppingCart.vue?vue&type=style&index=0&id=41ea784f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"41ea784f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/menu/ShoppingCart.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ShoppingCart.vue?vue&type=template&id=41ea784f&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.cartList.length\n  var g1 = _vm.cartList.length\n  var g2 = _vm.cartList.length\n  var l0 = !(g2 === 0)\n    ? _vm.__map(_vm.cartList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g3 = item.specs && item.specs.trim()\n        return {\n          $orig: $orig,\n          g3: g3,\n        }\n      })\n    : null\n  var g4 = _vm.totalPrice.toFixed(2)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ShoppingCart.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ShoppingCart.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"cart-popup\" @click.stop>\r\n\t\t<view class=\"mask\" @click=\"handleClose\"></view>\r\n\t\t<view class=\"content\" @click.stop>\r\n\t\t\t<!-- 顶部标题栏 -->\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<!-- 清空按钮 -->\r\n\t\t\t\t<view class=\"clear-btn\" @click=\"handleClear\" v-if=\"cartList.length > 0\">\r\n\t\t\t\t\t<text>清空</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"title\">购物车</text>\r\n\t\t\t\t<view class=\"close-btn\" @click=\"handleClose\">\r\n\t\t\t\t\t<text class=\"close-icon\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 购物车列表 -->\r\n\t\t\t<scroll-view \r\n\t\t\t\tscroll-y \r\n\t\t\t\tclass=\"cart-list\"\r\n\t\t\t\t:style=\"{ maxHeight: cartList.length > 0 ? '60vh' : '30vh' }\"\r\n\t\t\t>\r\n\t\t\t\t<view v-if=\"cartList.length === 0\" class=\"empty-cart\">\r\n\t\t\t\t\t<text class=\"empty-text\">购物车是空的</text>\r\n\t\t\t\t\t<text class=\"empty-tip\">快去选购喜欢的商品吧~</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-else\r\n\t\t\t\t\tclass=\"cart-item\"\r\n\t\t\t\t\tv-for=\"(item, index) in cartList\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t<image class=\"item-image\" :src=\"item.image\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t<view class=\"item-details\">\r\n\t\t\t\t\t\t\t<text class=\"item-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t<view class=\"item-spec\" v-if=\"item.specs && item.specs.trim()\">{{item.specs}}</view>\r\n\t\t\t\t\t\t\t<view class=\"item-price\">\r\n\t\t\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\r\n\t\t\t\t\t\t\t\t<text class=\"price-value\">{{item.price}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"quantity-control\">\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"quantity-btn minus-btn\"\r\n\t\t\t\t\t\t\t:class=\"{'delete-state': item.count === 1}\"\r\n\t\t\t\t\t\t\t@click=\"decreaseQuantity(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text>-</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"number\">{{item.count}}</text>\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"quantity-btn plus-btn\"\r\n\t\t\t\t\t\t\t@click=\"increaseQuantity(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text>+</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t\r\n\t\t\t<!-- 底部结算栏 -->\r\n\t\t\t<view class=\"footer\">\r\n\t\t\t\t<view class=\"total-info\">\r\n\t\t\t\t\t<text class=\"total-label\">合计:</text>\r\n\t\t\t\t\t<view class=\"total-price\">\r\n\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\r\n\t\t\t\t\t\t<text class=\"price-value\">{{totalPrice.toFixed(2)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"checkout-btn\" @click=\"handleCheckout\">\r\n\t\t\t\t\t<text>去结算</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tprops: {\r\n\t\tcartList: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: () => []\r\n\t\t},\r\n\t\ttotalPrice: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 0\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 关闭购物车弹窗\r\n\t\thandleClose() {\r\n\t\t\tthis.$emit('close')\r\n\t\t},\r\n\t\t\r\n\t\t// 清空购物车 (保留方法但不在界面显示按钮)\r\n\t\thandleClear() {\r\n\t\t\tif (this.cartList.length === 0) return\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确定要清空购物车吗？',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t// 清空购物车\r\n\t\t\t\t\t\tthis.$emit('update', {\r\n\t\t\t\t\t\t\tlist: [],\r\n\t\t\t\t\t\t\ttotal: 0,\r\n\t\t\t\t\t\t\tprice: 0\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 发送清空购物车事件\r\n\t\t\t\t\t\tuni.$emit('clearCart')\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 显示提示\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '购物车已清空',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 减少商品数量\r\n\t\tdecreaseQuantity(index) {\r\n\t\t\tconst item = this.cartList[index]\r\n\t\t\tif (item.count <= 1) {\r\n\t\t\t\t// 数量为1时，删除该商品\r\n\t\t\t\tthis.cartList.splice(index, 1)\r\n\t\t\t\t\r\n\t\t\t\t// 显示删除提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已删除商品',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t// 如果购物车为空，关闭弹窗\r\n\t\t\t\tif (this.cartList.length === 0) {\r\n\t\t\t\t\tthis.$emit('close')\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 减少数量\r\n\t\t\t\titem.count -= 1\r\n\t\t\t\titem.totalPrice = Number(item.price) * item.count\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 更新购物车\r\n\t\t\tthis.updateCart()\r\n\t\t},\r\n\t\t\r\n\t\t// 增加商品数量\r\n\t\tincreaseQuantity(index) {\r\n\t\t\tconst item = this.cartList[index]\r\n\t\t\t\r\n\t\t\t// 增加数量\r\n\t\t\titem.count += 1\r\n\t\t\titem.totalPrice = Number(item.price) * item.count\r\n\t\t\t\r\n\t\t\t// 更新购物车\r\n\t\t\tthis.updateCart()\r\n\t\t},\r\n\t\t\r\n\t\t// 更新购物车\r\n\t\tupdateCart() {\r\n\t\t\t// 计算总数和总价\r\n\t\t\tconst total = this.cartList.reduce((sum, item) => sum + item.count, 0)\r\n\t\t\tconst price = this.cartList.reduce((sum, item) => sum + item.totalPrice, 0)\r\n\t\t\t\r\n\t\t\t// 更新购物车数据\r\n\t\t\tthis.$emit('update', {\r\n\t\t\t\tlist: this.cartList,\r\n\t\t\t\ttotal,\r\n\t\t\t\tprice\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 去结算\r\n\t\thandleCheckout() {\r\n\t\t\tif (this.cartList.length === 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '购物车为空',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 保存购物车数据\r\n\t\t\tuni.setStorageSync('cartData', {\r\n\t\t\t\tlist: this.cartList,\r\n\t\t\t\ttotal: this.cartList.reduce((sum, item) => sum + item.count, 0),\r\n\t\t\t\tprice: this.totalPrice\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t// 关闭购物车弹窗\r\n\t\t\tthis.handleClose()\r\n\t\t\t\r\n\t\t\t// 跳转到订单提交页面\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/order/orderSubmit'\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cart-popup {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tz-index: 999;\r\n\t\r\n\t.mask {\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\tanimation: fadeIn 0.3s ease-out;\r\n\t}\r\n\t\r\n\t.content {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 1000;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 32rpx 32rpx 0 0;\r\n\t\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\tbox-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);\r\n\t\tanimation: slideUp 0.3s ease-out;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\t\r\n\t\t.header {\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 30rpx 40rpx;\r\n\t\t\tborder-bottom: 1rpx solid #f2f2f2;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbackground: linear-gradient(to bottom, #f9f9f9, #ffffff);\r\n\t\t\tborder-radius: 32rpx 32rpx 0 0;\r\n\t\t\t\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\tright: 30rpx;\r\n\t\t\t\theight: 1rpx;\r\n\t\t\t\tbackground: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: -10rpx;\r\n\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground: linear-gradient(to right, #8cd548, #6ab52e);\r\n\t\t\t\t\tborder-radius: 2rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.clear-btn {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\r\n\t\t\t\t.clear-icon {\r\n\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t\topacity: 0.6;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\ttransition: all 0.2s;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tcolor: #ff5722;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.clear-icon {\r\n\t\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.close-btn {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 30rpx;\r\n\t\t\t\twidth: 60rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tbackground: rgba(0, 0, 0, 0.05);\r\n\t\t\t\ttransition: all 0.2s;\r\n\t\t\t\t\r\n\t\t\t\t.close-icon {\r\n\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tline-height: 1;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground: rgba(0, 0, 0, 0.1);\r\n\t\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.cart-list {\r\n\t\t\tpadding: 20rpx 20rpx;\r\n\t\t\t\r\n\t\t\t.empty-cart {\r\n\t\t\t\tpadding: 80rpx 0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\t\r\n\t\t\t\t.empty-icon {\r\n\t\t\t\t\twidth: 140rpx;\r\n\t\t\t\t\theight: 140rpx;\r\n\t\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\topacity: 0.5;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.empty-text {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.empty-tip {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.cart-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-around;\r\n\t\t\t\tpadding: 24rpx 0;\r\n\t\t\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\theight: 1rpx;\r\n\t\t\t\t\tbackground: linear-gradient(to right, rgba(0,0,0,0.01), rgba(0,0,0,0.03), rgba(0,0,0,0.01));\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.item-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmax-width: 55%;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tpadding-right: 10rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.item-image {\r\n\t\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t\t\t\tobject-fit: cover;\r\n\t\t\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\t\t\tborder: 2rpx solid #fff;\r\n\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.item-details {\r\n\t\t\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tmin-width: 0;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.item-name {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t\tmax-width: 100%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.item-spec {\r\n\t\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\tpadding: 2rpx 8rpx;\r\n\t\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\tmax-width: 100%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.item-price {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: baseline;\r\n\t\t\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.price-symbol {\r\n\t\t\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\t\t\tcolor: #ff5722;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.price-value {\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tcolor: #ff5722;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.quantity-control {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\tbackground: rgba(0, 0, 0, 0.02);\r\n\t\t\t\t\tborder-radius: 100rpx;\r\n\t\t\t\t\tpadding: 2rpx;\r\n\t\t\t\t\tmargin-left: auto;\r\n\t\t\t\t\twidth: 110rpx;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tmargin-right: 40rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.quantity-btn {\r\n\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\ttransition: all 0.2s;\r\n\t\t\t\t\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.minus-btn {\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 数量为1时，显示删除状态\r\n\t\t\t\t\t\t\t&.delete-state {\r\n\t\t\t\t\t\t\t\tbackground: #ffebee;\r\n\t\t\t\t\t\t\t\tborder: 1rpx solid #ffcdd2;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tcolor: #f44336;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\t\t\tbackground: #ffcdd2;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&.plus-btn {\r\n\t\t\t\t\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\t\tbackground: linear-gradient(135deg, #7bc53a 0%, #5aa41e 100%);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.number {\r\n\t\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tmin-width: 20rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.footer {\r\n\t\t\tpadding: 24rpx 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tborder-top: 1rpx solid #f2f2f2;\r\n\t\t\tbackground: #fff;\r\n\t\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t&::before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: 0;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\tright: 30rpx;\r\n\t\t\t\theight: 1rpx;\r\n\t\t\t\tbackground: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.total-info {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: baseline;\r\n\t\t\t\t\r\n\t\t\t\t.total-label {\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.total-price {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: baseline;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.price-symbol {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #ff5722;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.price-value {\r\n\t\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\t\tcolor: #ff5722;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.checkout-btn {\r\n\t\t\t\tpadding: 0 50rpx;\r\n\t\t\t\theight: 88rpx;\r\n\t\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\t\tborder-radius: 44rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbox-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);\r\n\t\t\t\tposition: relative;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t\r\n\t\t\t\t// 添加闪光效果\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tleft: -100%;\r\n\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tbackground: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);\r\n\t\t\t\t\ttransform: skewX(-25deg);\r\n\t\t\t\t\tanimation: shine 3s infinite;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tz-index: 2;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t\topacity: 0.9;\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #7bc53a 0%, #5aa41e 100%);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@keyframes fadeIn {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@keyframes slideUp {\r\n\tfrom {\r\n\t\ttransform: translateY(100%);\r\n\t}\r\n\tto {\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n@keyframes shine {\r\n\t0% {\r\n\t\tleft: -100%;\r\n\t}\r\n\t20% {\r\n\t\tleft: 100%;\r\n\t}\r\n\t100% {\r\n\t\tleft: 100%;\r\n\t}\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ShoppingCart.vue?vue&type=style&index=0&id=41ea784f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./ShoppingCart.vue?vue&type=style&index=0&id=41ea784f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948310078\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}