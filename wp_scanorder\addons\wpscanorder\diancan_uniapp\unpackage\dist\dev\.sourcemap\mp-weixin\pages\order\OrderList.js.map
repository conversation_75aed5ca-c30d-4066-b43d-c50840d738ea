{"version": 3, "sources": ["webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/OrderList.vue?6679", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/OrderList.vue?08eb", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/OrderList.vue?5db5", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/OrderList.vue?7521", "uni-app:///pages/order/OrderList.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/OrderList.vue?419e", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/OrderList.vue?9900"], "names": ["props", "orderList", "type", "default", "currentTab", "computed", "processedOrderList", "order", "products", "status", "orderNo", "createTime", "totalPrice", "methods", "getTotalCount", "getStatusText", "goToDetail", "uni", "url", "reorder", "title", "setTimeout", "checkOrder", "goToComment", "icon", "payOrder", "itemList", "success", "payType", "wxPay", "payResult", "fail", "console", "balancePay", "content", "confirmText", "cancelText", "checkPaymentResult", "query<PERSON><PERSON>ult", "cancelOrder", "res", "result"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqFtrB;AAAA;AAAA;AAAA,gBAEA;EACAA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EAEAE;IACAC;MACA;QACA,uCACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QAAA;MAEA;IACA;EACA;EAEAC;IACAC;MACA;QACA;MACA;MACA;QAAA;MAAA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;IAEAC;MACAC;QACAC;MACA;IACA;IAEAC;MACA;MACAF;QACAG;MACA;MAEAC;QACAJ;QACAA;UACAC;QACA;MACA;IACA;IAEAI;MACA;IACA;IAEA;IACAC;MACAN;QACAG;QACAI;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAf,yCAEA;gBACAO;kBACAS;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACAC;8BAEA;gCACA;gCACA;8BACA;gCACA;gCACA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAb;kBACAG;kBACAI;gBACA;gBAAA;cAAA;gBAIA;gBACAP,mDACAa;kBACAH;oBACA;kBACA;kBACAI;oBACAC;oBACAf;sBACAG;sBACAI;oBACA;kBACA;gBAAA,GACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAA;kBACAG;kBACAI;gBACA;gBACAQ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAhB;kBAAAG;gBAAA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAU;gBACAb;gBAEA;kBACAA;oBACAG;oBACAI;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;oBACA;oBACAP;sBACAG;sBACAc;sBACAC;sBACAC;sBACAT;wBACA;0BACA;0BACAV;4BACAC;0BACA;wBACA;sBACA;oBACA;kBACA;kBACAD;oBACAG;oBACAI;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAA;kBACAG;kBACAI;gBACA;gBACAQ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEApB;kBAAAG;gBAAA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAkB;gBACArB;gBAEA;kBACAA;oBACAG;oBACAI;kBACA;;kBAEA;kBACA;gBACA;kBACAP;oBACAG;oBACAI;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAP;gBACAA;kBACAG;kBACAI;gBACA;gBACAQ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MACA;MAEAtB;QACAG;QACAc;QACAP;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAa;sBAAA;sBAAA;oBAAA;oBAAA;oBAEAvB;sBAAAG;oBAAA;oBAAA;oBAAA,OAEA;kBAAA;oBAAAqB;oBACAxB;oBAEA;sBACAA;wBACAG;wBACAI;sBACA;;sBAEA;sBACA;oBACA;sBACAP;wBACAG;wBACAI;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAP;oBACAA;sBACAG;sBACAI;oBACA;oBACAQ;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACtWA;AAAA;AAAA;AAAA;AAA6vC,CAAgB,kmCAAG,EAAC,C;;;;;;;;;;;ACAjxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/OrderList.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./OrderList.vue?vue&type=template&id=5e6bf282&\"\nvar renderjs\nimport script from \"./OrderList.vue?vue&type=script&lang=js&\"\nexport * from \"./OrderList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./OrderList.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/OrderList.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderList.vue?vue&type=template&id=5e6bf282&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.processedOrderList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = [\n      \"pending\",\n      \"paid\",\n      \"cooking\",\n      \"cooked\",\n      \"delivering\",\n      \"processing\",\n      \"completed\",\n      \"cancelled\",\n    ].includes(item.status)\n    var m0 = item.status_text || _vm.getStatusText(item.status)\n    var m1 = _vm.getTotalCount(item.products)\n    var g1 = [\"pending\", \"paid\"].includes(item.status)\n    return {\n      $orig: $orig,\n      g0: g0,\n      m0: m0,\n      m1: m1,\n      g1: g1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderList.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"order-list\">\n    <view \n      v-for=\"(item, index) in processedOrderList\" \n      :key=\"index\" \n      class=\"order-card\"\n      @tap=\"goToDetail(item)\"\n    >\n      <!-- 订单头部 -->\n      <view class=\"order-header\">\n        <view class=\"left\">\n          <text class=\"order-no\">订单号: {{item.order_no || item.orderNo}}</text>\n          <text class=\"order-time\">{{item.createtime_text || item.createTime}}</text>\n        </view>\n        <view class=\"status-tag\" :class=\"{\n          'status-pending': item.status === 'pending',\n          'status-paid': item.status === 'paid',\n          'status-cooking': item.status === 'cooking',\n          'status-cooked': item.status === 'cooked',\n          'status-delivering': item.status === 'delivering',\n          'status-processing': item.status === 'processing',\n          'status-completed': item.status === 'completed',\n          'status-cancelled': item.status === 'cancelled',\n          'status-default': !['pending', 'paid', 'cooking', 'cooked', 'delivering', 'processing', 'completed', 'cancelled'].includes(item.status)\n        }\">\n          {{item.status_text || getStatusText(item.status)}}\n        </view>\n      </view>\n\n      <!-- 商品列表 -->\n      <view class=\"product-list\">\n        <view class=\"product-item\" v-for=\"(product, pIndex) in item.products\" :key=\"pIndex\">\n          <view class=\"product-info\">\n          <text class=\"product-name\">{{product.name}}</text>\n            <text class=\"product-specs\" v-if=\"product.specs\">{{product.specs}}</text>\n          </view>\n          <view class=\"product-price\">\n            <text class=\"price\">¥{{product.price}}</text>\n            <text class=\"count\">×{{product.count}}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 订单底部 -->\n      <view class=\"order-footer\">\n        <view class=\"total\">\n          <text class=\"count-text\">共{{getTotalCount(item.products)}}件商品</text>\n          <text class=\"price\">实付 <text class=\"price-value\">¥{{item.final_price || item.totalPrice}}</text></text>\n        </view>\n        <view class=\"actions\">\n          <view \n            class=\"action-btn outline\" \n            @tap.stop=\"reorder(item)\"\n          >再来一单</view>\n          \n          <view \n            class=\"action-btn outline\" \n            v-if=\"['pending', 'paid'].includes(item.status)\" \n            @tap.stop=\"cancelOrder(item)\"\n          >取消订单</view>\n          \n          <view \n            class=\"action-btn primary\" \n            v-if=\"item.status === 'pending'\" \n            @tap.stop=\"payOrder(item)\"\n          >去支付</view>\n          \n          <view \n            class=\"action-btn primary\" \n            v-if=\"item.status === 'paid' || item.status === 'cooking' || item.status === 'cooked' || item.status === 'delivering'\" \n            @tap.stop=\"checkOrder(item)\"\n          >查看进度</view>\n          \n          <view \n            class=\"action-btn primary\" \n            v-if=\"item.status === 'completed'\" v-show=\"false\"\n            @tap.stop=\"goToComment(item)\"\n          >评价订单</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getWxPayParams, queryWxPayResult, payOrderWithBalance, cancelOrder } from '@/api/order';\n\nexport default {\n  props: {\n    orderList: {\n      type: Array,\n      default: () => []\n    },\n    currentTab: {\n      type: Number,\n      default: 0\n    }\n  },\n  \n  computed: {\n    processedOrderList() {\n        return this.orderList.map(order => {\n        return {\n          ...order,\n          products: order.products || [],\n          status:  order.status,\n          orderNo: order.orderNo || '',\n          createTime: order.createTime || '',\n          totalPrice: order.totalPrice || 0\n        };\n      });\n    }\n  },\n  \n  methods: {\n    getTotalCount(products) {\n      if (!products || !Array.isArray(products)) {\n        return 0;\n      }\n      return products.reduce((sum, item) => sum + (item.count || 0), 0);\n    },\n    \n    getStatusText(status) {\n      const statusMap = {\n        'pending': '待支付',\n        'paid': '已支付',\n        'cooking': '制作中',\n        'cooked': '制作完成',\n        'delivering': '配送中',\n        'processing': '进行中',\n        'completed': '已完成',\n        'cancelled': '已取消'\n      };\n      \n      return statusMap[status] || status || '未知状态';\n    },\n    \n    goToDetail(item) {\n      uni.navigateTo({\n        url: `/pages/order/detail?id=${item.order_no || item.orderNo}`\n      });\n    },\n    \n    reorder(item) {\n      // 实现再来一单功能\n      uni.showLoading({\n        title: '加载中...'\n      });\n      \n      setTimeout(() => {\n        uni.hideLoading();\n        uni.switchTab({\n          url: '/pages/menu/menu'\n        });\n      }, 1000);\n    },\n    \n    checkOrder(item) {\n      this.goToDetail(item);\n    },\n    \n    // 评价订单\n    goToComment(item) {\n      uni.showToast({\n        title: '评价功能开发中',\n        icon: 'none'\n      });\n      \n      // 可以跳转到评价页面，这里先用提示代替\n      // uni.navigateTo({\n      //   url: `/pages/order/comment?id=${item.order_no || item.orderNo}`\n      // });\n    },\n    \n    // 支付订单\n    async payOrder(item) {\n      const orderNo = item.order_no || item.orderNo;\n      \n      // 弹出支付方式选择\n      uni.showActionSheet({\n        itemList: ['微信支付', '余额支付'],\n        success: async (res) => {\n          const payType = res.tapIndex;\n          \n          if (payType === 0) {\n            // 微信支付\n            this.wxPay(orderNo);\n          } else if (payType === 1) {\n            // 余额支付\n            this.balancePay(orderNo);\n          }\n        }\n      });\n    },\n    \n    // 微信支付\n    async wxPay(orderNo) {\n      try {\n        // 获取微信支付参数\n        const payResult = await getWxPayParams(orderNo);\n        \n        if (payResult.code !== 1) {\n          uni.showToast({\n            title: payResult.msg || '获取支付参数失败',\n            icon: 'none'\n          });\n          return;\n        }\n        \n        // 调用微信支付\n        uni.requestPayment({\n          ...payResult.data,\n          success: () => {\n            this.checkPaymentResult(orderNo);\n          },\n          fail: (err) => {\n            console.log('支付失败', err);\n            uni.showToast({\n              title: '支付已取消',\n              icon: 'none'\n            });\n          }\n        });\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: '支付异常，请稍后再试',\n          icon: 'none'\n        });\n        console.error('支付出错', error);\n      }\n    },\n    \n    // 余额支付\n    async balancePay(orderNo) {\n      try {\n        uni.showLoading({ title: '处理中...' });\n        \n        // 调用余额支付API\n        const payResult = await payOrderWithBalance(orderNo);\n        uni.hideLoading();\n        \n        if (payResult.code === 1) {\n          uni.showToast({\n            title: '支付成功',\n            icon: 'none'\n          });\n          // 通知父组件刷新订单列表\n          this.$emit('refresh');\n        } else {\n          // 如果为2 跳转充值页面\n          if (payResult.code === 2) {\n            // 显示确认对话框，让用户选择是否跳转到充值页面\n            uni.showModal({\n              title: '余额不足',\n              content: '您的余额不足，是否前往充值？',\n              confirmText: '去充值',\n              cancelText: '取消',\n              success: (res) => {\n                if (res.confirm) {\n                  // 用户点击确认，跳转到充值页面\n                  uni.navigateTo({\n                    url: '/pages/my/recharge'\n                  });\n                }\n              }\n            });\n          }\n          uni.showToast({\n            title: payResult.msg || '余额不足',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: '支付失败，请稍后再试',\n          icon: 'none'\n        });\n        console.error('余额支付出错', error);\n      }\n    },\n    \n    // 检查支付结果\n    async checkPaymentResult(orderNo) {\n      try {\n        uni.showLoading({ title: '正在查询支付结果...' });\n        \n        // 查询支付结果\n        const queryResult = await queryWxPayResult(orderNo);\n        uni.hideLoading();\n        \n        if (queryResult.code === 1 && queryResult.data && queryResult.data.pay_status === 1) {\n          uni.showToast({\n            title: '支付成功',\n            icon: 'none'\n          });\n          \n          // 通知父组件刷新订单列表\n          this.$emit('refresh');\n        } else {\n          uni.showToast({\n            title: queryResult.msg || '支付结果查询失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        uni.hideLoading();\n        uni.showToast({\n          title: '查询支付结果失败',\n          icon: 'none'\n        });\n        console.error('查询支付结果出错', error);\n      }\n    },\n    \n    // 取消订单\n    cancelOrder(item) {\n      const orderNo = item.order_no || item.orderNo;\n      \n      uni.showModal({\n        title: '取消订单',\n        content: '确定要取消该订单吗？',\n        success: async (res) => {\n          if (res.confirm) {\n            try {\n              uni.showLoading({ title: '处理中...' });\n              \n              const result = await cancelOrder(orderNo);\n              uni.hideLoading();\n              \n              if (result.code === 1) {\n                uni.showToast({\n                  title: '订单已取消',\n                  icon: 'none'\n                });\n                \n                // 通知父组件刷新订单列表\n                this.$emit('refresh');\n              } else {\n                uni.showToast({\n                  title: result.msg || '取消失败，请稍后再试',\n                  icon: 'none'\n                });\n              }\n            } catch (error) {\n              uni.hideLoading();\n              uni.showToast({\n                title: '取消订单失败',\n                icon: 'none'\n              });\n              console.error('取消订单出错', error);\n            }\n          }\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n@import '@/styles/theme.scss';\n\n.order-list {\n  padding: 0 20rpx;\n  \n  .order-card {\n    margin-bottom: 30rpx;\n    background-color: #fff;\n    border-radius: 16rpx;\n    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\n    overflow: hidden;\n  }\n  \n  .order-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 30rpx;\n    border-bottom: 1rpx solid #f5f5f5;\n    \n    .left {\n      display: flex;\n      flex-direction: column;\n      \n      .order-no {\n        font-size: 28rpx;\n        color: #333;\n        font-weight: 500;\n        margin-bottom: 8rpx;\n      }\n      \n      .order-time {\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n    \n    .status-tag {\n      padding: 6rpx 16rpx;\n      border-radius: 24rpx;\n      font-size: 24rpx;\n      font-weight: 500;\n      \n      &.status-pending {\n        background-color: #fff8e6;\n        color: #ff9500;\n      }\n      \n      &.status-paid {\n        background-color: #e6edff;\n        color: #4c84ff;\n      }\n      \n      &.status-cooking {\n        background-color: #ffebeb;\n        color: #ff6b6b;\n      }\n      \n      &.status-cooked {\n        background-color: #fff0e0;\n        color: #ffa64d;\n      }\n      \n      &.status-delivering {\n        background-color: #e8efff;\n        color: #5e7ce0;\n      }\n      \n      &.status-processing {\n        background-color: #e6f7ff;\n        color: #0076ff;\n      }\n      \n      &.status-completed {\n        background-color: #e6fff0;\n        color: #52c41a;\n      }\n      \n      &.status-cancelled {\n        background-color: #f5f5f5;\n        color: #999;\n      }\n      \n      &.status-default {\n        background-color: #f5f5f5;\n        color: #666;\n      }\n    }\n  }\n  \n  .product-list {\n    padding: 20rpx 30rpx;\n    \n    .product-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      padding: 16rpx 0;\n      \n      &:not(:last-child) {\n        border-bottom: 1rpx solid #f5f5f5;\n      }\n      \n      .product-info {\n        flex: 1;\n        margin-right: 20rpx;\n        \n        .product-name {\n          font-size: 28rpx;\n          color: #333;\n          margin-bottom: 4rpx;\n      }\n      \n      .product-specs {\n          font-size: 24rpx;\n          color: #999;\n          display: block;\n          margin-top: 6rpx;\n        }\n      }\n      \n      .product-price {\n        display: flex;\n        align-items: center;\n        \n        .price {\n          font-size: 28rpx;\n          color: #333;\n          font-weight: 500;\n        }\n        \n        .count {\n          margin-left: 12rpx;\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n    }\n  }\n  \n  .order-footer {\n    padding: 24rpx 30rpx;\n    \n    .total {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 24rpx;\n      \n      .count-text {\n        font-size: 24rpx;\n        color: #666;\n      }\n      \n      .price {\n        font-size: 24rpx;\n        color: #333;\n        \n        .price-value {\n          font-size: 32rpx;\n        font-weight: bold;\n          color: #ff4d4f;\n        }\n      }\n    }\n    \n    .actions {\n      display: flex;\n      justify-content: flex-end;\n      \n      .action-btn {\n        padding: 12rpx 24rpx;\n        border-radius: 30rpx;\n        font-size: 26rpx;\n        margin-left: 20rpx;\n        \n        &.outline {\n          border: 1rpx solid #ddd;\n          color: #666;\n        }\n        \n        &.primary {\n          background-color: #8cd548;\n          color: #fff;\n        }\n        \n        &:active {\n          opacity: 0.8;\n        }\n      }\n    }\n  }\n}\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderList.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./OrderList.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948310038\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}