<view class="login-container"><view data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" class="back-button" bindtap="__e"><image class="back-icon" src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png"></image></view><view class="merchant-name"><text>{{merchantInfo.name||'点餐小程序'}}</text></view><view class="logo-container"><image class="logo-image" src="{{merchantInfo.logo||'/static/my/default-avatar.png'}}" mode="aspectFill"></image></view><view class="merchant-title"><text>{{merchantInfo.name||'点餐小程序'}}</text></view><view class="benefit-text"><text>成为会员，立享更多优惠福利</text></view><view class="auth-desc"><text>授权绑定手机号 为您提供更好的服务</text></view><view class="login-btn-wrap"><button class="login-btn" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">微信一键登录</button></view><view class="agreement-wrap"><view data-event-opts="{{[['tap',[['toggleAgreement',['$event']]]]]}}" class="checkbox" bindtap="__e"><view class="{{['checkbox-inner',(isAgree)?'checked':'']}}"></view></view><text class="agreement-text">已阅读并同意</text><text data-event-opts="{{[['tap',[['viewPrivacyPolicy',['$event']]]]]}}" class="agreement-link" bindtap="__e">{{"《"+(merchantInfo.name||'点餐小程序')+"个人信息保护政策》"}}</text><text data-event-opts="{{[['tap',[['viewUserAgreement',['$event']]]]]}}" class="agreement-link" bindtap="__e">{{"《"+(merchantInfo.name||'点餐小程序')+"用户服务协议》"}}</text></view><view data-event-opts="{{[['tap',[['skipLogin',['$event']]]]]}}" class="skip-login" bindtap="__e"><text>暂不登录</text></view></view>