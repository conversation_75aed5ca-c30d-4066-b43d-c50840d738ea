<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">充值记录</text>
      </view>
    </view>

    <!-- 记录列表 -->
    <view class="record-list" v-if="recordList.length > 0">
      <view class="record-item" v-for="(item, index) in recordList" :key="index">
        <view class="left">
          <text class="amount">+¥{{item.amount}}</text>
          <text v-if="item.send_amount && Number(item.send_amount) > 0" class="gift">赠送¥{{item.send_amount}}</text>
          <text class="time">{{item.create_time_text || formatTime(item.createtime)}}</text>
        </view>
        <view class="right">
          <text class="status" :class="item.status === '1' ? 'success' : 'failed'">
            {{item.status_text || (item.status === '1' ? '充值成功' : '充值失败')}}
          </text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" v-if="recordList.length > 0 && hasMore">
      <text @tap="loadMore" v-if="!loading">点击加载更多</text>
      <text v-else>加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="recordList.length === 0 && !loading">
      <image class="empty-image" src="/static/order/e186e04e8774da64b58c96a8bb479840.png" />
      <text class="empty-text">暂无充值记录～</text>
    </view>

    <!-- 加载中 -->
    <view class="loading" v-if="loading && recordList.length === 0">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import { getRechargeList } from '@/api/recharge.js';

export default {
  data() {
    return {
      recordList: [],
      page: 1,
      limit: 10,
      total: 0,
      loading: false,
      hasMore: false
    }
  },

  onLoad() {
    this.loadRechargeRecords();
  },

  methods: {
    // 加载充值记录
    async loadRechargeRecords(isLoadMore = false) {
      if (this.loading) return;
      
      try {
        this.loading = true;
        
        if (!isLoadMore) {
          this.page = 1;
        }
        
        const res = await getRechargeList({
          page: this.page,
          limit: this.limit
        });
        
        if (res.code === 1 && res.data) {
          // 处理数据
          const list = res.data.list || [];
          this.total = res.data.total || 0;
          
          // 处理返回的数据，确保格式统一
          const processedList = list.map(item => {
            return {
              ...item,
              // 确保状态字段格式一致
              status: String(item.status),
              // 如果没有状态文本，根据状态生成
              status_text: item.status_text || (String(item.status) === '1' ? '充值成功' : '充值失败'),
              // 确保金额格式一致
              amount: item.amount ? parseFloat(item.amount).toFixed(2) : '0.00',
              send_amount: item.send_amount ? parseFloat(item.send_amount).toFixed(2) : '0.00',
              // 使用格式化后的时间或者格式化时间戳
              create_time_text: item.create_time_text || this.formatTime(item.createtime)
            };
          });
          
          if (isLoadMore) {
            // 加载更多时，追加数据
            this.recordList = [...this.recordList, ...processedList];
          } else {
            // 首次加载，直接赋值
            this.recordList = processedList;
          }
          
          // 判断是否还有更多数据
          this.hasMore = this.recordList.length < this.total;
        } else {
          uni.showToast({
            title: res.msg || '获取充值记录失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取充值记录失败:', error);
        uni.showToast({
          title: '获取充值记录失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++;
        this.loadRechargeRecords(true);
      }
    },
    
    // 格式化时间戳
    formatTime(timestamp) {
      if (!timestamp) return '--';
      
      const date = new Date(timestamp * 1000);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hour = String(date.getHours()).padStart(2, '0');
      const minute = String(date.getMinutes()).padStart(2, '0');
      const second = String(date.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    },
    
    handleBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
  }
}

.record-list {
  padding: 20rpx;
  
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .left {
      .amount {
        font-size: 36rpx;
        color: #333;
        font-weight: bold;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .gift {
        font-size: 24rpx;
        color: #8cd548;
        display: block;
        margin-bottom: 8rpx;
      }
      
      .time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .right {
      .status {
        font-size: 26rpx;
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
        
        &.success {
          color: #8cd548;
          background: rgba(140, 213, 72, 0.1);
        }
        
        &.failed {
          color: #ff4444;
          background: rgba(255, 68, 68, 0.1);
        }
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: 30rpx 0;
  
  text {
    font-size: 24rpx;
    color: #999;
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  
  text {
    font-size: 26rpx;
    color: #999;
  }
}

.empty-state {
  padding-top: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .empty-image {
    width: 400rpx;
    height: 300rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style> 