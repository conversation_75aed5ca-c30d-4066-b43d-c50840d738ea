@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-db675620 {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}
.header.data-v-db675620 {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-db675620 {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-db675620 {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-db675620 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.address-list.data-v-db675620 {
  padding: 20rpx;
}
.address-list .address-item.data-v-db675620 {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.address-list .address-item .info .user-info.data-v-db675620 {
  margin-bottom: 16rpx;
}
.address-list .address-item .info .user-info .name.data-v-db675620 {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
}
.address-list .address-item .info .user-info .phone.data-v-db675620 {
  font-size: 28rpx;
  color: #666;
}
.address-list .address-item .info .user-info .tag.data-v-db675620 {
  font-size: 22rpx;
  color: #8cd548;
  background: rgba(140, 213, 72, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
}
.address-list .address-item .info .address.data-v-db675620 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}
.address-list .address-item .actions.data-v-db675620 {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1px solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.address-list .address-item .actions .default.data-v-db675620 {
  display: flex;
  align-items: center;
}
.address-list .address-item .actions .default .radio.data-v-db675620 {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  margin-right: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.address-list .address-item .actions .default .radio.active.data-v-db675620 {
  border-color: #8cd548;
}
.address-list .address-item .actions .default .radio.active .inner.data-v-db675620 {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #8cd548;
}
.address-list .address-item .actions .default text.data-v-db675620 {
  font-size: 26rpx;
  color: #666;
}
.address-list .address-item .actions .edit-delete.data-v-db675620 {
  display: flex;
  align-items: center;
}
.address-list .address-item .actions .edit-delete .edit.data-v-db675620, .address-list .address-item .actions .edit-delete .delete.data-v-db675620 {
  display: flex;
  align-items: center;
  margin-left: 32rpx;
}
.address-list .address-item .actions .edit-delete .edit text.data-v-db675620, .address-list .address-item .actions .edit-delete .delete text.data-v-db675620 {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
}
.add-btn.data-v-db675620 {
  position: fixed;
  left: 40rpx;
  right: 40rpx;
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-btn text.data-v-db675620 {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.add-btn.data-v-db675620:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}

