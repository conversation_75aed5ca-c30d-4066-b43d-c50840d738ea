@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-28b3842c {
  min-height: 100vh;
  background: #f8f8f8;
}
.header.data-v-28b3842c {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-28b3842c {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-28b3842c {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-28b3842c {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.poster-content.data-v-28b3842c {
  padding: 30rpx;
}
.poster-content .poster-card.data-v-28b3842c {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.poster-content .poster-card .user-avatar.data-v-28b3842c {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}
.poster-content .poster-card .user-name.data-v-28b3842c {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 12rpx;
}
.poster-content .poster-card .invite-text.data-v-28b3842c {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}
.poster-content .poster-card .qr-code.data-v-28b3842c {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 20rpx;
}
.poster-content .poster-card .tip.data-v-28b3842c {
  font-size: 26rpx;
  color: #999;
}
.poster-content .share-btns.data-v-28b3842c {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
.poster-content .share-btns .btn-item.data-v-28b3842c {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 40rpx;
}
.poster-content .share-btns .btn-item text.data-v-28b3842c {
  font-size: 26rpx;
  color: #333;
  margin-top: 12rpx;
}
.poster-content .share-btns .btn-item.data-v-28b3842c:active {
  opacity: 0.8;
}
.poster-content .invite-rules.data-v-28b3842c {
  margin-top: 60rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
}
.poster-content .invite-rules .rule-title.data-v-28b3842c {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.poster-content .invite-rules .rule-item.data-v-28b3842c {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
}

