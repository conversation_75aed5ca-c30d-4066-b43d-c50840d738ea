{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?c918", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?78b0", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?eec8", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?9207", "uni-app:///pages/order/orderSubmit.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?f6ba", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?b2fa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "cartList", "totalPrice", "discountAmount", "finalPrice", "pickupType", "remark", "paymentMethod", "storeInfo", "name", "pickupTime", "selectedTime", "timeSlots", "showTimePicker", "tempSelectedTime", "showRemarkPopup", "tempRemark", "<PERSON><PERSON><PERSON><PERSON>", "userBalance", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSubmitting", "computed", "finalAmount", "onLoad", "uni", "console", "onUnload", "methods", "handleBack", "goToMenu", "url", "switchPickupType", "showTimeSelector", "closeTimeSelector", "selectImmediatePickup", "selectTime", "confirmTimeSelect", "title", "icon", "generateTimeSlots", "slots", "time", "desc", "formatTime", "selectCoupon", "animationType", "animationDuration", "calculateDiscount", "duration", "addRemark", "switchPayment", "selectAddress", "submitOrder", "item", "orderData", "products", "id", "price", "count", "specs", "image", "spec_type", "props_text", "diningType", "address", "phone", "payment_method", "coupon", "coupon_id", "amount", "res", "orderNo", "orderList", "newOrder", "order_no", "createTime", "status", "pay_status", "type", "dining_type", "payRes", "isSuccess", "content", "confirmText", "cancelText", "success", "setTimeout", "closeRemarkPopup", "confirmRemark", "getUserBalanceInfo", "formatAddress", "handleWxPay", "payParamsRes", "payParams", "provider", "timeStamp", "nonceStr", "package", "signType", "paySign", "queryRes", "isPaid", "fail", "complete", "handlePaymentSuccess", "getMerchantDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAoqB,CAAgB,qnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+RxrB;AACA;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;;IAEA;IACA;IACA;MACA;MACA;MACA;IACA;;IAEA;IACA;IACA;MACA;MACA;MACA;QACA;MACA;IACA;;IAEA;IACA;IACA;MACA;QACA;MACA;MACA;QACA;QACA;MACA;IACA;;IAEA;IACAC;MACA;IACA;;IAEA;IACAA;MACAC;MACA;MACA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAF;IACAA;EACA;EACAG;IACA;IACAC;MACAJ;IACA;IAEA;IACAK;MACA;MACAL;QACAnB;QACAC;QACAC;MACA;;MAEA;MACAiB;;MAEA;MACAA;QACAM;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;QACAZ;UACAa;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;;MAEA;MACA;QACA;QACA;;QAEA;QACA;UACAC;YACAC;YACAC;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACApB;QACAnB;QACAC;QACAC;QACAY;QACAF;MACA;;MAEA;MACAO;QACAM;QACAe;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;UACA;UACAvB;YACAa;YACAC;YACAU;UACA;UACA;UACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA3B;QACAnB;QACAC;QACAC;QACAI;QACAM;MACA;;MAEA;MACAO;QACAM;MACA;IACA;IAEA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBACA3B;gBACAD;kBACAa;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA,iCACAd;kBACAa;kBACAC;gBACA;cAAA;gBAGA;gBACA;kBACA;oBACAe;oBACA5B;kBACA;oBACA4B;oBACAA;oBACA5B;kBACA;gBACA;;gBAEA;gBACA6B;kBACAC;oBACA9B;oBACA;sBACA+B;sBACA/C;sBACAgD;sBACAC;sBACAxD;sBACAyD;sBACAC;sBACAC;sBACAC;oBACA;kBACA;kBACA5D;kBACAE;kBACAD;kBACA4D;kBACAzD;kBACA0D;oBACAvD;oBACAwD;oBACAD;kBACA;kBAAA;kBACAC;kBACAC;kBAAA;kBACAC;oBACAX;oBACAY;oBACAC;oBACA5D;kBACA;gBACA,GAEA;;gBACAgB;gBAAA;gBAAA,OACA;cAAA;gBAAA6C;gBACA7C;;gBAEA;;gBAGA;gBACA;kBACA8C;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;kBACA;kBACAA;gBACA;kBACA;kBACAA;kBACA9C;gBACA;;gBAEA;gBACAA;gBACAA;;gBAEA;gBACA+C;gBACAC,2CACAnB;kBACAoB;kBAAA;kBACAH;kBAAA;kBACAI;kBACAC;kBACAC;kBAAA;kBACAC;kBACAC;kBAAA;kBACAxB;oBAAA,uCACAF;sBACAM;oBAAA;kBAAA,CACA;gBAAA;gBAEAa;gBACAhD;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;kBACAa;gBACA;gBAAA;gBAGA;gBACAZ;gBAAA;gBAAA,OACA;cAAA;gBAAAuD;gBACAvD;;gBAEA;gBACAD;;gBAEA;gBACAyD;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;kBACA;kBACAzD;oBACAa;oBACA6C;oBACAC;oBACAC;oBACAC;sBACA;wBACA;wBACA7D;0BACAM;wBACA;sBACA;oBACA;kBACA;gBACA;gBACA;gBACAN;kBACAa;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;gBACAd;gBAEAC;gBACAD;kBACAa;kBACAC;gBACA;;gBAEA;gBACAgD;kBACA9D;oBACAM;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAL;gBACAD;kBACAa;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACAgD;kBACA;kBACA7D;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA8D;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACAhE;UACAa;UACAC;QACA;MACA;IACA;IAEA;IACAmD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAnB;gBACA7C;;gBAEA;gBACA;gBACA;kBACA;kBACA;oBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;gBACA;kBACAA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBAEAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAiE;MACA;;MAEA;MACA,cACA1B,wBACAA,oBACAA,uBACA;;MAEA;MACA;IACA;IACA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACAnE;;gBAEA;gBAAA,MACAmE;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAC,+BAEA;gBACArE;kBACAsE;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAd;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACA5D;;8BAEA;8BAAA;8BAEAD;gCACAa;8BACA;8BAAA;8BAAA,OAEA;4BAAA;8BAAA+D;8BAEA5E;;8BAEA;8BACA6E,gCACAD,kBACAA,+BACAA,+DACAA;8BAEA;gCACA;gCACA;8BACA;gCACA;gCACA3E;gCACA;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAD;8BACAC;8BACA;8BACA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;kBACA6E;oBACA7E;;oBAEA;oBACA;sBACAD;wBACAa;wBACAC;sBACA;oBACA;sBACAd;wBACAa;wBACAC;wBACAU;sBACA;oBACA;;oBAEA;oBACAsC;sBACA9D;wBACAM;sBACA;oBACA;kBACA;kBACAyE;oBACA;oBACA9E;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACAC;gBACAD;kBACAa;kBACAC;kBACAU;gBACA;;gBAEA;gBACAsC;kBACA9D;oBACAM;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACA0E;MACA;MACAhF;QACAa;QACAC;MACA;;MAEA;MACAmC;MACAA;MACAD;MACAhD;;MAEA;MACAA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACAA;;MAEA;MACA8D;QACA9D;UACAM;QACA;MACA;IACA;IACA;IACA2E;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAnC;gBACA7C;gBAEA;kBACA;kBACA;oBACAhB;oBACAuD;oBACAC;oBACAvD;kBACA;gBACA;kBACAe;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;kBACAhB;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACv+BA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,4nCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/orderSubmit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/orderSubmit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./orderSubmit.vue?vue&type=template&id=5c101a1c&scoped=true&\"\nvar renderjs\nimport script from \"./orderSubmit.vue?vue&type=script&lang=js&\"\nexport * from \"./orderSubmit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderSubmit.vue?vue&type=style&index=0&id=5c101a1c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5c101a1c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/orderSubmit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=template&id=5c101a1c&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.pickupType === \"takeout\" && _vm.selectedAddress\n      ? _vm.formatAddress(_vm.selectedAddress)\n      : null\n  var g0 = (_vm.userBalance || 0).toFixed(2)\n  var g1 = (_vm.totalPrice - _vm.discountAmount).toFixed(2)\n  var g2 = _vm.tempRemark.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<!-- 顶部导航栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"nav-bar\">\r\n\t\t\t\t<image \r\n\t\t\t\t\tclass=\"back-icon\" \r\n\t\t\t\t\tsrc=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n\t\t\t\t\t@tap=\"handleBack\"\r\n\t\t\t\t/>\r\n\t\t\t\t<text class=\"title\">确认订单</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主要内容区域 -->\r\n\t\t<scroll-view class=\"content\" scroll-y>\r\n\t\t\t<!-- 商家信息卡片 -->\r\n\t\t\t<view class=\"section-card store-section\">\r\n\t\t\t\t<view class=\"store-info\">\r\n\t\t\t\t\t<image class=\"store-icon\" src=\"/static/order_submit/7cf83e4f8b5fdfea873cd11d4b01d044.png\" />\r\n\t\t\t\t\t<view class=\"store-detail\">\r\n\t\t\t\t\t\t<text class=\"store-name\">{{storeInfo.name || '加载中...'}}</text>\r\n\t\t\t\t\t\t<text class=\"pickup-time\">{{storeInfo.pickupTime || '尽快取餐'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 取餐方式选择 -->\r\n\t\t\t\t<view class=\"pickup-type\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"type-item\" \r\n\t\t\t\t\t\t:class=\"{'active': pickupType === 'dine-in'}\"\r\n\t\t\t\t\t\t@tap=\"switchPickupType('dine-in')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<image src=\"/static/order_submit/bbdacb2185a84d001e76b2cfde875314.png\" />\r\n\t\t\t\t\t\t<view class=\"type-info\">\r\n\t\t\t\t\t\t\t<text class=\"type-name\">堂食</text>\r\n\t\t\t\t\t\t\t<text class=\"type-desc\">店内就餐</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"type-item\"\r\n\t\t\t\t\t\t:class=\"{'active': pickupType === 'takeout'}\"\r\n\t\t\t\t\t\t@tap=\"switchPickupType('takeout')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<image src=\"/static/order_submit/42647fbb92d04591f3631ba857a2035f.png\" />\r\n\t\t\t\t\t\t<view class=\"type-info\">\r\n\t\t\t\t\t\t\t<text class=\"type-name\">外带</text>\r\n\t\t\t\t\t\t\t<text class=\"type-desc\">打包带走</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 取餐时间选择 -->\r\n\t\t\t\t<view class=\"delivery-options\" v-if=\"pickupType === 'takeout'\">\r\n\t\t\t\t\t<!-- 取餐时间选择器 -->\r\n\t\t\t\t\t<view class=\"option-item\" @tap=\"showTimeSelector\">\r\n\t\t\t\t\t\t<view class=\"option-left\">\r\n\t\t\t\t\t\t\t<image src=\"/static/order_submit/time.png\" class=\"option-icon\" />\r\n\t\t\t\t\t\t\t<text class=\"option-label\">取餐时间</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"option-right\">\r\n\t\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t\t<text class=\"option-value\">{{ selectedTime ? selectedTime : '立即取餐' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#ccc\" size=\"24\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 收货地址选择 -->\r\n\t\t\t\t\t<view class=\"option-item\" @tap=\"selectAddress\">\r\n\t\t\t\t\t\t<view class=\"option-left\">\r\n\t\t\t\t\t\t\t<image src=\"/static/order_submit/address.png\" class=\"option-icon\" />\r\n\t\t\t\t\t\t\t<text class=\"option-label\">收货地址</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"option-right\">\r\n\t\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t\t<text class=\"option-value\" v-if=\"selectedAddress\">\r\n\t\t\t\t\t\t\t\t\t{{selectedAddress.name}} {{selectedAddress.phone}}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t<text class=\"option-placeholder\" v-else>请选择收货地址</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#ccc\" size=\"24\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"address-detail\" v-if=\"selectedAddress\">\r\n\t\t\t\t\t\t<text>{{formatAddress(selectedAddress)}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 订单商品列表 -->\r\n\t\t\t<view class=\"section-card\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<text class=\"section-title\">订单商品</text>\r\n\t\t\t\t\t<view class=\"add-btn\" @tap=\"goToMenu\">\r\n\t\t\t\t\t\t<text>继续点单</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"product-list\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"product-item\" \r\n\t\t\t\t\t\tv-for=\"(item, index) in cartList\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<image class=\"product-image\" :src=\"item.image\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t<text class=\"product-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"product-spec\" v-if=\"item.props_text\">{{item.props_text}}</text>\r\n\t\t\t\t\t\t\t<view class=\"price-info\">\r\n\t\t\t\t\t\t\t\t<text class=\"price\">¥{{item.price}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product-count\">\r\n\t\t\t\t\t\t\t<text class=\"count\">×{{item.count}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 优惠券和备注 -->\r\n\t\t\t<view class=\"section-card\">\r\n\t\t\t\t<view class=\"option-item\" @tap=\"selectCoupon\">\r\n\t\t\t\t\t<view class=\"option-left\">\r\n\t\t\t\t\t\t<image src=\"/static/order/coupon.png\" class=\"option-icon\" />\r\n\t\t\t\t\t\t<text class=\"option-label\">优惠券</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"option-right\">\r\n\t\t\t\t\t\t<text class=\"discount\" v-if=\"selectedCoupon\">-¥{{discountAmount}}</text>\r\n\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t<text class=\"option-value\" v-if=\"selectedCoupon\">{{selectedCoupon.name}}</text>\r\n\t\t\t\t\t\t\t<text class=\"option-placeholder\" v-else>未使用优惠券</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#ccc\" size=\"24\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"option-item\" @tap=\"addRemark\">\r\n\t\t\t\t\t<view class=\"option-left\">\r\n\t\t\t\t\t\t<image src=\"/static/order/remark.png\" class=\"option-icon\" />\r\n\t\t\t\t\t\t<text class=\"option-label\">备注</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"option-right\">\r\n\t\t\t\t\t\t<view class=\"value-container\">\r\n\t\t\t\t\t\t\t<text class=\"option-value\" v-if=\"remark\">{{remark}}</text>\r\n\t\t\t\t\t\t\t<text class=\"option-placeholder\" v-else>添加备注</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#ccc\" size=\"24\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 支付方式 -->\r\n\t\t\t<view class=\"section-card\">\r\n\t\t\t\t<view class=\"section-header\">\r\n\t\t\t\t\t<text class=\"section-title\">支付方式</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-list\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"payment-item\"\r\n\t\t\t\t\t\t:class=\"{'active': paymentMethod === 'wechat'}\"\r\n\t\t\t\t\t\t@tap=\"switchPayment('wechat')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"payment-info\">\r\n\t\t\t\t\t\t\t<image class=\"payment-icon\" src=\"/static/order/wx.png\" />\r\n\t\t\t\t\t\t\t<text class=\"payment-name\">微信支付</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :class=\"{'active': paymentMethod === 'wechat'}\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"payment-item\"\r\n\t\t\t\t\t\t:class=\"{'active': paymentMethod === 'balance'}\"\r\n\t\t\t\t\t\t@tap=\"switchPayment('balance')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"payment-info\">\r\n\t\t\t\t\t\t\t<image class=\"payment-icon\" src=\"/static/order/yuer.png\" />\r\n\t\t\t\t\t\t\t<text class=\"payment-name\">余额支付</text>\r\n\t\t\t\t\t\t\t<text class=\"balance-info\">当前余额: ¥{{(userBalance || 0).toFixed(2)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :class=\"{'active': paymentMethod === 'balance'}\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 底部空白区域，防止内容被底部支付栏遮挡 -->\r\n\t\t<view style=\"height: 180rpx;\"></view>\r\n\r\n\t\t<!-- 底部结算栏 -->\r\n\t\t<view class=\"bottom-bar\">\r\n\t\t\t<view class=\"price-info\">\r\n\t\t\t\t<view class=\"price-details\">\r\n\t\t\t\t\t<text class=\"total-text\">合计</text>\r\n\t\t\t\t\t<text class=\"total-price\">¥{{(totalPrice - discountAmount).toFixed(2)}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"discount-text\" v-if=\"discountAmount > 0\">(已优惠¥{{discountAmount}})</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"submit-btn\" :class=\"{'disabled': isSubmitting}\" @tap=\"submitOrder\">\r\n\t\t\t\t<text>{{isSubmitting ? '处理中...' : '立即支付'}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 时间选择弹窗 -->\r\n\t\t<u-popup \r\n\t\t\t:show=\"showTimePicker\"\r\n\t\t\t@close=\"closeTimeSelector\"\r\n\t\t\tmode=\"bottom\"\r\n\t\t\tborder-radius=\"20\"\r\n\t\t>\r\n\t\t\t<view class=\"time-selector\">\r\n\t\t\t\t<view class=\"selector-header\">\r\n\t\t\t\t\t<text class=\"title\">选择取餐时间</text>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closeTimeSelector\">\r\n\t\t\t\t\t\t<u-icon name=\"close\" color=\"#999\" size=\"32\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view class=\"time-list\" scroll-y>\r\n\t\t\t\t\t<!-- 立即取餐选项 -->\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"time-option immediate\"\r\n\t\t\t\t\t\t:class=\"{'active': tempSelectedTime === ''}\"\r\n\t\t\t\t\t\t@tap=\"selectImmediatePickup\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"time-content\">\r\n\t\t\t\t\t\t\t<text class=\"time\">立即取餐</text>\r\n\t\t\t\t\t\t\t<text class=\"desc\">预计15分钟内可取</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :class=\"{'active': tempSelectedTime === ''}\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 其他时间选项 -->\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"time-option\"\r\n\t\t\t\t\t\tv-for=\"(item, index) in timeSlots\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t:class=\"{'active': tempSelectedTime === item.time}\"\r\n\t\t\t\t\t\t@tap=\"selectTime(item)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"time-content\">\r\n\t\t\t\t\t\t\t<text class=\"time\">{{item.time}}</text>\r\n\t\t\t\t\t\t\t<text class=\"desc\">{{item.desc}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"radio\" :class=\"{'active': tempSelectedTime === item.time}\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t<view class=\"selector-footer\">\r\n\t\t\t\t\t<view class=\"confirm-btn\" @tap=\"confirmTimeSelect\">\r\n\t\t\t\t\t\t<text>确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\r\n\t\t<!-- 在底部添加备注弹窗 -->\r\n\t\t<u-popup \r\n\t\t\t:show=\"showRemarkPopup\"\r\n\t\t\t@close=\"closeRemarkPopup\"\r\n\t\t\tmode=\"bottom\"\r\n\t\t\tborder-radius=\"20\"\r\n\t\t>\r\n\t\t\t<view class=\"remark-editor\">\r\n\t\t\t\t<view class=\"editor-header\">\r\n\t\t\t\t\t<text class=\"title\">添加备注</text>\r\n\t\t\t\t\t<view class=\"close\" @tap=\"closeRemarkPopup\">\r\n\t\t\t\t\t\t<u-icon name=\"close\" color=\"#999\" size=\"32\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"editor-content\">\r\n\t\t\t\t\t<textarea\r\n\t\t\t\t\t\tv-model=\"tempRemark\"\r\n\t\t\t\t\t\tclass=\"remark-input\"\r\n\t\t\t\t\t\tplaceholder=\"请输入备注信息，比如：少糖、少冰等\"\r\n\t\t\t\t\t\t:maxlength=\"100\"\r\n\t\t\t\t\t\tauto-height\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<view class=\"word-count\">{{tempRemark.length}}/100</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"editor-footer\">\r\n\t\t\t\t\t<view class=\"confirm-btn\" @tap=\"confirmRemark\">\r\n\t\t\t\t\t\t<text>确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { addOrder, payOrderWithBalance, getWxPayParams, queryWxPayResult } from '@/api/order.js'\r\nimport { getUserBalance } from '@/api/user.js'\r\nimport { getDefaultAddress } from '@/api/address.js'\r\nimport { getMerchantInfo } from '@/api/merchant.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tstatusBarHeight: 0,\r\n\t\t\tcartList: [], // 购物车商品列表\r\n\t\t\ttotalPrice: 0, // 总价\r\n\t\t\tdiscountAmount: 0, // 优惠金额\r\n\t\t\tfinalPrice: 0, // 最终支付金额\r\n\t\t\tpickupType: 'dine-in', // 取餐方式：dine-in(堂食) / takeout(外带)\r\n\t\t\tremark: '', // 备注\r\n\t\t\tpaymentMethod: 'wechat', // 支付方式：wechat(微信) / balance(余额)\r\n\t\t\tstoreInfo: {\r\n\t\t\t\tname: '',\r\n\t\t\t\tpickupTime: '尽快取餐'\r\n\t\t\t},\r\n\t\t\tselectedTime: '', // 选中的预约时间\r\n\t\t\ttimeSlots: [], // 可选时间段列表\r\n\t\t\tshowTimePicker: false, // 控制时间选择弹窗显示\r\n\t\t\ttempSelectedTime: '', // 临时存储选中的时间\r\n\t\t\tshowRemarkPopup: false, // 控制备注弹窗显示\r\n\t\t\ttempRemark: '', // 临时存储备注信息\r\n\t\t\tselectedAddress: null, // 选中的收货地址\r\n\t\t\tuserBalance: 0, // 用户余额\r\n\t\t\tselectedCoupon: null, // 选中的优惠券\r\n\t\t\tisSubmitting: false, // 是否正在提交订单，防止重复点击\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\t// 计算最终支付金额\r\n\t\tfinalAmount() {\r\n\t\t\treturn (this.totalPrice - this.discountAmount).toFixed(2)\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 获取状态栏高度\r\n\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\tthis.statusBarHeight = systemInfo.statusBarHeight\r\n\t\t\r\n\t\t// 获取购物车数据\r\n\t\tconst cartData = uni.getStorageSync('cartData')\r\n\t\tif (cartData) {\r\n\t\t\tthis.cartList = cartData.list\r\n\t\t\tthis.totalPrice = cartData.price\r\n\t\t\tthis.finalPrice = cartData.price\r\n\t\t}\r\n\t\t\r\n\t\t// 获取用餐方式\r\n\t\tconst diningType = uni.getStorageSync('diningType')\r\n\t\tif (diningType) {\r\n\t\t\tthis.pickupType = diningType\r\n\t\t\t// 如果是外卖模式，生成时间段列表\r\n\t\t\tif (diningType === 'takeout') {\r\n\t\t\t\tthis.generateTimeSlots()\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 恢复暂存的订单数据\r\n\t\tconst tempOrderData = uni.getStorageSync('tempOrderData')\r\n\t\tif (tempOrderData) {\r\n\t\t\tif (tempOrderData.selectedAddress) {\r\n\t\t\t\tthis.selectedAddress = tempOrderData.selectedAddress\r\n\t\t\t}\r\n\t\t\tif (tempOrderData.selectedCoupon) {\r\n\t\t\t\tthis.selectedCoupon = tempOrderData.selectedCoupon\r\n\t\t\t\tthis.calculateDiscount()\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 监听地址选择结果\r\n\t\tuni.$on('addressSelected', (address) => {\r\n\t\t\tthis.selectedAddress = address;\r\n\t\t})\r\n\t\t\r\n\t\t// 监听优惠券选择结果\r\n\t\tuni.$on('couponSelected', (coupon) => {\r\n\t\t\tconsole.log('选择的优惠券:', coupon)\r\n\t\t\tthis.selectedCoupon = coupon\r\n\t\t\tthis.calculateDiscount()\r\n\t\t})\r\n\t\t\r\n\t\t// 获取用户余额信息\r\n\t\tthis.getUserBalanceInfo()\r\n\t\t\r\n\t\t// 获取商户信息\r\n\t\tthis.getMerchantDetail()\r\n\t},\r\n\tonUnload() {\r\n\t\t// 移除事件监听\r\n\t\tuni.$off('addressSelected');\r\n\t\tuni.$off('couponSelected');\r\n\t},\r\n\tmethods: {\r\n\t\t// 返回上一页\r\n\t\thandleBack() {\r\n\t\t\tuni.navigateBack()\r\n\t\t},\r\n\t\t\r\n\t\t// 继续点单\r\n\t\tgoToMenu() {\r\n\t\t\t// 保存当前订单信息到缓存\r\n\t\t\tuni.setStorageSync('tempOrderData', {\r\n\t\t\t\tpickupType: this.pickupType,\r\n\t\t\t\tremark: this.remark,\r\n\t\t\t\tpaymentMethod: this.paymentMethod\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t// 保存当前的用餐方式到菜单页面使用\r\n\t\t\tuni.setStorageSync('diningType', this.pickupType)\r\n\t\t\t\r\n\t\t\t// 跳转到点餐页面\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/menu/menu'\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 切换取餐方式\r\n\t\tswitchPickupType(type) {\r\n\t\t\tthis.pickupType = type\r\n\t\t\tif (type === 'takeout') {\r\n\t\t\t\tthis.generateTimeSlots()\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 显示时间选择器\r\n\t\tshowTimeSelector() {\r\n\t\t\tthis.generateTimeSlots()\r\n\t\t\tthis.tempSelectedTime = this.selectedTime\r\n\t\t\tthis.showTimePicker = true\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭时间选择器\r\n\t\tcloseTimeSelector() {\r\n\t\t\tthis.showTimePicker = false\r\n\t\t\tthis.tempSelectedTime = this.selectedTime\r\n\t\t},\r\n\t\t\r\n\t\t// 选择立即取餐\r\n\t\tselectImmediatePickup() {\r\n\t\t\tthis.tempSelectedTime = ''\r\n\t\t},\r\n\t\t\r\n\t\t// 选择时间\r\n\t\tselectTime(item) {\r\n\t\t\tthis.tempSelectedTime = item.time\r\n\t\t},\r\n\t\t\r\n\t\t// 确认时间选择\r\n\t\tconfirmTimeSelect() {\r\n\t\t\t// 如果是立即取餐，则清空selectedTime\r\n\t\t\tif (this.tempSelectedTime === '') {\r\n\t\t\t\tthis.selectedTime = ''\r\n\t\t\t} else if (this.tempSelectedTime) {\r\n\t\t\t\tthis.selectedTime = this.tempSelectedTime\r\n\t\t\t} else {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请选择取餐时间',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.showTimePicker = false\r\n\t\t},\r\n\t\t\r\n\t\t// 生成时间段列表\r\n\t\tgenerateTimeSlots() {\r\n\t\t\tconst slots = []\r\n\t\t\tconst now = new Date()\r\n\t\t\tconst startTime = new Date(now.setMinutes(now.getMinutes() + 15))\r\n\t\t\t\r\n\t\t\t// 生成从现在开始到晚上10点的时间段\r\n\t\t\tfor (let i = 0; i < 40; i++) {\r\n\t\t\t\tconst time = new Date(startTime.getTime() + i * 15 * 60000)\r\n\t\t\t\tconst hours = time.getHours()\r\n\t\t\t\t\r\n\t\t\t\t// 只显示营业时间内的时间段（假设营业时间到22:00）\r\n\t\t\t\tif (hours < 22) {\r\n\t\t\t\t\tslots.push({\r\n\t\t\t\t\t\ttime: this.formatTime(time),\r\n\t\t\t\t\t\tdesc: i === 0 ? '最早可取餐时间' : ''\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tthis.timeSlots = slots\r\n\t\t},\r\n\t\t\r\n\t\t// 格式化时间\r\n\t\tformatTime(date) {\r\n\t\t\tconst hours = date.getHours().toString().padStart(2, '0')\r\n\t\t\tconst minutes = date.getMinutes().toString().padStart(2, '0')\r\n\t\t\treturn `${hours}:${minutes}`\r\n\t\t},\r\n\t\t\r\n\t\t// 选择优惠券\r\n\t\tselectCoupon() {\r\n\t\t\t// 保存当前订单信息到缓存\r\n\t\t\tuni.setStorageSync('tempOrderData', {\r\n\t\t\t\tpickupType: this.pickupType,\r\n\t\t\t\tremark: this.remark,\r\n\t\t\t\tpaymentMethod: this.paymentMethod,\r\n\t\t\t\tselectedCoupon: this.selectedCoupon,\r\n\t\t\t\tselectedAddress: this.selectedAddress\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t// 跳转到优惠券选择页面，并传递订单金额\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/coupon/coupon?amount=${this.totalPrice}`,\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 计算优惠金额\r\n\t\tcalculateDiscount() {\r\n\t\t\tif (!this.selectedCoupon) {\r\n\t\t\t\tthis.discountAmount = 0\r\n\t\t\t} else {\r\n\t\t\t\t// 获取优惠券金额和限制金额\r\n\t\t\t\tconst couponAmount = parseFloat(this.selectedCoupon.amount || 0)\r\n\t\t\t\tconst couponLimit = parseFloat(this.selectedCoupon.limit || 0)\r\n\t\t\t\t\r\n\t\t\t\t// 检查订单金额是否满足优惠券使用条件\r\n\t\t\t\tif (this.totalPrice >= couponLimit) {\r\n\t\t\t\t\tthis.discountAmount = couponAmount\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果不满足条件，提示用户并清除优惠券选择\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: `订单金额需满${couponLimit}元才能使用此优惠券`,\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.selectedCoupon = null\r\n\t\t\t\t\tthis.discountAmount = 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 计算最终价格\r\n\t\t\tthis.finalPrice = Math.max(0, this.totalPrice - this.discountAmount).toFixed(2)\r\n\t\t},\r\n\t\t\r\n\t\t// 添加备注\r\n\t\taddRemark() {\r\n\t\t\tthis.showRemarkPopup = true\r\n\t\t},\r\n\t\t\r\n\t\t// 切换支付方式\r\n\t\tswitchPayment(method) {\r\n\t\t\tthis.paymentMethod = method\r\n\t\t},\r\n\t\t\r\n\t\t// 选择地址\r\n\t\tselectAddress() {\r\n\t\t\t// 保存当前订单信息到缓存\r\n\t\t\tuni.setStorageSync('tempOrderData', {\r\n\t\t\t\tpickupType: this.pickupType,\r\n\t\t\t\tremark: this.remark,\r\n\t\t\t\tpaymentMethod: this.paymentMethod,\r\n\t\t\t\tselectedTime: this.selectedTime,\r\n\t\t\t\tselectedAddress: this.selectedAddress\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t// 跳转到地址选择页面\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/address/address'\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 提交订单\r\n\t\tasync submitOrder() {\r\n\t\t\t// 防止重复提交订单\r\n\t\t\tif (this.isSubmitting) {\r\n\t\t\t\tconsole.log('订单正在提交中，请勿重复点击');\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请勿重复提交',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 设置提交状态为true\r\n\t\t\tthis.isSubmitting = true;\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\t// 如果是外带方式且未选择地址，提示选择地址\r\n\t\t\t\tif (this.pickupType === 'takeout' && !this.selectedAddress) {\r\n\t\t\t\t\tthis.isSubmitting = false; // 重置提交状态\r\n\t\t\t\t\treturn uni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择收货地址',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查并确保购物车中的商品都有规格信息\r\n\t\t\t\tthis.cartList.forEach(item => {\r\n\t\t\t\t\tif (!item.props_text && item.specs) {\r\n\t\t\t\t\t\titem.props_text = item.specs;\r\n\t\t\t\t\t\tconsole.log('提交前修复商品规格信息:', item.name, item.props_text);\r\n\t\t\t\t\t} else if (!item.props_text) {\r\n\t\t\t\t\t\titem.props_text = '默认规格';\r\n\t\t\t\t\t\titem.specs = '默认规格';\r\n\t\t\t\t\t\tconsole.log('提交前设置商品默认规格:', item.name);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 构建订单数据\r\n\t\t\t\tconst orderData = {\r\n\t\t\t\t\tproducts: this.cartList.map(item => {\r\n\t\t\t\t\t\tconsole.log('商品规格信息:', item.name, 'props_text:', item.props_text, 'specs:', item.specs);\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\tname: item.name,\r\n\t\t\t\t\t\t\tprice: item.price,\r\n\t\t\t\t\t\t\tcount: item.count,\r\n\t\t\t\t\t\t\ttotalPrice: item.totalPrice,\r\n\t\t\t\t\t\t\tspecs: item.props_text || item.specs || '默认规格',\r\n\t\t\t\t\t\t\timage: item.image || '',\r\n\t\t\t\t\t\t\tspec_type: item.spec_type || 'single',\r\n\t\t\t\t\t\t\tprops_text: item.props_text || item.specs || '默认规格'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}),\r\n\t\t\t\t\ttotalPrice: this.totalPrice,\r\n\t\t\t\t\tfinalPrice: this.finalPrice,\r\n\t\t\t\t\tdiscountAmount: this.discountAmount,\r\n\t\t\t\t\tdiningType: this.pickupType,\r\n\t\t\t\t\tremark: this.remark,\r\n\t\t\t\t\taddress: this.selectedAddress ? {\r\n\t\t\t\t\t\tname: this.selectedAddress.name || '',\r\n\t\t\t\t\t\tphone: this.selectedAddress.phone || '',\r\n\t\t\t\t\t\taddress: this.formatAddress(this.selectedAddress)\r\n\t\t\t\t\t} : null, // 添加地址信息\r\n\t\t\t\t\tphone: this.selectedAddress ? this.selectedAddress.phone : '',\r\n\t\t\t\t\tpayment_method: this.paymentMethod, // 添加支付方式\r\n\t\t\t\t\tcoupon: this.selectedCoupon ? {\r\n\t\t\t\t\t\tid: this.selectedCoupon.id,\r\n\t\t\t\t\t\tcoupon_id: this.selectedCoupon.coupon_id || this.selectedCoupon.id,\r\n\t\t\t\t\t\tamount: this.selectedCoupon.amount,\r\n\t\t\t\t\t\tname: this.selectedCoupon.name\r\n\t\t\t\t\t} : null // 添加优惠券信息\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 调用添加订单接口\r\n\t\t\t\tconsole.log('提交订单数据:', JSON.stringify(orderData));\r\n\t\t\t\tconst res = await addOrder(orderData);\r\n\t\t\t\tconsole.log('订单提交响应:', JSON.stringify(res));\r\n\t\t\t\t\r\n\t\t\t\t// 检查响应中是否包含订单号\r\n\t\t\t\tlet orderNo;\r\n\t\t\t\t\r\n\t\t\t\t// 尝试从不同位置获取订单号\r\n\t\t\t\tif (res.data && res.data.order_no) {\r\n\t\t\t\t\torderNo = res.data.order_no;\r\n\t\t\t\t} else if (res.data && res.data.orderNo) {\r\n\t\t\t\t\torderNo = res.data.orderNo;\r\n\t\t\t\t} else if (res.order_no) {\r\n\t\t\t\t\torderNo = res.order_no;\r\n\t\t\t\t} else if (res.orderNo) {\r\n\t\t\t\t\torderNo = res.orderNo;\r\n\t\t\t\t} else if (res.data && res.data.id) {\r\n\t\t\t\t\t// 有些API可能使用id作为订单号\r\n\t\t\t\t\torderNo = res.data.id;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果找不到订单号，使用时间戳作为临时订单号\r\n\t\t\t\t\torderNo = 'temp_' + new Date().getTime();\r\n\t\t\t\t\tconsole.warn('未找到有效订单号，使用临时ID:', orderNo);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取正确的订单号\r\n\t\t\t\tconsole.log('最终使用的订单号:', orderNo);\r\n\t\t\t\tconsole.log('获取到的订单号:', orderNo);\r\n\t\t\t\t\r\n\t\t\t\t// 保存订单到本地存储，用于订单详情页显示\r\n\t\t\t\tconst orderList = uni.getStorageSync('orderList') || [];\r\n\t\t\t\tconst newOrder = {\r\n\t\t\t\t\t...orderData,\r\n\t\t\t\t\torder_no: orderNo, // 使用后端API返回的订单号\r\n\t\t\t\t\torderNo: orderNo, // 保留兼容性\r\n\t\t\t\t\tcreateTime: new Date().toLocaleString(),\r\n\t\t\t\t\tstatus: 'processing',\r\n\t\t\t\t\tpay_status: 0, // 初始支付状态为未支付\r\n\t\t\t\t\ttype: this.pickupType,\r\n\t\t\t\t\tdining_type: this.pickupType, // 添加与API一致的字段\r\n\t\t\t\t\tproducts: orderData.products.map(item => ({\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tspecs: item.props_text || ''\r\n\t\t\t\t\t}))\r\n\t\t\t\t};\r\n\t\t\t\torderList.unshift(newOrder);\r\n\t\t\t\tuni.setStorageSync('orderList', orderList);\r\n\t\t\t\t\r\n\t\t\t\t// 如果用户选择了余额支付，调用余额支付API\r\n\t\t\t\tif (this.paymentMethod === 'balance') {\r\n\t\t\t\t\t// 显示加载提示\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '余额支付中...'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 调用余额支付API\r\n\t\t\t\t\t\tconsole.log('调用余额支付API，订单号:', orderNo);\r\n\t\t\t\t\t\tconst payRes = await payOrderWithBalance(orderNo);\r\n\t\t\t\t\t\tconsole.log('余额支付响应:', JSON.stringify(payRes));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 关闭加载提示\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 检查支付结果 - 支持多种响应格式\r\n\t\t\t\t\t\tconst isSuccess = payRes.code === 1;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tif (isSuccess) {\r\n\t\t\t\t\t\t\t// 支付成功\r\n\t\t\t\t\t\t\tthis.handlePaymentSuccess(newOrder, orderList);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 刷新用户余额\r\n\t\t\t\t\t\t\tawait this.getUserBalanceInfo();\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 如果为2 跳转充值页面\r\n\t\t\t\t\t\t\tif (payRes.code === 2) {\r\n\t\t\t\t\t\t\t\t// 显示确认对话框，让用户选择是否跳转到充值页面\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '余额不足',\r\n\t\t\t\t\t\t\t\t\tcontent: '您的余额不足，是否前往充值？',\r\n\t\t\t\t\t\t\t\t\tconfirmText: '去充值',\r\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t// 用户点击确认，跳转到充值页面\r\n\t\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: '/pages/my/recharge'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 支付失败\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: payRes.msg || payRes.message || '余额支付失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} catch (payError) {\r\n\t\t\t\t\t\t// 关闭加载提示\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.error('余额支付过程中出错:', payError);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: payError.message || payError.msg || '余额支付失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 出错后，仍然跳转到订单详情页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\turl: `/pages/order/detail?order_no=${orderNo}`\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 微信支付处理\r\n\t\t\t\t\tthis.handleWxPay(orderNo, newOrder, orderList);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('提交订单失败:', e);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: e.message || e.msg || '下单失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t} finally {\r\n\t\t\t\t// 无论成功或失败，都要重置提交状态\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.isSubmitting = false;\r\n\t\t\t\t\tconsole.log('重置订单提交状态');\r\n\t\t\t\t}, 2000); // 延迟2秒重置，防止快速连续点击\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭备注弹窗\r\n\t\tcloseRemarkPopup() {\r\n\t\t\tthis.showRemarkPopup = false\r\n\t\t},\r\n\t\t\r\n\t\t// 确认备注\r\n\t\tconfirmRemark() {\r\n\t\t\tif (this.tempRemark) {\r\n\t\t\t\tthis.remark = this.tempRemark\r\n\t\t\t\tthis.showRemarkPopup = false\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '备注已添加',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 添加获取用户余额的方法\r\n\t\tasync getUserBalanceInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await getUserBalance()\r\n\t\t\t\tconsole.log('获取余额返回数据:', JSON.stringify(res))\r\n\t\t\t\t\r\n\t\t\t\t// 根据接口返回格式解析余额数据\r\n\t\t\t\t// {\"code\":1,\"msg\":\"Get balance successful\",\"time\":\"1750841022\",\"data\":\"1000.00\"}\r\n\t\t\t\tif (res.code === 1 && res.data) {\r\n\t\t\t\t\t// data直接是余额字符串\r\n\t\t\t\t\tif (typeof res.data === 'string') {\r\n\t\t\t\t\t\tthis.userBalance = parseFloat(res.data)\r\n\t\t\t\t\t\tconsole.log('从字符串中解析余额:', this.userBalance)\r\n\t\t\t\t\t} else if (typeof res.data === 'number') {\r\n\t\t\t\t\t\tthis.userBalance = res.data\r\n\t\t\t\t\t\tconsole.log('获取到数字余额:', this.userBalance)\r\n\t\t\t\t\t} else if (typeof res.data === 'object' && res.data.balance) {\r\n\t\t\t\t\t\tthis.userBalance = parseFloat(res.data.balance)\r\n\t\t\t\t\t\tconsole.log('从对象中解析余额:', this.userBalance)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('API返回格式不符合预期:', res)\r\n\t\t\t\t\tthis.userBalance = 0\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 确保余额是有效数字\r\n\t\t\t\tif (isNaN(this.userBalance)) {\r\n\t\t\t\t\tthis.userBalance = 0\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('最终用户余额:', this.userBalance)\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取用户余额失败:', e)\r\n\t\t\t\tthis.userBalance = 0\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 格式化地址\r\n\t\tformatAddress(address) {\r\n\t\t\tif (!address) return '';\r\n\t\t\t\r\n\t\t\t// 构建省市区字符串\r\n\t\t\tconst region = [\r\n\t\t\t\taddress.province || '',\r\n\t\t\t\taddress.city || '',\r\n\t\t\t\taddress.district || ''\r\n\t\t\t].filter(Boolean).join(' ');\r\n\t\t\t\r\n\t\t\t// 添加详细地址\r\n\t\t\treturn `${region} ${address.address || ''}`.trim();\r\n\t\t},\r\n\t\t// 微信支付处理\r\n\t\tasync handleWxPay(orderNo, newOrder, orderList) {\r\n\t\t\ttry {\r\n\t\t\t\t// 调用获取微信支付参数的API\r\n\t\t\t\tconst payParamsRes = await getWxPayParams(orderNo);\r\n\t\t\t\tconsole.log('获取微信支付参数返回:', JSON.stringify(payParamsRes));\r\n\t\t\t\t\r\n\t\t\t\t// 检查API返回是否成功\r\n\t\t\t\tif (payParamsRes.code !== 1 || !payParamsRes.data) {\r\n\t\t\t\t\tthrow new Error(payParamsRes.msg || '获取支付参数失败');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 提取支付参数\r\n\t\t\t\tconst payParams = payParamsRes.data;\r\n\t\t\t\t\r\n\t\t\t\t// 发起微信支付\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\tprovider: 'wxpay',\r\n\t\t\t\t\ttimeStamp: payParams.timeStamp,\r\n\t\t\t\t\tnonceStr: payParams.nonceStr,\r\n\t\t\t\t\tpackage: payParams.package,\r\n\t\t\t\t\tsignType: payParams.signType,\r\n\t\t\t\t\tpaySign: payParams.paySign,\r\n\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\tconsole.log('微信支付成功:', JSON.stringify(res));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 查询支付结果（有些系统可能需要后端确认支付状态）\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle: '确认支付结果...'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tconst queryRes = await queryWxPayResult(orderNo);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 根据pay_status判断支付状态，0=未支付，1=已支付\r\n\t\t\t\t\t\t\tconst isPaid = queryRes.code === 1 && \r\n\t\t\t\t\t\t\t\t\t\t  queryRes.data && \r\n\t\t\t\t\t\t\t\t\t\t  (queryRes.data.paid === true || \r\n\t\t\t\t\t\t\t\t\t\t   (queryRes.data.order && queryRes.data.order.pay_status === 1) ||\r\n\t\t\t\t\t\t\t\t\t\t   queryRes.data.pay_status === 1);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tif (isPaid) {\r\n\t\t\t\t\t\t\t\t// 支付成功处理\r\n\t\t\t\t\t\t\t\tthis.handlePaymentSuccess(newOrder, orderList);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t// 虽然微信返回成功，但后端查询失败的情况\r\n\t\t\t\t\t\t\t\tconsole.warn('支付确认失败:', JSON.stringify(queryRes));\r\n\t\t\t\t\t\t\t\t// 仍然按支付成功处理，等待后端异步通知\r\n\t\t\t\t\t\t\t\tthis.handlePaymentSuccess(newOrder, orderList);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (queryError) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tconsole.error('查询支付结果失败:', queryError);\r\n\t\t\t\t\t\t\t// 仍然按支付成功处理，等待后端异步通知\r\n\t\t\t\t\t\t\tthis.handlePaymentSuccess(newOrder, orderList);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('微信支付失败:', JSON.stringify(err));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 处理用户取消支付的情况\r\n\t\t\t\t\t\tif (err.errMsg && err.errMsg.indexOf('cancel') > -1) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付已取消',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付失败: ' + (err.errMsg || '未知错误'),\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 支付失败后，仍然跳转到订单详情页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\turl: `/pages/order/detail?order_no=${orderNo}`\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t\t// 支付流程完成\r\n\t\t\t\t\t\tconsole.log('微信支付流程完成');\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} catch (e) {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('微信支付处理异常:', e);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: e.message || '支付失败，请稍后再试',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 异常情况下，也跳转到订单详情页\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: `/pages/order/detail?order_no=${orderNo}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 1500);\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 处理支付成功逻辑\r\n\t\thandlePaymentSuccess(newOrder, orderList) {\r\n\t\t\t// 显示支付成功提示\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '支付成功',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 更新订单状态\r\n\t\t\tnewOrder.status = 'paid';\r\n\t\t\tnewOrder.pay_status = 1; // 设置支付状态为已支付\r\n\t\t\torderList[0] = newOrder;\r\n\t\t\tuni.setStorageSync('orderList', orderList);\r\n\t\t\t\r\n\t\t\t// 清空购物车数据\r\n\t\t\tuni.removeStorageSync('cartData');\r\n\t\t\tuni.removeStorageSync('cartList');\r\n\t\t\tuni.removeStorageSync('cartTotal');\r\n\t\t\tuni.removeStorageSync('diningType');\r\n\t\t\tuni.removeStorageSync('tempOrderData');\r\n\t\t\t\r\n\t\t\t// 通知菜单页面清空购物车\r\n\t\t\tuni.$emit('clearCart');\r\n\t\t\t\r\n\t\t\t// 跳转到订单详情页\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\turl: `/pages/order/detail?order_no=${newOrder.order_no}`\r\n\t\t\t\t});\r\n\t\t\t}, 1500);\r\n\t\t},\r\n\t\t// 获取商户信息\r\n\t\tasync getMerchantDetail() {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await getMerchantInfo()\r\n\t\t\t\tconsole.log('获取商户信息返回:', JSON.stringify(res))\r\n\t\t\t\t\r\n\t\t\t\tif (res.code === 1 && res.data) {\r\n\t\t\t\t\t// 更新商户信息\r\n\t\t\t\t\tthis.storeInfo = {\r\n\t\t\t\t\t\tname: res.data.name || res.data.store_name || res.data.title || '',\r\n\t\t\t\t\t\taddress: res.data.address || '',\r\n\t\t\t\t\t\tphone: res.data.phone || res.data.tel || '',\r\n\t\t\t\t\t\tpickupTime: '尽快取餐'\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.warn('商户信息获取失败:', res)\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取商户信息异常:', e)\r\n\t\t\t\t// 设置默认值，防止页面显示异常\r\n\t\t\t\tthis.storeInfo = {\r\n\t\t\t\t\tname: 'KKmall京基店2楼188号',\r\n\t\t\t\t\tpickupTime: '尽快取餐'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f8f8;\r\n\twidth: 100%;\r\n\toverflow-x: hidden; /* 防止水平溢出 */\r\n\tbox-sizing: border-box;\r\n\t\r\n\t.header {\r\n\t\tbackground: #fff;\r\n\t\tpadding-top: 88rpx;\r\n\t\twidth: 100%;\r\n\t\t\r\n\t\t.nav-bar {\r\n\t\t\tposition: relative;\r\n\t\t\theight: 88rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\t\r\n\t\t\t.back-icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tpadding: 10rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.content {\r\n\t\tpadding: 20rpx;\r\n\t\tpadding-bottom: 180rpx;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\t\r\n\t\t.section-card {\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t\t\r\n\t\t.section-header {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\r\n\t\t\t.section-title {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.add-btn {\r\n\t\t\t\tpadding: 12rpx 24rpx;\r\n\t\t\t\tborder-radius: 100rpx;\r\n\t\t\t\tborder: 2rpx solid #8cd548;\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #8cd548;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.store-section {\r\n\t\t\tborder-top: 4rpx solid #fff;\r\n\t\t\t\r\n\t\t\t.store-info {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t.store-icon {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.store-detail {\r\n\t\t\t\t\t.store-name {\r\n\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.pickup-time {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.pickup-type {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tgap: 20rpx;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\r\n\t\t\t\t.type-item {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tpadding: 20rpx;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\tborder: 2rpx solid #eee;\r\n\t\t\t\t\ttransition: all 0.3s;\r\n\t\t\t\t\t\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.type-info {\r\n\t\t\t\t\t\t.type-name {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tmargin-bottom: 4rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.type-desc {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\tborder-color: #8cd548;\r\n\t\t\t\t\t\tbackground: rgba(140, 213, 72, 0.08);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.delivery-options {\r\n\t\t\t\t.option-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tpadding: 24rpx 0;\r\n\t\t\t\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.option-left {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.option-icon {\r\n\t\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.option-label {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.option-right {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tflex-shrink: 1;\r\n\t\t\t\t\t\tmin-width: 0; /* 确保可以缩小 */\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.discount {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #ff4444;\r\n\t\t\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.value-container {\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t\tmin-width: 0;\r\n\t\t\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.option-value {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.option-placeholder {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.address-detail {\r\n\t\t\t\t\tpadding: 16rpx 0 0 52rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tline-height: 1.5;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.product-list {\r\n\t\t\t.product-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&:not(:last-child) {\r\n\t\t\t\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.product-image {\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tflex-shrink: 0; /* 防止图片缩小 */\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.product-info {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\tpadding-right: 80rpx; /* 为数量留出更多空间 */\r\n\t\t\t\t\tmin-width: 0; /* 确保flex项可以正确缩小 */\r\n\t\t\t\t\t\r\n\t\t\t\t\t.product-name {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.product-spec {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.price-info {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.price {\r\n\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.product-count {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 0;\r\n\t\t\t\t\tbottom: 20rpx;\r\n\t\t\t\t\twidth: 60rpx; /* 固定宽度 */\r\n\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.count {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.option-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 24rpx 0;\r\n\t\t\t\r\n\t\t\t&:not(:last-child) {\r\n\t\t\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.option-left {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.option-icon {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.option-label {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.option-right {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tflex-shrink: 1;\r\n\t\t\t\tmin-width: 0; /* 确保可以缩小 */\r\n\t\t\t\t\r\n\t\t\t\t.discount {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #ff4444;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.value-container {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tmin-width: 0;\r\n\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.option-value {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.option-placeholder {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.payment-list {\r\n\t\t\t.payment-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\t\r\n\t\t\t\t&:not(:last-child) {\r\n\t\t\t\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.payment-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.payment-icon {\r\n\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\t\tmargin-right: 16rpx;\r\n\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.payment-name {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t\t\t\tflex-shrink: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.balance-info {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.radio {\r\n\t\t\t\t\twidth: 36rpx;\r\n\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\tborder: 2rpx solid #ddd;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\tborder-color: #8cd548;\r\n\t\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\twidth: 20rpx;\r\n\t\t\t\t\t\t\theight: 20rpx;\r\n\t\t\t\t\t\t\tbackground: #8cd548;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.active {\r\n\t\t\t\t\t.payment-name {\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.bottom-bar {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: #fff;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom));\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\t\r\n\t\t.price-info {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\t\r\n\t\t\t.price-details {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.total-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.total-price {\r\n\t\t\t\t\tfont-size: 38rpx;\r\n\t\t\t\t\tcolor: #ff4d4f;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.discount-text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.submit-btn {\r\n\t\t\tpadding: 0 60rpx;\r\n\t\t\theight: 88rpx;\r\n\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\tborder-radius: 44rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbox-shadow: 0 6rpx 12rpx rgba(140, 213, 72, 0.2);\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.98);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.disabled {\r\n\t\t\t\tbackground: #cccccc;\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t\tbox-shadow: none;\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.time-selector {\r\n\tbackground: #fff;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\r\n\t.selector-header {\r\n\t\tpadding: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\r\n\t\t.title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t\r\n\t\t.close {\r\n\t\t\tpadding: 10rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.time-list {\r\n\t\tmax-height: 600rpx;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\t\r\n\t\t.time-option {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #f8f8f8;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.time-content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t\r\n\t\t\t\t.time {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tmargin-bottom: 6rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.desc {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.radio {\r\n\t\t\t\twidth: 36rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\tborder: 2rpx solid #ddd;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tborder-color: #8cd548;\r\n\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&::after {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\twidth: 20rpx;\r\n\t\t\t\t\t\theight: 20rpx;\r\n\t\t\t\t\t\tbackground: #8cd548;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.active {\r\n\t\t\t\tbackground: rgba(140, 213, 72, 0.1);\r\n\t\t\t\t\r\n\t\t\t\t.time {\r\n\t\t\t\t\tcolor: #8cd548;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&.immediate {\r\n\t\t\t\tbackground: #e6f7d9;\r\n\t\t\t\tborder: 1rpx solid #8cd548;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.selector-footer {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-top: 1rpx solid #f5f5f5;\r\n\t\t\r\n\t\t.confirm-btn {\r\n\t\t\theight: 88rpx;\r\n\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\tborder-radius: 44rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.98);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.remark-editor {\r\n\tbackground: #fff;\r\n\tborder-radius: 20rpx 20rpx 0 0;\r\n\t\r\n\t.editor-header {\r\n\t\tpadding: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t\t\r\n\t\t.title {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t\r\n\t\t.close {\r\n\t\t\tpadding: 10rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.editor-content {\r\n\t\tpadding: 30rpx;\r\n\t\t\r\n\t\t.remark-input {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 200rpx;\r\n\t\t\tborder: 1rpx solid #ddd;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tline-height: 1.5;\r\n\t\t\tresize: none;\r\n\t\t}\r\n\t\t\r\n\t\t.word-count {\r\n\t\t\ttext-align: right;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tmargin-top: 10rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.editor-footer {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-top: 1rpx solid #f5f5f5;\r\n\t\t\r\n\t\t.confirm-btn {\r\n\t\t\theight: 88rpx;\r\n\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\tborder-radius: 44rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\t\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.98);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=style&index=0&id=5c101a1c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=style&index=0&id=5c101a1c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948310003\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}