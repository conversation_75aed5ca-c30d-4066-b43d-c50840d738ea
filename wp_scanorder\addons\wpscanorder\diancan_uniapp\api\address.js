import request from '@/utils/request.js';

/**
 * 获取地址列表
 */
export function getAddressList() {
  return request({
    url: '/address/lists',
    method: 'GET'
  });
}

/**
 * 获取默认地址
 */
export function getDefaultAddress() {
  return request({
    url: '/address/getDefault',
    method: 'GET'
  });
}

/**
 * 添加地址
 * @param {Object} data - 地址数据
 */
export function addAddress(data) {
  return request({
    url: '/address/add',
    method: 'POST',
    data
  });
}

/**
 * 编辑地址
 * @param {Object} data - 地址数据
 */
export function editAddress(data) {
  return request({
    url: '/address/edit',
    method: 'POST',
    data
  });
}

/**
 * 删除地址
 * @param {Number|String} id - 地址ID
 */
export function deleteAddress(id) {
  console.log('API - 删除地址，接收到ID:', id);
  
  // 确保id参数始终是字符串类型
  const safeId = String(id || '');
  
  if (!safeId) {
    console.error('删除地址失败: 传入的ID为空');
    return Promise.reject({ code: 0, msg: '无效的地址ID' });
  }
  
  return request({
    url: '/address/delete',
    method: 'POST',
    data: { id: safeId }
  });
}

/**
 * 设置默认地址
 * @param {Number|String} id - 地址ID
 */
export function setDefaultAddress(id) {
  console.log('API - 设置默认地址，接收到ID:', id);
  
  // 确保id参数始终是字符串类型
  const safeId = String(id || '');
  
  if (!safeId) {
    console.error('设置默认地址失败: 传入的ID为空');
    return Promise.reject({ code: 0, msg: '无效的地址ID' });
  }
  
  return request({
    url: '/address/setDefault',
    method: 'POST',
    data: { id: safeId }
  });
}

/**
 * 获取地址详情
 * @param {Number|String} id - 地址ID
 */
export function getAddressDetail(id) {
  console.log('API - 获取地址详情，接收到ID:', id);
  
  // 确保id参数始终是字符串类型
  const safeId = String(id || '');
  
  if (!safeId) {
    console.error('获取地址详情失败: 传入的ID为空');
    return Promise.reject({ code: 0, msg: '无效的地址ID' });
  }
  
  return request({
    url: '/address/detail',
    method: 'GET',
    params: { id: safeId }
  });
} 