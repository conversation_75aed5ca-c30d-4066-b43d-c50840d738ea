const baseUrl = 'http://fastadmin.cn/addons/wpscanorder'; // www.example.com 替换为你的实际域名 

// 导出baseUrl供其他文件使用
export { baseUrl };

const request = (options) => {
    return new Promise((resolve, reject) => {
        // 添加token到请求头
        const token = uni.getStorageSync('token');
        const header = {
            'content-type': options.contentType || 'application/json',
        };
        
        if (token) {
            header['token'] = token;
        } else {
            console.warn('请求未携带token，用户可能未登录');
        }
        
        // 完整URL
        const fullUrl = baseUrl + options.url;
        
        // 请求参数处理
        let requestData = options.method === 'GET' ? options.params : options.data;
        
        // 处理表单格式的请求
        if (options.contentType === 'application/x-www-form-urlencoded') {
            // 如果是表单格式，确保正确处理数据
            if (requestData && typeof requestData === 'object') {
                // 处理URL编码
                header['content-type'] = 'application/x-www-form-urlencoded';
                
                // 将对象转换为URL编码字符串
                const params = [];
                for (let key in requestData) {
                    if (requestData.hasOwnProperty(key) && requestData[key] !== undefined) {
                        params.push(encodeURIComponent(key) + '=' + encodeURIComponent(requestData[key]));
                    }
                }
                requestData = params.join('&');
            }
        }
        
        // 详细检查请求参数
        if (options.url.includes('delete') || options.url.includes('setDefault')) {
            console.log(`======== 特殊接口参数检查 ========`);
            console.log(`接口路径: ${options.url}`);
            console.log(`请求方法: ${options.method}`);
            console.log(`请求头: ${JSON.stringify(header)}`);
            console.log(`请求数据: ${JSON.stringify(requestData)}`);
            
            // 针对这些特殊接口，确保id参数存在并且正确格式化
            if (typeof requestData === 'object' && requestData !== null) {
                if (requestData.id === undefined || requestData.id === null || requestData.id === '') {
                    console.error(`特殊接口缺少ID参数`);
                } else {
                    console.log(`特殊接口ID参数存在: ${requestData.id}, 类型: ${typeof requestData.id}`);
                    // 确保id为字符串类型
                    if (typeof requestData.id !== 'string') {
                        requestData.id = String(requestData.id);
                        console.log(`已将ID转换为字符串: ${requestData.id}`);
                    }
                }
            } else {
                console.error(`特殊接口请求数据格式错误: ${JSON.stringify(requestData)}`);
            }
        }
        
        // 记录详细请求日志
        console.log(`=========== 请求开始 ===========`);
        console.log(`请求方法: ${options.method || 'GET'}`);
        console.log(`请求URL: ${fullUrl}`);
        console.log(`请求头: ${JSON.stringify(header)}`);
        console.log(`请求数据: ${typeof requestData === 'string' ? requestData : JSON.stringify(requestData)}`);
        console.log(`================================`);
        
        uni.request({
            url: fullUrl,
            method: options.method || 'GET',
            data: requestData,
            header: header,
            success: (res) => {
                // 记录响应日志
                console.log(`=========== 响应开始 ===========`);
                console.log(`响应URL: ${fullUrl}`);
                console.log(`响应状态码: ${res.statusCode}`);
                console.log(`响应数据: ${JSON.stringify(res.data)}`);
                console.log(`================================`);
                
                if (res.statusCode === 200) {
                    // 直接返回完整的响应，让业务代码处理状态码
                    resolve(res.data);
                } else if (res.statusCode === 401) { // 处理401未授权状态码
                    // 清除过期的登录信息
                    uni.removeStorageSync('token');
                    uni.removeStorageSync('userInfo');
                    
                    // 保存当前页面路径
                    const pages = getCurrentPages();
                    const currentPage = pages[pages.length - 1];
                    const url = currentPage ? currentPage.$page.fullPath : '';
                    
                    if (url) {
                        uni.setStorageSync('redirect_url', url);
                    }
                    
                    // 直接跳转到登录页面
                    uni.redirectTo({
                        url: '/pages/login/login'
                    });
                    
                    reject({
                        code: 401,
                        msg: '登录已过期，请重新登录'
                    });
                } else {
                    reject({
                        code: res.statusCode,
                        msg: '服务器响应异常: ' + res.statusCode
                    });
                }
            },
            fail: (err) => {
                // 记录详细错误
                console.error('=========== 请求失败 ===========');
                console.error(`请求URL: ${fullUrl}`);
                console.error(`错误信息: ${JSON.stringify(err)}`);
                console.error(`================================`);
                
                reject({
                    code: -1,
                    msg: err.errMsg || '网络请求失败'
                });
            }
        });
    });
};

export default request;