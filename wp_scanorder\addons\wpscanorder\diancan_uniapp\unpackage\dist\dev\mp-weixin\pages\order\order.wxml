<view class="page"><nav-bar vue-id="3c50aaa2-1" title="我的订单" isTabbar="{{true}}" background="white" bind:__l="__l"></nav-bar><view class="navbar-placeholder" style="{{'height:'+(statusBarHeight+88+'px')+';'}}"></view><view class="tab-section"><block wx:for="{{tabs}}" wx:for-item="tab" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['switchTab',[index]]]]]}}" class="{{['tab-item',currentTab===index?'active':'']}}" bindtap="__e"><text class="tab-text">{{tab.name}}</text><block wx:if="{{currentTab===index}}"><view class="tab-line"></view></block></view></block></view><view class="content-container"><block wx:if="{{$root.g0>0}}"><order-list vue-id="3c50aaa2-2" order-list="{{orderList}}" current-tab="{{currentTab}}" data-event-opts="{{[['^refresh',[['refreshOrders']]]]}}" bind:refresh="__e" bind:__l="__l"></order-list></block><block wx:else><empty-state vue-id="3c50aaa2-3" text="您暂时还没有订单哦～" image="/static/order/e186e04e8774da64b58c96a8bb479840.png" showAction="{{true}}" actionText="去点单" data-event-opts="{{[['^action',[['goToMenu']]]]}}" bind:action="__e" bind:__l="__l"></empty-state></block><block wx:if="{{$root.g1>0}}"><view class="loading-more"><block wx:if="{{isLoadingMore}}"><text class="loading-text">加载中...</text></block><block wx:else><block wx:if="{{!hasMore}}"><text class="no-more-text">没有更多订单了</text></block></block></view></block></view></view>