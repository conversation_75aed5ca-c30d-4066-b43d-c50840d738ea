<template>
	<view class="page">
		<!-- 使用通用导航栏 -->
		<nav-bar 
			title="我的订单" 
			:isTabbar="true" 
			background="white"
		></nav-bar>
		
		<!-- 导航栏占位 -->
		<view class="navbar-placeholder" :style="{ height: statusBarHeight + 88 + 'px' }"></view>

		<!-- 订单类型切换 -->
		<view class="tab-section">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index"
				:class="['tab-item', currentTab === index ? 'active' : '']"
				@tap="switchTab(index)"
			>
				<text class="tab-text">{{tab.name}}</text>
				<view v-if="currentTab === index" class="tab-line"></view>
			</view>
		</view>

		<!-- 订单内容区域 -->
		<view class="content-container">
			<!-- 有订单数据时显示订单列表组件 -->
			<OrderList 
				v-if="orderList.length > 0"
				:order-list="orderList"
				:current-tab="currentTab"
				@refresh="refreshOrders"
			/>

			<!-- 使用通用空状态组件 -->
			<empty-state 
				v-else
				text="您暂时还没有订单哦～"
				image="/static/order/e186e04e8774da64b58c96a8bb479840.png"
				:showAction="true"
				actionText="去点单"
				@action="goToMenu"
			></empty-state>
			
			<!-- 加载状态提示 -->
			<view class="loading-more" v-if="orderList.length > 0">
				<text v-if="isLoadingMore" class="loading-text">加载中...</text>
				<text v-else-if="!hasMore" class="no-more-text">没有更多订单了</text>
			</view>
		</view>
	</view>
</template>

<script>
import OrderList from './OrderList.vue';
import { myOrders } from '@/api/order';

export default {
	components: {
		OrderList
	},
	data() {
		return {
			statusBarHeight: 20,
			currentTab: 0,
			tabs: [
				{ name: '全部订单' },
				{ name: '堂食订单' },
				{ name: '外卖订单' }
			],
			orderList: [],
			originalOrders: [],
			refreshTimer: null, // 定时刷新的定时器
			isLoading: false, // 是否正在加载数据
			page: 1, // 当前页码
			pageSize: 10, // 每页数量
			hasMore: true, // 是否有更多数据
			isLoadingMore: false // 是否正在加载更多
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync();
		this.statusBarHeight = systemInfo.statusBarHeight;
		
		// 加载订单数据
		this.loadOrders(true);
	},
	
	onShow() {
		// 每次进入页面时重新加载数据
		this.loadOrders(true);
		
		// 启动定时刷新
		this.startAutoRefresh();
	},
	
	onHide() {
		// 页面隐藏时清除定时器
		this.clearAutoRefresh();
	},
	
	onUnload() {
		// 页面卸载时清除定时器
		this.clearAutoRefresh();
	},
	
	// 添加上拉触底事件处理函数
	onReachBottom() {
		if (this.hasMore && !this.isLoading && !this.isLoadingMore) {
			this.loadMoreOrders();
		}
	},
	
	// 添加下拉刷新事件处理函数
	onPullDownRefresh() {
		this.refreshOrders();
	},
	methods: {
		switchTab(index) {
			if (this.currentTab === index) return;
			this.currentTab = index;
			// 切换标签时重置分页并重新加载
			this.page = 1;
			this.hasMore = true;
			this.filterOrders();
		},
		
		filterOrders() {
			if (this.currentTab === 0) {
				// 全部订单
				this.orderList = [...this.originalOrders];
			} else if (this.currentTab === 1) {
				// 堂食订单
				this.orderList = this.originalOrders.filter(order => order.orderType === 'dine-in');
			} else {
				// 外卖订单
				this.orderList = this.originalOrders.filter(order => order.orderType === 'takeout');
			}
		},
		
		async loadOrders(isRefresh = false) {
			// 如果正在加载，则不重复加载
			if (this.isLoading) {
				return;
			}
			
			// 刷新时重置页码
			if (isRefresh) {
				this.page = 1;
				this.hasMore = true;
			}
			
			this.isLoading = true;
			try {
				// 静默加载，不显示loading，除非是首次加载
				const isFirstLoad = this.originalOrders.length === 0;
				if (isFirstLoad) {
					uni.showLoading({
						title: '加载中...'
					});
				}
				
				const { data } = await myOrders({
					page: this.page,
					pageSize: this.pageSize
				});
				
				// 检查是否还有更多数据
				if (!data.list || data.list.length < this.pageSize) {
					this.hasMore = false;
				}
				
				// 处理订单数据
				const processedOrders = data.list.map(order => {
					return {
						...order,
						orderType: order.dining_type === 'dine-in' ? 'dine-in' : 'takeout',
						status: order.status,
						status_text: order.status_text,
						products: order.order_products ? order.order_products.map(p => ({
							id: p.product_id,
							name: p.product_name,
							price: p.product_price,
							count: p.quantity,
							specs: p.specs || '',
							totalPrice: p.total_price
						})) : [],
					};
				});
				
				// 如果是刷新，则替换原有数据
				if (isRefresh) {
					this.originalOrders = processedOrders;
				} else {
					// 否则追加数据
					this.originalOrders = [...this.originalOrders, ...processedOrders];
				}
				
				// 根据当前标签过滤数据
				this.filterOrders();
				
				if (isFirstLoad) {
					uni.hideLoading();
				}
				
				// 如果是下拉刷新，停止下拉刷新动画
				if (isRefresh) {
					uni.stopPullDownRefresh();
				}
			} catch (error) {
				console.error('加载订单失败:', error);
				// 只有首次加载失败才显示错误提示
				if (this.originalOrders.length === 0) {
					uni.showToast({
						title: '加载订单失败',
						icon: 'none'
					});
				}
				
				// 停止下拉刷新动画
				uni.stopPullDownRefresh();
			} finally {
				if (this.originalOrders.length === 0) {
					uni.hideLoading();
				}
				this.isLoading = false;
			}
		},
		
		// 加载更多订单
		async loadMoreOrders() {
			if (this.isLoadingMore || !this.hasMore) return;
			
			this.isLoadingMore = true;
			this.page++;
			
			try {
				const { data } = await myOrders({
					page: this.page,
					pageSize: this.pageSize
				});
				
				// 检查是否还有更多数据
				if (!data.list || data.list.length < this.pageSize) {
					this.hasMore = false;
				}
				
				// 处理订单数据
				const processedOrders = data.list.map(order => {
					return {
						...order,
						orderType: order.dining_type === 'dine-in' ? 'dine-in' : 'takeout',
						status: order.status,
						status_text: order.status_text,
						products: order.order_products ? order.order_products.map(p => ({
							id: p.product_id,
							name: p.product_name,
							price: p.product_price,
							count: p.quantity,
							specs: p.specs || '',
							totalPrice: p.total_price
						})) : [],
					};
				});
				
				// 追加数据
				this.originalOrders = [...this.originalOrders, ...processedOrders];
				
				// 根据当前标签过滤数据
				this.filterOrders();
				
			} catch (error) {
				console.error('加载更多订单失败:', error);
				this.page--; // 加载失败，恢复页码
				uni.showToast({
					title: '加载更多失败',
					icon: 'none'
				});
			} finally {
				this.isLoadingMore = false;
			}
		},
		
		async refreshOrders() {
			await this.loadOrders(true);
		},
		
		// 启动自动刷新
		startAutoRefresh() {
			// 先清除可能存在的定时器
			this.clearAutoRefresh();
			
			// 设置新的定时器，每10秒刷新一次
			this.refreshTimer = setInterval(() => {
				console.log('定时刷新订单数据');
				this.loadOrders();
			}, 10000); // 10秒
			
			console.log('启动订单自动刷新');
		},
		
		// 清除自动刷新定时器
		clearAutoRefresh() {
			if (this.refreshTimer) {
				clearInterval(this.refreshTimer);
				this.refreshTimer = null;
				console.log('停止订单自动刷新');
			}
		},
		
		goToMenu() {
			uni.switchTab({
				url: '/pages/menu/menu'
			});
		}
	}
}
</script>

<style lang="scss">
@import '@/styles/theme.scss';

.page {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.navbar-placeholder {
	background-color: #fff;
}

.tab-section {
	display: flex;
	justify-content: space-between;
	background-color: #fff;
	padding: 0 40rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
	
	.tab-item {
		position: relative;
		padding: 24rpx 0;
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.tab-text {
			font-size: 28rpx;
			color: #999;
			transition: all 0.3s;
		}
		
		.tab-line {
			position: absolute;
			bottom: 0;
			width: 60rpx;
			height: 4rpx;
			background-color: #8cd548;
			border-radius: 2rpx;
			transition: all 0.3s;
		}
		
		&.active {
			.tab-text {
				color: #8cd548;
				font-weight: 500;
			}
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

.content-container {
	flex: 1;
	padding: 0 0 30rpx;
}

.loading-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 20rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #999;
	margin-right: 10rpx;
}

.no-more-text {
	font-size: 28rpx;
	color: #999;
}
</style>