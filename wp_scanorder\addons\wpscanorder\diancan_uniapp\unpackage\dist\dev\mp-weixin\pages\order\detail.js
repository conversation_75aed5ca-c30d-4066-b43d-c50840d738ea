(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/order/detail"],{

/***/ 289:
/*!****************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/main.js?{"page":"pages%2Forder%2Fdetail"} ***!
  \****************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _detail = _interopRequireDefault(__webpack_require__(/*! ./pages/order/detail.vue */ 290));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_detail.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 290:
/*!*********************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue ***!
  \*********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./detail.vue?vue&type=template&id=57d42baa&scoped=true& */ 291);
/* harmony import */ var _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./detail.vue?vue&type=script&lang=js& */ 293);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _detail_vue_vue_type_style_index_0_id_57d42baa_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detail.vue?vue&type=style&index=0&id=57d42baa&lang=scss&scoped=true& */ 295);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "57d42baa",
  null,
  false,
  _detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/order/detail.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 291:
/*!****************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?vue&type=template&id=57d42baa&scoped=true& ***!
  \****************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=57d42baa&scoped=true& */ 292);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_template_id_57d42baa_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 292:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?vue&type=template&id=57d42baa&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-icon/u-icon.vue */ 372))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = !_vm.loading && Object.keys(_vm.order).length > 0
  var m0 =
    g0 &&
    !(_vm.order.status === "pending") &&
    !(_vm.order.status === "cancelled")
      ? _vm.getStatusDesc()
      : null
  var g1 =
    (_vm.order.dining_type || _vm.order.type) === "takeout" && _vm.order.address
      ? _vm.order.address.replace(/,/g, "\n")
      : null
  var g2 = (_vm.order.total_price || _vm.order.totalPrice || 0).toFixed(2)
  var g3 = (_vm.order.discount_amount || _vm.order.discountAmount || 0).toFixed(
    2
  )
  var g4 = (
    _vm.order.final_price ||
    _vm.order.finalPrice ||
    _vm.order.total_price ||
    _vm.order.totalPrice ||
    0
  ).toFixed(2)
  var g5 = !_vm.loading && Object.keys(_vm.order).length > 0
  var g6 = g5 ? ["pending", "paid"].includes(_vm.order.status) : null
  var g7 =
    g5 && !(_vm.order.status === "pending")
      ? ["paid", "cooking", "cooked", "delivering"].includes(_vm.order.status)
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        m0: m0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
        g7: g7,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 293:
/*!**********************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js& */ 294);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 294:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 55));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 57));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _order = __webpack_require__(/*! @/api/order */ 195);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      order: {},
      loading: true,
      orderId: '',
      refreshTimer: null,
      lastRefreshTime: 0
    };
  },
  onShow: function onShow() {
    console.log('页面显示');
    this.startAutoRefresh();
  },
  onHide: function onHide() {
    console.log('页面隐藏，清除自动刷新');
    this.clearAutoRefresh();
  },
  onUnload: function onUnload() {
    console.log('页面卸载，清除自动刷新');
    this.clearAutoRefresh();
    // 确保在页面卸载时取消所有定时器
    clearInterval(this.refreshTimer);
    this.refreshTimer = null;
  },
  computed: {
    // 判断订单是否已支付
    isPaid: function isPaid() {
      if (this.order.status == 'cancelled') {
        return false;
      }
      // 如果订单有pay_status字段，则根据pay_status判断
      if (this.order.pay_status !== undefined) {
        return this.order.pay_status === 1 || this.order.pay_status === '1' || this.order.pay_status === true;
      }
      // 如果订单有支付时间，认为已支付
      if (this.order.pay_time || this.order.payTime) {
        return true;
      }
      // 如果都没有，则根据订单状态判断（completed状态认为是已支付）
      return this.order.status === 'completed';
    }
  },
  onLoad: function onLoad(options) {
    console.log('接收到的参数:', options);

    // 获取订单号
    var orderId = options.order_no || options.orderNo || options.id || '';
    console.log('页面接收到的订单ID:', orderId);

    // 保存订单ID，用于页面重新显示时刷新数据
    this.orderId = orderId;
    if (orderId) {
      // 显示加载中
      uni.showLoading({
        title: '加载中...'
      });

      // 优先从API获取最新订单数据
      if (!orderId.startsWith('temp_')) {
        // 直接从API获取订单详情
        console.log('直接从API获取订单详情');
        this.fetchOrderDetail(orderId);
      }
    } else {
      uni.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
      setTimeout(function () {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 从API获取订单详情
    fetchOrderDetail: function fetchOrderDetail(orderId) {
      var _this = this;
      var silent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      // 设置加载状态
      this.loading = true;

      // 如果是临时ID，不进行API请求
      if (orderId.startsWith('temp_')) {
        console.log('临时ID不进行API请求');
        if (!silent) {
          uni.hideLoading();
          uni.showToast({
            title: '订单信息暂未同步',
            icon: 'none'
          });
        }
        this.loading = false;
        return;
      }

      // 直接使用订单号
      var order_no = orderId;
      console.log('正在从API获取订单详情，订单号:', order_no);
      (0, _order.getOrderDetail)(order_no).then(function (res) {
        console.log('API返回的订单详情:', res);
        if (res.code === 1 && res.data) {
          // 处理订单数据
          var orderData = res.data;
          console.log('处理前的订单数据:', JSON.stringify(orderData));

          // 使用通用方法处理订单数据
          setTimeout(function () {
            _this.processOrderData(orderData);
            console.log('订单数据处理完成');

            // 初始化自动刷新（首次加载完成后）
            _this.startAutoRefresh();
          }, 10);
        } else {
          uni.showToast({
            title: res.msg || '订单不存在',
            icon: 'none'
          });
          setTimeout(function () {
            uni.navigateBack();
          }, 1500);
        }
      }).catch(function (err) {
        console.error('获取订单详情失败:', err);
        uni.hideLoading();
        uni.showToast({
          title: '获取订单详情失败',
          icon: 'none'
        });
        setTimeout(function () {
          uni.navigateBack();
        }, 1500);
      }).finally(function () {
        _this.loading = false;
        if (!silent) {
          uni.hideLoading();
        }
      });
    },
    handleBack: function handleBack() {
      // 返回到首页
      uni.switchTab({
        url: '/pages/order/order'
      });
    },
    // 手动刷新订单数据
    refreshOrderData: function refreshOrderData() {
      var silent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
      if (this.loading) {
        if (!silent) {
          uni.showToast({
            title: '正在加载中...',
            icon: 'none'
          });
        }
        return;
      }

      // 控制刷新频率，防止频繁请求
      var now = Date.now();
      if (now - this.lastRefreshTime < 3000) {
        // 3秒内不重复刷新
        console.log('刷新过于频繁，跳过');
        return;
      }
      this.lastRefreshTime = now;
      if (this.orderId) {
        if (!silent) {
          uni.showLoading({
            title: '刷新数据...'
          });
        }
        console.log((silent ? '自动' : '手动') + '刷新订单数据:', this.orderId);

        // 设置临时loading状态但不影响UI
        var tempLoading = this.loading;
        this.fetchOrderDetail(this.orderId, silent);
      } else {
        if (!silent) {
          uni.showToast({
            title: '订单ID不存在',
            icon: 'none'
          });
        }
      }
    },
    // 处理订单数据
    processOrderData: function processOrderData(orderData) {
      try {
        // 确保订单商品数据格式正确
        if (orderData.order_products && Array.isArray(orderData.order_products)) {
          // 使用接口返回的订单商品数据
          orderData.order_products = orderData.order_products.map(function (product) {
            return _objectSpread(_objectSpread({}, product), {}, {
              image: product.image || '',
              specs: product.props_text || product.specs || '',
              count: product.quantity || product.count || 1,
              price: parseFloat(product.price || product.product_price || 0)
            });
          });
          // 将order_products复制到products字段
          orderData.products = (0, _toConsumableArray2.default)(orderData.order_products);
        } else if (orderData.products && Array.isArray(orderData.products)) {
          // 兼容旧数据格式
          orderData.products = orderData.products.map(function (product) {
            return _objectSpread(_objectSpread({}, product), {}, {
              image: product.image || '',
              specs: product.props_text || product.specs || '',
              count: product.count || product.quantity || 1,
              price: parseFloat(product.price || product.product_price || 0)
            });
          });
        } else {
          orderData.products = [];
        }

        // 确保价格字段为数字
        orderData.total_price = parseFloat(orderData.total_price || 0);
        orderData.final_price = parseFloat(orderData.final_price || orderData.totalPrice || orderData.total_price || 0);
        orderData.discount_amount = parseFloat(orderData.discount_amount || 0);

        // 更新订单数据
        this.order = orderData;
        return orderData;
      } catch (error) {
        console.error('处理订单数据失败:', error);
        return orderData;
      }
    },
    getStatusDesc: function getStatusDesc() {
      var orderType = this.order.dining_type || this.order.type;
      var orderStatus = this.order.status;

      // 如果订单数据不完整，返回空字符串
      if (!orderType || !orderStatus) {
        return '';
      }

      // 根据不同状态和用餐方式返回不同描述
      if (orderType === 'dine-in') {
        // 堂食订单
        switch (orderStatus) {
          case 'paid':
            return "\u60A8\u7684\u8BA2\u5355\u5DF2\u652F\u4ED8\u6210\u529F\uFF0C\u6B63\u5728\u5B89\u6392\u5236\u4F5C\uFF0C\u8BF7\u8010\u5FC3\u7B49\u5F85";
          case 'cooking':
            return "\u60A8\u7684\u8BA2\u5355\u6B63\u5728\u5236\u4F5C\u4E2D\uFF0C\u9884\u8BA13\u5206\u949F\u540E\u53EF\u53D6\u9910\uFF0C\u53D6\u9910\u53F7\uFF1A".concat(this.order.pickup_code || '');
          case 'cooked':
            return "\u60A8\u7684\u8BA2\u5355\u5DF2\u5236\u4F5C\u5B8C\u6210\uFF0C\u8BF7\u524D\u5F80\u53D6\u9910\u533A\u53D6\u9910\uFF0C\u53D6\u9910\u53F7\uFF1A".concat(this.order.pickup_code || '');
          case 'completed':
            return "\u611F\u8C22\u60A8\u7684\u5149\u4E34\uFF0C\u671F\u5F85\u60A8\u518D\u6B21\u60E0\u987E";
          default:
            return "\u9884\u8BA13\u5206\u949F\u540E\u53EF\u53D6\u9910\uFF0C\u53D6\u9910\u53F7\uFF1A".concat(this.order.pickup_code || '');
        }
      } else {
        // 外卖订单
        switch (orderStatus) {
          case 'paid':
            return "\u60A8\u7684\u8BA2\u5355\u5DF2\u652F\u4ED8\u6210\u529F\uFF0C\u6B63\u5728\u5B89\u6392\u5236\u4F5C\uFF0C\u8BF7\u8010\u5FC3\u7B49\u5F85";
          case 'cooking':
            return "\u60A8\u7684\u8BA2\u5355\u6B63\u5728\u5236\u4F5C\u4E2D\uFF0C\u5236\u4F5C\u5B8C\u6210\u540E\u5C06\u4E3A\u60A8\u914D\u9001";
          case 'cooked':
            return "\u60A8\u7684\u8BA2\u5355\u5DF2\u5236\u4F5C\u5B8C\u6210\uFF0C\u6B63\u5728\u7B49\u5F85\u914D\u9001\u5458\u53D6\u9910";
          case 'delivering':
            return "\u60A8\u7684\u8BA2\u5355\u6B63\u5728\u914D\u9001\u4E2D\uFF0C\u9884\u8BA1".concat(this.order.estimate_time || '15', "\u5206\u949F\u9001\u8FBE");
          case 'completed':
            return "\u8BA2\u5355\u5DF2\u9001\u8FBE\uFF0C\u611F\u8C22\u60A8\u7684\u60E0\u987E";
          default:
            return "\u9884\u8BA115\u5206\u949F\u9001\u8FBE\uFF0C\u914D\u9001\u5458\uFF1A".concat(this.order.delivery_name || '');
        }
      }
    },
    reorder: function reorder() {
      // 获取订单商品数据
      var products = this.order.products || this.order.order_products || [];
      if (!this.order || !products || !Array.isArray(products) || products.length === 0) {
        uni.showToast({
          title: '订单数据异常',
          icon: 'none'
        });
        return;
      }

      // 构建购物车数据
      var cartData = {
        list: products.map(function (item) {
          var price = parseFloat(item.price || item.product_price || 0);
          var count = parseInt(item.quantity || item.count || 1);
          return {
            id: item.product_id || item.id,
            name: item.product_name || item.name,
            price: price,
            count: count,
            totalPrice: price * count,
            image: item.image || '',
            specs: item.props_text || item.specs || '',
            specSelected: true
          };
        }),
        total: products.reduce(function (sum, item) {
          return sum + parseInt(item.quantity || item.count || 1);
        }, 0),
        price: parseFloat(this.order.total_price || this.order.totalPrice || 0)
      };

      // 保存到本地存储
      uni.setStorageSync('cartData', cartData);

      // 保存用餐方式
      uni.setStorageSync('diningType', this.order.type);

      // 显示提示
      uni.showToast({
        title: '已添加到购物车',
        icon: 'none',
        duration: 1500,
        success: function success() {
          // 跳转到菜单页
          setTimeout(function () {
            uni.switchTab({
              url: '/pages/menu/menu'
            });
          }, 1500);
        }
      });
    },
    checkProgress: function checkProgress() {
      // 如果正在加载数据，不允许查看进度
      if (this.loading) {
        uni.showToast({
          title: '订单信息加载中...',
          icon: 'none'
        });
        return;
      }

      // 未支付的订单不能查看进度
      if (!this.isPaid) {
        uni.showToast({
          title: '请先完成支付',
          icon: 'none'
        });
        return;
      }
      uni.showModal({
        title: '订单进度',
        content: this.getProgressDesc(),
        showCancel: false,
        confirmText: '我知道了'
      });
    },
    // 评价订单
    goComment: function goComment() {
      uni.showToast({
        title: '评价功能开发中',
        icon: 'none'
      });

      // 可以跳转到评价页面，这里先用提示代替
      // uni.navigateTo({
      //   url: `/pages/order/comment?id=${this.order.order_no || this.order.orderNo}`
      // });
    },
    getProgressDesc: function getProgressDesc() {
      var orderType = this.order.dining_type || this.order.type;
      var orderStatus = this.order.status;

      // 如果订单数据不完整，返回加载中提示
      if (!orderType || !orderStatus) {
        return '正在获取订单进度信息...';
      }
      if (orderType === 'dine-in') {
        // 堂食订单
        switch (orderStatus) {
          case 'paid':
            return "\u60A8\u7684\u5802\u98DF\u8BA2\u5355\u5DF2\u652F\u4ED8\u6210\u529F\n\u6B63\u5728\u5B89\u6392\u5236\u4F5C\n\u9884\u8BA12-3\u5206\u949F\u540E\u5F00\u59CB\u5236\u4F5C\n\u53D6\u9910\u53F7\uFF1A".concat(this.order.pickup_code || '');
          case 'cooking':
            return "\u60A8\u7684\u5802\u98DF\u8BA2\u5355\u6B63\u5728\u5236\u4F5C\u4E2D\n\u5236\u4F5C\u8FDB\u5EA6\uFF1A50%\n\u9884\u8BA15\u5206\u949F\u540E\u53EF\u4EE5\u53D6\u9910\n\u53D6\u9910\u53F7\uFF1A".concat(this.order.pickup_code || '', "\n\u8BF7\u524D\u5F80\u53D6\u9910\u533A\u7B49\u5019");
          case 'cooked':
            return "\u60A8\u7684\u5802\u98DF\u8BA2\u5355\u5DF2\u5236\u4F5C\u5B8C\u6210\n\u5236\u4F5C\u8FDB\u5EA6\uFF1A100%\n\u8BF7\u7ACB\u5373\u524D\u5F80\u53D6\u9910\u533A\u53D6\u9910\n\u53D6\u9910\u53F7\uFF1A".concat(this.order.pickup_code || '');
          case 'completed':
            return "\u60A8\u7684\u5802\u98DF\u8BA2\u5355\u5DF2\u5B8C\u6210\n\u611F\u8C22\u60A8\u7684\u5149\u4E34\uFF0C\u671F\u5F85\u60A8\u518D\u6B21\u60E0\u987E";
          default:
            return "\u60A8\u7684\u5802\u98DF\u8BA2\u5355\u6B63\u5728\u5236\u4F5C\u4E2D\n\u5236\u4F5C\u8FDB\u5EA6\uFF1A80%\n\u9884\u8BA13\u5206\u949F\u540E\u53EF\u4EE5\u53D6\u9910\n\u53D6\u9910\u53F7\uFF1A".concat(this.order.pickup_code || '', "\n\u8BF7\u524D\u5F80\u53D6\u9910\u533A\u7B49\u5019");
        }
      } else {
        // 外卖订单
        switch (orderStatus) {
          case 'paid':
            return "\u60A8\u7684\u5916\u5356\u8BA2\u5355\u5DF2\u652F\u4ED8\u6210\u529F\n\u6B63\u5728\u5B89\u6392\u5236\u4F5C\n\u9884\u8BA12-3\u5206\u949F\u540E\u5F00\u59CB\u5236\u4F5C\n\u914D\u9001\u5730\u5740\uFF1A".concat(this.order.address || '未设置地址');
          case 'cooking':
            return "\u60A8\u7684\u5916\u5356\u8BA2\u5355\u6B63\u5728\u5236\u4F5C\u4E2D\n\u5236\u4F5C\u8FDB\u5EA6\uFF1A50%\n\u9884\u8BA115\u5206\u949F\u540E\u5F00\u59CB\u914D\u9001\n\u914D\u9001\u5730\u5740\uFF1A".concat(this.order.address || '未设置地址');
          case 'cooked':
            return "\u60A8\u7684\u5916\u5356\u8BA2\u5355\u5DF2\u5236\u4F5C\u5B8C\u6210\n\u5236\u4F5C\u8FDB\u5EA6\uFF1A100%\n\u6B63\u5728\u7B49\u5F85\u9A91\u624B\u63A5\u5355\n\u914D\u9001\u5730\u5740\uFF1A".concat(this.order.address || '未设置地址');
          case 'delivering':
            return "\u60A8\u7684\u5916\u5356\u8BA2\u5355\u6B63\u5728\u914D\u9001\u4E2D\n\u914D\u9001\u5458\uFF1A".concat(this.order.delivery_name || '', "\n\u8054\u7CFB\u7535\u8BDD\uFF1A").concat(this.order.delivery_phone || '', "\n\u9884\u8BA1").concat(this.order.estimate_time || '10', "\u5206\u949F\u9001\u8FBE\n\u914D\u9001\u5730\u5740\uFF1A").concat(this.order.address || '未设置地址');
          case 'completed':
            return "\u60A8\u7684\u5916\u5356\u8BA2\u5355\u5DF2\u9001\u8FBE\n\u611F\u8C22\u60A8\u7684\u60E0\u987E\uFF0C\u671F\u5F85\u60A8\u518D\u6B21\u4E0B\u5355";
          default:
            return "\u60A8\u7684\u5916\u5356\u8BA2\u5355\u6B63\u5728\u914D\u9001\u4E2D\n\u9884\u8BA115\u5206\u949F\u9001\u8FBE\n\u914D\u9001\u5730\u5740\uFF1A".concat(this.order.address || '未设置地址');
        }
      }
    },
    // 去支付
    goPay: function goPay() {
      var _this2 = this;
      // 获取订单号
      var orderNo = this.order.order_no || this.order.orderNo;
      if (!orderNo) {
        uni.showToast({
          title: '订单号不存在',
          icon: 'none'
        });
        return;
      }

      // 弹出支付方式选择
      uni.showActionSheet({
        itemList: ['微信支付', '余额支付'],
        success: function () {
          var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(res) {
            var payType;
            return _regenerator.default.wrap(function _callee$(_context) {
              while (1) {
                switch (_context.prev = _context.next) {
                  case 0:
                    payType = res.tapIndex;
                    if (payType === 0) {
                      // 微信支付
                      _this2.wxPay(orderNo);
                    } else if (payType === 1) {
                      // 余额支付
                      _this2.balancePay(orderNo);
                    }
                  case 2:
                  case "end":
                    return _context.stop();
                }
              }
            }, _callee);
          }));
          function success(_x) {
            return _success.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 微信支付
    wxPay: function wxPay(orderNo) {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var payResult;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return (0, _order.getWxPayParams)(orderNo);
              case 3:
                payResult = _context2.sent;
                uni.hideLoading();
                if (!(payResult.code !== 1)) {
                  _context2.next = 8;
                  break;
                }
                uni.showToast({
                  title: payResult.msg || '获取支付参数失败',
                  icon: 'none'
                });
                return _context2.abrupt("return");
              case 8:
                // 调用微信支付
                uni.requestPayment(_objectSpread(_objectSpread({}, payResult.data), {}, {
                  success: function success() {
                    _this3.checkPaymentResult(orderNo);
                  },
                  fail: function fail(err) {
                    console.log('支付失败', err);
                    uni.showToast({
                      title: '支付已取消',
                      icon: 'none'
                    });
                  }
                }));
                _context2.next = 15;
                break;
              case 11:
                _context2.prev = 11;
                _context2.t0 = _context2["catch"](0);
                uni.showToast({
                  title: '支付异常，请稍后再试',
                  icon: 'none'
                });
                console.error('支付出错', _context2.t0);
              case 15:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 11]]);
      }))();
    },
    // 余额支付
    balancePay: function balancePay(orderNo) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var payResult;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                uni.showLoading({
                  title: '处理中...'
                });

                // 调用余额支付API
                _context3.next = 4;
                return (0, _order.payOrderWithBalance)(orderNo);
              case 4:
                payResult = _context3.sent;
                uni.hideLoading();
                if (payResult.code === 1) {
                  uni.showToast({
                    title: '支付成功',
                    icon: 'none'
                  });
                  // 刷新订单数据
                  _this4.fetchOrderDetail(orderNo);
                } else {
                  // 如果为2 跳转充值页面
                  if (payResult.code === 2) {
                    // 显示确认对话框，让用户选择是否跳转到充值页面
                    uni.showModal({
                      title: '余额不足',
                      content: '您的余额不足，是否前往充值？',
                      confirmText: '去充值',
                      cancelText: '取消',
                      success: function success(res) {
                        if (res.confirm) {
                          // 用户点击确认，跳转到充值页面
                          uni.navigateTo({
                            url: '/pages/my/recharge'
                          });
                        }
                      }
                    });
                  }
                  uni.showToast({
                    title: payResult.msg || '余额不足',
                    icon: 'none'
                  });
                }
                _context3.next = 14;
                break;
              case 9:
                _context3.prev = 9;
                _context3.t0 = _context3["catch"](0);
                uni.hideLoading();
                uni.showToast({
                  title: '支付失败，请稍后再试',
                  icon: 'none'
                });
                console.error('余额支付出错', _context3.t0);
              case 14:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 9]]);
      }))();
    },
    // 检查支付结果
    checkPaymentResult: function checkPaymentResult(orderNo) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var queryResult;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                uni.showLoading({
                  title: '正在查询支付结果...'
                });

                // 查询支付结果
                _context4.next = 4;
                return (0, _order.queryWxPayResult)(orderNo);
              case 4:
                queryResult = _context4.sent;
                uni.hideLoading();
                if (queryResult.code === 1 && queryResult.data && queryResult.data.pay_status === 1) {
                  uni.showToast({
                    title: '支付成功',
                    icon: 'none'
                  });

                  // 刷新订单数据
                  _this5.fetchOrderDetail(orderNo);
                } else {
                  uni.showToast({
                    title: queryResult.msg || '支付结果查询失败',
                    icon: 'none'
                  });
                }
                _context4.next = 14;
                break;
              case 9:
                _context4.prev = 9;
                _context4.t0 = _context4["catch"](0);
                uni.hideLoading();
                uni.showToast({
                  title: '查询支付结果失败',
                  icon: 'none'
                });
                console.error('查询支付结果出错', _context4.t0);
              case 14:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 9]]);
      }))();
    },
    startAutoRefresh: function startAutoRefresh() {
      var _this6 = this;
      // 清除可能存在的定时器
      this.clearAutoRefresh();

      // 设置新的定时器，每10秒刷新一次
      this.refreshTimer = setInterval(function () {
        console.log('定时刷新订单数据');
        _this6.refreshOrderData(true); // 静默刷新
      }, 10000); // 10秒

      // 保存引用以便于完全清除
      this._refreshTimerId = this.refreshTimer;
      console.log('启动订单自动刷新，定时器ID:', this.refreshTimer);
    },
    clearAutoRefresh: function clearAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
        console.log('停止订单自动刷新');
      }
      // 确保没有其他定时器在运行
      if (typeof this._refreshTimerId !== 'undefined') {
        clearInterval(this._refreshTimerId);
        this._refreshTimerId = null;
      }
    },
    // 取消订单
    cancelOrder: function cancelOrder() {
      var _this7 = this;
      // 获取订单号
      var orderNo = this.order.order_no || this.order.orderNo;
      if (!orderNo) {
        uni.showToast({
          title: '订单号不存在',
          icon: 'none'
        });
        return;
      }
      uni.showModal({
        title: '取消订单',
        content: '确定要取消该订单吗？',
        success: function () {
          var _success2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5(res) {
            var result;
            return _regenerator.default.wrap(function _callee5$(_context5) {
              while (1) {
                switch (_context5.prev = _context5.next) {
                  case 0:
                    if (!res.confirm) {
                      _context5.next = 15;
                      break;
                    }
                    _context5.prev = 1;
                    uni.showLoading({
                      title: '处理中...'
                    });
                    _context5.next = 5;
                    return (0, _order.cancelOrder)(orderNo);
                  case 5:
                    result = _context5.sent;
                    uni.hideLoading();
                    if (result.code === 1) {
                      uni.showToast({
                        title: '订单已取消',
                        icon: 'none'
                      });

                      // 刷新订单数据
                      setTimeout(function () {
                        _this7.fetchOrderDetail(orderNo);
                      }, 500);
                    } else {
                      uni.showToast({
                        title: result.msg || '取消失败，请稍后再试',
                        icon: 'none'
                      });
                    }
                    _context5.next = 15;
                    break;
                  case 10:
                    _context5.prev = 10;
                    _context5.t0 = _context5["catch"](1);
                    uni.hideLoading();
                    uni.showToast({
                      title: '取消订单失败',
                      icon: 'none'
                    });
                    console.error('取消订单出错', _context5.t0);
                  case 15:
                  case "end":
                    return _context5.stop();
                }
              }
            }, _callee5, null, [[1, 10]]);
          }));
          function success(_x2) {
            return _success2.apply(this, arguments);
          }
          return success;
        }()
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 295:
/*!*******************************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?vue&type=style&index=0&id=57d42baa&lang=scss&scoped=true& ***!
  \*******************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_57d42baa_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=57d42baa&lang=scss&scoped=true& */ 296);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_57d42baa_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_57d42baa_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_57d42baa_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_57d42baa_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_detail_vue_vue_type_style_index_0_id_57d42baa_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 296:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?vue&type=style&index=0&id=57d42baa&lang=scss&scoped=true& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[289,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/detail.js.map