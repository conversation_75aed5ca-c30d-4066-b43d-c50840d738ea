@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-57d42baa {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}
.header.data-v-57d42baa {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-57d42baa {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.header .nav-bar .left.data-v-57d42baa {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
}
.header .nav-bar .left.data-v-57d42baa:active {
  opacity: 0.6;
}
.header .nav-bar .title.data-v-57d42baa {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  flex: 1;
  text-align: center;
}
.header .nav-bar .right.data-v-57d42baa {
  width: 88rpx;
  height: 88rpx;
}
.status-card.data-v-57d42baa {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding: 40rpx 30rpx;
}
.status-card.unpaid-card.data-v-57d42baa {
  background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);
}
.status-card.paid-card.data-v-57d42baa {
  background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);
}
.status-card.cooking-card.data-v-57d42baa {
  background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);
}
.status-card.cooked-card.data-v-57d42baa {
  background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);
}
.status-card.delivering-card.data-v-57d42baa {
  background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);
}
.status-card.completed-card.data-v-57d42baa {
  background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);
}
.status-card.cancelled-card.data-v-57d42baa {
  background: linear-gradient(135deg, #999999 0%, #666666 100%);
}
.status-card .status-info.data-v-57d42baa {
  text-align: center;
}
.status-card .status-info .status.data-v-57d42baa {
  font-size: 36rpx;
  color: #fff;
  font-weight: bold;
  margin-bottom: 12rpx;
  display: block;
}
.status-card .status-info .desc.data-v-57d42baa {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}
.status-card .status-info .auto-refresh-tip.data-v-57d42baa {
  margin-top: 16rpx;
  padding: 6rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 100rpx;
  display: inline-block;
}
.status-card .status-info .auto-refresh-tip text.data-v-57d42baa {
  font-size: 22rpx;
  color: #fff;
}
.order-card.data-v-57d42baa, .product-card.data-v-57d42baa {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}
.order-card .card-title.data-v-57d42baa, .product-card .card-title.data-v-57d42baa {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.order-card .info-item.data-v-57d42baa, .product-card .info-item.data-v-57d42baa {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
}
.order-card .info-item .label.data-v-57d42baa, .product-card .info-item .label.data-v-57d42baa {
  font-size: 28rpx;
  color: #666;
}
.order-card .info-item .value.data-v-57d42baa, .product-card .info-item .value.data-v-57d42baa {
  font-size: 28rpx;
  color: #333;
}
.product-list .product-item.data-v-57d42baa {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}
.product-list .product-item .product-name.data-v-57d42baa {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.product-list .product-item .product-specs.data-v-57d42baa {
  font-size: 24rpx;
  color: #999;
  margin: 0 20rpx;
}
.product-list .product-item .product-price.data-v-57d42baa {
  font-size: 28rpx;
  color: #333;
}
.product-list .product-item .product-price .count.data-v-57d42baa {
  margin-left: 10rpx;
  color: #999;
}
.price-info.data-v-57d42baa {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f5f5f5;
}
.price-info .price-item.data-v-57d42baa {
  display: flex;
  justify-content: space-between;
  padding: 12rpx 0;
}
.price-info .price-item .label.data-v-57d42baa {
  font-size: 26rpx;
  color: #666;
}
.price-info .price-item .value.data-v-57d42baa {
  font-size: 26rpx;
  color: #333;
}
.price-info .price-item .value.unpaid.data-v-57d42baa {
  color: #ff8500;
}
.price-info .price-item .value.paid.data-v-57d42baa {
  color: #6ab52e;
}
.price-info .price-item.total.data-v-57d42baa {
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1px dashed #eee;
}
.price-info .price-item.total .label.data-v-57d42baa {
  font-size: 28rpx;
  color: #333;
}
.price-info .price-item.total .value.data-v-57d42baa {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}
.price-info .price-item.total .value.unpaid.data-v-57d42baa {
  color: #ff8500;
}
.price-info .price-item.payment-status.data-v-57d42baa {
  margin-top: 12rpx;
  padding-top: 12rpx;
  border-top: 1px dashed #eee;
}
.bottom-bar.data-v-57d42baa {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: #fff;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}
.bottom-bar .btn.data-v-57d42baa {
  padding: 20rpx 40rpx;
  border-radius: 100rpx;
  font-size: 28rpx;
}
.bottom-bar .btn.outline.data-v-57d42baa {
  border: 1px solid #ddd;
}
.bottom-bar .btn.primary.data-v-57d42baa {
  background: #8cd548;
  color: #fff;
}

