<template>
  <view class="page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="back-btn" @tap="handleBack">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
        </view>
        <text class="page-title">商户详情</text>
        <view class="placeholder"></view>
      </view>
    </view>
    
    <!-- 占位元素，防止内容被导航栏遮挡 -->
    <view class="navbar-placeholder" :style="{ height: navbarHeight + 'px' }"></view>

    <!-- 商户信息头部 -->
    <view class="store-header">
      <view class="store-info-wrap">
        <image 
          class="store-logo" 
          :src="storeInfo.image || '/static/store/default.png'" 
          mode="aspectFill" 
        />
        <view class="store-brief">
          <text class="store-name">{{storeInfo.name}}</text>
          <view class="store-stats">
            <view class="rating-wrap">
              <text class="rating">{{storeInfo.rating}}分</text>
              <view class="rating-stars">
                <u-icon 
                  v-for="i in 5" 
                  :key="i"
                  :name="i <= Math.floor(storeInfo.rating) ? 'star-fill' : 'star'"
                  :color="i <= Math.floor(storeInfo.rating) ? '#ff9900' : '#ddd'"
                  size="24"
                ></u-icon>
              </view>
            </view>
            <text class="divider">|</text>
            <text class="sales">月售{{storeInfo.monthlySales}}单</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 商户信息 -->
    <view class="store-info">
      <!-- 营业信息卡片 -->
      <view class="info-card business-card">
        <view class="card-header">
          <u-icon name="info-circle" size="32" color="#333"></u-icon>
          <text class="title">营业信息</text>
        </view>
        <view class="business-info">
          <view class="time-item">
            <view class="time-header">
              <view class="header-left">
                <u-icon 
                  name="clock" 
                  size="28" 
                  color="#8cd548"
                ></u-icon>
                <text class="time-label">营业时间</text>
              </view>
              <view 
                class="status-tag" 
                :class="{ closed: !isOpen }"
              >
                <u-icon 
                  :name="isOpen ? 'checkmark-circle' : 'close-circle'" 
                  size="24" 
                  :color="isOpen ? '#8cd548' : '#ff6b6b'"
                ></u-icon>
                <text>{{ isOpen ? '营业中' : '已打烊' }}</text>
              </view>
            </view>
            <view class="time-periods">
              <text class="time-value">{{ storeInfo.businessHours }}</text>
              <text class="time-tips">*最后下单时间为打烊前30分钟</text>
            </view>
          </view>
          <view class="notice-item" v-if="storeInfo.notice">
            <u-icon name="volume" size="28" color="#ff9900"></u-icon>
            <view class="notice-content">
              <text class="notice-label">商家公告</text>
              <text class="notice-text">{{storeInfo.notice}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 地址信息卡片 -->
      <view class="info-card address-info">
        <view class="card-header">
          <u-icon name="map-fill" size="32" color="#ff6b6b"></u-icon>
          <text class="title">商家地址</text>
        </view>
        <view class="address-content">
          <view class="location-info">
            <view class="address-header">
              <view class="distance-tag">
                <u-icon name="map" size="24" color="#ff6b6b"></u-icon>
                <text>距您{{storeInfo.distance}}km</text>
              </view>
              <view class="nav-btn" @tap="openMap">
                <u-icon size="28" color="#ff6b6b"></u-icon>
                <text>去导航</text>
              </view>
            </view>
          </view>
          <view class="address-detail">
            <u-icon name="home" size="28" color="#666"></u-icon>
            <text class="address-text">{{storeInfo.address}}</text>
          </view>
        </view>
      </view>

      <!-- 资质信息卡片 -->
      <view class="info-card qualification" v-if="storeInfo.qualifications && storeInfo.qualifications.length > 0">
        <view class="card-header">
          <u-icon name="file-text" size="32" color="#333"></u-icon>
          <text class="title">商家资质</text>
        </view>
        <view class="qual-content">
          <scroll-view 
            class="qual-scroll" 
            scroll-x 
            show-scrollbar="false"
            enhanced
          >
            <view 
              class="qual-item" 
              v-for="(item, index) in storeInfo.qualifications" 
              :key="index" 
              @tap="previewImage(index)"
            >
              <image class="qual-image" :src="item.image" mode="aspectFill" />
              <text class="qual-tips">点击查看</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <view class="contact" @tap="contactStore">
        <u-icon name="phone-fill" size="32" color="#666"></u-icon>
        <text>联系商家</text>
      </view>
      <view class="order-btn" @tap="goToMenu">
        <u-icon name="shopping-cart" size="32" color="#fff" class="cart-icon"></u-icon>
        <text>去点餐</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getMerchantInfo } from '@/api/merchant'

export default {
  navigationBarTitleText: '商户详情',
  navigationBarBackgroundColor: '#ffffff',
  navigationBarTextStyle: 'black',

  data() {
    return {
      storeInfo: {
        name: '',
        image: '',
        rating: 5.0,
        monthlySales: 0,
        businessHours: '',
        notice: '',
        address: '',
        distance: 0,
        phone: '',
        qualifications: []
      },
      merchantId: 0,
      statusBarHeight: 0,
      navbarHeight: 0,
      isOpen: true,
      currentTime: ''
    }
  },

  onLoad(options) {
    // 获取传递过来的商户ID
    this.merchantId = options.id || 0
    
    // 初始化导航栏高度
    this.initNavBarHeight()
    
    // 获取商户信息
    if (this.merchantId) {
      this.getMerchantDetail()
    } else {
      // 无商户ID时获取默认商户
      this.getMerchantDetail()
    }
    
    // 不在这里检查营业状态，而是在获取数据后检查
    // this.checkBusinessStatus()
  },

  methods: {
    initNavBarHeight() {
      // 获取状态栏高度
      const systemInfo = uni.getSystemInfoSync()
      this.statusBarHeight = systemInfo.statusBarHeight
      // 计算导航栏总高度 (状态栏 + 导航内容区)
      this.navbarHeight = this.statusBarHeight + 44
    },
    
    handleBack() {
      uni.navigateBack({
        delta: 1
      })
    },

    async getMerchantDetail() {
      try {
        const res = await getMerchantInfo()
        console.log('获取到的商户信息:', res)

        // 检查API返回的数据结构
        if (res && res.code === 1 && res.data) {
          const merchantData = res.data
          
          // 从data中提取商户信息
          this.storeInfo = {
            name: merchantData.name || '',
            image: merchantData.logo_image || '',
            rating: Number(merchantData.rating) || 5.0,
            monthlySales: merchantData.monthly_sales || 0,
            businessHours: merchantData.business_hours || '',
            notice: merchantData.announcement || '',
            address: merchantData.address || '',
            distance: 0,
            phone: merchantData.phone || '',
            qualifications: merchantData.qualification_images 
              ? merchantData.qualification_images.map(image => ({
                  name: '商家资质',
                  image: image
                }))
              : []
          }
          
          console.log('处理后的商户信息:', this.storeInfo)
          
          // 获取到商户信息后再检查营业状态
          this.checkBusinessStatus()
        } else {
          throw new Error('API返回数据格式错误')
        }
      } catch (e) {
        console.error('获取商户详情失败:', e)
        uni.showToast({
          title: '获取商户详情失败',
          icon: 'none'
        })
      }
    },

    openMap() {
      // 打开地图导航
      // 注意：这里使用的是固定坐标，实际应用中应该从API获取商户的经纬度
      const latitude = 22.544925  // 默认纬度
      const longitude = 114.109078  // 默认经度
      
      uni.openLocation({
        latitude: latitude,
        longitude: longitude,
        name: this.storeInfo.name,
        address: this.storeInfo.address,
        success: () => {
          console.log('打开地图成功')
        },
        fail: (err) => {
          console.error('打开地图失败:', err)
          uni.showToast({
            title: '导航失败，请手动导航',
            icon: 'none'
          })
        }
      })
    },

    contactStore() {
      uni.makePhoneCall({
        phoneNumber: this.storeInfo.phone
      })
    },

    goToMenu() {
      uni.switchTab({
        url: '/pages/menu/menu'
      })
    },

    checkBusinessStatus() {
      try {
        // 获取当前时间
        const now = new Date()
        const hours = now.getHours()
        const minutes = now.getMinutes()
        const currentTime = hours * 100 + minutes // 例如：10:30 => 1030
        
        // 解析营业时间格式（假设格式为 "HH:MM-HH:MM"）
        if (this.storeInfo.businessHours) {
          const timeRange = this.storeInfo.businessHours.split('-')
          if (timeRange.length === 2) {
            const startTimeStr = timeRange[0].trim()
            const endTimeStr = timeRange[1].trim()
            
            // 转换为数字比较（例如："10:00" => 1000）
            const startTime = parseInt(startTimeStr.replace(':', ''))
            const endTime = parseInt(endTimeStr.replace(':', ''))
            
            // 判断当前时间是否在营业时间范围内
            this.isOpen = currentTime >= startTime && currentTime <= endTime
            return
          }
        }
        
        // 如果解析失败，使用默认值
        this.isOpen = hours >= 9 && hours < 22
      } catch (e) {
        console.error('检查营业状态失败:', e)
        // 出错时使用默认值
        this.isOpen = true
      }
    },

    previewImage(index) {
      const images = this.storeInfo.qualifications.map(item => item.image)
      uni.previewImage({
        current: index,
        urls: images,
        indicator: 'number',
        loop: true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
  
  .navbar-content {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16rpx;
    position: relative;
    
    .back-btn {
      width: 88rpx;
      height: 44px;
      display: flex;
      align-items: center;
      
      &:active {
        opacity: 0.7;
      }
    }
    
    .page-title {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
    
    .placeholder {
      width: 88rpx;
      height: 44px;
    }
  }
}

.navbar-placeholder {
  width: 100%;
}

.store-header {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 24rpx;
  
  .store-info-wrap {
    display: flex;
    align-items: center;
    padding: 0 4rpx;
    
    .store-logo {
      width: 140rpx;
      height: 140rpx;
      border-radius: 12rpx;
      border: 1rpx solid rgba(0, 0, 0, 0.05);
      background: #f8f8f8;
    }
    
    .store-brief {
      flex: 1;
      margin-left: 24rpx;
      
      .store-name {
        font-size: 36rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 12rpx;
      }
      
      .store-stats {
        display: flex;
        align-items: center;
        
        .rating-wrap {
          display: flex;
          align-items: center;
          
          .rating {
            font-size: 28rpx;
            color: #ff9900;
            font-weight: 600;
            margin-right: 8rpx;
          }
          
          .rating-stars {
            display: flex;
            gap: 4rpx;
          }
        }
        
        .divider {
          margin: 0 16rpx;
          font-size: 24rpx;
          color: #ddd;
        }
        
        .sales {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}

.store-info {
  padding: 24rpx;
  
  .info-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
    border: 1rpx solid rgba(0, 0, 0, 0.02);
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .business-card {
    .business-info {
      .time-item {
        display: flex;
        flex-direction: column;
        padding: 24rpx;
        background: linear-gradient(to bottom, #f9f9f9, #f5f5f5);
        border-radius: 16rpx;
        margin-bottom: 24rpx;
        border: 1rpx solid rgba(0, 0, 0, 0.05);
        
        .time-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16rpx;
          
          .header-left {
            display: flex;
            align-items: center;
            
            .u-icon {
              margin-right: 8rpx;
            }
            
            .time-label {
              font-size: 28rpx;
              color: #333;
              font-weight: 600;
            }
          }
          
          .status-tag {
            display: flex;
            align-items: center;
            padding: 6rpx 16rpx;
            background: rgba(140, 213, 72, 0.1);
            border-radius: 24rpx;
            
            .u-icon {
              margin-right: 4rpx;
            }
            
            text {
              font-size: 24rpx;
              color: #8cd548;
              font-weight: 500;
            }
            
            &.closed {
              background: rgba(255, 107, 107, 0.1);
              
              text {
                color: #ff6b6b;
              }
            }
          }
        }
        
        .time-periods {
          padding: 16rpx;
          background: #fff;
          border-radius: 12rpx;
          border: 1rpx solid rgba(0, 0, 0, 0.03);
          
          .time-value {
            display: block;
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
            margin-bottom: 8rpx;
          }
          
          .time-tips {
            display: block;
            font-size: 24rpx;
            color: #999;
            font-style: italic;
          }
        }
      }

      .notice-item {
        margin-top: 16rpx;
        padding: 20rpx;
        background: rgba(255, 153, 0, 0.05);
        border-radius: 12rpx;
        display: flex;
        align-items: flex-start;
        
        .u-icon {
          margin-top: 4rpx;
          flex-shrink: 0;
          margin-right: 12rpx;
        }
        
        .notice-content {
          flex: 1;
          
          .notice-label {
            display: block;
            font-size: 24rpx;
            color: #ff9900;
            font-weight: 500;
            margin-bottom: 8rpx;
          }
          
          .notice-text {
            display: block;
            font-size: 26rpx;
            color: #666;
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
        
        &:active {
          background: rgba(255, 153, 0, 0.08);
        }
      }
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 0 4rpx;
    
    .title {
      font-size: 28rpx;
      color: #333;
      font-weight: 600;
      margin-left: 12rpx;
    }
  }
  
  .address-info {
    .address-content {
      background: #f9f9f9;
      border-radius: 12rpx;
      padding: 24rpx;
      
      .location-info {
        .address-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20rpx;
        }
        .distance-tag {
          display: inline-flex;
          align-items: center;
          background: rgba(255, 107, 107, 0.1);
          padding: 6rpx 16rpx;
          border-radius: 24rpx;
          .u-icon {
            margin-right: 6rpx;
          }
          text {
            font-size: 24rpx;
            color: #ff6b6b;
            font-weight: 500;
          }
        }
        .nav-btn {
          display: flex;
          align-items: center;
          padding: 6rpx 16rpx;
          background: #fff;
          border-radius: 24rpx;
          border: 1rpx solid rgba(255, 107, 107, 0.2);
          .u-icon {
            margin-right: 4rpx;
          }
          text {
            font-size: 24rpx;
            color: #ff6b6b;
            font-weight: 500;
          }
          &:active {
            opacity: 0.8;
            transform: scale(0.98);
          }
        }
      }
      .address-detail {
        display: flex;
        align-items: flex-start;
        background: #fff;
        padding: 16rpx;
        border-radius: 12rpx;
        .u-icon {
          margin-right: 8rpx;
          margin-top: 4rpx;
        }
        .address-text {
          flex: 1;
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      }
    }
  }
  
  .qualification {
    .qual-content {
      position: relative;
      .qual-scroll {
        width: 100%;
        white-space: nowrap;
        padding: 12rpx 0;
        
        .qual-item {
          display: inline-block;
          width: 240rpx;
          margin-right: 20rpx;
          position: relative;
          
          &:last-child {
            margin-right: 0;
          }
          
          .qual-image {
            width: 100%;
            height: 320rpx;
            border-radius: 12rpx;
            background: #f8f8f8;
          }
          
          .qual-tips {
            position: absolute;
            left: 50%;
            bottom: 16rpx;
            transform: translateX(-50%);
            font-size: 24rpx;
            color: #fff;
            background: rgba(0, 0, 0, 0.6);
            padding: 4rpx 16rpx;
            border-radius: 20rpx;
            white-space: nowrap;
          }
          
          &:active {
            opacity: 0.9;
            transform: scale(0.98);
          }
        }
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.08);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  
  .contact {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12rpx 32rpx;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1rpx;
      height: 32rpx;
      background: rgba(0, 0, 0, 0.1);
    }
    
    text {
      font-size: 24rpx;
      color: #666;
      margin-top: 4rpx;
    }
  }
  
  .order-btn {
    flex: 1;
    height: 88rpx;
    margin-left: 32rpx;
    background: linear-gradient(135deg, #8cd548 0%, #7bc438 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 16rpx rgba(140, 213, 72, 0.25);
    
    .cart-icon {
      margin-right: 8rpx;
    }
    
    text {
      font-size: 32rpx;
      color: #fff;
      font-weight: 500;
    }
    
    &:active {
      transform: scale(0.98);
      box-shadow: 0 2rpx 8rpx rgba(140, 213, 72, 0.2);
    }
  }
}
</style> 