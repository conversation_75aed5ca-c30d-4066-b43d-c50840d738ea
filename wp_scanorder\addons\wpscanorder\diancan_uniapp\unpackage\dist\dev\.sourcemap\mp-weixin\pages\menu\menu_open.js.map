{"version": 3, "sources": ["webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu_open.vue?d63e", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu_open.vue?af6b", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu_open.vue?51d7", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu_open.vue?5019", "uni-app:///pages/menu/menu_open.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu_open.vue?768b", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu_open.vue?e04d"], "names": ["props", "product", "type", "required", "data", "specGroups", "quantity", "selectedAnimation", "groupIndex", "itemIndex", "created", "group", "console", "computed", "currentPrice", "selectedSpecsSummary", "name", "value", "methods", "handleClose", "handleSpecSelect", "setTimeout", "decreaseQuantity", "increaseQuantity", "handleAddToCart", "id", "price", "image", "count", "totalPrice", "spec_type", "specs", "props_text"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuFtrB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;QACA;;QAEA;QACA;UACA;YACA;YACAC;cAAA;YAAA;YACA;YACAA;UACA;QACA;MACA;QACA;QACA;MACA;;MAEA;MACA;IACA;MACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;MACA;QACA;UAAA;QAAA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;UACAC;UACAC;QACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACAT;QAAA;MAAA;MACA;MACAA;;MAEA;MACA;QACAH;QACAC;MACA;;MAEA;MACAY;QACA;UACAb;UACAC;QACA;MACA;IACA;IAEA;IACAa;MACAV;MACA;MACA;IACA;IAEA;IACAW;MACAX;MACA;IACA;IAEA;IACAY;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAR;UACAC;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACAL;;MAEA;MACA;QACAa;QACAT;QACAU;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;;MAEA;MACApB;;MAEA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAAqxC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACAzyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/menu/menu_open.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./menu_open.vue?vue&type=template&id=483f7033&scoped=true&\"\nvar renderjs\nimport script from \"./menu_open.vue?vue&type=script&lang=js&\"\nexport * from \"./menu_open.vue?vue&type=script&lang=js&\"\nimport style0 from \"./menu_open.vue?vue&type=style&index=0&id=483f7033&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"483f7033\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/menu/menu_open.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu_open.vue?vue&type=template&id=483f7033&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.selectedSpecsSummary.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu_open.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu_open.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"spec-popup\" @click.stop>\n\t\t<view class=\"mask\" @click=\"handleClose\"></view>\n\t\t<view class=\"content\" @click.stop>\n\t\t\t<!-- 商品基本信息 -->\n\t\t\t<view class=\"product-info\">\n\t\t\t\t<image class=\"product-image\" :src=\"product.image\" mode=\"aspectFill\" />\n\t\t\t\t<view class=\"info\">\n\t\t\t\t\t<text class=\"name\">{{product.name}}</text>\n\t\t\t\t\t<view class=\"price\">\n\t\t\t\t\t\t<text class=\"symbol\">¥</text>\n\t\t\t\t\t\t<text class=\"value\">{{currentPrice}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"close-btn\" @click=\"handleClose\">\n\t\t\t\t\t<text class=\"close-icon\">×</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 规格选择区域 -->\n\t\t\t<scroll-view scroll-y class=\"specs-container\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"spec-group\" \n\t\t\t\t\tv-for=\"(group, groupIndex) in specGroups\" \n\t\t\t\t\t:key=\"groupIndex\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"group-title\">\n\t\t\t\t\t\t<text class=\"group-name\">{{group.name}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"spec-options\">\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tclass=\"spec-item\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'active': item.is_default,\n\t\t\t\t\t\t\t\t'animate-select': selectedAnimation.groupIndex === groupIndex && selectedAnimation.itemIndex === itemIndex\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t\tv-for=\"(item, itemIndex) in group.values\"\n\t\t\t\t\t\t\t:key=\"itemIndex\"\n\t\t\t\t\t\t\t@click=\"handleSpecSelect(groupIndex, itemIndex)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text>{{item.value}}</text>\n\t\t\t\t\t\t\t<text class=\"price-tag\" v-if=\"group.name === '规格'\">\n\t\t\t\t\t\t\t\t¥{{item.price}}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<!-- 添加已选规格摘要 -->\n\t\t\t\t<view class=\"selected-summary\">\n\t\t\t\t\t<text class=\"summary-title\">已选:</text>\n\t\t\t\t\t<view class=\"summary-content\">\n\t\t\t\t\t\t<text v-for=\"(spec, index) in selectedSpecsSummary\" :key=\"index\" class=\"summary-item\">\n\t\t\t\t\t\t\t{{spec.name}}:{{spec.value}}\n\t\t\t\t\t\t\t<text v-if=\"index < selectedSpecsSummary.length - 1\">、</text>\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t\n\t\t\t<!-- 底部操作区 -->\n\t\t\t<view class=\"bottom-action\">\n\t\t\t\t<view class=\"quantity-control\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"quantity-btn minus-btn\" \n\t\t\t\t\t\t:class=\"{'disabled': quantity <= 1}\"\n\t\t\t\t\t\t@click=\"decreaseQuantity\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text>-</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"number\">{{quantity}}</text>\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"quantity-btn plus-btn\"\n\t\t\t\t\t\t@click=\"increaseQuantity\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text>+</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"add-btn\" @click=\"handleAddToCart\">\n\t\t\t\t\t<text>加入购物车</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tprops: {\n\t\tproduct: {\n\t\t\ttype: Object,\n\t\t\trequired: true\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tspecGroups: [],\n\t\t\tquantity: 1, // 使用本地数据存储数量\n\t\t\tselectedAnimation: {\n\t\t\t\tgroupIndex: -1,\n\t\t\t\titemIndex: -1\n\t\t\t} // 选中动画控制\n\t\t}\n\t},\n\tcreated() {\n\t\t// 深拷贝规格数据,避免直接修改props\n\t\ttry {\n\t\t\tif (this.product && this.product.property) {\n\t\t\t\tthis.specGroups = JSON.parse(JSON.stringify(this.product.property));\n\t\t\t\t\n\t\t\t\t// 初始化每个规格组的第一个选项为默认选中\n\t\t\t\tthis.specGroups.forEach(group => {\n\t\t\t\t\tif (group.values && group.values.length > 0) {\n\t\t\t\t\t\t// 先重置所有选项\n\t\t\t\t\t\tgroup.values.forEach(item => item.is_default = false);\n\t\t\t\t\t\t// 设置第一个为默认选中\n\t\t\t\t\t\tgroup.values[0].is_default = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 如果property不存在，初始化为空数组\n\t\t\t\tthis.specGroups = [];\n\t\t\t}\n\t\t\t\n\t\t\t// 初始化数量\n\t\t\tthis.quantity = this.product.number || 1;\n\t\t} catch (error) {\n\t\t\tconsole.error('解析规格数据错误:', error);\n\t\t\tthis.specGroups = [];\n\t\t\tthis.quantity = 1;\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 计算当前选中规格对应的价格\n\t\tcurrentPrice() {\n\t\t\tconst sizeGroup = this.specGroups.find(group => group.name === '规格')\n\t\t\tif (sizeGroup) {\n\t\t\t\tconst selectedSize = sizeGroup.values.find(item => item.is_default)\n\t\t\t\treturn selectedSize ? selectedSize.price : this.product.price\n\t\t\t}\n\t\t\treturn this.product.price\n\t\t},\n\t\t\n\t\t// 计算已选规格摘要\n\t\tselectedSpecsSummary() {\n\t\t\treturn this.specGroups.map(group => {\n\t\t\t\tconst selected = group.values.find(item => item.is_default);\n\t\t\t\treturn {\n\t\t\t\t\tname: group.name,\n\t\t\t\t\tvalue: selected ? selected.value : ''\n\t\t\t\t};\n\t\t\t}).filter(item => item.value);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 关闭弹窗\n\t\thandleClose() {\n\t\t\tthis.$emit('close')\n\t\t},\n\t\t\n\t\t// 选择规格\n\t\thandleSpecSelect(groupIndex, itemIndex) {\n\t\t\tconst group = this.specGroups[groupIndex]\n\t\t\t// 取消该组其他选项的选中状态\n\t\t\tgroup.values.forEach(item => item.is_default = false)\n\t\t\t// 设置当前选项为选中状态\n\t\t\tgroup.values[itemIndex].is_default = true\n\t\t\t\n\t\t\t// 触发选中动画\n\t\t\tthis.selectedAnimation = {\n\t\t\t\tgroupIndex,\n\t\t\t\titemIndex\n\t\t\t}\n\t\t\t\n\t\t\t// 300ms后重置动画状态\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.selectedAnimation = {\n\t\t\t\t\tgroupIndex: -1,\n\t\t\t\t\titemIndex: -1\n\t\t\t\t}\n\t\t\t}, 300)\n\t\t},\n\t\t\n\t\t// 减少数量\n\t\tdecreaseQuantity() {\n\t\t\tconsole.log('减少数量')\n\t\t\tif (this.quantity <= 1) return\n\t\t\tthis.quantity -= 1\n\t\t},\n\t\t\n\t\t// 增加数量\n\t\tincreaseQuantity() {\n\t\t\tconsole.log('增加数量')\n\t\t\tthis.quantity += 1\n\t\t},\n\t\t\n\t\t// 添加到购物车\n\t\thandleAddToCart() {\n\t\t\t// 获取所有选中的规格\n\t\t\tconst selectedSpecs = this.specGroups.map(group => {\n\t\t\t\tconst selected = group.values.find(item => item.is_default)\n\t\t\t\treturn {\n\t\t\t\t\tname: group.name,\n\t\t\t\t\tvalue: selected ? selected.value : ''\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\t// 将选中的规格格式化为字符串\n\t\t\tconst specText = selectedSpecs.map(spec => `${spec.name}:${spec.value}`).join('，');\n\t\t\tconsole.log('多规格商品选择结果:', specText);\n\t\t\t\n\t\t\t// 构建购物车商品数据\n\t\t\tconst cartItem = {\n\t\t\t\tid: this.product.id,\n\t\t\t\tname: this.product.name,\n\t\t\t\tprice: this.currentPrice,\n\t\t\t\timage: this.product.image,\n\t\t\t\tcount: this.quantity, // 使用本地数量\n\t\t\t\ttotalPrice: this.currentPrice * this.quantity,\n\t\t\t\tspec_type: 'multi',\n\t\t\t\tspecs: specText,\n\t\t\t\tprops_text: specText\n\t\t\t}\n\t\t\t\n\t\t\t// 输出调试信息\n\t\t\tconsole.log('添加到购物车的商品数据:', JSON.stringify(cartItem));\n\t\t\t\n\t\t\t// 发出添加到购物车事件\n\t\t\tthis.$emit('add-to-cart', cartItem);\n\t\t\t\n\t\t\t// 自动关闭弹窗\n\t\t\tthis.handleClose();\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.spec-popup {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tz-index: 999;\n\t\n\t.mask {\n\t\tposition: absolute;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground: rgba(0, 0, 0, 0.6);\n\t}\n\t\n\t.content {\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 1000;\n\t\tbackground: #fff;\n\t\tborder-radius: 32rpx 32rpx 0 0;\n\t\tpadding-bottom: env(safe-area-inset-bottom);\n\t\tbox-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);\n\t\tanimation: slideUp 0.3s ease-out;\n\t\t\n\t\t.product-info {\n\t\t\tpadding: 40rpx 30rpx 30rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: flex-start;\n\t\t\tposition: relative;\n\t\t\tborder-bottom: 1rpx solid #f2f2f2;\n\t\t\tbackground: linear-gradient(to bottom, #f9f9f9, #ffffff);\n\t\t\tborder-radius: 32rpx 32rpx 0 0;\n\t\t\t\n\t\t\t&::after {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 30rpx;\n\t\t\t\tright: 30rpx;\n\t\t\t\theight: 1rpx;\n\t\t\t\tbackground: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));\n\t\t\t}\n\t\t\t\n\t\t\t.product-image {\n\t\t\t\twidth: 160rpx;\n\t\t\t\theight: 160rpx;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tbox-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);\n\t\t\t\tobject-fit: cover;\n\t\t\t\tborder: 2rpx solid #ffffff;\n\t\t\t}\n\t\t\t\n\t\t\t.info {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-left: 30rpx;\n\t\t\t\tpadding-top: 10rpx;\n\t\t\t\t\n\t\t\t\t.name {\n\t\t\t\t\tfont-size: 34rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\tmargin-bottom: 16rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.price {\n\t\t\t\t\tmargin-top: 30rpx;\n\t\t\t\t\tcolor: #ff5722;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: baseline;\n\t\t\t\t\t\n\t\t\t\t\t.symbol {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t.value {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.close-btn {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 20rpx;\n\t\t\t\tright: 20rpx;\n\t\t\t\twidth: 64rpx;\n\t\t\t\theight: 64rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tbackground: rgba(0, 0, 0, 0.05);\n\t\t\t\t\n\t\t\t\t.close-icon {\n\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tline-height: 1;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:active {\n\t\t\t\t\tbackground: rgba(0, 0, 0, 0.1);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.specs-container {\n\t\t\tmax-height: 60vh;\n\t\t\tpadding: 20rpx 20rpx;\n\t\t\toverflow-y: auto;\n\t\t\t\n\t\t\t.spec-group {\n\t\t\t\tmargin-bottom: 30rpx;\n\t\t\t\tpadding: 20rpx;\n\t\t\t\tposition: relative;\n\t\t\t\tbackground: #FFFFFF;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);\n\t\t\t\tborder: 1rpx solid rgba(0, 0, 0, 0.03);\n\t\t\t\toverflow: hidden;\n\t\t\t\t\n\t\t\t\t// 添加左侧装饰条\n\t\t\t\t&::before {\n\t\t\t\t\tcontent: '';\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\twidth: 6rpx;\n\t\t\t\t\tbackground: linear-gradient(to bottom, #8cd548, #6ab52e);\n\t\t\t\t\tborder-radius: 0 3rpx 3rpx 0;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.group-title {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\tpadding-left: 16rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\t\n\t\t\t\t\t.group-name {\n\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 添加标题右侧装饰线\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\theight: 1rpx;\n\t\t\t\t\t\tbackground: linear-gradient(to right, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0.02) 100%);\n\t\t\t\t\t\tmargin-left: 16rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.spec-options {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\tgap: 16rpx;\n\t\t\t\t\tpadding: 0 10rpx;\n\t\t\t\t\t\n\t\t\t\t\t.spec-item {\n\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\tmin-width: 130rpx;\n\t\t\t\t\t\tpadding: 16rpx 24rpx;\n\t\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\t\tbackground: #f8f8f8;\n\t\t\t\t\t\tborder-radius: 100rpx;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\ttransition: all 0.25s;\n\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\tborder: 1rpx solid transparent;\n\t\t\t\t\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 添加轻微的渐变背景\n\t\t\t\t\t\t&::before {\n\t\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\ttop: 0;\n\t\t\t\t\t\t\tleft: 0;\n\t\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\t\theight: 50%;\n\t\t\t\t\t\t\tbackground: linear-gradient(to bottom, rgba(255,255,255,0.5), rgba(255,255,255,0));\n\t\t\t\t\t\t\tborder-radius: 100rpx 100rpx 0 0;\n\t\t\t\t\t\t\tz-index: 1;\n\t\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 文字内容相对定位\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\tz-index: 2;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.active {\n\t\t\t\t\t\t\tbackground: rgba(140, 213, 72, 0.1);\n\t\t\t\t\t\t\tcolor: #8cd548;\n\t\t\t\t\t\t\tborder-color: #8cd548;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tbox-shadow: 0 2rpx 10rpx rgba(140, 213, 72, 0.2);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 活跃状态下的渐变背景\n\t\t\t\t\t\t\t&::before {\n\t\t\t\t\t\t\t\tbackground: linear-gradient(to bottom, rgba(140, 213, 72, 0.2), rgba(140, 213, 72, 0.05));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t.price-tag {\n\t\t\t\t\t\t\t\tcolor: #8cd548;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&.btn-hover {\n\t\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\t\tbackground: #e0e0e0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 选中动画\n\t\t\t\t\t\t&.animate-select {\n\t\t\t\t\t\t\tanimation: pulse 0.3s ease-out;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t.price-tag {\n\t\t\t\t\t\t\tmargin-left: 8rpx;\n\t\t\t\t\t\t\tcolor: #ff5722;\n\t\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tposition: relative;\n\t\t\t\t\t\t\tz-index: 2;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 已选规格摘要\n\t\t\t.selected-summary {\n\t\t\t\tmargin: 20rpx 0 40rpx;\n\t\t\t\tpadding: 20rpx;\n\t\t\t\tbackground: #f9f9f9;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: flex-start;\n\t\t\t\t\n\t\t\t\t.summary-title {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\tmargin-right: 16rpx;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.summary-content {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tline-height: 1.5;\n\t\t\t\t\t\n\t\t\t\t\t.summary-item {\n\t\t\t\t\t\tdisplay: inline;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\t.bottom-action {\n\t\t\tpadding: 20rpx 30rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\tborder-top: 1rpx solid #f2f2f2;\n\t\t\tbackground: #fff;\n\t\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);\n\t\t\tposition: relative;\n\t\t\t\n\t\t\t&::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 30rpx;\n\t\t\t\tright: 30rpx;\n\t\t\t\theight: 1rpx;\n\t\t\t\tbackground: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));\n\t\t\t}\n\t\t\t\n\t\t\t.quantity-control {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tbackground: #f8f8f8;\n\t\t\t\tborder-radius: 100rpx;\n\t\t\t\tpadding: 6rpx;\n\t\t\t\tbox-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.03);\n\t\t\t\t\n\t\t\t\t.quantity-btn {\n\t\t\t\t\twidth: 60rpx;\n\t\t\t\t\theight: 60rpx;\n\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\tbackground: #ffffff;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\ttransition: all 0.2s;\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);\n\t\t\t\t\t\n\t\t\t\t\ttext {\n\t\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&:active {\n\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\tbackground: #f5f5f5;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.btn-hover {\n\t\t\t\t\t\ttransform: scale(0.95);\n\t\t\t\t\t\topacity: 0.8;\n\t\t\t\t\t\tbackground: #f5f5f5;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.disabled {\n\t\t\t\t\t\topacity: 0.5;\n\t\t\t\t\t\tpointer-events: none;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t/* 增加点击区域 */\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\ttop: -20rpx;\n\t\t\t\t\t\tleft: -20rpx;\n\t\t\t\t\t\tright: -20rpx;\n\t\t\t\t\t\tbottom: -20rpx;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.minus-btn {\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.plus-btn {\n\t\t\t\t\t\tbackground: #8cd548;\n\t\t\t\t\t\t\n\t\t\t\t\t\ttext {\n\t\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t&:active {\n\t\t\t\t\t\t\tbackground: #7bc53a;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.number {\n\t\t\t\t\tmargin: 0 24rpx;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tmin-width: 50rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.add-btn {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-left: 30rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\n\t\t\t\tborder-radius: 40rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\ttransition: all 0.2s;\n\t\t\t\tbox-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);\n\t\t\t\tposition: relative;\n\t\t\t\toverflow: hidden;\n\t\t\t\t\n\t\t\t\t// 添加闪光效果\n\t\t\t\t&::before {\n\t\t\t\t\tcontent: '';\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tleft: -100%;\n\t\t\t\t\twidth: 50%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t\tbackground: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);\n\t\t\t\t\ttransform: skewX(-25deg);\n\t\t\t\t\tanimation: shine 3s infinite;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&:active {\n\t\t\t\t\ttransform: scale(0.98);\n\t\t\t\t\topacity: 0.9;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttext {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\tz-index: 2;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n@keyframes slideUp {\n\tfrom {\n\t\ttransform: translateY(100%);\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t}\n}\n\n@keyframes shine {\n\t0% {\n\t\tleft: -100%;\n\t}\n\t20% {\n\t\tleft: 100%;\n\t}\n\t100% {\n\t\tleft: 100%;\n\t}\n}\n\n@keyframes pulse {\n\t0% {\n\t\ttransform: scale(1);\n\t}\n\t50% {\n\t\ttransform: scale(0.95);\n\t}\n\t100% {\n\t\ttransform: scale(1);\n\t}\n}\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu_open.vue?vue&type=style&index=0&id=483f7033&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu_open.vue?vue&type=style&index=0&id=483f7033&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948310122\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}