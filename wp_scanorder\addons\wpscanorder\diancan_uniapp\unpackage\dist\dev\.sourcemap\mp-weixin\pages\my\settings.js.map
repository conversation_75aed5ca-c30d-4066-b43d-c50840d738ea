{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?671a", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?c11e", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?63eb", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?667b", "uni-app:///pages/my/settings.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?1b09", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?f513"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "nick<PERSON><PERSON>", "phone", "avatarUrl", "gender", "birthday", "birthdayOne", "loading", "showAvatarPopup", "showNicknamePopup", "showBirthdayPicker", "tempNickname", "formChanged", "birthdayColumns", "defaultBirthdayIndex", "selected<PERSON>ear", "<PERSON><PERSON><PERSON><PERSON>", "selected<PERSON>ay", "lastYearIndex", "lastMonthIndex", "picker<PERSON><PERSON>", "onLoad", "console", "onReady", "onShow", "onUnload", "clearTimeout", "methods", "goToCopyright", "uni", "url", "handleBack", "title", "content", "success", "formatPhone", "selectGender", "icon", "duration", "showGenderPicker", "itemList", "changeBirthday", "setTimeout", "closeBirthdayPicker", "initBirthdayPicker", "years", "months", "yearIndex", "updateDays", "days", "onPickerChange", "confirmBirthday", "cancelBirthday", "onDatePickerChange", "length", "initDatePicker", "defaultYearIndex", "defaultMonthIndex", "defaultValues", "saveUserProfile", "mask", "requestData", "nickname", "updateRes", "showAvatarOptions", "closeAvatar<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "count", "sizeType", "sourceType", "fail", "onChooseAvatar", "onNicknameInput", "onNicknameBlur", "onGetNickname", "editable", "placeholderText", "res", "onNicknameReview", "wxNickname", "handleNickname", "result", "showManualNicknameInput", "handleWechatNickname", "confirmNickname", "showPrivacySettings", "downloadWechatAvatar", "resolve", "reject", "uploadAvatar", "showToast", "uploadRes", "avatar", "changeName", "changePhone", "username", "saveUserInfo", "handleLogout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmLrrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eAEA;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QAAA;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;;IAEA;IACA;IACA;IACA;EACA;EAEA;EACAC;IACAD;EACA;EAEA;EACAE;IACA;IACA;IACAF;;IAEA;IACA;IACA;EACA;EAEA;EACAG;IACA;IACA;MACAC;MACA;MACAJ;IACA;EACA;EAEAK;IACA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAF;UACAG;UACAC;UACAC;YACA;cACAL;YACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAM;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;;MAEA;MACAP;QACAG;QACAK;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACAV;QACAW;QACAN;UACA;UACA;YACA;UACA;UACA;UAAA,KACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAO;MAAA;MACAnB;;MAEA;MACA;QACA;UACAU;UACAK;QACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACAK;QACA;QACA;;QAEA;QACAA;UACA;UACA;UACApB;UACAA;QACA;MACA;IACA;IAEA;IACAqB;MACArB;MACA;IACA;IAEA;IACAsB;MACA;MACA;QACAlB;MACA;MAEAJ;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACAuB;MACA;;MAEA;MACA;MACA;QACAC;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA,yCACAC,WACA;MAAA;MACA;MAAA,CACA;;MAEA;MACAzB;MACAA;IACA;IAEA;IACA0B;MACA;MACA;MACA1B;;MAEA;MACA;MACA;QACA2B;MACA;MAEA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MAEA5B;;MAEA;MACA;QACA;QACA;QACA;;QAEA;QACA;QACA;MACA;IACA;IAEA;IACA6B;MACA;MACA;MACA;MACA;MAEA;MAEA7B;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACAO;QACAG;QACAK;MACA;IACA;IAEA;IACAe;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;QACA;UAAAC;QAAA;UAAA;QAAA;;QAEA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACAjC;MACA;MACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;;MAEA;MACA;MACA;MACA;QACA;MACA;;MAEA;MACA,wBACAkC,mEACAC,mBACA,EACA;MAEAnC;QACAuB;QACAC;QACAG;QACAS;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA,iCACA9B;kBACAG;kBACAK;gBACA;cAAA;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAR;kBACAG;kBACA4B;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAEA;gBACAC;kBACAC;kBACA1D;kBACAC;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA0D;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAEAlC;kBACAG;kBACAK;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAf;gBACAO;kBACAG;kBACAK;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA2B;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEArC;QACAsC;QACAC;QACAC;QACAnC;UACA;;UAEA;UACAL;YACAG;YACA4B;UACA;;UAEA;UACA;QACA;QACAU;UACAhD;UACAO;YACAG;YACAK;UACA;QACA;MACA;IACA;IAEA;IACAkC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjD;gBACAnB;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA0B;kBACAG;kBACAK;gBACA;cAAA;gBAGA;gBACAR;kBACAG;kBACA4B;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAEA/B;gBACAA;kBACAG;kBACAK;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAR;gBACAP;gBACAO;kBACAG;kBACAK;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAmC;MACAlD;MACA;QACA;QACA;QACA;QACA;QAEAA;MACA;IACA;IAEA;IACAmD;MACAnD;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACAA;QACA;MACA;IACA;IAEA;IACAoD;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACApD;gBAEA;kBACA;kBACAO;oBACAG;oBACA2C;oBACAC;oBACA3C;oBACAC;sBAAA;wBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCAAA,MACA2C;kCAAA;kCAAA;gCAAA;gCACAf;gCAAA,IAEAA;kCAAA;kCAAA;gCAAA;gCAAA;8BAAA;gCAAA,MAIAA;kCAAA;kCAAA;gCAAA;gCAAA;8BAAA;gCAIA;gCACA;;gCAEA;gCACAjC;kCACAG;kCACA4B;gCACA;;gCAEA;gCAAA;gCAAA,OACA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CAEA;sBAAA;wBAAA;sBAAA;sBAAA;oBAAA;kBACA;gBACA;kBACAtC;kBACAO;oBACAG;oBACAK;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAyC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAxD;gBAEA;kBACA;kBACAyD,kCAEA;kBACA;oBACAzD;oBACAyD;oBACAzD;kBACA;oBACAA;kBACA;;kBAEA;kBACA;kBACA;kBACA;kBAEAO;oBACAG;oBACAK;kBACA;gBACA;kBACAf;kBACAO;oBACAG;oBACAK;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA2C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA1D;;gBAEA;gBAAA,IACAwC;kBAAA;kBAAA;gBAAA;gBAAA,kCAEA;cAAA;gBAGA;gBACA;;gBAEA;gBACAjC;kBACAG;kBACAC;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACA2C;gCAAA;gCAAA;8BAAA;8BACA;8BACAhD;gCACAG;gCACA4B;8BACA;;8BAEA;8BAAA;8BAAA,OACA;4BAAA;8BAAAqB;8BACA3D;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAEA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA4D;MAAA;MAAA;MACArD;QACAG;QACAC;QACA0C;QACAC;QACA1C;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,MACA2C;sBAAA;sBAAA;oBAAA;oBACA;oBACAhD;sBACAG;sBACA4B;oBACA;oBAEA;oBAAA;oBAAA,OACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAEA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAuB;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAtB;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAxC;gBACAO;gBAAA,kCACA;cAAA;gBAAA;gBAIAP;;gBAEA;gBACAuC;kBAAAC;gBAAA,GAEA;gBACAxC;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAyC;gBAEA;gBACAzC;;gBAEA;gBAAA,MACAyC;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACAzC;;gBAEA;gBACA;;gBAEA;gBACAO;kBACAG;kBACAK;kBACAC;gBACA;gBAAA,kCAEA;cAAA;gBAEA;gBACAhB;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;;gBAEA;gBACAO;kBACAG;kBACAK;kBACAC;gBACA;gBAAA,kCAEA;cAAA;gBAAA;gBAEA;gBACAT;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwD;MACAxD;QACAG;QACAK;MACA;IACA;IAEA;IACAiD;MACA;QACAzD;UACAC;UACAI;YACA;cACAqD;YACA;cACAC;YACA;UACA;UACAlB;YACAhD;YACAkE;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAxF;gBAAA;gBAAA,OACA;kBAAAyF;gBAAA;cAAA;gBAAA7B;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAEA;kBACAlC;kBACAA;oBACAG;oBACAK;kBACA;gBACA;gBAAA,mCACA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGA;kBACAR;kBACAA;oBACAG;oBACAK;kBACA;gBACA;gBACAf;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAuE;MACA;IACA;IAEA;IACAC;MAAA;MACAjE;QACAG;QACA2C;QACAC;QACA3C;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,MACA2C;sBAAA;sBAAA;oBAAA;oBAAA,IAEA;sBAAA;sBAAA;oBAAA;oBAAA,mCACAhD;sBACAG;sBACAK;oBACA;kBAAA;oBAGAnC,qBAEA;oBAAA,KACA;sBAAA;sBAAA;oBAAA;oBAAA;kBAAA;oBAAA,MACAA;sBAAA;sBAAA;oBAAA;oBAAA;kBAAA;oBAEA;oBACA2B;sBACAG;sBACA4B;oBACA;oBAAA;oBAAA;oBAAA,OAKA;sBAAAmC;oBAAA;kBAAA;oBAAAhC;oBAAA,MAEAA;sBAAA;sBAAA;oBAAA;oBACA;oBACA;oBACA;oBAEAlC;sBACAG;sBACAK;oBACA;oBAAA;oBAAA;kBAAA;oBAAA,MAEA;kBAAA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAGAf;oBACAO;sBACAG;sBACAK;oBACA;kBAAA;oBAAA;oBAEAR;oBACA;oBAAA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAmE;MACAnE;IACA;IAEAoE;MACApE;QACAG;QACAC;QACAC;UACA;YACAL;YACAA;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/iCA;AAAA;AAAA;AAAA;AAAoxC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAxyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/settings.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/settings.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./settings.vue?vue&type=template&id=3176ae3d&scoped=true&\"\nvar renderjs\nimport script from \"./settings.vue?vue&type=script&lang=js&\"\nexport * from \"./settings.vue?vue&type=script&lang=js&\"\nimport style0 from \"./settings.vue?vue&type=style&index=0&id=3176ae3d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3176ae3d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/settings.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=template&id=3176ae3d&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.formatPhone(_vm.userInfo.phone) || \"未绑定\"\n  var g0 = Date.now()\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">个人资料</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主体内容区 -->\r\n    <view class=\"content-container\">\r\n      <!-- 头像区域 -->\r\n      <view class=\"avatar-section\">\r\n        <image class=\"avatar\" :src=\"userInfo.avatarUrl || '/static/my/default-avatar.png'\" mode=\"aspectFill\" />\r\n        <button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" class=\"avatar-button\">\r\n          <view class=\"avatar-overlay\">\r\n            <view class=\"overlay-icon\">\r\n              <u-icon name=\"camera-fill\" color=\"#ffffff\" size=\"24\"></u-icon>\r\n            </view>\r\n          </view>\r\n        </button>\r\n      </view>\r\n      \r\n      <!-- 表单列表 -->\r\n      <view class=\"form-list\">\r\n        <!-- 昵称 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">昵称</view>\r\n          <view class=\"form-content\">\r\n            <input \r\n              class=\"form-input\" \r\n              type=\"nickname\"\r\n              placeholder=\"请输入昵称\" \r\n              :value=\"userInfo.nickName\"\r\n              @nicknamereview=\"onNicknameReview\"\r\n              @input=\"onNicknameInput\"\r\n              @blur=\"onNicknameBlur\"\r\n            />\r\n            <view class=\"form-right-btn\" v-if=\"!userInfo.nickName\">\r\n              <text class=\"btn-text\">设置昵称</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 手机号 -->\r\n        <view class=\"form-item\">\r\n          <view class=\"form-label\">手机</view>\r\n          <view class=\"form-content\">\r\n            <text class=\"form-text\">{{formatPhone(userInfo.phone) || '未绑定'}}</text>\r\n            <view class=\"form-right-btn\" @tap=\"changePhone\" v-show=\"false\">\r\n              <text class=\"btn-text\">修改</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 性别 -->\r\n        <view class=\"form-item\" @tap=\"showGenderPicker\">\r\n          <view class=\"form-label\">性别</view>\r\n          <view class=\"form-content\">\r\n            <text class=\"form-text\">{{userInfo.gender == '1' ? '男' : userInfo.gender == '2' ? '女' : '未设置'}}</text>\r\n            <view class=\"form-right-btn\">\r\n              <text class=\"btn-text\">修改</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 生日 -->\r\n        <view class=\"form-item\" @tap=\"changeBirthday\">\r\n          <view class=\"form-label\">生日</view>\r\n          <view class=\"form-content\">\r\n            <text class=\"form-text\">{{userInfo.birthday || userInfo.birthdayOne || '未设置'}}</text>\r\n            <view class=\"form-right-tip\" v-if=\"userInfo.birthday\">\r\n              <text class=\"tip-text\">(仅支持修改一次)</text>\r\n            </view>\r\n            <view class=\"form-right-btn\" v-else>\r\n              <text class=\"btn-text\">修改</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 隐私提示 -->\r\n      <view class=\"privacy-tip\">\r\n        <text class=\"tip-text\">根据未成年人保护相关法律规定，我们不主动处理14周岁以下未成年人的个人信息。如果您为14周岁以下的用户，请勿填写您的个人资料。</text>\r\n      </view>\r\n      \r\n      <!-- 保存按钮 -->\r\n      <view class=\"save-btn\" @tap=\"saveUserProfile\">\r\n        <text>保存</text>\r\n      </view>\r\n      \r\n      <!-- 关于我们 -->\r\n      <view class=\"about-container\">\r\n        <view class=\"about-btn\" @tap=\"goToCopyright\">\r\n          <text>版权声明</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 退出登录 -->\r\n      <view class=\"logout-container\">\r\n        <view class=\"logout-btn\" @tap=\"handleLogout\">\r\n          <text>退出登录</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 头像选择弹窗 -->\r\n    <u-popup v-model=\"showAvatarPopup\" mode=\"bottom\" border-radius=\"16\">\r\n      <view class=\"avatar-popup\">\r\n        <view class=\"popup-title\">设置头像</view>\r\n        <view class=\"popup-options\">\r\n          <view class=\"popup-option\" @tap=\"chooseAvatar\">\r\n            <u-icon name=\"photo\" size=\"32\" color=\"#333\"></u-icon>\r\n            <text>从相册选择</text>\r\n          </view>\r\n          <view class=\"popup-option\">\r\n            <button open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" class=\"wx-button\">\r\n              <u-icon name=\"weixin-fill\" size=\"32\" color=\"#07c160\"></u-icon>\r\n              <text>使用微信头像</text>\r\n            </button>\r\n          </view>\r\n        </view>\r\n        <view class=\"popup-cancel\" @tap=\"closeAvatarPopup\">取消</view>\r\n      </view>\r\n    </u-popup>\r\n    \r\n    <!-- 生日选择器 - 垂直滚动样式 -->\r\n    <view class=\"birthday-picker-modal\" v-show=\"showBirthdayPicker\" @tap.stop>\r\n      <view class=\"birthday-picker-mask\" @tap.stop=\"closeBirthdayPicker\"></view>\r\n      <view class=\"date-picker-content\" @tap.stop>\r\n        <view class=\"date-picker-header\">\r\n          <text class=\"date-picker-cancel\" @tap=\"closeBirthdayPicker\">取消</text>\r\n          <text class=\"date-picker-title\">选择生日</text>\r\n          <text class=\"date-picker-confirm\" @tap=\"confirmBirthday\">确认</text>\r\n        </view>\r\n        \r\n        <!-- 使用原生滚动选择器风格 -->\r\n        <view class=\"picker-view-container\">\r\n          <picker-view\r\n            class=\"date-picker-view\"\r\n            :value=\"defaultBirthdayIndex\"\r\n            @change=\"onPickerChange\"\r\n            :indicator-style=\"'height: 50px;'\"\r\n            :immediate-change=\"true\"\r\n            :key=\"Date.now()\"\r\n          >\r\n            <!-- 年份列 -->\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(year, index) in birthdayColumns[0]\" :key=\"index\">\r\n                {{year}}\r\n              </view>\r\n            </picker-view-column>\r\n\r\n            <!-- 月份列 -->\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(month, index) in birthdayColumns[1]\" :key=\"index\">\r\n                {{month}}\r\n              </view>\r\n            </picker-view-column>\r\n\r\n            <!-- 日期列 -->\r\n            <picker-view-column>\r\n              <view class=\"picker-item\" v-for=\"(day, index) in birthdayColumns[2]\" :key=\"index\">\r\n                {{day}}\r\n              </view>\r\n            </picker-view-column>\r\n          </picker-view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { updateUserProfile, uploadImage } from '@/api/user.js';\r\n// 移除单独导入的uview组件，因为已经全局引入\r\n\r\nexport default {\r\n  // 不需要单独注册组件，uView已全局注册\r\n  data() {\r\n    return {\r\n      userInfo: {\r\n        nickName: '',\r\n        phone: '',\r\n        avatarUrl: '',\r\n        gender: '', // 1: 男性, 2: 女性\r\n        birthday: '',\r\n        birthdayOne: '',\r\n      },\r\n      loading: false,\r\n      showAvatarPopup: false,\r\n      showNicknamePopup: false,\r\n      showBirthdayPicker: false, // 控制生日选择器显示\r\n      tempNickname: '',\r\n      formChanged: false, // 用于标记表单是否有修改\r\n      birthdayColumns: [[], [], []], // 年月日三列数据\r\n      defaultBirthdayIndex: [0, 0, 0], // 默认选中索引\r\n      selectedYear: '', // 当前选中的年\r\n      selectedMonth: '', // 当前选中的月\r\n      selectedDay: '', // 当前选中的日\r\n      lastYearIndex: 0, // 上次选择的年份索引\r\n      lastMonthIndex: 0, // 上次选择的月份索引\r\n      pickerKey: 0 // 用于强制重新渲染\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    this.userInfo = uni.getStorageSync('userInfo') || {}\r\n    console.log('用户信息加载完成:', this.userInfo);\r\n    \r\n    // 初始化临时昵称\r\n    this.tempNickname = this.userInfo.nickName || ''\r\n    this.initBirthdayPicker();\r\n    this.showBirthdayPicker = false;\r\n  },\r\n  \r\n  // 页面准备完成\r\n  onReady() {\r\n    console.log('页面准备完成');\r\n  },\r\n  \r\n  // 确保页面每次显示时重新获取数据\r\n  onShow() {\r\n    // 刷新用户信息\r\n    this.userInfo = uni.getStorageSync('userInfo') || {};\r\n    console.log('onShow 刷新用户信息:', this.userInfo);\r\n    \r\n    // 更新临时昵称并确保弹窗关闭\r\n    this.tempNickname = this.userInfo.nickName || '';\r\n    this.showNicknamePopup = false;\r\n  },\r\n  \r\n  // 页面卸载时清理资源\r\n  onUnload() {\r\n    // 清除昵称监听计时器\r\n    if (this.nicknameTimer) {\r\n      clearTimeout(this.nicknameTimer);\r\n      this.nicknameTimer = null;\r\n      console.log('页面卸载，清理昵称监听');\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 跳转到版权声明页面\r\n    goToCopyright() {\r\n      uni.navigateTo({\r\n        url: '/pages/copyright/copyright'\r\n      })\r\n    },\r\n\r\n    // 处理返回按钮\r\n    handleBack() {\r\n      // 如果表单有修改且未保存，显示提示\r\n      if (this.formChanged) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '您有未保存的修改，确定要离开吗？',\r\n          success: (res) => {\r\n            if (res.confirm) {\r\n              uni.navigateBack()\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        uni.navigateBack()\r\n      }\r\n    },\r\n    \r\n    // 格式化手机号码（中间部分用*替代）\r\n    formatPhone(phone) {\r\n      if (!phone) return '';\r\n      if (phone.length !== 11) return phone;\r\n      return phone.substring(0, 3) + '******' + phone.substring(9);\r\n    },\r\n    \r\n    // 选择性别\r\n    selectGender(gender) {\r\n      if (this.userInfo.gender === gender) return;\r\n      \r\n      this.userInfo.gender = gender;\r\n      this.formChanged = true;\r\n      \r\n      // 显示选择成功提示\r\n      uni.showToast({\r\n        title: gender === '1' ? '已选择男' : '已选择女',\r\n        icon: 'success',\r\n        duration: 1500\r\n      });\r\n    },\r\n    \r\n    // 性别选择弹窗\r\n    showGenderPicker() {\r\n      uni.showActionSheet({\r\n        itemList: ['男', '女'],\r\n        success: (res) => {\r\n          // 选择男性\r\n          if (res.tapIndex === 0) {\r\n            this.selectGender('1');\r\n          }\r\n          // 选择女性\r\n          else if (res.tapIndex === 1) {\r\n            this.selectGender('2');\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 修改生日\r\n    changeBirthday() {\r\n      console.log('触发生日修改方法');\r\n      \r\n      // 如果已经设置过生日，提示只能修改一次\r\n      if (this.userInfo.birthday) {\r\n        return uni.showToast({\r\n          title: '生日信息只能修改一次',\r\n          icon: 'none'\r\n        });\r\n      }\r\n\r\n      // 先清空默认值，避免旧值干扰\r\n      this.defaultBirthdayIndex = [0, 0, 0];\r\n      \r\n      // 延迟显示选择器\r\n      this.showBirthdayPicker = true;\r\n      \r\n      // 使用setTimeout延迟初始化，确保选择器DOM已渲染\r\n      setTimeout(() => {\r\n        // 初始化日期数据\r\n        this.initBirthdayPicker();\r\n        \r\n        // 延迟强制更新，确保索引值生效\r\n        setTimeout(() => {\r\n          // 强制更新视图\r\n          this.$forceUpdate();\r\n          console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);\r\n          console.log('日期选择器状态:', this.showBirthdayPicker, '索引值:', this.defaultBirthdayIndex);\r\n        }, 100);\r\n      }, 200);\r\n    },\r\n    \r\n    // 关闭生日选择器\r\n    closeBirthdayPicker() {\r\n      console.log('关闭日期选择器');\r\n      this.showBirthdayPicker = false;\r\n    },\r\n    \r\n    // 初始化日期选择器\r\n    initBirthdayPicker() {\r\n      // 清除可能的延迟任务\r\n      if (this.pickerTimer) {\r\n        clearTimeout(this.pickerTimer);\r\n      }\r\n      \r\n      console.log('初始化日期选择器');\r\n      const now = new Date();\r\n      const currentYear = now.getFullYear();\r\n      \r\n      // 直接指定2007年为默认年份\r\n      const defaultYear = 2007;\r\n      \r\n      // 年份范围：从1900年开始\r\n      const years = [];\r\n      for (let i = 1900; i <= currentYear; i++) {\r\n        years.push(i + '年');\r\n      }\r\n      \r\n      // 月份：1-12月\r\n      const months = [];\r\n      for (let i = 1; i <= 12; i++) {\r\n        months.push((i < 10 ? '0' + i : i) + '月');\r\n      }\r\n      \r\n      // 初始化日期数据（默认31天，后续会更新）\r\n      this.selectedYear = defaultYear;\r\n      this.selectedMonth = 1;\r\n      this.selectedDay = 1;\r\n      \r\n      // 根据选中的年月计算天数\r\n      const days = this.updateDays(this.selectedYear, this.selectedMonth);\r\n      \r\n      // 更新选择器数据\r\n      this.birthdayColumns = [years, months, days];\r\n      \r\n      // 计算默认年份索引 - 2007与1900的差值\r\n      const yearIndex = defaultYear - 1900;\r\n      \r\n      // 设置索引值 - 此处非常重要，必须使用新数组\r\n      this.$set(this, 'defaultBirthdayIndex', [\r\n        yearIndex,\r\n        0, // 1月\r\n        0  // 1日\r\n      ]);\r\n      \r\n      // 输出调试信息\r\n      console.log(`初始化日期选择器完成: 当前年份索引=${yearIndex}, 选中年份=${defaultYear}年`);\r\n      console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);\r\n    },\r\n    \r\n    // 更新日期数组\r\n    updateDays(year, month) {\r\n      // 计算指定年月的天数\r\n      const daysInMonth = new Date(year, month, 0).getDate();\r\n      console.log(`计算${year}年${month}月的天数:`, daysInMonth);\r\n      \r\n      // 更新日期数组\r\n      const days = [];\r\n      for (let i = 1; i <= daysInMonth; i++) {\r\n        days.push((i < 10 ? '0' + i : i) + '日');\r\n      }\r\n      \r\n      return days;\r\n    },\r\n    \r\n    // 处理picker-view整体变化\r\n    onPickerChange(e) {\r\n      const values = e.detail.value;\r\n      \r\n      // 获取年月日索引\r\n      const yearIndex = values[0];\r\n      const monthIndex = values[1];\r\n      const dayIndex = values[2];\r\n      \r\n      // 更新默认选中索引\r\n      this.defaultBirthdayIndex = values;\r\n      \r\n      // 获取不带单位的值\r\n      const yearText = this.birthdayColumns[0][yearIndex];\r\n      const monthText = this.birthdayColumns[1][monthIndex];\r\n      const dayText = this.birthdayColumns[2][dayIndex];\r\n      \r\n      // 获取数值\r\n      this.selectedYear = parseInt(yearText.replace('年', ''));\r\n      this.selectedMonth = parseInt(monthText.replace('月', ''));\r\n      this.selectedDay = parseInt(dayText.replace('日', ''));\r\n      \r\n      console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);\r\n      \r\n      // 如果年份或月份变化了，需要更新日期数组\r\n      if (this.lastYearIndex !== yearIndex || this.lastMonthIndex !== monthIndex) {\r\n        // 更新日期列\r\n        const newDays = this.updateDays(this.selectedYear, this.selectedMonth);\r\n        this.birthdayColumns[2] = newDays;\r\n        \r\n        // 记录当前选中的年月索引\r\n        this.lastYearIndex = yearIndex;\r\n        this.lastMonthIndex = monthIndex;\r\n      }\r\n    },\r\n    \r\n    // 确认生日选择\r\n    confirmBirthday() {\r\n      // 拼接生日\r\n      const year = this.selectedYear;\r\n      const month = String(this.selectedMonth).padStart(2, '0');\r\n      const day = String(this.selectedDay).padStart(2, '0');\r\n      \r\n      const birthday = `${year}-${month}-${day}`;\r\n      \r\n      console.log('确认选择生日:', birthday);\r\n      \r\n      // 设置生日\r\n      this.userInfo.birthdayOne = birthday;\r\n      this.formChanged = true;\r\n      \r\n      // 关闭选择器\r\n      this.showBirthdayPicker = false;\r\n      \r\n      // 显示成功提示\r\n      uni.showToast({\r\n        title: '生日设置成功',\r\n        icon: 'success'\r\n      });\r\n    },\r\n    \r\n    // 取消日期选择\r\n    cancelBirthday() {\r\n      this.showBirthdayPicker = false;\r\n    },\r\n    \r\n    // 处理日期选择\r\n    onDatePickerChange(e) {\r\n      const values = e.detail.value;\r\n      this.datePickerValue = values;\r\n      \r\n      // 当年份或月份变化时，需要更新日期数组\r\n      const year = this.years[values[0]];\r\n      const month = this.months[values[1]];\r\n      \r\n      // 更新日期数组\r\n      const daysInMonth = new Date(year, month, 0).getDate();\r\n      if (this.days.length !== daysInMonth) {\r\n        this.days = Array.from({ length: daysInMonth }, (_, i) => i + 1);\r\n        \r\n        // 如果当前选中的日期超出了这个月的最大天数，则调整\r\n        if (values[2] >= daysInMonth) {\r\n          this.datePickerValue[2] = daysInMonth - 1;\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 初始化日期选择器数据\r\n    initDatePicker() {\r\n      console.log('开始初始化日期选择器数据');\r\n      const now = new Date();\r\n      const currentYear = now.getFullYear();\r\n      const defaultYear = currentYear - 18; // 默认18岁\r\n      \r\n      // 创建年份数组 (1900年至今)\r\n      this.years = [];\r\n      for (let i = 1900; i <= currentYear; i++) {\r\n        this.years.push(i);\r\n      }\r\n      \r\n      // 创建月份数组\r\n      this.months = [];\r\n      for (let i = 1; i <= 12; i++) {\r\n        this.months.push(i);\r\n      }\r\n      \r\n      // 默认选中的年月\r\n      const defaultYearIndex = this.years.findIndex(y => y === defaultYear);\r\n      const defaultMonthIndex = 0; // 默认1月\r\n      \r\n      // 创建日期数组 (默认31天，后续会根据选择的年月动态调整)\r\n      this.days = [];\r\n      const daysInMonth = new Date(defaultYear, 1, 0).getDate();\r\n      for (let i = 1; i <= daysInMonth; i++) {\r\n        this.days.push(i);\r\n      }\r\n      \r\n      // 初始日期值\r\n      this.datePickerValue = [\r\n        defaultYearIndex > -1 ? defaultYearIndex : this.years.length - 18,\r\n        defaultMonthIndex,\r\n        0\r\n      ];\r\n      \r\n      console.log('日期选择器数据初始化完成:', {\r\n        years: this.years.length,\r\n        months: this.months.length,\r\n        days: this.days.length,\r\n        defaultValues: this.datePickerValue\r\n      });\r\n    },\r\n    \r\n    // 保存用户资料\r\n    async saveUserProfile() {\r\n      if (!this.formChanged) {\r\n        return uni.showToast({\r\n          title: '未做任何修改',\r\n          icon: 'none'\r\n        });\r\n      }\r\n      \r\n      if (this.loading) return;\r\n      \r\n      this.loading = true;\r\n      uni.showLoading({\r\n        title: '保存中...',\r\n        mask: true\r\n      });\r\n      if (this.userInfo.birthdayOne) {\r\n        this.userInfo.birthday = this.userInfo.birthdayOne;\r\n      }\r\n      try {\r\n        // 构建请求数据\r\n        const requestData = {\r\n          nickname: this.userInfo.nickName,\r\n          gender: this.userInfo.gender,\r\n          birthday: this.userInfo.birthday\r\n        };\r\n        \r\n        // 调用API更新用户资料\r\n        const updateRes = await updateUserProfile(requestData);\r\n        \r\n        if (updateRes && updateRes.code === 1) {\r\n          // 更新成功\r\n          this.saveUserInfo();\r\n          this.formChanged = false;\r\n          \r\n          uni.showToast({\r\n            title: '保存成功',\r\n            icon: 'none'\r\n          });\r\n        } else {\r\n          throw new Error((updateRes && updateRes.msg) ? updateRes.msg : '保存失败');\r\n        }\r\n      } catch (e) {\r\n        console.error('保存用户资料失败:', e);\r\n        uni.showToast({\r\n          title: e.message || '保存失败',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        // uni.hideLoading();\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 显示头像选项弹窗\r\n    showAvatarOptions() {\r\n      this.showAvatarPopup = true\r\n    },\r\n    \r\n    // 关闭头像选项弹窗\r\n    closeAvatarPopup() {\r\n      this.showAvatarPopup = false\r\n    },\r\n    \r\n    // 选择头像\r\n    chooseAvatar() {\r\n      this.showAvatarPopup = false\r\n      \r\n      uni.chooseImage({\r\n        count: 1,\r\n        sizeType: ['compressed'],\r\n        sourceType: ['album', 'camera'],\r\n        success: (res) => {\r\n          const tempFilePath = res.tempFilePaths[0]\r\n          \r\n          // 显示上传中提示\r\n          uni.showLoading({\r\n            title: '上传中...',\r\n            mask: true\r\n          })\r\n          \r\n          // 先上传图片到服务器\r\n          this.uploadAvatar(tempFilePath)\r\n        },\r\n        fail: (err) => {\r\n          console.error('选择图片失败:', err)\r\n          uni.showToast({\r\n            title: '选择失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 处理微信头像选择回调\r\n    async onChooseAvatar(e) {\r\n      console.log('选择头像回调:', e)\r\n      const { avatarUrl } = e.detail\r\n      \r\n      if (!avatarUrl) {\r\n        return uni.showToast({\r\n          title: '获取头像失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n      \r\n      // 显示上传中提示\r\n      uni.showLoading({\r\n        title: '上传中...',\r\n        mask: true\r\n      })\r\n      \r\n      try {\r\n        // 上传头像到服务器\r\n        await this.uploadAvatar(avatarUrl)\r\n        \r\n        uni.hideLoading()\r\n        uni.showToast({\r\n          title: '头像更新成功',\r\n          icon: 'none'\r\n        })\r\n      } catch (e) {\r\n        uni.hideLoading()\r\n        console.error('上传头像失败:', e)\r\n        uni.showToast({\r\n          title: e.message || '头像更新失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n    // 处理昵称输入\r\n    onNicknameInput(e) {\r\n      console.log('昵称输入事件:', e)\r\n      if (e.detail && e.detail.value) {\r\n        const nickname = e.detail.value.trim()\r\n        this.userInfo.nickName = nickname\r\n        this.tempNickname = nickname\r\n        this.formChanged = true\r\n        \r\n        console.log('昵称已更新:', nickname)\r\n      }\r\n    },\r\n    \r\n    // 处理昵称输入框失去焦点\r\n    onNicknameBlur(e) {\r\n      console.log('昵称输入框失去焦点:', e);\r\n      // 如果有值，确保更新\r\n      if (e.detail && e.detail.value) {\r\n        const nickname = e.detail.value.trim();\r\n        if (nickname && nickname !== this.userInfo.nickName) {\r\n          this.userInfo.nickName = nickname;\r\n          this.tempNickname = nickname;\r\n          this.formChanged = true;\r\n          console.log('失去焦点时更新昵称:', nickname);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 处理微信获取头像事件后询问用户昵称\r\n    async onGetNickname(e) {\r\n      console.log('获取头像事件:', e);\r\n      \r\n      try {\r\n        // 选择头像后，显示输入昵称对话框\r\n        uni.showModal({\r\n          title: '设置昵称',\r\n          editable: true,\r\n          placeholderText: '请输入昵称',\r\n          content: this.userInfo.nickName || '',\r\n          success: async (res) => {\r\n            if (res.confirm && res.content) {\r\n              const nickname = res.content.trim();\r\n              \r\n              if (!nickname) {\r\n                return;\r\n              }\r\n              \r\n              if (nickname === this.userInfo.nickName) {\r\n                return;\r\n              }\r\n              \r\n              // 设置临时昵称\r\n              this.tempNickname = nickname;\r\n              \r\n              // 显示提示\r\n              uni.showLoading({\r\n                title: '正在保存昵称...',\r\n                mask: true\r\n              });\r\n              \r\n              // 自动保存\r\n              await this.confirmNickname();\r\n            }\r\n          }\r\n        });\r\n      } catch (err) {\r\n        console.error('处理昵称获取事件出错:', err);\r\n        uni.showToast({\r\n          title: '获取昵称失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 处理微信昵称审核事件 - 这是使用nickname组件的关键方法\r\n    async onNicknameReview(e) {\r\n      console.log('昵称审核事件详情:', JSON.stringify(e));\r\n      \r\n      try {\r\n        // 获取微信昵称\r\n        let wxNickname = this.tempNickname;\r\n        \r\n        // 若未获取到昵称，记录错误\r\n        if (!wxNickname) {\r\n          console.error('未能获取到微信昵称，完整事件对象:', e);\r\n          wxNickname = \"微信用户\"; // 默认昵称\r\n          console.log('未获取到微信昵称，使用默认值');\r\n        } else {\r\n          console.log('成功获取到微信昵称:', wxNickname);\r\n        }\r\n        \r\n        // 更新昵称和表单状态\r\n        this.userInfo.nickName = wxNickname;\r\n        this.tempNickname = wxNickname;\r\n        this.formChanged = true;\r\n        \r\n        uni.showToast({\r\n          title: '已获取微信昵称',\r\n          icon: 'none'\r\n        });\r\n      } catch (err) {\r\n        console.error('处理昵称审核事件出错:', err);\r\n        uni.showToast({\r\n          title: '获取昵称失败',\r\n          icon: 'none'\r\n        });\r\n      }\r\n    },\r\n    \r\n    // 这个方法不再需要，我们直接在onNicknameReview中处理了\r\n    async handleNickname(nickname) {\r\n      console.log('处理获取到的昵称:', nickname);\r\n      \r\n      // 如果没有获取到昵称，则尝试使用直接输入\r\n      if (!nickname) {\r\n        // 如果没有获取到昵称，显示手动输入框\r\n        return this.showManualNicknameInput();\r\n      }\r\n      \r\n      // 设置临时昵称\r\n      this.tempNickname = nickname;\r\n      \r\n      // 显示确认对话框\r\n      uni.showModal({\r\n        title: '确认修改昵称',\r\n        content: `是否将昵称修改为\"${nickname}\"？`,\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            // 显示提示\r\n            uni.showLoading({\r\n              title: '正在保存昵称...',\r\n              mask: true\r\n            });\r\n            \r\n            // 保存昵称\r\n            const result = await this.confirmNickname();\r\n            console.log('保存昵称结果:', result);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 显示手动输入昵称的对话框\r\n    showManualNicknameInput(message = '请输入您的昵称') {\r\n      uni.showModal({\r\n        title: '设置昵称',\r\n        content: message,\r\n        editable: true,\r\n        placeholderText: '请输入昵称',\r\n        success: async (res) => {\r\n          if (res.confirm && res.content) {\r\n            // 显示提示并保存用户输入的昵称\r\n            uni.showLoading({\r\n              title: '正在保存昵称...',\r\n              mask: true\r\n            });\r\n            \r\n            this.tempNickname = res.content.trim();\r\n            await this.confirmNickname();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 处理点击昵称项\r\n    handleWechatNickname() {\r\n      // 显示昵称输入弹窗\r\n      this.showNicknamePopup = true;\r\n    },\r\n    \r\n    // 确认修改昵称\r\n    async confirmNickname() {\r\n      const nickname = this.tempNickname\r\n      \r\n      if (!nickname || nickname === this.userInfo.nickName) {\r\n        console.log('昵称为空或未更改，不执行保存操作');\r\n        uni.hideLoading();\r\n        return false\r\n      }\r\n      \r\n      try {\r\n        console.log('准备提交昵称到服务器:', nickname);\r\n        \r\n        // 构造请求参数\r\n        const requestData = { nickname };\r\n        \r\n        // 显示详细的请求参数，便于调试\r\n        console.log('请求参数:', JSON.stringify(requestData));\r\n        \r\n        // 直接调用API更新昵称\r\n        const updateRes = await updateUserProfile(requestData);\r\n        \r\n        // 记录响应详情\r\n        console.log('昵称更新接口响应:', JSON.stringify(updateRes));\r\n        \r\n        // 检查响应状态\r\n        if (updateRes && updateRes.code === 1) {\r\n          // 更新本地用户信息\r\n          this.userInfo.nickName = nickname;\r\n          this.saveUserInfo();\r\n          console.log('本地用户信息已更新:', JSON.stringify(this.userInfo));\r\n          \r\n          // 更新UI显示\r\n          this.$forceUpdate();\r\n          \r\n          // 显示成功提示\r\n          uni.showToast({\r\n            title: '昵称更新成功',\r\n            icon: 'success',\r\n            duration: 2000\r\n          });\r\n          \r\n          return true;\r\n        } else {\r\n          // 详细记录错误信息\r\n          console.error('接口返回错误:', updateRes);\r\n          throw new Error((updateRes && updateRes.msg) ? updateRes.msg : '昵称更新失败');\r\n        }\r\n      } catch (e) {\r\n        console.error('更新昵称失败:', e);\r\n        \r\n        // 显示错误提示\r\n        uni.showToast({\r\n          title: e.message || '昵称更新失败',\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n        \r\n        return false;\r\n      } finally {\r\n        // 确保无论成功失败都隐藏加载提示\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n    // 显示隐私设置\r\n    showPrivacySettings() {\r\n      uni.showToast({\r\n        title: '隐私设置功能开发中',\r\n        icon: 'none'\r\n      })\r\n    },\r\n    \r\n    // 下载微信头像\r\n    downloadWechatAvatar(avatarUrl) {\r\n      return new Promise((resolve, reject) => {\r\n        uni.downloadFile({\r\n          url: avatarUrl,\r\n          success: (res) => {\r\n            if (res.statusCode === 200) {\r\n              resolve(res.tempFilePath)\r\n            } else {\r\n              reject(new Error('下载头像失败'))\r\n            }\r\n          },\r\n          fail: (err) => {\r\n            console.error('下载微信头像失败:', err)\r\n            reject(err)\r\n          }\r\n        })\r\n      })\r\n    },\r\n    \r\n    // 上传头像\r\n    async uploadAvatar(filePath, showToast = true) {\r\n      try {\r\n        // 上传图片获取URL\r\n        const uploadRes = await uploadImage(filePath)\r\n        \r\n        if (uploadRes.code === 1 && uploadRes.data && uploadRes.data.url) {\r\n          // 获取到图片URL后，调用修改个人信息接口\r\n          const avatarUrl = uploadRes.data.url\r\n          const updateRes = await updateUserProfile({ avatar: avatarUrl })\r\n          \r\n          if (updateRes.code === 1) {\r\n            // 更新用户信息中的头像\r\n            this.userInfo.avatarUrl = avatarUrl\r\n            this.saveUserInfo()\r\n            \r\n            if (showToast) {\r\n              uni.hideLoading()\r\n              uni.showToast({\r\n                title: '头像更新成功',\r\n                icon: 'none'\r\n              })\r\n            }\r\n            return true\r\n          } else {\r\n            throw new Error(updateRes.msg || '保存头像失败')\r\n          }\r\n        } else {\r\n          throw new Error(uploadRes.msg || '图片上传失败')\r\n        }\r\n      } catch (e) {\r\n        if (showToast) {\r\n          uni.hideLoading()\r\n          uni.showToast({\r\n            title: e.msg || e.message || '上传失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n        console.error('上传头像失败:', e)\r\n        throw e\r\n      }\r\n    },\r\n\r\n    // 此方法已替换为toggleNicknameEdit\r\n    changeName() {\r\n      this.toggleNicknameEdit()\r\n    },\r\n\r\n    // 修改手机号\r\n    changePhone() {\r\n      uni.showModal({\r\n        title: '修改手机号',\r\n        editable: true,\r\n        placeholderText: '请输入手机号',\r\n        content: this.userInfo.phone || '',\r\n        success: async (res) => {\r\n          if(res.confirm && res.content) {\r\n            // 手机号格式验证\r\n            if(!/^1[3-9]\\d{9}$/.test(res.content)) {\r\n              return uni.showToast({\r\n                title: '手机号格式不正确',\r\n                icon: 'none'\r\n              })\r\n            }\r\n            \r\n            const phone = res.content\r\n            \r\n            // 避免重复提交\r\n            if (this.loading) return\r\n            if (phone === this.userInfo.phone) return\r\n              \r\n            this.loading = true\r\n            uni.showLoading({\r\n              title: '保存中...',\r\n              mask: true\r\n            })\r\n            \r\n            try {\r\n              // 接口文档中没有明确提到手机号修改的字段，这里使用username尝试\r\n              // 实际开发中需要根据后端接口确认正确的字段名\r\n              const updateRes = await updateUserProfile({ username: phone })\r\n              \r\n              if(updateRes.code === 1) {\r\n                // 更新本地用户信息\r\n                this.userInfo.phone = phone\r\n                this.saveUserInfo()\r\n                \r\n                uni.showToast({\r\n                  title: '手机号修改成功',\r\n                  icon: 'none'\r\n                })\r\n              } else {\r\n                throw new Error(updateRes.msg || '手机号修改失败')\r\n              }\r\n            } catch(e) {\r\n              console.error('修改手机号失败:', e)\r\n              uni.showToast({\r\n                title: e.msg || e.message || '修改失败',\r\n                icon: 'none'\r\n              })\r\n            } finally {\r\n              uni.hideLoading()\r\n              this.loading = false\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    // 保存用户信息\r\n    saveUserInfo() {\r\n      uni.setStorageSync('userInfo', this.userInfo)\r\n    },\r\n    \r\n    handleLogout() {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要退出登录吗？',\r\n        success: (res) => {\r\n          if(res.confirm) {\r\n            uni.removeStorageSync('token')\r\n            uni.removeStorageSync('userInfo')\r\n            uni.navigateBack()\r\n          }\r\n        }\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n  padding-bottom: 40rpx;\r\n}\r\n\r\n.header {\r\n  background: #fff;\r\n  padding-top: 88rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .back-icon {\r\n      position: absolute;\r\n      left: 30rpx;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 34rpx;\r\n      color: #000;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n// 主体内容区域样式\r\n.content-container {\r\n  padding-bottom: 40rpx;\r\n  \r\n  // 昵称输入弹窗样式\r\n  .nickname-popup {\r\n    padding: 40rpx 30rpx;\r\n    \r\n    .popup-title {\r\n      text-align: center;\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n      margin-bottom: 30rpx;\r\n    }\r\n    \r\n    .popup-content {\r\n      margin-bottom: 40rpx;\r\n      \r\n      .nickname-popup-input {\r\n        width: 100%;\r\n        height: 80rpx;\r\n        border: 1px solid #eee;\r\n        border-radius: 8rpx;\r\n        padding: 0 20rpx;\r\n        font-size: 28rpx;\r\n        box-sizing: border-box;\r\n      }\r\n    }\r\n    \r\n    .popup-buttons {\r\n    display: flex;\r\n    border-top: 1px solid #f5f5f5;\r\n    \r\n    .popup-btn {\r\n      flex: 1;\r\n      height: 90rpx;\r\n      line-height: 90rpx;\r\n      text-align: center;\r\n      font-size: 32rpx;\r\n      \r\n      &:active {\r\n        background-color: #f9f9f9;\r\n      }\r\n    }\r\n    \r\n    .cancel-btn {\r\n      color: #666;\r\n      border-right: 1px solid #f5f5f5;\r\n    }\r\n    \r\n    .normal-btn {\r\n      color: #2196F3;\r\n      font-weight: 500;\r\n      border-right: 1px solid #f5f5f5;\r\n    }\r\n    \r\n    .confirm-btn {\r\n      color: #07c160;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n  \r\n  .popup-tip {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n    text-align: center;\r\n    margin-bottom: 20rpx;\r\n  }\r\n}\r\n  \r\n  // 头像区域样式\r\n  .avatar-section {\r\n    position: relative;\r\n    padding: 40rpx 0 30rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #fff;\r\n    border-bottom: 1px solid #f5f5f5;\r\n    \r\n    .avatar {\r\n      width: 180rpx;\r\n      height: 180rpx;\r\n      border-radius: 50%;\r\n      border: 2rpx solid #f5f5f5;\r\n    }\r\n    \r\n    .avatar-button {\r\n      position: absolute;\r\n      bottom: 30rpx;\r\n      right: 50%;\r\n      transform: translateX(60rpx);\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      padding: 0;\r\n      margin: 0;\r\n      border: none;\r\n      background: transparent;\r\n      \r\n      &::after {\r\n        border: none;\r\n      }\r\n    }\r\n    \r\n    .avatar-overlay {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      background: rgba(0, 0, 0, 0.5);\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n  }\r\n  \r\n  // 表单区域样式\r\n  .form-list {\r\n    margin-top: 20rpx;\r\n    background: #fff;\r\n    \r\n    .form-item {\r\n      display: flex;\r\n      padding: 30rpx 40rpx;\r\n      border-bottom: 1px solid #f5f5f5;\r\n      \r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n      \r\n      .form-label {\r\n        width: 160rpx;\r\n        font-size: 32rpx;\r\n        color: #333;\r\n        padding-top: 8rpx;\r\n      }\r\n      \r\n      .form-content {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        \r\n        .form-input {\r\n          flex: 1;\r\n          height: 80rpx;\r\n          font-size: 32rpx;\r\n          color: #333;\r\n          padding: 0 10rpx;\r\n          \r\n          &::placeholder {\r\n            color: #999;\r\n          }\r\n        }\r\n        \r\n        .form-text {\r\n          font-size: 32rpx;\r\n          color: #333;\r\n        }\r\n        \r\n        .form-right-btn {\r\n          margin-left: 20rpx;\r\n          \r\n          .btn-text {\r\n            color: #576b95;\r\n            font-size: 32rpx;\r\n          }\r\n        }\r\n        \r\n        .form-right-tip {\r\n          margin-left: 20rpx;\r\n          \r\n          .tip-text {\r\n            color: #999;\r\n            font-size: 26rpx;\r\n          }\r\n        }\r\n        \r\n        // 性别选择样式\r\n        .gender-group {\r\n          display: flex;\r\n          flex: 1;\r\n          \r\n          .gender-option {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-right: 60rpx;\r\n            \r\n            .gender-radio {\r\n              width: 40rpx;\r\n              height: 40rpx;\r\n              border-radius: 50%;\r\n              border: 2rpx solid #ddd;\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              margin-right: 10rpx;\r\n              \r\n              .radio-inner {\r\n                width: 24rpx;\r\n                height: 24rpx;\r\n                border-radius: 50%;\r\n                background: #07c160;\r\n              }\r\n            }\r\n            \r\n            .gender-text {\r\n              font-size: 32rpx;\r\n              color: #333;\r\n            }\r\n            \r\n            &.active {\r\n              .gender-radio {\r\n                border-color: #07c160;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n  /* 已移动到nickname-section中 */\r\n  \r\n  .sync-wx-profile {\r\n    background: #07c160;\r\n    padding: 12rpx 30rpx;\r\n    border-radius: 30rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #fff;\r\n      margin-left: 10rpx;\r\n    }\r\n    \r\n    &:active {\r\n      opacity: 0.9;\r\n    }\r\n  }\r\n}\r\n\r\n// 表单列表样式\r\n.form-list {\r\n  margin: 30rpx;\r\n  \r\n  .form-section {\r\n    background: #fff;\r\n    border-radius: 12rpx;\r\n    margin-bottom: 20rpx;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n    overflow: hidden;\r\n    \r\n    .form-item {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 30rpx;\r\n      border-bottom: 1px solid #f5f5f5;\r\n      \r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n      \r\n      .item-left {\r\n        display: flex;\r\n        align-items: center;\r\n        \r\n        .label {\r\n          font-size: 30rpx;\r\n          color: #333;\r\n          margin-left: 16rpx;\r\n        }\r\n      }\r\n      \r\n      .item-right {\r\n        display: flex;\r\n        align-items: center;\r\n        flex-wrap: wrap;\r\n        \r\n        .value {\r\n          font-size: 30rpx;\r\n          color: #999;\r\n          margin-right: 16rpx;\r\n        }\r\n        \r\n        /* 已移除昵称按钮相关样式 */\r\n      }\r\n      \r\n      &:active {\r\n        background-color: #f9f9f9;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 隐私提示\r\n.privacy-tip {\r\n  padding: 30rpx 40rpx;\r\n  \r\n  .tip-text {\r\n    font-size: 26rpx;\r\n    color: #999;\r\n    line-height: 1.6;\r\n  }\r\n}\r\n\r\n// 保存按钮\r\n.save-btn {\r\n  margin: 40rpx 30rpx;\r\n  height: 90rpx;\r\n  background: #e64340;\r\n  border-radius: 8rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  text {\r\n    font-size: 34rpx;\r\n    color: #fff;\r\n    font-weight: 500;\r\n  }\r\n  \r\n  &:active {\r\n    opacity: 0.9;\r\n  }\r\n}\r\n\r\n// 关于我们容器\r\n.about-container {\r\n  margin-top: 40rpx;\r\n  text-align: center;\r\n}\r\n\r\n// 关于我们按钮\r\n.about-btn {\r\n  display: inline-block;\r\n  padding: 20rpx 40rpx;\r\n\r\n  text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n\r\n  &:active {\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n// 退出登录容器\r\n.logout-container {\r\n  margin-top: 20rpx;\r\n  text-align: center;\r\n}\r\n\r\n// 退出登录按钮\r\n.logout-btn {\r\n  display: inline-block;\r\n  padding: 20rpx 40rpx;\r\n  \r\n  text {\r\n    font-size: 32rpx;\r\n    color: #576b95;\r\n  }\r\n  \r\n  &:active {\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n// 头像选择弹窗\r\n.avatar-popup {\r\n  padding: 30rpx;\r\n  \r\n  .popup-title {\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    color: #333;\r\n    font-weight: bold;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  .popup-options {\r\n    display: flex;\r\n    justify-content: space-around;\r\n    margin-bottom: 40rpx;\r\n    \r\n    .popup-option {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 20rpx;\r\n      \r\n      text {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        margin-top: 16rpx;\r\n      }\r\n      \r\n      .wx-button {\r\n        background: transparent;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        padding: 0;\r\n        border: none;\r\n        line-height: normal;\r\n        \r\n        &::after {\r\n          border: none;\r\n        }\r\n        \r\n        text {\r\n          font-size: 28rpx;\r\n          color: #333;\r\n          margin-top: 16rpx;\r\n        }\r\n      }\r\n      \r\n      &:active {\r\n        opacity: 0.7;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .popup-cancel {\r\n    height: 90rpx;\r\n    line-height: 90rpx;\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    color: #333;\r\n    border-top: 1px solid #eee;\r\n    \r\n    &:active {\r\n      background-color: #f5f5f5;\r\n    }\r\n  }\r\n}\r\n\r\n// 昵称输入弹窗样式\r\n.nickname-popup {\r\n  padding: 40rpx 30rpx;\r\n  \r\n  .popup-title {\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    color: #333;\r\n    font-weight: bold;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .popup-content {\r\n    margin-bottom: 40rpx;\r\n    \r\n    .nickname-popup-input {\r\n      width: 100%;\r\n      height: 80rpx;\r\n      border: 1px solid #eee;\r\n      border-radius: 8rpx;\r\n      padding: 0 20rpx;\r\n      font-size: 28rpx;\r\n      box-sizing: border-box;\r\n    }\r\n  }\r\n}\r\n\r\n/* 微信昵称按钮样式 */\r\n.wx-nickname-btn {\r\n  background: transparent;\r\n  padding: 0;\r\n  margin: 0 0 0 10rpx;\r\n  line-height: normal;\r\n  border: none;\r\n  display: inline-block;\r\n  \r\n  &::after {\r\n    border: none;\r\n  }\r\n}\r\n\r\n/* 昵称弹窗样式 */\r\n/* 昵称弹窗 - 简约风格 */\r\n.nickname-popup-container {\r\n  width: 580rpx;\r\n  padding: 40rpx 30rpx;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n  animation: fadeIn 0.3s ease-out;\r\n}\r\n\r\n/* 标题区 */\r\n.nickname-popup-container .popup-header {\r\n  text-align: center;\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.nickname-popup-container .popup-header .popup-title {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n/* 输入区 */\r\n.nickname-popup-container .input-container {\r\n  margin-bottom: 30rpx;\r\n}\r\n\r\n.nickname-popup-container .input-container .input-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #f8f8f8;\r\n  border-radius: 8rpx;\r\n  padding: 4rpx 16rpx;\r\n  margin-bottom: 12rpx;\r\n}\r\n\r\n.nickname-popup-container .input-container .input-wrapper .input-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16rpx;\r\n}\r\n\r\n.nickname-popup-container .input-container .input-wrapper .nickname-edit-input {\r\n  flex: 1;\r\n  height: 80rpx;\r\n  font-size: 28rpx;\r\n  color: #333;\r\n  background: transparent;\r\n}\r\n\r\n.nickname-popup-container .input-container .input-tip {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n/* 按钮区 */\r\n.nickname-popup-container .action-btns {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.nickname-popup-container .action-btns .cancel-btn {\r\n  height: 80rpx;\r\n  line-height: 80rpx;\r\n  text-align: center;\r\n  font-size: 30rpx;\r\n  color: #07c160;\r\n  background: #f8f8f8;\r\n  border-radius: 8rpx;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.nickname-popup-container .action-btns .cancel-btn:active {\r\n  background-color: #f2f2f2;\r\n}\r\n\r\n.popup-buttons {\r\n  display: flex;\r\n  border-top: 1px solid #f5f5f5;\r\n  \r\n  .popup-btn {\r\n    flex: 1;\r\n    height: 90rpx;\r\n    line-height: 90rpx;\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    \r\n    &:active {\r\n      background-color: #f9f9f9;\r\n    }\r\n  }\r\n  \r\n  .cancel-btn {\r\n    color: #666;\r\n    border-right: 1px solid #f5f5f5;\r\n  }\r\n  \r\n  .confirm-btn {\r\n    color: #07c160;\r\n    font-weight: 500;\r\n  }\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes popIn {\r\n  0% {\r\n    opacity: 0;\r\n    transform: scale(0.9);\r\n  }\r\n  70% {\r\n    transform: scale(1.03);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n/* 简单的淡入动画 */\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 日期选择器样式 */\r\n.birthday-picker-modal {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 9999;\r\n}\r\n\r\n.birthday-picker-mask {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.date-picker-content {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  background-color: #fff;\r\n  border-top-left-radius: 12rpx;\r\n  border-top-right-radius: 12rpx;\r\n  overflow: hidden;\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n  z-index: 10000;\r\n  animation: slideUp 0.3s ease-out forwards;\r\n}\r\n\r\n.date-picker-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 30rpx;\r\n  height: 88rpx;\r\n  font-size: 32rpx;\r\n  background-color: #f8f8f8;\r\n  border-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.date-picker-cancel {\r\n  color: #888;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.date-picker-title {\r\n  color: #333;\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n}\r\n\r\n.date-picker-confirm {\r\n  color: #07c160;\r\n  font-weight: 500;\r\n  font-size: 28rpx;\r\n}\r\n\r\n.date-picker-view {\r\n  width: 100%;\r\n  height: 400rpx;\r\n  text-align: center;\r\n}\r\n\r\n.picker-item {\r\n  line-height: 50px;\r\n  text-align: center;\r\n  font-size: 32rpx;\r\n  color: #333;\r\n}\r\n\r\n@keyframes slideUp {\r\n  from {\r\n    transform: translateY(100%);\r\n  }\r\n  to {\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* 修正后新增的样式 */\r\n.picker-view-container {\r\n  width: 100%;\r\n  height: 300rpx;\r\n  background-color: #fff;\r\n  margin-top: 10rpx;\r\n}\r\n\r\n.date-picker-view {\r\n  width: 100%;\r\n  height: 100%;\r\n  text-align: center;\r\n}\r\n\r\n.picker-item {\r\n  line-height: 50px;\r\n  text-align: center;\r\n  font-size: 32rpx;\r\n  color: #333;\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=style&index=0&id=3176ae3d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=style&index=0&id=3176ae3d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309830\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}