@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.order-list {
  padding: 0 20rpx;
}
.order-list .order-card {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.order-list .order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.order-list .order-header .left {
  display: flex;
  flex-direction: column;
}
.order-list .order-header .left .order-no {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.order-list .order-header .left .order-time {
  font-size: 24rpx;
  color: #999;
}
.order-list .order-header .status-tag {
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.order-list .order-header .status-tag.status-pending {
  background-color: #fff8e6;
  color: #ff9500;
}
.order-list .order-header .status-tag.status-paid {
  background-color: #e6edff;
  color: #4c84ff;
}
.order-list .order-header .status-tag.status-cooking {
  background-color: #ffebeb;
  color: #ff6b6b;
}
.order-list .order-header .status-tag.status-cooked {
  background-color: #fff0e0;
  color: #ffa64d;
}
.order-list .order-header .status-tag.status-delivering {
  background-color: #e8efff;
  color: #5e7ce0;
}
.order-list .order-header .status-tag.status-processing {
  background-color: #e6f7ff;
  color: #0076ff;
}
.order-list .order-header .status-tag.status-completed {
  background-color: #e6fff0;
  color: #52c41a;
}
.order-list .order-header .status-tag.status-cancelled {
  background-color: #f5f5f5;
  color: #999;
}
.order-list .order-header .status-tag.status-default {
  background-color: #f5f5f5;
  color: #666;
}
.order-list .product-list {
  padding: 20rpx 30rpx;
}
.order-list .product-list .product-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16rpx 0;
}
.order-list .product-list .product-item:not(:last-child) {
  border-bottom: 1rpx solid #f5f5f5;
}
.order-list .product-list .product-item .product-info {
  flex: 1;
  margin-right: 20rpx;
}
.order-list .product-list .product-item .product-info .product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}
.order-list .product-list .product-item .product-info .product-specs {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 6rpx;
}
.order-list .product-list .product-item .product-price {
  display: flex;
  align-items: center;
}
.order-list .product-list .product-item .product-price .price {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.order-list .product-list .product-item .product-price .count {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #999;
}
.order-list .order-footer {
  padding: 24rpx 30rpx;
}
.order-list .order-footer .total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.order-list .order-footer .total .count-text {
  font-size: 24rpx;
  color: #666;
}
.order-list .order-footer .total .price {
  font-size: 24rpx;
  color: #333;
}
.order-list .order-footer .total .price .price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
}
.order-list .order-footer .actions {
  display: flex;
  justify-content: flex-end;
}
.order-list .order-footer .actions .action-btn {
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
}
.order-list .order-footer .actions .action-btn.outline {
  border: 1rpx solid #ddd;
  color: #666;
}
.order-list .order-footer .actions .action-btn.primary {
  background-color: #8cd548;
  color: #fff;
}
.order-list .order-footer .actions .action-btn:active {
  opacity: 0.8;
}

