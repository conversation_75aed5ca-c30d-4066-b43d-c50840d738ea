{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/receive.vue?ec89", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/receive.vue?8c37", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/receive.vue?1b68", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/receive.vue?f02c", "uni-app:///pages/coupon/receive.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/receive.vue?90fc", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/receive.vue?bb08"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "couponList", "amount", "limit", "name", "endDate", "received", "methods", "handleBack", "uni", "receiveCoupon", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqCprB;EACAC;IACA;MACAC,aACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAJ;QACAC;QACAC;QACAC;QACAC;MACA;IAEA;EACA;EAEAC;IACAC;MACAC;IACA;IAEAC;MACA;MAEA;MACAD;QACAE;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1EA;AAAA;AAAA;AAAA;AAAmxC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAvyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/coupon/receive.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/coupon/receive.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./receive.vue?vue&type=template&id=264d149a&scoped=true&\"\nvar renderjs\nimport script from \"./receive.vue?vue&type=script&lang=js&\"\nexport * from \"./receive.vue?vue&type=script&lang=js&\"\nimport style0 from \"./receive.vue?vue&type=style&index=0&id=264d149a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"264d149a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/coupon/receive.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=template&id=264d149a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">领取优惠券</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 优惠券列表 -->\r\n    <view class=\"coupon-list\">\r\n      <view class=\"coupon-item\" v-for=\"(item, index) in couponList\" :key=\"index\">\r\n        <view class=\"left\">\r\n          <view class=\"amount\">\r\n            <text class=\"symbol\">¥</text>\r\n            <text class=\"value\">{{item.amount}}</text>\r\n          </view>\r\n          <view class=\"limit\">满{{item.limit}}元可用</view>\r\n        </view>\r\n        <view class=\"right\">\r\n          <text class=\"name\">{{item.name}}</text>\r\n          <text class=\"date\">有效期至{{item.endDate}}</text>\r\n          <view class=\"receive-btn\" :class=\"{disabled: item.received}\" @tap=\"receiveCoupon(index)\">\r\n            <text>{{item.received ? '已领取' : '立即领取'}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      couponList: [\r\n        {\r\n          amount: 10,\r\n          limit: 50,\r\n          name: '新人专享券',\r\n          endDate: '2024-12-31',\r\n          received: false\r\n        },\r\n        {\r\n          amount: 5,\r\n          limit: 30,\r\n          name: '满减优惠券',\r\n          endDate: '2024-12-31',\r\n          received: false\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    handleBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    receiveCoupon(index) {\r\n      if(this.couponList[index].received) return\r\n      \r\n      this.couponList[index].received = true\r\n      uni.showToast({\r\n        title: '领取成功',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  background: #fff;\r\n  padding-top: 88rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .back-icon {\r\n      position: absolute;\r\n      left: 30rpx;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n\r\n.coupon-list {\r\n  padding: 20rpx;\r\n  \r\n  .coupon-item {\r\n    display: flex;\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    margin-bottom: 20rpx;\r\n    overflow: hidden;\r\n    \r\n    .left {\r\n      width: 220rpx;\r\n      background: #8cd548;\r\n      padding: 30rpx;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      \r\n      .amount {\r\n        display: flex;\r\n        align-items: baseline;\r\n        color: #fff;\r\n        \r\n        .symbol {\r\n          font-size: 32rpx;\r\n        }\r\n        \r\n        .value {\r\n          font-size: 60rpx;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n      \r\n      .limit {\r\n        font-size: 24rpx;\r\n        color: rgba(255,255,255,0.9);\r\n        margin-top: 8rpx;\r\n      }\r\n    }\r\n    \r\n    .right {\r\n      flex: 1;\r\n      padding: 30rpx;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      \r\n      .name {\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n      }\r\n      \r\n      .date {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n      }\r\n      \r\n      .receive-btn {\r\n        align-self: flex-end;\r\n        padding: 12rpx 32rpx;\r\n        background: #8cd548;\r\n        border-radius: 100rpx;\r\n        \r\n        text {\r\n          font-size: 26rpx;\r\n          color: #fff;\r\n        }\r\n        \r\n        &.disabled {\r\n          background: #ccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=style&index=0&id=264d149a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=style&index=0&id=264d149a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309896\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}