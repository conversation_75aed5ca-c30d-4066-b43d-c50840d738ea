<template>
	<view class="login-container">
		<!-- 返回按钮 -->
		<view class="back-button" @tap="handleBack">
			<image class="back-icon" src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" />
		</view>
		
		<!-- 商家名称标题 -->
		<view class="merchant-name">
			<text>{{merchantInfo.name || '点餐小程序'}}</text>
		</view>
		
		<!-- 商家LOGO -->
		<view class="logo-container">
			<image class="logo-image" :src="merchantInfo.logo || '/static/my/default-avatar.png'" mode="aspectFill" />
		</view>
		
		<!-- 商家名称 -->
		<view class="merchant-title">
			<text>{{merchantInfo.name || '点餐小程序'}}</text>
		</view>
		
		<!-- 会员权益宣传 -->
		<view class="benefit-text">
			<text>成为会员，立享更多优惠福利</text>
		</view>
		
		<!-- 授权说明 -->
		<view class="auth-desc">
			<text>授权绑定手机号 为您提供更好的服务</text>
		</view>
		
		<!-- 登录按钮 -->
		<view class="login-btn-wrap">
			<button class="login-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">微信一键登录</button>
		</view>
		
		<!-- 用户协议选项 -->
		<view class="agreement-wrap">
			<view class="checkbox" @tap="toggleAgreement">
				<view class="checkbox-inner" :class="{'checked': isAgree}"></view>
			</view>
			<text class="agreement-text">已阅读并同意</text>
			<text class="agreement-link" @tap="viewPrivacyPolicy">《{{merchantInfo.name || '点餐小程序'}}个人信息保护政策》</text>
			<text class="agreement-link" @tap="viewUserAgreement">《{{merchantInfo.name || '点餐小程序'}}用户服务协议》</text>
		</view>
		
		<!-- 暂不登录 -->
		<view class="skip-login" @tap="skipLogin">
			<text>暂不登录</text>
		</view>
	</view>
</template>

<script>
import { thirdLogin, thirdLoginFullPath } from '@/api/user';
import { getMerchantInfo } from '@/api/merchant';

export default {
	data() {
		return {
			isAgree: false,
			merchantInfo: {
				name: '',
				logo: ''
			},
			redirectUrl: '',
			loginParams: null
		}
	},
	onLoad(options) {
		// 获取商家信息
		this.getMerchantData();
		
		// 保存重定向URL
		if (options.redirect) {
			this.redirectUrl = decodeURIComponent(options.redirect);
		} else {
			// 获取之前可能存储的重定向URL
			const storedRedirect = uni.getStorageSync('redirect_url');
			if (storedRedirect) {
				this.redirectUrl = storedRedirect;
			}
		}
	},
	methods: {
		// 获取商家信息
		async getMerchantData() {
			try {
				const res = await getMerchantInfo();
				if (res && res.code === 1 && res.data) {
					this.merchantInfo = {
						name: res.data.name || '点餐小程序',
						logo: res.data.logo_image || '/static/my/default-avatar.png'
					};
				}
			} catch (error) {
				console.error('获取商家信息失败:', error);
			}
		},
		
		// 切换协议同意状态
		toggleAgreement() {
			this.isAgree = !this.isAgree;
		},
		
		// 查看隐私政策
		viewPrivacyPolicy() {
			// 跳转到隐私政策页面
			uni.showToast({
				title: '暂未开放',
				icon: 'none'
			});
		},
		
		// 查看用户协议
		viewUserAgreement() {
			// 跳转到用户协议页面
			uni.showToast({
				title: '暂未开放',
				icon: 'none'
			});
		},
		
		// 获取手机号
		async getPhoneNumber(e) {
			// 检查是否同意协议
			if (!this.isAgree) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none'
				});
				return;
			}
			
			// 判断是否拒绝授权
			if (e.detail.errMsg && e.detail.errMsg.indexOf('deny') > -1) {
				uni.showToast({
					title: '您已拒绝授权',
					icon: 'none'
				});
				return;
			}
			
			try {
				uni.showLoading({ title: '登录中...' });
				
				// 获取微信登录凭证
				let code;
				try {
					const loginResult = await uni.login();
					console.log('uni.login() 返回结果：', JSON.stringify(loginResult));
					
					// 处理返回结果格式，可能是数组或对象
					let codeResult = loginResult;
					
					// 如果返回的是数组格式 [null, {errMsg, code}]
					if (Array.isArray(loginResult) && loginResult.length > 1 && loginResult[1]) {
						codeResult = loginResult[1]; // 取第二个元素作为结果对象
						console.log('检测到数组格式返回，使用索引1的元素:', JSON.stringify(codeResult));
					}
					
					if (!codeResult) {
						throw new Error('uni.login()返回空结果');
					}
					
					// 检查返回值格式
					if (!codeResult.code) {
						console.error('登录凭证格式异常：', codeResult);
						throw new Error(`获取登录凭证失败: ${codeResult.errMsg || '未知错误'}`);
					}
					
					code = codeResult.code;
					console.log('成功获取登录凭证，code:', code);
				} catch (error) {
					console.error('登录凭证获取失败:', error);
					uni.hideLoading(); // 确保登录失败时关闭加载状态
					uni.showToast({
						title: error.message || '获取登录凭证失败，请检查网络后重试',
						icon: 'none',
						duration: 2000
					});
					return; // 中止登录流程
				}
				
				// 构造登录参数
				const loginParams = { code };
				console.log('构造登录参数:', JSON.stringify(loginParams));
				
				// 如果有手机号加密数据，增加到请求参数中
				if (e.detail.encryptedData && e.detail.iv) {
					loginParams.encryptedData = e.detail.encryptedData;
					loginParams.iv = e.detail.iv;
					console.log('添加手机号加密数据');
				}
				
				// 调用登录接口
				console.log('调用登录接口，参数:', JSON.stringify(loginParams));
				const response = await thirdLogin(loginParams);
				console.log('登录接口返回:', JSON.stringify(response));
				
				if (!response) {
					throw new Error('服务器无响应');
				}
				
				if (response.code !== 1) {
					throw new Error(response.msg || '登录失败');
				}
				
				if (!response.data) {
					throw new Error('服务器返回数据为空');
				}
				
				// 处理登录成功
				const userInfo = { 
					nickName: this.merchantInfo.name ? this.merchantInfo.name + '用户' : '新用户',
					avatarUrl: '/static/my/default-avatar.png'
				};
				await this.handleLoginSuccess(response.data, userInfo);
				
				// 跳转到目标页面
				this.redirectAfterLogin();
				
			} catch (error) {
				console.error('登录失败:', error);
				uni.showToast({
					title: error.message || '登录失败',
					icon: 'none',
					duration: 2000
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 处理登录成功
		async handleLoginSuccess(data, originalUserInfo) {
			try {
				// 提取核心数据
				const userData = data.userinfo || data.user || {};
				const token = userData.token || userData.accessToken || userData.access_token || data.token;
				
				if (!token) {
					throw new Error('登录凭证无效');
				}
				
				// 保存必要信息
				uni.setStorageSync('token', token);
				
				// 保存用户信息 - 优先使用服务器返回数据，兼容多种字段名
				const userInfo = {
					avatarUrl: userData.avatar || userData.avatarurl || originalUserInfo?.avatarUrl || '/static/my/default-avatar.png',
					nickName: userData.nickname || userData.name || originalUserInfo?.nickName || '用户',
					phone: userData.mobile || userData.phone || '',
					userId: userData.id || userData.user_id || '',
					gender: userData.gender || '',
					birthday: userData.birthday || '',
				};
				
				uni.setStorageSync('userInfo', userInfo);
				console.log('已保存用户信息:', JSON.stringify(userInfo));
				
				// 保存第三方数据（如果有）
				const thirdData = data.thirdinfo || data.third;
				if (thirdData?.openid || thirdData?.session_key) {
					uni.setStorageSync('thirdData', {
						openid: thirdData.openid || thirdData.open_id || '',
						session_key: thirdData.session_key || thirdData.sessionKey || ''
					});
					console.log('已保存第三方数据');
				}
				
				// 显示成功提示
				uni.showToast({ title: '登录成功', icon: 'none' });
				return true;
				
			} catch (error) {
				console.error('登录数据处理失败:', error);
				throw error;
			}
		},
		
		// 跳转到目标页面
		redirectAfterLogin() {
			setTimeout(() => {
				try {
					console.log('准备跳转，目标地址:', this.redirectUrl || '/pages/my/my');
					
					// 判断是否有重定向地址
					if (this.redirectUrl) {
						// 检查是否是tabBar页面
						const tabBarPages = [
							'/pages/home/<USER>',
							'/pages/menu/menu',
							'/pages/order/order',
							'/pages/my/my'
						];
						
						if (tabBarPages.includes(this.redirectUrl)) {
							console.log('检测到Tab页面，使用switchTab跳转');
							uni.switchTab({
								url: this.redirectUrl,
								success() {
									console.log('switchTab跳转成功');
								},
								fail(err) {
									console.error('switchTab跳转失败:', err);
									// 失败时尝试用redirectTo
									uni.redirectTo({
										url: '/pages/my/my'
									});
								}
							});
						} else {
							console.log('检测到普通页面，使用redirectTo跳转');
							uni.redirectTo({
								url: this.redirectUrl,
								success() {
									console.log('redirectTo跳转成功');
								},
								fail(err) {
									console.error('redirectTo跳转失败:', err);
									// 失败时尝试用switchTab跳转到我的页面
									uni.switchTab({
										url: '/pages/my/my'
									});
								}
							});
						}
						
						// 清除存储的重定向URL
						uni.removeStorageSync('redirect_url');
					} else {
						// 默认跳转到我的页面
						console.log('无重定向地址，默认跳转到我的页面');
						uni.switchTab({
							url: '/pages/my/my',
							success() {
								console.log('默认跳转成功');
							},
							fail(err) {
								console.error('默认跳转失败:', err);
							}
						});
					}
				} catch (error) {
					console.error('跳转过程出错:', error);
					// 出错时尝试最基本的跳转
					uni.switchTab({
						url: '/pages/my/my'
					});
				}
			}, 1500);
		},
		
		// 返回上一页
		handleBack() {
			// 获取当前页面栈
			const pages = getCurrentPages();
			
			// 如果当前只有登录页面一个页面，则跳转到首页
			if (pages.length <= 1) {
				uni.switchTab({
					url: '/pages/home/<USER>'
				});
			} else {
				// 返回上一页
				uni.navigateBack({
					delta: 1
				});
			}
		},
		
		// 暂不登录
		skipLogin() {
			// 获取当前页面栈
			const pages = getCurrentPages();
			
			// 如果当前只有登录页面一个页面，则跳转到首页
			if (pages.length <= 1) {
				uni.switchTab({
					url: '/pages/home/<USER>'
				});
			} else {
				// 返回上一页
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
}
</script>

<style lang="scss">
.login-container {
	min-height: 100vh;
	background-color: #FFFFFF;
	position: relative;
	padding: 0 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.back-button {
	position: absolute;
	left: 30rpx;
	top: 80rpx;
	z-index: 10;
}

.back-icon {
	width: 40rpx;
	height: 40rpx;
}

.merchant-name {
	margin-top: 80rpx;
	font-size: 36rpx;
	font-weight: bold;
	text-align: center;
	width: 100%;
	padding: 20rpx 0;
}

.logo-container {
	margin-top: 60rpx;
	margin-bottom: 30rpx;
	display: flex;
	justify-content: center;
}

.logo-image {
	width: 180rpx;
	height: 180rpx;
	border-radius: 50%;
	background-color: #f5f5f5;
}

.merchant-title {
	font-size: 34rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.benefit-text {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 80rpx;
}

.auth-desc {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 40rpx;
}

.login-btn-wrap {
	width: 100%;
	padding: 0 20rpx;
	margin-bottom: 40rpx;
}

.login-btn {
	height: 90rpx;
	line-height: 90rpx;
	background-color: #78c238;
	color: #FFFFFF;
	font-size: 32rpx;
	border-radius: 45rpx;
	font-weight: bold;
}

.agreement-wrap {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	margin-bottom: 60rpx;
	flex-wrap: wrap;
	padding: 0 20rpx;
}

.checkbox {
	width: 32rpx;
	height: 32rpx;
	border: 1px solid #CCCCCC;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 10rpx;
}

.checkbox-inner {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background-color: transparent;
	
	&.checked {
		background-color: #78c238;
	}
}

.agreement-text {
	font-size: 26rpx;
	color: #666666;
}

.agreement-link {
	font-size: 26rpx;
	color: #FF0000;
}

.skip-login {
	margin-top: 40rpx;
	padding: 20rpx 40rpx;
}

.skip-login text {
	font-size: 28rpx;
	color: #999999;
}
</style>