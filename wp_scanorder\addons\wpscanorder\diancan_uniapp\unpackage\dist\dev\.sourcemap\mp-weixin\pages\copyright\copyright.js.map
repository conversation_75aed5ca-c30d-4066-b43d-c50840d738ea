{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/copyright/copyright.vue?7f76", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/copyright/copyright.vue?507a", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/copyright/copyright.vue?62f7", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/copyright/copyright.vue?c8c0", "uni-app:///pages/copyright/copyright.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/copyright/copyright.vue?fd53", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/copyright/copyright.vue?cafa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "currentYear", "updateDate", "appInfo", "developer", "contact", "thirdPartyWorks", "name", "author", "type", "license", "sourceUrl", "licenseUrl", "description", "expandedSections", "third<PERSON><PERSON>y", "original", "ccLicense", "disclaimer", "onLoad", "methods", "goBack", "uni", "openLink", "title", "icon", "success", "toggleSection"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmMtrB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACA;MACAC,kBACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAN;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MAAA,CACA;;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;IACA;IAEA;IACAC;MACA;QACAD;UACAE;UACAC;QACA;QACA;MACA;;MAEA;MACAH;QACAvB;QACA2B;UACAJ;YACAE;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAE;MACA;IACA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;ACtSA;AAAA;AAAA;AAAA;AAAqxC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACAzyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/copyright/copyright.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/copyright/copyright.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./copyright.vue?vue&type=template&id=30c7cdd4&scoped=true&\"\nvar renderjs\nimport script from \"./copyright.vue?vue&type=script&lang=js&\"\nexport * from \"./copyright.vue?vue&type=script&lang=js&\"\nimport style0 from \"./copyright.vue?vue&type=style&index=0&id=30c7cdd4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"30c7cdd4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/copyright/copyright.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./copyright.vue?vue&type=template&id=30c7cdd4&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./copyright.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./copyright.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<view class=\"custom-navbar\">\r\n\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n\t\t\t<view class=\"navbar-content\">\r\n\t\t\t\t<view class=\"navbar-left\" @tap=\"goBack\">\r\n\t\t\t\t\t<u-icon name=\"arrow-left\" size=\"20\" color=\"#333\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"navbar-title\">版权声明</view>\r\n\t\t\t\t<view class=\"navbar-right\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 页面内容 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 页面标题 -->\r\n\t\t\t<view class=\"page-title\">版权声明</view>\r\n\t\t\t<view class=\"page-subtitle\">Copyright Notice</view>\r\n\r\n\t\t\t<!-- 第三方作品授权信息 -->\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<view class=\"section-title\" @tap=\"toggleSection('thirdParty')\">\r\n\t\t\t\t\t<view class=\"title-left\">\r\n\t\t\t\t\t\t<u-icon name=\"file-text\" size=\"18\" color=\"#FF6B35\"></u-icon>\r\n\t\t\t\t\t\t<text>第三方作品授权信息</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t:name=\"expandedSections.thirdParty ? 'arrow-up' : 'arrow-down'\"\r\n\t\t\t\t\t\tsize=\"16\"\r\n\t\t\t\t\t\tcolor=\"#666\">\r\n\t\t\t\t\t</u-icon>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"work-list\" v-show=\"expandedSections.thirdParty\">\r\n\t\t\t\t\t<view class=\"work-item\" v-for=\"(work, index) in thirdPartyWorks\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"work-header\">\r\n\t\t\t\t\t\t\t<text class=\"work-name\">{{ work.name }}</text>\r\n\t\t\t\t\t\t\t<view class=\"license-badge\" :class=\"[\r\n\t\t\t\t\t\t\t\twork.license === 'MIT' ? 'license-mit' : '',\r\n\t\t\t\t\t\t\t\twork.license === 'CC BY 4.0' ? 'license-cc' : '',\r\n\t\t\t\t\t\t\t\twork.license === 'Apache 2.0' ? 'license-apache' : '',\r\n\t\t\t\t\t\t\t\t(!work.license || (work.license !== 'MIT' && work.license !== 'CC BY 4.0' && work.license !== 'Apache 2.0')) ? 'license-default' : ''\r\n\t\t\t\t\t\t\t]\">{{ work.license }}</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"work-description\" v-if=\"work.description\">\r\n\t\t\t\t\t\t\t<text>{{ work.description }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"work-details\">\r\n\t\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t\t<text class=\"label\">原作者：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"value\">{{ work.author }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t\t<text class=\"label\">作品类型：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"value\">{{ work.type }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t\t<text class=\"label\">来源链接：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"link\" @tap=\"openLink(work.sourceUrl)\">点击查看原作品</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"detail-row\">\r\n\t\t\t\t\t\t\t\t<text class=\"label\">授权协议：</text>\r\n\t\t\t\t\t\t\t\t<text class=\"link\" @tap=\"openLink(work.licenseUrl)\">{{ work.license }} 协议详情</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 原创内容声明 -->\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<view class=\"section-title\" @tap=\"toggleSection('original')\">\r\n\t\t\t\t\t<view class=\"title-left\">\r\n\t\t\t\t\t\t<u-icon name=\"shield\" size=\"18\" color=\"#4CAF50\"></u-icon>\r\n\t\t\t\t\t\t<text>原创内容声明</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t:name=\"expandedSections.original ? 'arrow-up' : 'arrow-down'\"\r\n\t\t\t\t\t\tsize=\"16\"\r\n\t\t\t\t\t\tcolor=\"#666\">\r\n\t\t\t\t\t</u-icon>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"original-content\" v-show=\"expandedSections.original\">\r\n\t\t\t\t\t<view class=\"copyright-text\">\r\n\t\t\t\t\t\t<text>本小程序的原创内容（包括但不限于界面设计、功能逻辑、文案内容等）版权归 <text class=\"highlight\">{{ appInfo.developer }}</text> 所有。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"copyright-text\">\r\n\t\t\t\t\t\t<text>未经书面许可，任何个人或组织不得擅自复制、修改、分发或用于商业用途。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"copyright-text\">\r\n\t\t\t\t\t\t<text>© {{ currentYear }} {{ appInfo.developer }} 保留所有权利。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- CC BY 4.0 协议介绍 -->\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<view class=\"section-title\" @tap=\"toggleSection('ccLicense')\">\r\n\t\t\t\t\t<view class=\"title-left\">\r\n\t\t\t\t\t\t<u-icon name=\"bookmark\" size=\"18\" color=\"#FF9800\"></u-icon>\r\n\t\t\t\t\t\t<text>CC BY 4.0 协议介绍</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t:name=\"expandedSections.ccLicense ? 'arrow-up' : 'arrow-down'\"\r\n\t\t\t\t\t\tsize=\"16\"\r\n\t\t\t\t\t\tcolor=\"#666\">\r\n\t\t\t\t\t</u-icon>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"cc-license\" v-show=\"expandedSections.ccLicense\">\r\n\t\t\t\t\t<view class=\"license-intro\">\r\n\t\t\t\t\t\t<text class=\"intro-text\">知识共享署名 4.0 国际许可协议（CC BY 4.0）是一种开放的版权许可协议，允许他人分发、重新混合、调整和基于您的作品进行创作，甚至是在商业性使用的情况下，只要他们给您署名。</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"license-features\">\r\n\t\t\t\t\t\t<view class=\"feature-title\">\r\n\t\t\t\t\t\t\t<text>您可以自由地：</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"feature-item\">\r\n\t\t\t\t\t\t\t<u-icon name=\"checkmark-circle\" size=\"16\" color=\"#4CAF50\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"feature-text\"><text class=\"feature-bold\">分享</text> — 在任何媒介以任何形式复制、发行本作品</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"feature-item\">\r\n\t\t\t\t\t\t\t<u-icon name=\"checkmark-circle\" size=\"16\" color=\"#4CAF50\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"feature-text\"><text class=\"feature-bold\">演绎</text> — 修改、转换或以本作品为基础进行创作</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"feature-item\">\r\n\t\t\t\t\t\t\t<u-icon name=\"checkmark-circle\" size=\"16\" color=\"#4CAF50\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"feature-text\"><text class=\"feature-bold\">商业性使用</text> — 在任何用途下，甚至商业目的</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"license-conditions\">\r\n\t\t\t\t\t\t<view class=\"condition-title\">\r\n\t\t\t\t\t\t\t<text>惟须遵守下列条件：</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"condition-item\">\r\n\t\t\t\t\t\t\t<u-icon name=\"info-circle\" size=\"16\" color=\"#FF6B35\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"condition-text\"><text class=\"condition-bold\">署名</text> — 您必须给出适当的署名，提供指向本许可协议的链接，同时标明是否（对原始作品）作了修改。您可以用任何合理的方式来署名，但是不得以任何方式暗示许可人为您或您的使用背书。</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"license-links\">\r\n\t\t\t\t\t\t<view class=\"link-item\" @tap=\"openLink('https://creativecommons.org/licenses/by/4.0/deed.zh')\">\r\n\t\t\t\t\t\t\t<u-icon name=\"external-link\" size=\"14\" color=\"#1976d2\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"link-text\">查看完整协议条款</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"link-item\" @tap=\"openLink('https://js.design/community?category=detail&type=resource&id=66b1e3824979a82bdd2c417d')\">\r\n\t\t\t\t\t\t\t<u-icon name=\"external-link\" size=\"14\" color=\"#1976d2\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"link-text\">CC BY 4.0 协议详细说明</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 免责声明 -->\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<view class=\"section-title\" @tap=\"toggleSection('disclaimer')\">\r\n\t\t\t\t\t<view class=\"title-left\">\r\n\t\t\t\t\t\t<u-icon name=\"info-circle\" size=\"18\" color=\"#2196F3\"></u-icon>\r\n\t\t\t\t\t\t<text>免责声明</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t:name=\"expandedSections.disclaimer ? 'arrow-up' : 'arrow-down'\"\r\n\t\t\t\t\t\tsize=\"16\"\r\n\t\t\t\t\t\tcolor=\"#666\">\r\n\t\t\t\t\t</u-icon>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"disclaimer\" v-show=\"expandedSections.disclaimer\">\r\n\t\t\t\t\t<view class=\"disclaimer-text\">\r\n\t\t\t\t\t\t<text>1. 本页面所列第三方作品的版权归原作者所有，我们已按照相应开源协议的要求进行署名。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"disclaimer-text\">\r\n\t\t\t\t\t\t<text>2. 如发现任何版权问题或侵权行为，请及时联系我们，我们将在收到通知后立即处理。</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"disclaimer-text\">\r\n\t\t\t\t\t\t<text>3. 联系方式：{{ appInfo.contact }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 更新信息 -->\r\n\t\t\t<view class=\"update-info\">\r\n\t\t\t\t<text>最后更新：{{ updateDate }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tstatusBarHeight: 0,\r\n\t\t\tcurrentYear: new Date().getFullYear(),\r\n\t\t\tupdateDate: '2025-01-14',\r\n\t\t\tappInfo: {\r\n\t\t\t\tdeveloper: '扫码点餐系统开发团队',\r\n\t\t\t\tcontact: '<EMAIL>'\r\n\t\t\t},\r\n\t\t\t// 第三方作品列表 - 根据实际使用情况填写\r\n\t\t\tthirdPartyWorks: [\r\n\t\t\t\t{\r\n\t\t\t\t\tname: 'uView UI 组件库',\r\n\t\t\t\t\tauthor: 'uView团队',\r\n\t\t\t\t\ttype: 'UI组件库',\r\n\t\t\t\t\tlicense: 'MIT',\r\n\t\t\t\t\tsourceUrl: 'https://github.com/umicro/uView2.0',\r\n\t\t\t\t\tlicenseUrl: 'https://opensource.org/licenses/MIT',\r\n\t\t\t\t\tdescription: '基于Vue.js的移动端UI组件库，提供丰富的组件和工具'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: 'Feather Icons',\r\n\t\t\t\t\tauthor: 'Cole Bemis',\r\n\t\t\t\t\ttype: '图标素材',\r\n\t\t\t\t\tlicense: 'MIT',\r\n\t\t\t\t\tsourceUrl: 'https://feathericons.com/',\r\n\t\t\t\t\tlicenseUrl: 'https://opensource.org/licenses/MIT',\r\n\t\t\t\t\tdescription: '简洁美观的开源图标集，用于界面图标显示'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: 'CC BY 4.0 设计素材',\r\n\t\t\t\t\tauthor: '即时设计社区',\r\n\t\t\t\t\ttype: '设计素材',\r\n\t\t\t\t\tlicense: 'CC BY 4.0',\r\n\t\t\t\t\tsourceUrl: 'https://js.design/community?category=detail&type=resource&id=66b1e3824979a82bdd2c417d',\r\n\t\t\t\t\tlicenseUrl: 'https://creativecommons.org/licenses/by/4.0/',\r\n\t\t\t\t\tdescription: '遵循CC BY 4.0协议的高质量设计素材，可自由使用和修改'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: 'Uni-App框架',\r\n\t\t\t\t\tauthor: 'DCloud团队',\r\n\t\t\t\t\ttype: '开发框架',\r\n\t\t\t\t\tlicense: 'Apache 2.0',\r\n\t\t\t\t\tsourceUrl: 'https://uniapp.dcloud.io/',\r\n\t\t\t\t\tlicenseUrl: 'https://www.apache.org/licenses/LICENSE-2.0',\r\n\t\t\t\t\tdescription: '跨平台应用开发框架，支持多端发布'\r\n\t\t\t\t}\r\n\t\t\t\t// 可以根据实际使用的第三方资源继续添加\r\n\t\t\t],\r\n\t\t\t// 控制折叠状态\r\n\t\t\texpandedSections: {\r\n\t\t\t\tthirdParty: true,\r\n\t\t\t\toriginal: true,\r\n\t\t\t\tccLicense: true,\r\n\t\t\t\tdisclaimer: true\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 获取状态栏高度\r\n\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\tthis.statusBarHeight = systemInfo.statusBarHeight\r\n\t},\r\n\tmethods: {\r\n\t\t// 返回上一页\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack()\r\n\t\t},\r\n\t\t\r\n\t\t// 打开外部链接\r\n\t\topenLink(url) {\r\n\t\t\tif (!url) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '链接不可用',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\t// 复制链接到剪贴板（小程序限制无法直接打开外部链接）\r\n\t\t\tuni.setClipboardData({\r\n\t\t\t\tdata: url,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '链接已复制到剪贴板',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 切换章节展开/折叠状态\r\n\t\ttoggleSection(section) {\r\n\t\t\tthis.expandedSections[section] = !this.expandedSections[section]\r\n\t\t},\r\n\r\n\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tbackground-color: #f8f9fa;\r\n\tmin-height: 100vh;\r\n}\r\n\r\n// 自定义导航栏\r\n.custom-navbar {\r\n\tbackground-color: #fff;\r\n\tborder-bottom: 1px solid #eee;\r\n\t\r\n\t.navbar-content {\r\n\t\theight: 44px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 16px;\r\n\t\t\r\n\t\t.navbar-left, .navbar-right {\r\n\t\t\twidth: 60px;\r\n\t\t}\r\n\t\t\r\n\t\t.navbar-title {\r\n\t\t\tfont-size: 18px;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 页面内容\r\n.content {\r\n\tpadding: 20px 16px;\r\n}\r\n\r\n// 页面标题\r\n.page-title {\r\n\tfont-size: 28px;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\ttext-align: center;\r\n\tmargin-bottom: 8px;\r\n}\r\n\r\n.page-subtitle {\r\n\tfont-size: 14px;\r\n\tcolor: #666;\r\n\ttext-align: center;\r\n\tmargin-bottom: 32px;\r\n}\r\n\r\n// 章节样式\r\n.section {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12px;\r\n\tpadding: 20px;\r\n\tmargin-bottom: 16px;\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\r\n}\r\n\r\n.section-title {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 16px;\r\n\tpadding: 8px 0;\r\n\tcursor: pointer;\r\n\r\n\t.title-left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\ttext {\r\n\t\t\tfont-size: 18px;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #333;\r\n\t\t\tmargin-left: 8px;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 第三方作品列表\r\n.work-list {\r\n\t.work-item {\r\n\t\tborder: 1px solid #e0e0e0;\r\n\t\tborder-radius: 8px;\r\n\t\tpadding: 16px;\r\n\t\tmargin-bottom: 12px;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.work-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmargin-bottom: 12px;\r\n\r\n\t.work-name {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.license-badge {\r\n\t\tpadding: 4px 8px;\r\n\t\tborder-radius: 4px;\r\n\t\tfont-size: 12px;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tcolor: #666;\r\n\r\n\t\t&.license-mit {\r\n\t\t\tbackground-color: #e8f5e8;\r\n\t\t\tcolor: #2e7d32;\r\n\t\t}\r\n\r\n\t\t&.license-cc {\r\n\t\t\tbackground-color: #fff3e0;\r\n\t\t\tcolor: #f57c00;\r\n\t\t}\r\n\r\n\t\t&.license-apache {\r\n\t\t\tbackground-color: #e3f2fd;\r\n\t\t\tcolor: #1976d2;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.work-description {\r\n\tmargin-bottom: 12px;\r\n\tpadding: 8px 12px;\r\n\tbackground-color: #f8f9fa;\r\n\tborder-radius: 6px;\r\n\tborder-left: 3px solid #FF6B35;\r\n\r\n\ttext {\r\n\t\tfont-size: 13px;\r\n\t\tcolor: #666;\r\n\t\tline-height: 1.5;\r\n\t}\r\n}\r\n\r\n.work-details {\r\n\t.detail-row {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 8px;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t\t\r\n\t\t.label {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #666;\r\n\t\t\twidth: 80px;\r\n\t\t\tflex-shrink: 0;\r\n\t\t}\r\n\t\t\r\n\t\t.value {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #333;\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\t\t\r\n\t\t.link {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #1976d2;\r\n\t\t\ttext-decoration: underline;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 原创内容声明\r\n.original-content {\r\n\t.copyright-text {\r\n\t\tmargin-bottom: 12px;\r\n\t\tline-height: 1.6;\r\n\t\t\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\t\t\r\n\t\ttext {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t\t\r\n\t\t.highlight {\r\n\t\t\tcolor: #FF6B35;\r\n\t\t\tfont-weight: 600;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// CC BY 4.0 协议介绍\r\n.cc-license {\r\n\t.license-intro {\r\n\t\tmargin-bottom: 20px;\r\n\t\tpadding: 16px;\r\n\t\tbackground-color: #fff3e0;\r\n\t\tborder-radius: 8px;\r\n\t\tborder-left: 4px solid #FF9800;\r\n\r\n\t\t.intro-text {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #333;\r\n\t\t\tline-height: 1.6;\r\n\t\t}\r\n\t}\r\n\r\n\t.license-features, .license-conditions {\r\n\t\tmargin-bottom: 20px;\r\n\r\n\t\t.feature-title, .condition-title {\r\n\t\t\tmargin-bottom: 12px;\r\n\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 16px;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.feature-item, .condition-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: flex-start;\r\n\t\t\tmargin-bottom: 10px;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.feature-text, .condition-text {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t\tmargin-left: 8px;\r\n\t\t\t\tflex: 1;\r\n\t\t\t}\r\n\r\n\t\t\t.feature-bold, .condition-bold {\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #FF6B35;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.license-links {\r\n\t\tborder-top: 1px solid #eee;\r\n\t\tpadding-top: 16px;\r\n\r\n\t\t.link-item {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-bottom: 12px;\r\n\t\t\tpadding: 8px 0;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\r\n\t\t\t.link-text {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: #1976d2;\r\n\t\t\t\tmargin-left: 8px;\r\n\t\t\t\ttext-decoration: underline;\r\n\t\t\t}\r\n\r\n\t\t\t&:active {\r\n\t\t\t\topacity: 0.7;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 免责声明\r\n.disclaimer {\r\n\t.disclaimer-text {\r\n\t\tmargin-bottom: 12px;\r\n\t\tline-height: 1.6;\r\n\r\n\t\t&:last-child {\r\n\t\t\tmargin-bottom: 0;\r\n\t\t}\r\n\r\n\t\ttext {\r\n\t\t\tfont-size: 14px;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 更新信息\r\n.update-info {\r\n\ttext-align: center;\r\n\tmargin-top: 32px;\r\n\tpadding-top: 16px;\r\n\tborder-top: 1px solid #eee;\r\n\t\r\n\ttext {\r\n\t\tfont-size: 12px;\r\n\t\tcolor: #999;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./copyright.vue?vue&type=style&index=0&id=30c7cdd4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./copyright.vue?vue&type=style&index=0&id=30c7cdd4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309685\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}