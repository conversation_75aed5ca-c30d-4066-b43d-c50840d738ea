<button class="{{['custom-btn','btn-'+type,size?'btn-'+size:'',block?'btn-block':'',round?'btn-round':'',disabled?'btn-disabled':'']}}" disabled="{{disabled}}" form-type="{{formType}}" open-type="{{openType}}" data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" bindtap="__e"><block wx:if="{{icon}}"><custom-icon class="btn-icon" vue-id="8a64eaca-1" name="{{icon}}" color="{{iconColor}}" size="{{iconSize}}" bind:__l="__l"></custom-icon></block><slot></slot></button>