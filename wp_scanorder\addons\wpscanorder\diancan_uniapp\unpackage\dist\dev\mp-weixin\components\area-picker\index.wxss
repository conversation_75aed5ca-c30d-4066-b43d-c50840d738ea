@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.area-picker-header.data-v-36ea85b4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: 0 30rpx;
  border-bottom: 1px solid #f5f5f5;
}
.area-picker-header .title.data-v-36ea85b4 {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.area-picker-header .cancel-btn.data-v-36ea85b4, .area-picker-header .confirm-btn.data-v-36ea85b4 {
  font-size: 28rpx;
  padding: 20rpx 0;
}
.area-picker-header .cancel-btn.data-v-36ea85b4 {
  color: #999;
}
.area-picker-header .confirm-btn.data-v-36ea85b4 {
  color: #8cd548;
}
.area-picker-body.data-v-36ea85b4 {
  display: flex;
  height: 500rpx;
}
.area-picker-body .area-column.data-v-36ea85b4 {
  flex: 1;
  height: 100%;
}
.area-picker-body .area-column .area-scroll.data-v-36ea85b4 {
  height: 100%;
}
.area-picker-body .area-column .area-scroll .area-item.data-v-36ea85b4 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #666;
}
.area-picker-body .area-column .area-scroll .area-item.active.data-v-36ea85b4 {
  color: #8cd548;
  font-weight: 500;
}
.area-picker-body .area-column .area-scroll .area-loading.data-v-36ea85b4, .area-picker-body .area-column .area-scroll .area-empty.data-v-36ea85b4 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
}
.area-picker-body .area-column .area-scroll .area-loading text.data-v-36ea85b4, .area-picker-body .area-column .area-scroll .area-empty text.data-v-36ea85b4 {
  font-size: 26rpx;
  color: #999;
}

