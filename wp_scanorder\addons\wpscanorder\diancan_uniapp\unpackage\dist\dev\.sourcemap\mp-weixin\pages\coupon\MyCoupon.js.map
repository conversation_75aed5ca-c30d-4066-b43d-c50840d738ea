{"version": 3, "sources": ["webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/MyCoupon.vue?057b", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/MyCoupon.vue?79ab", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/MyCoupon.vue?e06c", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/MyCoupon.vue?9b5b", "uni-app:///pages/coupon/MyCoupon.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/MyCoupon.vue?f4f5", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/MyCoupon.vue?36a9"], "names": ["name", "data", "currentTab", "tabs", "couponList", "loading", "error", "errorMsg", "computed", "indicatorLeft", "filteredCouponList", "onLoad", "methods", "handleBack", "uni", "switchTab", "fetchCoupons", "res", "item", "status", "console", "useCoupon", "success"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6ErrB;AAAA;AAAA;AAAA,eAEA;EACAA;EAEAC;IACA;MACAC;MACAC,OACA;QAAAH;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAI;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;QACA;UAAA;QAAA;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;kBACA;kBACA;oBACA;oBACA;oBACA,uCACAC;sBACAC;oBAAA;kBAEA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;;MAEA;MACA;;MAEA;MACAP;QACAQ;UACA;UACAR;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAAoxC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAxyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/coupon/MyCoupon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./MyCoupon.vue?vue&type=template&id=2aa2dbf2&scoped=true&\"\nvar renderjs\nimport script from \"./MyCoupon.vue?vue&type=script&lang=js&\"\nexport * from \"./MyCoupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./MyCoupon.vue?vue&type=style&index=0&id=2aa2dbf2&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2aa2dbf2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/coupon/MyCoupon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./MyCoupon.vue?vue&type=template&id=2aa2dbf2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && !_vm.error ? _vm.filteredCouponList.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      item.status !== \"expired\" ? _vm.useCoupon(item) : \"\"\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./MyCoupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./MyCoupon.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"wp_-flex-col page\">\r\n    <view class=\"wp_-flex-col\">\r\n      <!-- 顶部导航栏 -->\r\n      <nav-bar title=\"我的优惠券\" @back=\"handleBack\"></nav-bar>\r\n\r\n      <!-- 状态切换 -->\r\n      <view class=\"wp_-flex-row wp_-justify-between wp_-relative section_4\">\r\n        <view \r\n          v-for=\"(tab, index) in tabs\" \r\n          :key=\"index\"\r\n          :class=\"['wp_-flex-row wp_-justify-start wp_-items-center text-wrapper', {'active': currentTab === index}]\"\r\n          @tap=\"switchTab(index)\"\r\n        >\r\n          <text class=\"font\" :class=\"{'active-text': currentTab === index}\">{{tab.name}}</text>\r\n        </view>\r\n        <view class=\"section_5 pos_3\" :style=\"{left: indicatorLeft}\"></view>\r\n      </view>\r\n\r\n      <!-- 加载状态 -->\r\n      <view class=\"loading-container\" v-if=\"loading\">\r\n        <view class=\"loading-circle\"></view>\r\n        <text class=\"loading-text\">加载中...</text>\r\n      </view>\r\n\r\n      <!-- 错误提示 -->\r\n      <view class=\"error-container\" v-else-if=\"error\">\r\n        <text class=\"error-text\">{{errorMsg}}</text>\r\n        <view class=\"retry-btn\" @tap=\"fetchCoupons\">\r\n          <text class=\"retry-text\">重试</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 优惠券列表 -->\r\n      <view class=\"wp_-flex-col list\" v-else>\r\n        <view \r\n          class=\"wp_-flex-col wp_-mt-10 section_6 list-item\" \r\n          v-for=\"(item, index) in filteredCouponList\" \r\n          :key=\"index\"\r\n        >\r\n          <view class=\"wp_-flex-row wp_-justify-between group\">\r\n            <view class=\"wp_-flex-row wp_-items-baseline wp_-self-start group_2\">\r\n              <text class=\"font_4 text_7\">￥</text>\r\n              <text class=\"font_3 text_6\">{{item.amount}}</text>\r\n            </view>\r\n            <view class=\"wp_-flex-col wp_-items-center\">\r\n              <text class=\"font_2 text_5\">{{item.name}}</text>\r\n              <text class=\"font_5 text_8 mt-17\">有效期至{{item.expireDate}}</text>\r\n            </view>\r\n            <view \r\n              class=\"wp_-flex-col wp_-justify-start wp_-items-center wp_-self-start text-wrapper_3 view\"\r\n              :class=\"{'disabled': item.status === 'expired'}\"\r\n              @tap=\"item.status !== 'expired' ? useCoupon(item) : ''\"\r\n            >\r\n              <text class=\"font_5 text_9\">{{item.status === 'expired' ? '已过期' : '使用'}}</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"wp_-flex-col mt-3\">\r\n            <view class=\"wp_-flex-row wp_-self-stretch\">\r\n              <text class=\"font_6 text_10\">优惠券</text>\r\n              <text class=\"font_6 ml-1\">（元）</text>\r\n            </view>\r\n            <text class=\"wp_-self-center font_5 text_11\">{{item.condition}}</text>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 空状态 -->\r\n        <view class=\"empty-state\" v-if=\"filteredCouponList.length === 0\">\r\n          <image class=\"empty-image\" src=\"/static/order/e186e04e8774da64b58c96a8bb479840.png\" />\r\n          <text class=\"empty-text\">暂无优惠券</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getMyCoupons } from '@/api/user.js'\r\n\r\nexport default {\r\n  name: 'MyCoupon',\r\n  \r\n  data() {\r\n    return {\r\n      currentTab: 0,\r\n      tabs: [\r\n        { name: '全部' },\r\n        { name: '未使用' }, \r\n        { name: '已过期' }\r\n      ],\r\n      couponList: [],\r\n      loading: false,\r\n      error: false,\r\n      errorMsg: '加载失败，请重试'\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    indicatorLeft() {\r\n      return 82 + (this.currentTab * 120) + 'rpx'\r\n    },\r\n    \r\n    // 根据当前选中的标签筛选优惠券列表\r\n    filteredCouponList() {\r\n      if (this.currentTab === 0) {\r\n        return this.couponList\r\n      } else if (this.currentTab === 1) {\r\n        return this.couponList.filter(item => item.status === 'valid')\r\n      } else {\r\n        return this.couponList.filter(item => item.status === 'expired')\r\n      }\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    this.fetchCoupons()\r\n  },\r\n\r\n  methods: {\r\n    handleBack() {\r\n      uni.navigateBack()\r\n    },\r\n\r\n    switchTab(index) {\r\n      this.currentTab = index\r\n    },\r\n    \r\n    // 获取优惠券列表\r\n    async fetchCoupons() {\r\n      this.loading = true\r\n      this.error = false\r\n      \r\n      try {\r\n        const res = await getMyCoupons()\r\n        if (res.code === 1) {\r\n          // 处理后台返回的数据，添加状态标识\r\n          this.couponList = res.data.map(item => {\r\n            // 检查优惠券是否已过期\r\n            const isExpired = new Date(item.expireDate) < new Date()\r\n            return {\r\n              ...item,\r\n              status: isExpired ? 'expired' : 'valid'\r\n            }\r\n          })\r\n        } else {\r\n          this.error = true\r\n          this.errorMsg = res.msg || '获取优惠券失败'\r\n        }\r\n      } catch (error) {\r\n        console.error('获取优惠券列表失败:', error)\r\n        this.error = true\r\n        this.errorMsg = '网络异常，请稍后重试'\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    useCoupon(coupon) {\r\n      if (coupon.status === 'expired') return\r\n      \r\n      // TODO: 使用优惠券逻辑\r\n      this.$emit('use-coupon', coupon)\r\n      \r\n      // 关闭当前页面，将选中的优惠券传回上一页\r\n      uni.navigateBack({\r\n        success: () => {\r\n          // 通过事件总线传递数据\r\n          uni.$emit('selected-coupon', coupon)\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.page {\r\n  background-color: #f8fafb;\r\n  width: 100%;\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n  height: 100%;\r\n\r\n  .section_4 {\r\n    padding: 28rpx 56rpx 36rpx;\r\n    background-color: #ffffff;\r\n    position: relative;\r\n\r\n    .text-wrapper {\r\n      width: 90rpx;\r\n      \r\n      &.active .font {\r\n        color: #77d431;\r\n      }\r\n    }\r\n\r\n    .font {\r\n      font-size: 30rpx;\r\n      font-family: MiSans;\r\n      line-height: 27.6rpx;\r\n      color: #a2a2a2;\r\n      \r\n      &.active-text {\r\n        color: #77d431;\r\n      }\r\n    }\r\n\r\n    .section_5 {\r\n      background-color: #8cd548;\r\n      border-radius: 322rpx;\r\n      width: 40rpx;\r\n      height: 4rpx;\r\n      position: absolute;\r\n      bottom: 20rpx;\r\n      transition: left 0.3s;\r\n    }\r\n  }\r\n\r\n  .loading-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 100rpx 0;\r\n    \r\n    .loading-circle {\r\n      width: 60rpx;\r\n      height: 60rpx;\r\n      border: 4rpx solid #f3f3f3;\r\n      border-top: 4rpx solid #8cd548;\r\n      border-radius: 50%;\r\n      animation: spin 1s linear infinite;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .loading-text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n  \r\n  .error-container {\r\n    padding: 100rpx 0;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    \r\n    .error-text {\r\n      font-size: 28rpx;\r\n      color: #ff5b5b;\r\n      margin-bottom: 30rpx;\r\n    }\r\n    \r\n    .retry-btn {\r\n      padding: 16rpx 60rpx;\r\n      background-color: #8cd548;\r\n      border-radius: 100rpx;\r\n      \r\n      .retry-text {\r\n        font-size: 28rpx;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .empty-state {\r\n    padding: 100rpx 0;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    \r\n    .empty-image {\r\n      width: 200rpx;\r\n      height: 200rpx;\r\n      margin-bottom: 30rpx;\r\n    }\r\n    \r\n    .empty-text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n\r\n  .list {\r\n    padding: 30rpx 12rpx 0;\r\n\r\n    .section_6 {\r\n      margin: 10rpx 8rpx 0;\r\n      padding: 36rpx 12rpx 24rpx 48rpx;\r\n      background-color: #ffffff;\r\n      border-radius: 20rpx;\r\n\r\n      .group {\r\n        padding-left: 16rpx;\r\n\r\n        .group_2 {\r\n          margin-top: 16rpx;\r\n          \r\n          .font_3 {\r\n            font-size: 40rpx;\r\n            color: #000000;\r\n          }\r\n\r\n          .font_4 {\r\n            font-size: 24rpx;\r\n            color: #000000;\r\n          }\r\n        }\r\n\r\n        .font_2 {\r\n          font-size: 32rpx;\r\n          color: #000000;\r\n        }\r\n\r\n        .text-wrapper_3 {\r\n          margin-top: 52rpx;\r\n          padding: 8rpx 0;\r\n          background-color: #77d431;\r\n          border-radius: 6rpx;\r\n          width: 100rpx;\r\n          height: 40rpx;\r\n          text-align: center;\r\n\r\n          .text_9 {\r\n            color: #ffffff;\r\n          }\r\n          \r\n          &.disabled {\r\n            background-color: #cccccc;\r\n          }\r\n        }\r\n      }\r\n\r\n      .font_5 {\r\n        font-size: 28rpx;\r\n        color: #b3b3b3;\r\n      }\r\n\r\n      .font_6 {\r\n        font-size: 28rpx;\r\n        color: #000000;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.mt-17 {\r\n  margin-top: 34rpx;\r\n}\r\n\r\n.mt-3 {\r\n  margin-top: 6rpx;\r\n}\r\n\r\n.ml-1 {\r\n  margin-left: 2rpx;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./MyCoupon.vue?vue&type=style&index=0&id=2aa2dbf2&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./MyCoupon.vue?vue&type=style&index=0&id=2aa2dbf2&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948310059\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}