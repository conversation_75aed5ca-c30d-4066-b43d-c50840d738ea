import request from '@/utils/request';

export const getBanners = () => {
    return request({
        url: '/banner/index',
        method: 'GET'
    });
}; 

/**
 * 获取热销商品列表
 * @param {Object} params - 请求参数
 * @param {number} [params.limit=10] - 获取商品的数量限制
 * @returns {Promise} 热销商品数据
 */
export const getTopSellingProducts = (params = {}) => {
    return request({
        url: '/product/topSelling',
        method: 'GET',
        params
    });
}; 