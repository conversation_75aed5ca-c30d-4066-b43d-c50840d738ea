<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">邀请好友</text>
      </view>
    </view>

    <!-- 海报内容 -->
    <view class="poster-content">
      <view class="poster-card">
        <image class="user-avatar" :src="userInfo.avatarUrl || '/static/my/default-avatar.png'" mode="aspectFill" />
        <text class="user-name">{{userInfo.nickName || '用户昵称'}}</text>
        <text class="invite-text">诚邀您体验美食</text>
        <image class="qr-code" src="/static/invite/qrcode.png" mode="aspectFill" />
        <text class="tip">扫码立即注册</text>
      </view>

      <!-- 分享按钮组 -->
      <view class="share-btns">
        <view class="btn-item" @tap="saveImage">
          <u-icon name="download" size="40" color="#333"></u-icon>
          <text>保存图片</text>
        </view>
        <view class="btn-item" @tap="shareToFriend">
          <u-icon name="weixin-fill" size="40" color="#333"></u-icon>
          <text>分享好友</text>
        </view>
      </view>

      <!-- 邀请说明 -->
      <view class="invite-rules">
        <view class="rule-title">活动规则</view>
        <view class="rule-item">1. 邀请好友注册并完成首单</view>
        <view class="rule-item">2. 好友获得10元新人券</view>
        <view class="rule-item">3. 您获得5元现金奖励</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {}
    }
  },
  
  onLoad() {
    this.userInfo = uni.getStorageSync('userInfo') || {}
  },
  
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    
    // 保存海报图片
    saveImage() {
      uni.showLoading({
        title: '保存中...'
      })
      
      setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
          title: '保存成功',
          icon: 'none'
        })
      }, 1500)
    },
    
    // 分享给好友
    shareToFriend() {
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
  }
}

.poster-content {
  padding: 30rpx;
  
  .poster-card {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    
    .user-avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      margin-bottom: 20rpx;
    }
    
    .user-name {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 12rpx;
    }
    
    .invite-text {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 40rpx;
    }
    
    .qr-code {
      width: 300rpx;
      height: 300rpx;
      margin-bottom: 20rpx;
    }
    
    .tip {
      font-size: 26rpx;
      color: #999;
    }
  }
  
  .share-btns {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;
    
    .btn-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 40rpx;
      
      text {
        font-size: 26rpx;
        color: #333;
        margin-top: 12rpx;
      }
      
      &:active {
        opacity: 0.8;
      }
    }
  }
  
  .invite-rules {
    margin-top: 60rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    
    .rule-title {
      font-size: 30rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    
    .rule-item {
      font-size: 26rpx;
      color: #666;
      line-height: 1.8;
    }
  }
}
</style> 