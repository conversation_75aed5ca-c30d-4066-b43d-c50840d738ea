@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.icon-text {
  display: inline-flex;
  align-items: center;
}
.icon-text.right-icon {
  flex-direction: row-reverse;
}
.icon-text.right-icon .icon {
  margin-left: 10rpx;
  margin-right: 0;
}
.icon-text .icon {
  margin-right: 10rpx;
}
.icon-text .text {
  font-size: 28rpx;
}
.icon-grid {
  display: flex;
  flex-wrap: wrap;
}
.icon-grid .icon-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
}
.icon-grid .icon-grid-item .icon-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
.description .list-item {
  margin: 10rpx 0;
  padding-left: 30rpx;
}
.description .tip {
  display: block;
  margin-top: 30rpx;
  color: #8cd548;
  font-weight: bold;
}
.example-section {
  margin-bottom: 40rpx;
}
.example-section:last-child {
  margin-bottom: 0;
}
.example-section .example-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.example-section .example-content .code-block {
  margin-top: 20rpx;
  background-color: #f7f7f7;
  padding: 20rpx;
  border-radius: 8rpx;
}
.example-section .example-content .code-block .code {
  font-family: monospace;
  font-size: 24rpx;
  color: #666666;
  word-break: break-all;
}

