<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <view class="left" @tap="handleBack">
          <u-icon name="arrow-left" color="#333" size="20"></u-icon>
        </view>
        <text class="title">订单详情</text>
        <view class="right" @tap="refreshOrderData">
          <u-icon name="reload" color="#333" size="20"></u-icon>
        </view>
      </view>
    </view>

    <!-- 订单状态 -->
    <view class="status-card" :class="{
      'unpaid-card': !isPaid, 
      'paid-card': order.status === 'paid',
      'cooking-card': order.status === 'cooking',
      'cooked-card': order.status === 'cooked',
      'delivering-card': order.status === 'delivering',
      'completed-card': order.status === 'completed',
      'cancelled-card': order.status === 'cancelled'
    }">
      <view class="status-info" v-if="!loading && Object.keys(order).length > 0">
        <text class="status" v-if="order.status === 'pending'">待支付</text>
        <text class="status" v-else-if="order.status === 'paid'">已支付</text>
        <text class="status" v-else-if="order.status === 'cooking'">制作中</text>
        <text class="status" v-else-if="order.status === 'cooked'">制作完成</text>
        <text class="status" v-else-if="order.status === 'delivering'">配送中</text>
        <text class="status" v-else-if="order.status === 'completed'">已完成</text>
        <text class="status" v-else-if="order.status === 'cancelled'">已取消</text>
        <text class="status" v-else>{{isPaid ? '已支付' : '待支付'}}</text>
        
        <text class="desc" v-if="order.status === 'pending'">请尽快完成支付，订单将在30分钟后自动取消</text>
        <text class="desc" v-else-if="order.status === 'cancelled'">订单已取消，请重新下单</text>
        <text class="desc" v-else>{{getStatusDesc()}}</text>
        
        <view class="auto-refresh-tip" v-if="order.status !== 'completed' && order.status !== 'cancelled'">
          <text>订单状态自动刷新中</text>
        </view>
      </view>
      <view class="status-info" v-else>
        <text class="status">加载中...</text>
        <text class="desc">正在获取订单信息</text>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-card">
      <view class="card-title">订单信息</view>
      <view class="info-item">
        <text class="label">取餐号</text>
        <text class="value">{{order.pickup_code || order.pickup_code || ''}}</text>
      </view>
      <view class="info-item">
        <text class="label">订单编号</text>
        <text class="value">{{order.order_no || order.orderNo}}</text>
      </view>
      <view class="info-item">
        <text class="label">下单时间</text>
        <text class="value">{{order.createtime_text || order.createTime}}</text>
      </view>
      <view class="info-item">
        <text class="label">用餐方式</text>
        <text class="value">{{(order.dining_type || order.type) === 'dine-in' ? '堂食' : '外卖'}}</text>
      </view>
      <view class="info-item" v-if="(order.dining_type || order.type) === 'takeout'">
        <text class="label">配送地址</text>
        <text class="value">{{order.address ? order.address.replace(/,/g, '\n') : '未设置地址'}}</text>
      </view>
      <view class="info-item">
        <text class="label">备注信息</text>
        <text class="value">{{order.remark || '无'}}</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="product-card">
      <view class="card-title">商品信息</view>
      <view class="product-list">
        <view class="product-item" v-for="(item, index) in order.products" :key="index">
          <text class="product-name">{{item.product_name || item.name}}</text>
          <text class="product-specs">{{item.props_text || item.specs || ''}}</text>
          <view class="product-price">
            <text>¥{{item.price || item.product_price || 0}}</text>
            <text class="count">x{{item.quantity || item.count || 1}}</text>
          </view>
        </view>
      </view>
      <view class="price-info">
        <view class="price-item">
          <text class="label">商品小计</text>
          <text class="value">¥{{(order.total_price || order.totalPrice || 0).toFixed(2)}}</text>
        </view>
        <view class="price-item">
          <text class="label">优惠金额</text>
          <text class="value">-¥{{(order.discount_amount || order.discountAmount || 0).toFixed(2)}}</text>
        </view>
        <view class="price-item total">
          <text class="label">实付金额</text>
          <text class="value" :class="{'unpaid': !isPaid}">¥{{(order.final_price || order.finalPrice || order.total_price || order.totalPrice || 0).toFixed(2)}}</text>
        </view>
        <view class="price-item payment-status">
          <text class="label">支付状态</text>
          <text class="value" :class="{'paid': isPaid, 'unpaid': !isPaid}">{{isPaid ? '已支付' : '待支付'}}</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-bar">
      <view class="btn outline" @tap="reorder">再来一单</view>
      
      <template v-if="!loading && Object.keys(order).length > 0">
        <!-- 订单取消按钮 -->
        <view class="btn outline" 
              v-if="['pending', 'paid'].includes(order.status)"
              @tap="cancelOrder">
          取消订单
        </view>
        
        <!-- 待支付状态显示去支付按钮 -->
        <view class="btn primary" v-if="order.status === 'pending'" @tap="goPay">
          去支付
        </view>
        
        <!-- 已支付、制作中、制作完成、配送中状态显示查看进度按钮 -->
        <view class="btn primary" v-else-if="['paid', 'cooking', 'cooked', 'delivering'].includes(order.status)" @tap="checkProgress">
          查看进度
        </view>
        
        <!-- 已完成状态可以评价 -->
        <view class="btn primary" v-else-if="order.status === 'completed'" v-show="false" @tap="goComment">
          评价订单
        </view>
      </template>
    </view>
  </view>
</template>

<script>
import { getOrderDetail, getWxPayParams, queryWxPayResult, payOrderWithBalance, cancelOrder } from '@/api/order';

export default {
  data() {
    return {
      order: {},
      loading: true,
      orderId: '',
      refreshTimer: null,
      lastRefreshTime: 0
    }
  },
  
  onShow() {
    console.log('页面显示');
    
    this.startAutoRefresh();
  },
  
  onHide() {
    console.log('页面隐藏，清除自动刷新');
    this.clearAutoRefresh();
  },
  
  onUnload() {
    console.log('页面卸载，清除自动刷新');
    this.clearAutoRefresh();
    // 确保在页面卸载时取消所有定时器
    clearInterval(this.refreshTimer);
    this.refreshTimer = null;
  },
  
  computed: {
    // 判断订单是否已支付
    isPaid() {
      if (this.order.status == 'cancelled') {
        return false
      }
      // 如果订单有pay_status字段，则根据pay_status判断
      if (this.order.pay_status !== undefined) {
        return this.order.pay_status === 1 || this.order.pay_status === '1' || this.order.pay_status === true;
      }
      // 如果订单有支付时间，认为已支付
      if (this.order.pay_time || this.order.payTime) {
        return true;
      }
      // 如果都没有，则根据订单状态判断（completed状态认为是已支付）
      return this.order.status === 'completed';
    }
  },
  
  onLoad(options) {
    console.log('接收到的参数:', options)
    
    // 获取订单号
    const orderId = options.order_no || options.orderNo || options.id || '';
    
    console.log('页面接收到的订单ID:', orderId);
    
    // 保存订单ID，用于页面重新显示时刷新数据
    this.orderId = orderId;
    
    if(orderId) {
      // 显示加载中
      uni.showLoading({
        title: '加载中...'
      });
      
      // 优先从API获取最新订单数据
      if (!orderId.startsWith('temp_')) {
        // 直接从API获取订单详情
        console.log('直接从API获取订单详情');
        this.fetchOrderDetail(orderId);
      }
    } else {
      uni.showToast({
        title: '订单ID不存在',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  },
  
  methods: {
    // 从API获取订单详情
    fetchOrderDetail(orderId, silent = false) {
      // 设置加载状态
      this.loading = true;
      
      // 如果是临时ID，不进行API请求
      if (orderId.startsWith('temp_')) {
        console.log('临时ID不进行API请求');
        if (!silent) {
          uni.hideLoading();
          uni.showToast({
            title: '订单信息暂未同步',
            icon: 'none'
          });
        }
        this.loading = false;
        return;
      }
      
      // 直接使用订单号
      const order_no = orderId;
      
      console.log('正在从API获取订单详情，订单号:', order_no);
      getOrderDetail(order_no)
        .then(res => {
          console.log('API返回的订单详情:', res);
          
          if (res.code === 1 && res.data) {
            // 处理订单数据
            const orderData = res.data;
            console.log('处理前的订单数据:', JSON.stringify(orderData));
            
            // 使用通用方法处理订单数据
            setTimeout(() => {
              this.processOrderData(orderData);
              console.log('订单数据处理完成');
              
              // 初始化自动刷新（首次加载完成后）
              this.startAutoRefresh();
            }, 10);
          } else {
            uni.showToast({
              title: res.msg || '订单不存在',
              icon: 'none'
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          }
        })
        .catch(err => {
          console.error('获取订单详情失败:', err);
          
          uni.hideLoading();
          
          uni.showToast({
            title: '获取订单详情失败',
            icon: 'none'
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        })
        .finally(() => {
          this.loading = false;
          if (!silent) {
            uni.hideLoading();
          }
        });
    },
    
    handleBack() {
      // 返回到首页
      uni.switchTab({
        url: '/pages/order/order'
      })
    },
    
    // 手动刷新订单数据
    refreshOrderData(silent = false) {
      if (this.loading) {
        if (!silent) {
          uni.showToast({
            title: '正在加载中...',
            icon: 'none'
          });
        }
        return;
      }
      
      // 控制刷新频率，防止频繁请求
      const now = Date.now();
      if (now - this.lastRefreshTime < 3000) { // 3秒内不重复刷新
        console.log('刷新过于频繁，跳过');
        return;
      }
      
      this.lastRefreshTime = now;
      
      if (this.orderId) {
        if (!silent) {
          uni.showLoading({
            title: '刷新数据...'
          });
        }
        console.log((silent ? '自动' : '手动') + '刷新订单数据:', this.orderId);
        
        // 设置临时loading状态但不影响UI
        const tempLoading = this.loading;
        this.fetchOrderDetail(this.orderId, silent);
      } else {
        if (!silent) {
          uni.showToast({
            title: '订单ID不存在',
            icon: 'none'
          });
        }
      }
    },

    // 处理订单数据
    processOrderData(orderData) {
      try {
        // 确保订单商品数据格式正确
        if (orderData.order_products && Array.isArray(orderData.order_products)) {
          // 使用接口返回的订单商品数据
          orderData.order_products = orderData.order_products.map(product => ({
            ...product,
            image: product.image || '',
            specs: product.props_text || product.specs || '',
            count: product.quantity || product.count || 1,
            price: parseFloat(product.price || product.product_price || 0)
          }));
          // 将order_products复制到products字段
          orderData.products = [...orderData.order_products];
        } else if (orderData.products && Array.isArray(orderData.products)) {
          // 兼容旧数据格式
          orderData.products = orderData.products.map(product => ({
            ...product,
            image: product.image || '',
            specs: product.props_text || product.specs || '',
            count: product.count || product.quantity || 1,
            price: parseFloat(product.price || product.product_price || 0)
          }));
        } else {
          orderData.products = [];
        }
        
        // 确保价格字段为数字
        orderData.total_price = parseFloat(orderData.total_price || 0);
        orderData.final_price = parseFloat(orderData.final_price || orderData.totalPrice || orderData.total_price || 0);
        orderData.discount_amount = parseFloat(orderData.discount_amount || 0);
        
        // 更新订单数据
        this.order = orderData;
        
        return orderData;
      } catch (error) {
        console.error('处理订单数据失败:', error);
        return orderData;
      }
    },
    
    getStatusDesc() {
      const orderType = this.order.dining_type || this.order.type;
      const orderStatus = this.order.status;
      
      // 如果订单数据不完整，返回空字符串
      if (!orderType || !orderStatus) {
        return '';
      }
      
      // 根据不同状态和用餐方式返回不同描述
      if (orderType === 'dine-in') {
        // 堂食订单
        switch (orderStatus) {
          case 'paid':
            return `您的订单已支付成功，正在安排制作，请耐心等待`;
          case 'cooking':
            return `您的订单正在制作中，预计3分钟后可取餐，取餐号：${this.order.pickup_code || ''}`;
          case 'cooked':
            return `您的订单已制作完成，请前往取餐区取餐，取餐号：${this.order.pickup_code || ''}`;
          case 'completed':
            return `感谢您的光临，期待您再次惠顾`;
          default:
            return `预计3分钟后可取餐，取餐号：${this.order.pickup_code || ''}`;
        }
      } else {
        // 外卖订单
        switch (orderStatus) {
          case 'paid':
            return `您的订单已支付成功，正在安排制作，请耐心等待`;
          case 'cooking':
            return `您的订单正在制作中，制作完成后将为您配送`;
          case 'cooked':
            return `您的订单已制作完成，正在等待配送员取餐`;
          case 'delivering':
            return `您的订单正在配送中，预计${this.order.estimate_time || '15'}分钟送达`;
          case 'completed':
            return `订单已送达，感谢您的惠顾`;
          default:
            return `预计15分钟送达，配送员：${this.order.delivery_name || ''}`;
        }
      }
    },
    
    reorder() {
      // 获取订单商品数据
      const products = this.order.products || this.order.order_products || [];
      
      if (!this.order || !products || !Array.isArray(products) || products.length === 0) {
        uni.showToast({
          title: '订单数据异常',
          icon: 'none'
        });
        return;
      }
      
      // 构建购物车数据
      const cartData = {
        list: products.map(item => {
          const price = parseFloat(item.price || item.product_price || 0);
          const count = parseInt(item.quantity || item.count || 1);
          return {
            id: item.product_id || item.id,
            name: item.product_name || item.name,
            price: price,
            count: count,
            totalPrice: price * count,
          image: item.image || '',
            specs: item.props_text || item.specs || '',
          specSelected: true
          };
        }),
        total: products.reduce((sum, item) => sum + parseInt(item.quantity || item.count || 1), 0),
        price: parseFloat(this.order.total_price || this.order.totalPrice || 0)
      }
      
      // 保存到本地存储
      uni.setStorageSync('cartData', cartData)
      
      // 保存用餐方式
      uni.setStorageSync('diningType', this.order.type)
      
      // 显示提示
      uni.showToast({
        title: '已添加到购物车',
        icon: 'none',
        duration: 1500,
        success: () => {
          // 跳转到菜单页
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/menu/menu'
            })
          }, 1500)
        }
      })
    },
    
    checkProgress() {
      // 如果正在加载数据，不允许查看进度
      if (this.loading) {
        uni.showToast({
          title: '订单信息加载中...',
          icon: 'none'
        });
        return;
      }
      
      // 未支付的订单不能查看进度
      if (!this.isPaid) {
        uni.showToast({
          title: '请先完成支付',
          icon: 'none'
        });
        return;
      }
      
      uni.showModal({
        title: '订单进度',
        content: this.getProgressDesc(),
        showCancel: false,
        confirmText: '我知道了'
      })
    },
    
    // 评价订单
    goComment() {
      uni.showToast({
        title: '评价功能开发中',
        icon: 'none'
      });
      
      // 可以跳转到评价页面，这里先用提示代替
      // uni.navigateTo({
      //   url: `/pages/order/comment?id=${this.order.order_no || this.order.orderNo}`
      // });
    },
    
    getProgressDesc() {
      const orderType = this.order.dining_type || this.order.type;
      const orderStatus = this.order.status;
      
      // 如果订单数据不完整，返回加载中提示
      if (!orderType || !orderStatus) {
        return '正在获取订单进度信息...';
      }
      
      if(orderType === 'dine-in') {
        // 堂食订单
        switch(orderStatus) {
          case 'paid':
            return `您的堂食订单已支付成功
正在安排制作
预计2-3分钟后开始制作
取餐号：${this.order.pickup_code || ''}`;
          case 'cooking':
            return `您的堂食订单正在制作中
制作进度：50%
预计5分钟后可以取餐
取餐号：${this.order.pickup_code || ''}
请前往取餐区等候`;
          case 'cooked':
            return `您的堂食订单已制作完成
制作进度：100%
请立即前往取餐区取餐
取餐号：${this.order.pickup_code || ''}`;
          case 'completed':
            return `您的堂食订单已完成
感谢您的光临，期待您再次惠顾`;
          default:
            return `您的堂食订单正在制作中
制作进度：80%
预计3分钟后可以取餐
取餐号：${this.order.pickup_code || ''}
请前往取餐区等候`;
        }
      } else {
        // 外卖订单
        switch(orderStatus) {
          case 'paid':
            return `您的外卖订单已支付成功
正在安排制作
预计2-3分钟后开始制作
配送地址：${this.order.address || '未设置地址'}`;
          case 'cooking':
            return `您的外卖订单正在制作中
制作进度：50%
预计15分钟后开始配送
配送地址：${this.order.address || '未设置地址'}`;
          case 'cooked':
            return `您的外卖订单已制作完成
制作进度：100%
正在等待骑手接单
配送地址：${this.order.address || '未设置地址'}`;
          case 'delivering':
            return `您的外卖订单正在配送中
配送员：${this.order.delivery_name || ''}
联系电话：${this.order.delivery_phone || ''}
预计${this.order.estimate_time || '10'}分钟送达
配送地址：${this.order.address || '未设置地址'}`;
          case 'completed':
            return `您的外卖订单已送达
感谢您的惠顾，期待您再次下单`;
          default:
            return `您的外卖订单正在配送中
预计15分钟送达
配送地址：${this.order.address || '未设置地址'}`;
        }
      }
    },
    
    // 去支付
    goPay() {
      // 获取订单号
      const orderNo = this.order.order_no || this.order.orderNo;
      if (!orderNo) {
        uni.showToast({
          title: '订单号不存在',
          icon: 'none'
        });
        return;
      }
      
      // 弹出支付方式选择
      uni.showActionSheet({
        itemList: ['微信支付', '余额支付'],
        success: async (res) => {
          const payType = res.tapIndex;
          
          if (payType === 0) {
            // 微信支付
            this.wxPay(orderNo);
          } else if (payType === 1) {
            // 余额支付
            this.balancePay(orderNo);
          }
        }
      });
    },
    
    // 微信支付
    async wxPay(orderNo) {
      try {
        // 获取微信支付参数
        const payResult = await getWxPayParams(orderNo);
        uni.hideLoading();
        
        if (payResult.code !== 1) {
          uni.showToast({
            title: payResult.msg || '获取支付参数失败',
            icon: 'none'
          });
          return;
        }
        
        // 调用微信支付
        uni.requestPayment({
          ...payResult.data,
          success: () => {
            this.checkPaymentResult(orderNo);
          },
          fail: (err) => {
            console.log('支付失败', err);
            uni.showToast({
              title: '支付已取消',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        uni.showToast({
          title: '支付异常，请稍后再试',
          icon: 'none'
        });
        console.error('支付出错', error);
      }
    },
    
    // 余额支付
    async balancePay(orderNo) {
      try {
        uni.showLoading({ title: '处理中...' });
        
        // 调用余额支付API
        const payResult = await payOrderWithBalance(orderNo);
        uni.hideLoading();
        
        if (payResult.code === 1) {
          uni.showToast({
            title: '支付成功',
            icon: 'none'
          });
          // 刷新订单数据
          this.fetchOrderDetail(orderNo);
        } else {
          // 如果为2 跳转充值页面
          if (payResult.code === 2) {
            // 显示确认对话框，让用户选择是否跳转到充值页面
            uni.showModal({
              title: '余额不足',
              content: '您的余额不足，是否前往充值？',
              confirmText: '去充值',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  // 用户点击确认，跳转到充值页面
                  uni.navigateTo({
                    url: '/pages/my/recharge'
                  });
                }
              }
            });
          }
          uni.showToast({
            title: payResult.msg || '余额不足',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '支付失败，请稍后再试',
          icon: 'none'
        });
        console.error('余额支付出错', error);
      }
    },
    
    // 检查支付结果
    async checkPaymentResult(orderNo) {
      try {
        uni.showLoading({ title: '正在查询支付结果...' });
        
        // 查询支付结果
        const queryResult = await queryWxPayResult(orderNo);
        uni.hideLoading();
        
        if (queryResult.code === 1 && queryResult.data && queryResult.data.pay_status === 1) {
          uni.showToast({
            title: '支付成功',
            icon: 'none'
          });
          
          // 刷新订单数据
          this.fetchOrderDetail(orderNo);
        } else {
          uni.showToast({
            title: queryResult.msg || '支付结果查询失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: '查询支付结果失败',
          icon: 'none'
        });
        console.error('查询支付结果出错', error);
      }
    },
    
    startAutoRefresh() {
      // 清除可能存在的定时器
      this.clearAutoRefresh();
      
      // 设置新的定时器，每10秒刷新一次
      this.refreshTimer = setInterval(() => {
        console.log('定时刷新订单数据');
        this.refreshOrderData(true); // 静默刷新
      }, 10000); // 10秒
      
      // 保存引用以便于完全清除
      this._refreshTimerId = this.refreshTimer;
      
      console.log('启动订单自动刷新，定时器ID:', this.refreshTimer);
    },
    
    clearAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
        console.log('停止订单自动刷新');
      }
      // 确保没有其他定时器在运行
      if (typeof this._refreshTimerId !== 'undefined') {
        clearInterval(this._refreshTimerId);
        this._refreshTimerId = null;
      }
    },
    
    // 取消订单
    cancelOrder() {
      // 获取订单号
      const orderNo = this.order.order_no || this.order.orderNo;
      if (!orderNo) {
        uni.showToast({
          title: '订单号不存在',
          icon: 'none'
        });
        return;
      }
      
      uni.showModal({
        title: '取消订单',
        content: '确定要取消该订单吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({ title: '处理中...' });
              
              const result = await cancelOrder(orderNo);
              uni.hideLoading();
              
              if (result.code === 1) {
                uni.showToast({
                  title: '订单已取消',
                  icon: 'none'
                });
                
                // 刷新订单数据
                setTimeout(() => {
                  this.fetchOrderDetail(orderNo);
                }, 500);
              } else {
                uni.showToast({
                  title: result.msg || '取消失败，请稍后再试',
                  icon: 'none'
                });
              }
            } catch (error) {
              uni.hideLoading();
              uni.showToast({
                title: '取消订单失败',
                icon: 'none'
              });
              console.error('取消订单出错', error);
            }
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    
    .left {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      
      &:active {
        opacity: 0.6;
      }
    }
    
    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      flex: 1;
      text-align: center;
    }
    
    .right {
      width: 88rpx;
      height: 88rpx;
    }
  }
}

.status-card {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding: 40rpx 30rpx;
  
  &.unpaid-card {
    background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);
  }
  &.paid-card {
    background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);
  }
  &.cooking-card {
    background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);
  }
  &.cooked-card {
    background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);
  }
  &.delivering-card {
    background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);
  }
  &.completed-card {
    background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);
  }
  &.cancelled-card {
    background: linear-gradient(135deg, #999999 0%, #666666 100%);
  }
  
  .status-info {
    text-align: center;
    
    .status {
      font-size: 36rpx;
      color: #fff;
      font-weight: bold;
      margin-bottom: 12rpx;
      display: block;
    }
    
    .desc {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.9);
    }
    
    .auto-refresh-tip {
      margin-top: 16rpx;
      padding: 6rpx 16rpx;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 100rpx;
      display: inline-block;
      
      text {
        font-size: 22rpx;
        color: #fff;
      }
    }
  }
}

.order-card, .product-card {
  margin: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  
  .card-title {
    font-size: 30rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 16rpx 0;
    
    .label {
      font-size: 28rpx;
      color: #666;
    }
    
    .value {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.product-list {
  .product-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #f5f5f5;
    
    .product-name {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
    
    .product-specs {
      font-size: 24rpx;
      color: #999;
      margin: 0 20rpx;
    }
    
    .product-price {
      font-size: 28rpx;
      color: #333;
      
      .count {
        margin-left: 10rpx;
        color: #999;
      }
    }
  }
}

.price-info {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f5f5f5;
  
  .price-item {
    display: flex;
    justify-content: space-between;
    padding: 12rpx 0;
    
    .label {
      font-size: 26rpx;
      color: #666;
    }
    
            .value {
          font-size: 26rpx;
          color: #333;
          
          &.unpaid {
            color: #ff8500;
          }
          
          &.paid {
            color: #6ab52e;
          }
        }
    
            &.total {
          margin-top: 12rpx;
          padding-top: 12rpx;
          border-top: 1px dashed #eee;
          
          .label {
            font-size: 28rpx;
            color: #333;
          }
          
          .value {
            font-size: 32rpx;
            color: #ff4444;
            font-weight: bold;
            
            &.unpaid {
              color: #ff8500;
            }
          }
        }
        
        &.payment-status {
          margin-top: 12rpx;
          padding-top: 12rpx;
          border-top: 1px dashed #eee;
        }
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: #fff;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  
      .btn {
      padding: 20rpx 40rpx;
      border-radius: 100rpx;
      font-size: 28rpx;
      
      &.outline {
        border: 1px solid #ddd;
      }
    
    &.primary {
      background: #8cd548;
      color: #fff;
    }
  }
}
</style> 