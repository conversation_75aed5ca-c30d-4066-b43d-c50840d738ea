(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/my/settings"],{

/***/ 256:
/*!***************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/main.js?{"page":"pages%2Fmy%2Fsettings"} ***!
  \***************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _settings = _interopRequireDefault(__webpack_require__(/*! ./pages/my/settings.vue */ 257));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_settings.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 257:
/*!********************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./settings.vue?vue&type=template&id=3176ae3d&scoped=true& */ 258);
/* harmony import */ var _settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./settings.vue?vue&type=script&lang=js& */ 260);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _settings_vue_vue_type_style_index_0_id_3176ae3d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./settings.vue?vue&type=style&index=0&id=3176ae3d&lang=scss&scoped=true& */ 262);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "3176ae3d",
  null,
  false,
  _settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/my/settings.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 258:
/*!***************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?vue&type=template&id=3176ae3d&scoped=true& ***!
  \***************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=template&id=3176ae3d&scoped=true& */ 259);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_template_id_3176ae3d_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 259:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?vue&type=template&id=3176ae3d&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-icon/u-icon.vue */ 372))
    },
    uPopup: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-popup/u-popup.vue */ 402))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = _vm.formatPhone(_vm.userInfo.phone) || "未绑定"
  var g0 = Date.now()
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        g0: g0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 260:
/*!*********************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=script&lang=js& */ 261);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 261:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 55));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 57));
var _user = __webpack_require__(/*! @/api/user.js */ 166);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
// 移除单独导入的uview组件，因为已经全局引入
var _default = {
  // 不需要单独注册组件，uView已全局注册
  data: function data() {
    return {
      userInfo: {
        nickName: '',
        phone: '',
        avatarUrl: '',
        gender: '',
        // 1: 男性, 2: 女性
        birthday: '',
        birthdayOne: ''
      },
      loading: false,
      showAvatarPopup: false,
      showNicknamePopup: false,
      showBirthdayPicker: false,
      // 控制生日选择器显示
      tempNickname: '',
      formChanged: false,
      // 用于标记表单是否有修改
      birthdayColumns: [[], [], []],
      // 年月日三列数据
      defaultBirthdayIndex: [0, 0, 0],
      // 默认选中索引
      selectedYear: '',
      // 当前选中的年
      selectedMonth: '',
      // 当前选中的月
      selectedDay: '',
      // 当前选中的日
      lastYearIndex: 0,
      // 上次选择的年份索引
      lastMonthIndex: 0,
      // 上次选择的月份索引
      pickerKey: 0 // 用于强制重新渲染
    };
  },
  onLoad: function onLoad() {
    this.userInfo = uni.getStorageSync('userInfo') || {};
    console.log('用户信息加载完成:', this.userInfo);

    // 初始化临时昵称
    this.tempNickname = this.userInfo.nickName || '';
    this.initBirthdayPicker();
    this.showBirthdayPicker = false;
  },
  // 页面准备完成
  onReady: function onReady() {
    console.log('页面准备完成');
  },
  // 确保页面每次显示时重新获取数据
  onShow: function onShow() {
    // 刷新用户信息
    this.userInfo = uni.getStorageSync('userInfo') || {};
    console.log('onShow 刷新用户信息:', this.userInfo);

    // 更新临时昵称并确保弹窗关闭
    this.tempNickname = this.userInfo.nickName || '';
    this.showNicknamePopup = false;
  },
  // 页面卸载时清理资源
  onUnload: function onUnload() {
    // 清除昵称监听计时器
    if (this.nicknameTimer) {
      clearTimeout(this.nicknameTimer);
      this.nicknameTimer = null;
      console.log('页面卸载，清理昵称监听');
    }
  },
  methods: {
    // 跳转到版权声明页面
    goToCopyright: function goToCopyright() {
      uni.navigateTo({
        url: '/pages/copyright/copyright'
      });
    },
    // 处理返回按钮
    handleBack: function handleBack() {
      // 如果表单有修改且未保存，显示提示
      if (this.formChanged) {
        uni.showModal({
          title: '提示',
          content: '您有未保存的修改，确定要离开吗？',
          success: function success(res) {
            if (res.confirm) {
              uni.navigateBack();
            }
          }
        });
      } else {
        uni.navigateBack();
      }
    },
    // 格式化手机号码（中间部分用*替代）
    formatPhone: function formatPhone(phone) {
      if (!phone) return '';
      if (phone.length !== 11) return phone;
      return phone.substring(0, 3) + '******' + phone.substring(9);
    },
    // 选择性别
    selectGender: function selectGender(gender) {
      if (this.userInfo.gender === gender) return;
      this.userInfo.gender = gender;
      this.formChanged = true;

      // 显示选择成功提示
      uni.showToast({
        title: gender === '1' ? '已选择男' : '已选择女',
        icon: 'success',
        duration: 1500
      });
    },
    // 性别选择弹窗
    showGenderPicker: function showGenderPicker() {
      var _this = this;
      uni.showActionSheet({
        itemList: ['男', '女'],
        success: function success(res) {
          // 选择男性
          if (res.tapIndex === 0) {
            _this.selectGender('1');
          }
          // 选择女性
          else if (res.tapIndex === 1) {
            _this.selectGender('2');
          }
        }
      });
    },
    // 修改生日
    changeBirthday: function changeBirthday() {
      var _this2 = this;
      console.log('触发生日修改方法');

      // 如果已经设置过生日，提示只能修改一次
      if (this.userInfo.birthday) {
        return uni.showToast({
          title: '生日信息只能修改一次',
          icon: 'none'
        });
      }

      // 先清空默认值，避免旧值干扰
      this.defaultBirthdayIndex = [0, 0, 0];

      // 延迟显示选择器
      this.showBirthdayPicker = true;

      // 使用setTimeout延迟初始化，确保选择器DOM已渲染
      setTimeout(function () {
        // 初始化日期数据
        _this2.initBirthdayPicker();

        // 延迟强制更新，确保索引值生效
        setTimeout(function () {
          // 强制更新视图
          _this2.$forceUpdate();
          console.log('选择的日期:', _this2.selectedYear, _this2.selectedMonth, _this2.selectedDay);
          console.log('日期选择器状态:', _this2.showBirthdayPicker, '索引值:', _this2.defaultBirthdayIndex);
        }, 100);
      }, 200);
    },
    // 关闭生日选择器
    closeBirthdayPicker: function closeBirthdayPicker() {
      console.log('关闭日期选择器');
      this.showBirthdayPicker = false;
    },
    // 初始化日期选择器
    initBirthdayPicker: function initBirthdayPicker() {
      // 清除可能的延迟任务
      if (this.pickerTimer) {
        clearTimeout(this.pickerTimer);
      }
      console.log('初始化日期选择器');
      var now = new Date();
      var currentYear = now.getFullYear();

      // 直接指定2007年为默认年份
      var defaultYear = 2007;

      // 年份范围：从1900年开始
      var years = [];
      for (var i = 1900; i <= currentYear; i++) {
        years.push(i + '年');
      }

      // 月份：1-12月
      var months = [];
      for (var _i = 1; _i <= 12; _i++) {
        months.push((_i < 10 ? '0' + _i : _i) + '月');
      }

      // 初始化日期数据（默认31天，后续会更新）
      this.selectedYear = defaultYear;
      this.selectedMonth = 1;
      this.selectedDay = 1;

      // 根据选中的年月计算天数
      var days = this.updateDays(this.selectedYear, this.selectedMonth);

      // 更新选择器数据
      this.birthdayColumns = [years, months, days];

      // 计算默认年份索引 - 2007与1900的差值
      var yearIndex = defaultYear - 1900;

      // 设置索引值 - 此处非常重要，必须使用新数组
      this.$set(this, 'defaultBirthdayIndex', [yearIndex, 0,
      // 1月
      0 // 1日
      ]);

      // 输出调试信息
      console.log("\u521D\u59CB\u5316\u65E5\u671F\u9009\u62E9\u5668\u5B8C\u6210: \u5F53\u524D\u5E74\u4EFD\u7D22\u5F15=".concat(yearIndex, ", \u9009\u4E2D\u5E74\u4EFD=").concat(defaultYear, "\u5E74"));
      console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);
    },
    // 更新日期数组
    updateDays: function updateDays(year, month) {
      // 计算指定年月的天数
      var daysInMonth = new Date(year, month, 0).getDate();
      console.log("\u8BA1\u7B97".concat(year, "\u5E74").concat(month, "\u6708\u7684\u5929\u6570:"), daysInMonth);

      // 更新日期数组
      var days = [];
      for (var i = 1; i <= daysInMonth; i++) {
        days.push((i < 10 ? '0' + i : i) + '日');
      }
      return days;
    },
    // 处理picker-view整体变化
    onPickerChange: function onPickerChange(e) {
      var values = e.detail.value;

      // 获取年月日索引
      var yearIndex = values[0];
      var monthIndex = values[1];
      var dayIndex = values[2];

      // 更新默认选中索引
      this.defaultBirthdayIndex = values;

      // 获取不带单位的值
      var yearText = this.birthdayColumns[0][yearIndex];
      var monthText = this.birthdayColumns[1][monthIndex];
      var dayText = this.birthdayColumns[2][dayIndex];

      // 获取数值
      this.selectedYear = parseInt(yearText.replace('年', ''));
      this.selectedMonth = parseInt(monthText.replace('月', ''));
      this.selectedDay = parseInt(dayText.replace('日', ''));
      console.log('选择的日期:', this.selectedYear, this.selectedMonth, this.selectedDay);

      // 如果年份或月份变化了，需要更新日期数组
      if (this.lastYearIndex !== yearIndex || this.lastMonthIndex !== monthIndex) {
        // 更新日期列
        var newDays = this.updateDays(this.selectedYear, this.selectedMonth);
        this.birthdayColumns[2] = newDays;

        // 记录当前选中的年月索引
        this.lastYearIndex = yearIndex;
        this.lastMonthIndex = monthIndex;
      }
    },
    // 确认生日选择
    confirmBirthday: function confirmBirthday() {
      // 拼接生日
      var year = this.selectedYear;
      var month = String(this.selectedMonth).padStart(2, '0');
      var day = String(this.selectedDay).padStart(2, '0');
      var birthday = "".concat(year, "-").concat(month, "-").concat(day);
      console.log('确认选择生日:', birthday);

      // 设置生日
      this.userInfo.birthdayOne = birthday;
      this.formChanged = true;

      // 关闭选择器
      this.showBirthdayPicker = false;

      // 显示成功提示
      uni.showToast({
        title: '生日设置成功',
        icon: 'success'
      });
    },
    // 取消日期选择
    cancelBirthday: function cancelBirthday() {
      this.showBirthdayPicker = false;
    },
    // 处理日期选择
    onDatePickerChange: function onDatePickerChange(e) {
      var values = e.detail.value;
      this.datePickerValue = values;

      // 当年份或月份变化时，需要更新日期数组
      var year = this.years[values[0]];
      var month = this.months[values[1]];

      // 更新日期数组
      var daysInMonth = new Date(year, month, 0).getDate();
      if (this.days.length !== daysInMonth) {
        this.days = Array.from({
          length: daysInMonth
        }, function (_, i) {
          return i + 1;
        });

        // 如果当前选中的日期超出了这个月的最大天数，则调整
        if (values[2] >= daysInMonth) {
          this.datePickerValue[2] = daysInMonth - 1;
        }
      }
    },
    // 初始化日期选择器数据
    initDatePicker: function initDatePicker() {
      console.log('开始初始化日期选择器数据');
      var now = new Date();
      var currentYear = now.getFullYear();
      var defaultYear = currentYear - 18; // 默认18岁

      // 创建年份数组 (1900年至今)
      this.years = [];
      for (var i = 1900; i <= currentYear; i++) {
        this.years.push(i);
      }

      // 创建月份数组
      this.months = [];
      for (var _i2 = 1; _i2 <= 12; _i2++) {
        this.months.push(_i2);
      }

      // 默认选中的年月
      var defaultYearIndex = this.years.findIndex(function (y) {
        return y === defaultYear;
      });
      var defaultMonthIndex = 0; // 默认1月

      // 创建日期数组 (默认31天，后续会根据选择的年月动态调整)
      this.days = [];
      var daysInMonth = new Date(defaultYear, 1, 0).getDate();
      for (var _i3 = 1; _i3 <= daysInMonth; _i3++) {
        this.days.push(_i3);
      }

      // 初始日期值
      this.datePickerValue = [defaultYearIndex > -1 ? defaultYearIndex : this.years.length - 18, defaultMonthIndex, 0];
      console.log('日期选择器数据初始化完成:', {
        years: this.years.length,
        months: this.months.length,
        days: this.days.length,
        defaultValues: this.datePickerValue
      });
    },
    // 保存用户资料
    saveUserProfile: function saveUserProfile() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var requestData, updateRes;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (_this3.formChanged) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return", uni.showToast({
                  title: '未做任何修改',
                  icon: 'none'
                }));
              case 2:
                if (!_this3.loading) {
                  _context.next = 4;
                  break;
                }
                return _context.abrupt("return");
              case 4:
                _this3.loading = true;
                uni.showLoading({
                  title: '保存中...',
                  mask: true
                });
                if (_this3.userInfo.birthdayOne) {
                  _this3.userInfo.birthday = _this3.userInfo.birthdayOne;
                }
                _context.prev = 7;
                // 构建请求数据
                requestData = {
                  nickname: _this3.userInfo.nickName,
                  gender: _this3.userInfo.gender,
                  birthday: _this3.userInfo.birthday
                }; // 调用API更新用户资料
                _context.next = 11;
                return (0, _user.updateUserProfile)(requestData);
              case 11:
                updateRes = _context.sent;
                if (!(updateRes && updateRes.code === 1)) {
                  _context.next = 18;
                  break;
                }
                // 更新成功
                _this3.saveUserInfo();
                _this3.formChanged = false;
                uni.showToast({
                  title: '保存成功',
                  icon: 'none'
                });
                _context.next = 19;
                break;
              case 18:
                throw new Error(updateRes && updateRes.msg ? updateRes.msg : '保存失败');
              case 19:
                _context.next = 25;
                break;
              case 21:
                _context.prev = 21;
                _context.t0 = _context["catch"](7);
                console.error('保存用户资料失败:', _context.t0);
                uni.showToast({
                  title: _context.t0.message || '保存失败',
                  icon: 'none'
                });
              case 25:
                _context.prev = 25;
                // uni.hideLoading();
                _this3.loading = false;
                return _context.finish(25);
              case 28:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[7, 21, 25, 28]]);
      }))();
    },
    // 显示头像选项弹窗
    showAvatarOptions: function showAvatarOptions() {
      this.showAvatarPopup = true;
    },
    // 关闭头像选项弹窗
    closeAvatarPopup: function closeAvatarPopup() {
      this.showAvatarPopup = false;
    },
    // 选择头像
    chooseAvatar: function chooseAvatar() {
      var _this4 = this;
      this.showAvatarPopup = false;
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: function success(res) {
          var tempFilePath = res.tempFilePaths[0];

          // 显示上传中提示
          uni.showLoading({
            title: '上传中...',
            mask: true
          });

          // 先上传图片到服务器
          _this4.uploadAvatar(tempFilePath);
        },
        fail: function fail(err) {
          console.error('选择图片失败:', err);
          uni.showToast({
            title: '选择失败',
            icon: 'none'
          });
        }
      });
    },
    // 处理微信头像选择回调
    onChooseAvatar: function onChooseAvatar(e) {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var avatarUrl;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                console.log('选择头像回调:', e);
                avatarUrl = e.detail.avatarUrl;
                if (avatarUrl) {
                  _context2.next = 4;
                  break;
                }
                return _context2.abrupt("return", uni.showToast({
                  title: '获取头像失败',
                  icon: 'none'
                }));
              case 4:
                // 显示上传中提示
                uni.showLoading({
                  title: '上传中...',
                  mask: true
                });
                _context2.prev = 5;
                _context2.next = 8;
                return _this5.uploadAvatar(avatarUrl);
              case 8:
                uni.hideLoading();
                uni.showToast({
                  title: '头像更新成功',
                  icon: 'none'
                });
                _context2.next = 17;
                break;
              case 12:
                _context2.prev = 12;
                _context2.t0 = _context2["catch"](5);
                uni.hideLoading();
                console.error('上传头像失败:', _context2.t0);
                uni.showToast({
                  title: _context2.t0.message || '头像更新失败',
                  icon: 'none'
                });
              case 17:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[5, 12]]);
      }))();
    },
    // 处理昵称输入
    onNicknameInput: function onNicknameInput(e) {
      console.log('昵称输入事件:', e);
      if (e.detail && e.detail.value) {
        var nickname = e.detail.value.trim();
        this.userInfo.nickName = nickname;
        this.tempNickname = nickname;
        this.formChanged = true;
        console.log('昵称已更新:', nickname);
      }
    },
    // 处理昵称输入框失去焦点
    onNicknameBlur: function onNicknameBlur(e) {
      console.log('昵称输入框失去焦点:', e);
      // 如果有值，确保更新
      if (e.detail && e.detail.value) {
        var nickname = e.detail.value.trim();
        if (nickname && nickname !== this.userInfo.nickName) {
          this.userInfo.nickName = nickname;
          this.tempNickname = nickname;
          this.formChanged = true;
          console.log('失去焦点时更新昵称:', nickname);
        }
      }
    },
    // 处理微信获取头像事件后询问用户昵称
    onGetNickname: function onGetNickname(e) {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                console.log('获取头像事件:', e);
                try {
                  // 选择头像后，显示输入昵称对话框
                  uni.showModal({
                    title: '设置昵称',
                    editable: true,
                    placeholderText: '请输入昵称',
                    content: _this6.userInfo.nickName || '',
                    success: function () {
                      var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(res) {
                        var nickname;
                        return _regenerator.default.wrap(function _callee3$(_context3) {
                          while (1) {
                            switch (_context3.prev = _context3.next) {
                              case 0:
                                if (!(res.confirm && res.content)) {
                                  _context3.next = 10;
                                  break;
                                }
                                nickname = res.content.trim();
                                if (nickname) {
                                  _context3.next = 4;
                                  break;
                                }
                                return _context3.abrupt("return");
                              case 4:
                                if (!(nickname === _this6.userInfo.nickName)) {
                                  _context3.next = 6;
                                  break;
                                }
                                return _context3.abrupt("return");
                              case 6:
                                // 设置临时昵称
                                _this6.tempNickname = nickname;

                                // 显示提示
                                uni.showLoading({
                                  title: '正在保存昵称...',
                                  mask: true
                                });

                                // 自动保存
                                _context3.next = 10;
                                return _this6.confirmNickname();
                              case 10:
                              case "end":
                                return _context3.stop();
                            }
                          }
                        }, _callee3);
                      }));
                      function success(_x) {
                        return _success.apply(this, arguments);
                      }
                      return success;
                    }()
                  });
                } catch (err) {
                  console.error('处理昵称获取事件出错:', err);
                  uni.showToast({
                    title: '获取昵称失败',
                    icon: 'none'
                  });
                }
              case 2:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    // 处理微信昵称审核事件 - 这是使用nickname组件的关键方法
    onNicknameReview: function onNicknameReview(e) {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var wxNickname;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                console.log('昵称审核事件详情:', JSON.stringify(e));
                try {
                  // 获取微信昵称
                  wxNickname = _this7.tempNickname; // 若未获取到昵称，记录错误
                  if (!wxNickname) {
                    console.error('未能获取到微信昵称，完整事件对象:', e);
                    wxNickname = "微信用户"; // 默认昵称
                    console.log('未获取到微信昵称，使用默认值');
                  } else {
                    console.log('成功获取到微信昵称:', wxNickname);
                  }

                  // 更新昵称和表单状态
                  _this7.userInfo.nickName = wxNickname;
                  _this7.tempNickname = wxNickname;
                  _this7.formChanged = true;
                  uni.showToast({
                    title: '已获取微信昵称',
                    icon: 'none'
                  });
                } catch (err) {
                  console.error('处理昵称审核事件出错:', err);
                  uni.showToast({
                    title: '获取昵称失败',
                    icon: 'none'
                  });
                }
              case 2:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    // 这个方法不再需要，我们直接在onNicknameReview中处理了
    handleNickname: function handleNickname(nickname) {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                console.log('处理获取到的昵称:', nickname);

                // 如果没有获取到昵称，则尝试使用直接输入
                if (nickname) {
                  _context7.next = 3;
                  break;
                }
                return _context7.abrupt("return", _this8.showManualNicknameInput());
              case 3:
                // 设置临时昵称
                _this8.tempNickname = nickname;

                // 显示确认对话框
                uni.showModal({
                  title: '确认修改昵称',
                  content: "\u662F\u5426\u5C06\u6635\u79F0\u4FEE\u6539\u4E3A\"".concat(nickname, "\"\uFF1F"),
                  success: function () {
                    var _success2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6(res) {
                      var result;
                      return _regenerator.default.wrap(function _callee6$(_context6) {
                        while (1) {
                          switch (_context6.prev = _context6.next) {
                            case 0:
                              if (!res.confirm) {
                                _context6.next = 6;
                                break;
                              }
                              // 显示提示
                              uni.showLoading({
                                title: '正在保存昵称...',
                                mask: true
                              });

                              // 保存昵称
                              _context6.next = 4;
                              return _this8.confirmNickname();
                            case 4:
                              result = _context6.sent;
                              console.log('保存昵称结果:', result);
                            case 6:
                            case "end":
                              return _context6.stop();
                          }
                        }
                      }, _callee6);
                    }));
                    function success(_x2) {
                      return _success2.apply(this, arguments);
                    }
                    return success;
                  }()
                });
              case 5:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    // 显示手动输入昵称的对话框
    showManualNicknameInput: function showManualNicknameInput() {
      var _this9 = this;
      var message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '请输入您的昵称';
      uni.showModal({
        title: '设置昵称',
        content: message,
        editable: true,
        placeholderText: '请输入昵称',
        success: function () {
          var _success3 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8(res) {
            return _regenerator.default.wrap(function _callee8$(_context8) {
              while (1) {
                switch (_context8.prev = _context8.next) {
                  case 0:
                    if (!(res.confirm && res.content)) {
                      _context8.next = 5;
                      break;
                    }
                    // 显示提示并保存用户输入的昵称
                    uni.showLoading({
                      title: '正在保存昵称...',
                      mask: true
                    });
                    _this9.tempNickname = res.content.trim();
                    _context8.next = 5;
                    return _this9.confirmNickname();
                  case 5:
                  case "end":
                    return _context8.stop();
                }
              }
            }, _callee8);
          }));
          function success(_x3) {
            return _success3.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 处理点击昵称项
    handleWechatNickname: function handleWechatNickname() {
      // 显示昵称输入弹窗
      this.showNicknamePopup = true;
    },
    // 确认修改昵称
    confirmNickname: function confirmNickname() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var nickname, requestData, updateRes;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                nickname = _this10.tempNickname;
                if (!(!nickname || nickname === _this10.userInfo.nickName)) {
                  _context9.next = 5;
                  break;
                }
                console.log('昵称为空或未更改，不执行保存操作');
                uni.hideLoading();
                return _context9.abrupt("return", false);
              case 5:
                _context9.prev = 5;
                console.log('准备提交昵称到服务器:', nickname);

                // 构造请求参数
                requestData = {
                  nickname: nickname
                }; // 显示详细的请求参数，便于调试
                console.log('请求参数:', JSON.stringify(requestData));

                // 直接调用API更新昵称
                _context9.next = 11;
                return (0, _user.updateUserProfile)(requestData);
              case 11:
                updateRes = _context9.sent;
                // 记录响应详情
                console.log('昵称更新接口响应:', JSON.stringify(updateRes));

                // 检查响应状态
                if (!(updateRes && updateRes.code === 1)) {
                  _context9.next = 22;
                  break;
                }
                // 更新本地用户信息
                _this10.userInfo.nickName = nickname;
                _this10.saveUserInfo();
                console.log('本地用户信息已更新:', JSON.stringify(_this10.userInfo));

                // 更新UI显示
                _this10.$forceUpdate();

                // 显示成功提示
                uni.showToast({
                  title: '昵称更新成功',
                  icon: 'success',
                  duration: 2000
                });
                return _context9.abrupt("return", true);
              case 22:
                // 详细记录错误信息
                console.error('接口返回错误:', updateRes);
                throw new Error(updateRes && updateRes.msg ? updateRes.msg : '昵称更新失败');
              case 24:
                _context9.next = 31;
                break;
              case 26:
                _context9.prev = 26;
                _context9.t0 = _context9["catch"](5);
                console.error('更新昵称失败:', _context9.t0);

                // 显示错误提示
                uni.showToast({
                  title: _context9.t0.message || '昵称更新失败',
                  icon: 'none',
                  duration: 2000
                });
                return _context9.abrupt("return", false);
              case 31:
                _context9.prev = 31;
                // 确保无论成功失败都隐藏加载提示
                uni.hideLoading();
                return _context9.finish(31);
              case 34:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9, null, [[5, 26, 31, 34]]);
      }))();
    },
    // 显示隐私设置
    showPrivacySettings: function showPrivacySettings() {
      uni.showToast({
        title: '隐私设置功能开发中',
        icon: 'none'
      });
    },
    // 下载微信头像
    downloadWechatAvatar: function downloadWechatAvatar(avatarUrl) {
      return new Promise(function (resolve, reject) {
        uni.downloadFile({
          url: avatarUrl,
          success: function success(res) {
            if (res.statusCode === 200) {
              resolve(res.tempFilePath);
            } else {
              reject(new Error('下载头像失败'));
            }
          },
          fail: function fail(err) {
            console.error('下载微信头像失败:', err);
            reject(err);
          }
        });
      });
    },
    // 上传头像
    uploadAvatar: function uploadAvatar(filePath) {
      var _arguments = arguments,
        _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var showToast, uploadRes, avatarUrl, updateRes;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                showToast = _arguments.length > 1 && _arguments[1] !== undefined ? _arguments[1] : true;
                _context10.prev = 1;
                _context10.next = 4;
                return (0, _user.uploadImage)(filePath);
              case 4:
                uploadRes = _context10.sent;
                if (!(uploadRes.code === 1 && uploadRes.data && uploadRes.data.url)) {
                  _context10.next = 20;
                  break;
                }
                // 获取到图片URL后，调用修改个人信息接口
                avatarUrl = uploadRes.data.url;
                _context10.next = 9;
                return (0, _user.updateUserProfile)({
                  avatar: avatarUrl
                });
              case 9:
                updateRes = _context10.sent;
                if (!(updateRes.code === 1)) {
                  _context10.next = 17;
                  break;
                }
                // 更新用户信息中的头像
                _this11.userInfo.avatarUrl = avatarUrl;
                _this11.saveUserInfo();
                if (showToast) {
                  uni.hideLoading();
                  uni.showToast({
                    title: '头像更新成功',
                    icon: 'none'
                  });
                }
                return _context10.abrupt("return", true);
              case 17:
                throw new Error(updateRes.msg || '保存头像失败');
              case 18:
                _context10.next = 21;
                break;
              case 20:
                throw new Error(uploadRes.msg || '图片上传失败');
              case 21:
                _context10.next = 28;
                break;
              case 23:
                _context10.prev = 23;
                _context10.t0 = _context10["catch"](1);
                if (showToast) {
                  uni.hideLoading();
                  uni.showToast({
                    title: _context10.t0.msg || _context10.t0.message || '上传失败',
                    icon: 'none'
                  });
                }
                console.error('上传头像失败:', _context10.t0);
                throw _context10.t0;
              case 28:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10, null, [[1, 23]]);
      }))();
    },
    // 此方法已替换为toggleNicknameEdit
    changeName: function changeName() {
      this.toggleNicknameEdit();
    },
    // 修改手机号
    changePhone: function changePhone() {
      var _this12 = this;
      uni.showModal({
        title: '修改手机号',
        editable: true,
        placeholderText: '请输入手机号',
        content: this.userInfo.phone || '',
        success: function () {
          var _success4 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11(res) {
            var phone, updateRes;
            return _regenerator.default.wrap(function _callee11$(_context11) {
              while (1) {
                switch (_context11.prev = _context11.next) {
                  case 0:
                    if (!(res.confirm && res.content)) {
                      _context11.next = 31;
                      break;
                    }
                    if (/^1[3-9]\d{9}$/.test(res.content)) {
                      _context11.next = 3;
                      break;
                    }
                    return _context11.abrupt("return", uni.showToast({
                      title: '手机号格式不正确',
                      icon: 'none'
                    }));
                  case 3:
                    phone = res.content; // 避免重复提交
                    if (!_this12.loading) {
                      _context11.next = 6;
                      break;
                    }
                    return _context11.abrupt("return");
                  case 6:
                    if (!(phone === _this12.userInfo.phone)) {
                      _context11.next = 8;
                      break;
                    }
                    return _context11.abrupt("return");
                  case 8:
                    _this12.loading = true;
                    uni.showLoading({
                      title: '保存中...',
                      mask: true
                    });
                    _context11.prev = 10;
                    _context11.next = 13;
                    return (0, _user.updateUserProfile)({
                      username: phone
                    });
                  case 13:
                    updateRes = _context11.sent;
                    if (!(updateRes.code === 1)) {
                      _context11.next = 20;
                      break;
                    }
                    // 更新本地用户信息
                    _this12.userInfo.phone = phone;
                    _this12.saveUserInfo();
                    uni.showToast({
                      title: '手机号修改成功',
                      icon: 'none'
                    });
                    _context11.next = 21;
                    break;
                  case 20:
                    throw new Error(updateRes.msg || '手机号修改失败');
                  case 21:
                    _context11.next = 27;
                    break;
                  case 23:
                    _context11.prev = 23;
                    _context11.t0 = _context11["catch"](10);
                    console.error('修改手机号失败:', _context11.t0);
                    uni.showToast({
                      title: _context11.t0.msg || _context11.t0.message || '修改失败',
                      icon: 'none'
                    });
                  case 27:
                    _context11.prev = 27;
                    uni.hideLoading();
                    _this12.loading = false;
                    return _context11.finish(27);
                  case 31:
                  case "end":
                    return _context11.stop();
                }
              }
            }, _callee11, null, [[10, 23, 27, 31]]);
          }));
          function success(_x4) {
            return _success4.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 保存用户信息
    saveUserInfo: function saveUserInfo() {
      uni.setStorageSync('userInfo', this.userInfo);
    },
    handleLogout: function handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: function success(res) {
          if (res.confirm) {
            uni.removeStorageSync('token');
            uni.removeStorageSync('userInfo');
            uni.navigateBack();
          }
        }
      });
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 262:
/*!******************************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?vue&type=style&index=0&id=3176ae3d&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_style_index_0_id_3176ae3d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=style&index=0&id=3176ae3d&lang=scss&scoped=true& */ 263);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_style_index_0_id_3176ae3d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_style_index_0_id_3176ae3d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_style_index_0_id_3176ae3d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_style_index_0_id_3176ae3d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_settings_vue_vue_type_style_index_0_id_3176ae3d_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 263:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/settings.vue?vue&type=style&index=0&id=3176ae3d&lang=scss&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[256,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/settings.js.map