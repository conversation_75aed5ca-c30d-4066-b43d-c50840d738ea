<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">我的优惠券</text>
      </view>
    </view>
    
    <!-- 状态切换 -->
    <view class="tab-container">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        :class="['tab-item', { active: currentTab === index }]"
        @tap="switchTab(index)"
      >
        <text class="tab-text">{{tab.name}}</text>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading" v-if="loading">
      <view class="loading-circle"></view>
      <text class="loading-text">加载中...</text>
    </view>
    
    <!-- 错误提示 -->
    <view class="error-state" v-else-if="error">
      <text class="error-text">{{errorMsg}}</text>
      <view class="retry-btn" @tap="refreshCoupons">
        <text>重新加载</text>
      </view>
    </view>

    <!-- 优惠券列表 -->
    <scroll-view 
      class="coupon-scroll" 
      scroll-y 
      v-else
      :style="{ height: scrollHeight + 'px' }"
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refreshCoupons"
    >
      <view class="coupon-list">
        <view 
          class="coupon-item" 
          v-for="(item, index) in filterCoupons()" 
          :key="index"
          :class="{
            'disabled': this.orderAmount > 0 && !item.isAvailable,
            'expired': item.status === 'expired' || item.status === 'used'
          }"
          @tap="selectCoupon(item)"
        >
          <!-- 金额区域 -->
          <view class="coupon-amount">
            <text class="symbol">¥</text>
            <text class="value">{{item.coupon ? item.coupon.amount : item.amount}}</text>
            <text class="limit">满{{item.coupon ? item.coupon.min_amount : item.limit}}元可用</text>
          </view>
          
          <!-- 内容区域 -->
          <view class="coupon-info">
            <view class="info-top">
              <text class="name">{{item.coupon ? item.coupon.name : item.name}}</text>
              <view class="status-tag" :class="item.status">
                {{item.status_text || (item.status === 'unused' || item.status === 'valid' ? '未使用' : '已过期')}}
              </view>
            </view>
            
            <text class="desc">{{item.desc || '全场通用'}}</text>
            
            <view class="info-bottom">
              <text class="date">有效期至 {{item.expire_time ? formatDate(item.expire_time) : (item.endDate || item.expireDate || '暂无期限')}}</text>
              <view class="use-btn" v-if="item.status === 'unused' || item.status === 'valid'">
                <text>立即使用</text>
              </view>
            </view>
          </view>
          
          <!-- 不可用标签 -->
          <view class="unavailable-tag" v-if="this.orderAmount > 0 && !item.isAvailable && (item.status === 'unused' || item.status === 'valid')">
            <text>不满足使用条件</text>
          </view>
          
          <!-- 过期水印 -->
          <view class="expired-watermark" v-if="item.status === 'expired' || item.status === 'used'">
            <text>已过期</text>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="filterCoupons().length === 0">
        <image class="empty-image" src="/static/order/e186e04e8774da64b58c96a8bb479840.png" />
        <text class="empty-text">暂无优惠券～</text>
        <view class="action-btn" @tap="goToMenu">
          <text>去领券</text>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="loading-more" v-if="loadingMore">
        <view class="loading-dot"></view>
        <text>加载中...</text>
      </view>
      
      <!-- 底部提示 -->
      <view class="bottom-tip" v-if="!hasMore && couponList.length > 0">
        <text>— 已经到底了 —</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getMyCoupons } from '@/api/user.js'
import MyCoupon from './MyCoupon.vue'

export default {
  components: {
    MyCoupon
  },
  
  data() {
    return {
      currentTab: 0,
      tabs: [
        { name: '全部' },
        { name: '未使用' },
        { name: '已过期' }
      ],
      couponList: [],
      loading: false,
      error: false,
      errorMsg: '获取优惠券失败，请重试',
      orderAmount: 0,
      scrollHeight: 500, // 默认高度，将在onReady中计算
      // 分页相关
      page: 1,
      limit: 10,
      hasMore: true,
      loadingMore: false,
      refreshing: false
    }
  },
  
  onLoad(options) {
    if (options.amount) {
      this.orderAmount = parseFloat(options.amount)
      console.log('订单金额:', this.orderAmount)
    }
    
    this.fetchCoupons()
  },
  
  onReady() {
    // 计算列表区域高度
    this.calcScrollHeight()
  },
  
  onShow() {
    // 每次显示页面时刷新数据
    this.refreshCoupons()
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshCoupons()
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMore()
  },

  methods: {
    // 计算滚动区域高度
    calcScrollHeight() {
      const query = uni.createSelectorQuery().in(this)
      query.select('.header').boundingClientRect()
      query.select('.tab-container').boundingClientRect()
      query.selectViewport().boundingClientRect()
      
      query.exec(res => {
        if (res[0] && res[1] && res[2]) {
          const headerHeight = res[0].height
          const tabHeight = res[1].height
          const windowHeight = res[2].height
          
          // 计算列表可用高度 = 窗口高度 - 头部高度 - tab高度 - 底部安全区域(20px)
          this.scrollHeight = windowHeight - headerHeight - tabHeight - 20
        }
      })
    },
    
    // 刷新优惠券列表
    async refreshCoupons() {
      if (this.refreshing) return
      
      this.refreshing = true
      this.page = 1
      this.hasMore = true
      
      try {
        await this.fetchCoupons()
        uni.stopPullDownRefresh()
      } catch (error) {
        console.error('刷新优惠券异常:', error)
      } finally {
        this.refreshing = false
      }
    },
    
    // 加载更多优惠券
    async loadMore() {
      if (!this.hasMore || this.loading || this.loadingMore) return
      
      this.loadingMore = true
      this.page++
      
      try {
        await this.fetchCoupons(true)
      } catch (error) {
        console.error('加载更多优惠券异常:', error)
        // 加载失败时恢复页码
        this.page--
      } finally {
        this.loadingMore = false
      }
    },
    
    // 获取优惠券列表
    async fetchCoupons(isLoadMore = false) {
      if (!isLoadMore) {
        this.loading = true
      }
      this.error = false
      
      try {
        const params = {
          page: this.page,
          limit: this.limit
        }
        
        const res = await getMyCoupons(params)
        console.log('优惠券接口返回数据:', res)
        
        if (res.code === 1) {
          // 检查data的类型并正确处理
          let couponsData = []
          let total = 0
          
          if (res.data) {
            // 尝试获取总数
            if (typeof res.data === 'object' && res.data !== null) {
              total = res.data.total || res.data.count || 0
            }
            
            if (Array.isArray(res.data)) {
              // data直接是数组
              couponsData = res.data
            } else if (typeof res.data === 'object') {
              // data是对象，尝试从对象中找到数组
              // 查找对象中可能的数组属性
              const possibleArrayProps = Object.keys(res.data).filter(key => 
                Array.isArray(res.data[key])
              )
              
              if (possibleArrayProps.length > 0) {
                // 使用找到的第一个数组
                couponsData = res.data[possibleArrayProps[0]]
              } else if (res.data.list) {
                // 常见的列表字段名
                couponsData = res.data.list
              } else if (res.data.items) {
                // 常见的列表字段名
                couponsData = res.data.items
              } else if (res.data.coupons) {
                // 常见的列表字段名
                couponsData = res.data.coupons
              } else if (res.data.rows) {
                // 常见的列表字段名
                couponsData = res.data.rows
              } else {
                // 如果没有找到数组，将对象转换为单项数组
                couponsData = [res.data]
              }
            }
          }
          
          // 处理每个优惠券数据
          const processedCoupons = couponsData.map(item => {
            // 检查优惠券是否已过期
            const expireDate = item.endDate || item.expireDate
            const isExpired = expireDate ? new Date(expireDate) < new Date() : false
            
            // 优先使用服务器返回的status_text字段判断状态
            let couponStatus = 'unused';
            if (item.status_text) {
              if (item.status_text === '已过期' || item.status_text === '已使用') {
                couponStatus = 'expired';
              } else if (item.status_text === '未使用' || item.status_text === '有效') {
                couponStatus = 'unused';
              }
            } else if (isExpired) {
              couponStatus = 'expired';
            } else if (item.status) {
              couponStatus = item.status;
            }
            
            // 处理优惠券金额和最低消费，优先从coupon嵌套对象获取
            let couponAmount = 0;
            let couponLimit = 0;
            let couponName = '';
            
            // 检查是否有coupon子对象
            if (item.coupon) {
              couponAmount = parseFloat(item.coupon.amount || 0);
              couponLimit = parseFloat(item.coupon.min_amount || 0);
              couponName = item.coupon.name || '';
            } else {
              // 直接从item对象获取
              couponAmount = parseFloat(item.amount || 0);
              couponLimit = parseFloat(item.min_amount || item.limit || 0);
              couponName = item.name || '';
            }
            
            return {
              ...item,
              // 确保必要字段存在
              amount: couponAmount,
              limit: couponLimit,
              name: couponName || '优惠券',
              desc: item.desc || '全场通用',
              endDate: expireDate || '暂无期限',
              // 设置状态
              status: couponStatus
            }
          })
          
          // 更新列表数据
          if (isLoadMore) {
            this.couponList = [...this.couponList, ...processedCoupons]
          } else {
            this.couponList = processedCoupons
          }
          
          // 判断是否还有更多数据
          if (total > 0) {
            this.hasMore = this.couponList.length < total
          } else {
            // 如果接口没有返回总数，则根据返回数据判断
            this.hasMore = processedCoupons.length >= this.limit
          }
          
          console.log('处理后的优惠券数据:', this.couponList)
        } else {
          this.error = true
          this.errorMsg = res.msg || '获取优惠券失败'
          console.error('获取优惠券失败:', res.msg)
        }
      } catch (error) {
        this.error = true
        this.errorMsg = '网络异常，请稍后重试'
        console.error('获取优惠券异常:', error)
      } finally {
        this.loading = false
      }
    },
    
    handleBack() {
      uni.navigateBack()
    },
    
    switchTab(index) {
      if (this.currentTab !== index) {
        this.currentTab = index
        this.refreshCoupons()
      }
    },
    
    goToMenu() {
      uni.switchTab({
        url: '/pages/menu/menu'
      })
    },
    
    // 根据tab筛选优惠券
    filterCoupons() {
      // 筛选基于tab的优惠券
      let filteredList = []
      
      if(this.currentTab === 0) {
        filteredList = this.couponList
      } else if(this.currentTab === 1) {
        // 未使用标签：显示status为unused或valid的，或status_text为"未使用"/"有效"的
        filteredList = this.couponList.filter(item => {
          // 优先使用status_text字段
          if (item.status_text) {
            return item.status_text === '未使用' || item.status_text === '有效';
          }
          // 否则使用status字段
          return item.status === 'unused' || item.status === 'valid';
        });
      } else {
        // 已过期标签：显示status为expired的，或status_text为"已过期"/"已使用"的
        filteredList = this.couponList.filter(item => {
          // 优先使用status_text字段
          if (item.status_text) {
            return item.status_text === '已过期' || item.status_text === '已使用';
          }
          // 否则使用status字段
          return item.status === 'expired' || item.status === 'used';
        });
      }
      
      // 如果有订单金额，标记优惠券是否可用
      if (this.orderAmount > 0) {
        filteredList.forEach(item => {
          const minAmount = parseFloat(item.coupon ? item.coupon.min_amount : item.limit) || 0
          item.isAvailable = this.orderAmount >= minAmount && (item.status === 'unused' || item.status === 'valid')
        })
      }
      
      return filteredList
    },
    
    // 格式化日期时间戳
    formatDate(timestamp) {
      if (!timestamp) return '暂无期限';
      
      try {
        // 检查时间戳格式
        const date = new Date(Number(timestamp) * 1000);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '暂无期限';
        }
        
        // 格式化为 YYYY-MM-DD
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
      } catch (e) {
        console.error('日期格式化错误:', e);
        return '暂无期限';
      }
    },
    
    // 选择优惠券
    selectCoupon(item) {
      // 判断优惠券是否可用
      const minAmount = parseFloat(item.coupon ? item.coupon.min_amount : item.limit) || 0
      const status = item.status
      
      // 如果优惠券已过期或订单金额不满足条件，则提示但不选择
      if (status === 'expired' || status === 'used') {
        uni.showToast({
          title: '该优惠券已过期或已使用',
          icon: 'none'
        })
        return
      }
      
      if (this.orderAmount > 0 && this.orderAmount < minAmount) {
        uni.showToast({
          title: `订单金额需满${minAmount}元才能使用`,
          icon: 'none'
        })
        return
      }
      
      // 整理优惠券数据
      const couponData = {
        id: item.id,
        coupon_id: item.coupon ? item.coupon.id : item.id,
        amount: parseFloat(item.coupon ? item.coupon.amount : item.amount),
        limit: parseFloat(item.coupon ? item.coupon.min_amount : item.limit),
        name: item.coupon ? item.coupon.name : item.name,
        desc: item.desc || '全场通用',
        expire_time: item.expire_time || item.endDate || item.expireDate
      }
      
      // 将选择的优惠券通过事件传递回订单页
      uni.$emit('couponSelected', couponData)
      
      // 返回上一页
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
  }
}

.tab-container {
  display: flex;
  background: #fff;
  padding: 0 40rpx 16rpx;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: #f5f5f5;
    transform: scaleY(0.5);
  }
  
  .tab-item {
    flex: 1;
    display: flex;
    justify-content: center;
    padding: 20rpx 0;
    position: relative;
    
    .tab-text {
      font-size: 28rpx;
      color: #666;
      position: relative;
      padding: 0 6rpx 10rpx;
      line-height: 1.4;
      
      &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 4rpx;
        border-radius: 2rpx;
        background: transparent;
        transition: background-color 0.3s;
      }
    }
    
    &.active {
      .tab-text {
        color: #8cd548;
        font-weight: 500;
        
        &::after {
          background: #8cd548;
        }
      }
    }
  }
}

.coupon-scroll {
  flex: 1;
}

.coupon-list {
  padding: 30rpx 20rpx;
  
  .coupon-item {
    position: relative;
    margin-bottom: 30rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
    display: flex;
    
    &.disabled {
      opacity: 0.8;
      
      .coupon-amount {
        background: #f0f0f0;
        
        .symbol, .value {
          color: #999;
        }
      }
    }
    
    &.expired {
      .coupon-amount {
        background: #f0f0f0;
        
        .symbol, .value, .limit {
          color: #999;
        }
      }
      
      .coupon-info {
        opacity: 0.7;
      }
    }
    
    .coupon-amount {
      width: 200rpx;
      background: #8cd548;
      padding: 30rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 16rpx;
        background-image: 
          radial-gradient(circle at 0 16rpx, transparent 0, transparent 8rpx, #fff 8rpx, #fff 16rpx),
          radial-gradient(circle at 0 16rpx, transparent 0, transparent 8rpx, #fff 8rpx, #fff 16rpx);
        background-size: 16rpx 32rpx;
        background-position: 0 0, 0 16rpx;
        background-repeat: repeat-y;
      }
      
      .symbol {
        font-size: 28rpx;
        color: #fff;
      }
      
      .value {
        font-size: 60rpx;
        line-height: 1;
        color: #fff;
        font-weight: bold;
        margin: 6rpx 0;
      }
      
      .limit {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.9);
        margin-top: 8rpx;
      }
    }
    
    .coupon-info {
      flex: 1;
      padding: 24rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .info-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;
        
        .name {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          flex: 1;
          padding-right: 20rpx;
          line-height: 1.4;
        }
        
        .status-tag {
          font-size: 22rpx;
          padding: 2rpx 12rpx;
          border-radius: 20rpx;
          white-space: nowrap;
          display: flex;
          align-items: center;
          height: 32rpx;
          
          &.unused, &.valid {
            color: #8cd548;
            background: rgba(140, 213, 72, 0.1);
          }
          
          &.expired {
            color: #999;
            background: #f5f5f5;
          }
        }
      }
      
      .desc {
        font-size: 24rpx;
        color: #999;
        margin-bottom: auto;
        line-height: 1.4;
      }
      
      .info-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;
        
        .date {
          font-size: 22rpx;
          color: #999;
          line-height: 1.4;
        }
        
        .use-btn {
          background: #8cd548;
          border-radius: 30rpx;
          padding: 6rpx 20rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          
          text {
            font-size: 22rpx;
            color: #fff;
            line-height: 1;
          }
        }
      }
    }
    
    .unavailable-tag {
      position: absolute;
      right: 24rpx;
      top: 24rpx;
      background: #ff5b5b;
      border-radius: 20rpx;
      padding: 4rpx 12rpx;
      z-index: 1;
      height: 32rpx;
      display: flex;
      align-items: center;
      
      text {
        font-size: 22rpx;
        color: #fff;
        line-height: 1;
      }
    }
    
    .expired-watermark {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%) rotate(-30deg);
      
      text {
        font-size: 80rpx;
        color: rgba(153, 153, 153, 0.2);
        font-weight: bold;
      }
    }
  }
}

.loading {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .loading-circle {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #8cd548;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  .loading-text {
    font-size: 28rpx;
    color: #999;
  }
}

.error-state {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .error-text {
    font-size: 28rpx;
    color: #ff5b5b;
    margin-bottom: 30rpx;
  }
  
  .retry-btn {
    padding: 16rpx 60rpx;
    background: #8cd548;
    border-radius: 100rpx;
    
    text {
      font-size: 28rpx;
      color: #ffffff;
    }
  }
}

.empty-state {
  padding-top: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .empty-image {
    width: 300rpx;
    height: 225rpx;
    margin-bottom: 40rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
  
  .action-btn {
    padding: 20rpx 60rpx;
    background: #8cd548;
    border-radius: 100rpx;
    
    text {
      font-size: 28rpx;
      color: #fff;
    }
  }
}

.loading-more {
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-dot {
    width: 40rpx;
    height: 40rpx;
    border: 3rpx solid rgba(140, 213, 72, 0.2);
    border-top: 3rpx solid #8cd548;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10rpx;
  }
  
  text {
    font-size: 24rpx;
    color: #999;
  }
}

.bottom-tip {
  padding: 30rpx 0 50rpx;
  text-align: center;
  
  text {
    font-size: 24rpx;
    color: #999;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>