@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.custom-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  height: 80rpx;
  padding: 0 40rpx;
  border-radius: 16rpx;
  border: none;
  margin: 0;
  position: relative;
  overflow: hidden;
}
.custom-btn::after {
  border: none;
}
.custom-btn .btn-icon {
  margin-right: 10rpx;
}
.custom-btn.btn-primary {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  color: #fff;
}
.custom-btn.btn-outline {
  background-color: transparent;
  border: 1px solid #8cd548;
  color: #8cd548;
}
.custom-btn.btn-info {
  background-color: #1890ff;
  color: #fff;
}
.custom-btn.btn-success {
  background-color: #52c41a;
  color: #fff;
}
.custom-btn.btn-warning {
  background-color: #faad14;
  color: #fff;
}
.custom-btn.btn-danger {
  background-color: #f5222d;
  color: #fff;
}
.custom-btn.btn-sm {
  height: 60rpx;
  font-size: 28rpx;
  padding: 0 30rpx;
}
.custom-btn.btn-lg {
  height: 100rpx;
  font-size: 36rpx;
  padding: 0 50rpx;
}
.custom-btn.btn-block {
  width: 100%;
  display: flex;
}
.custom-btn.btn-round {
  border-radius: 100rpx;
}
.custom-btn.btn-disabled {
  opacity: 0.6;
  background-color: #cccccc;
  color: #fff;
}
.custom-btn.btn-disabled.btn-outline {
  background-color: transparent;
  border-color: #cccccc;
  color: #cccccc;
}

