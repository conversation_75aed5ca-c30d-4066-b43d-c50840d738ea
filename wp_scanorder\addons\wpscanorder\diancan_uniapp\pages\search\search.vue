<template>
	<view class="page">
		<!-- 顶部搜索栏 -->
		<view class="header">
			<view class="nav-bar">
				<image 
					class="back-icon" 
					src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
					@tap="handleBack"
				/>
				<view class="search-bar">
					<view class="search-box">
						<u-icon name="search" size="20" color="#999"></u-icon>
						<input class="search-input" v-model="keyword" placeholder="搜索商品" placeholder-class="placeholder"
							confirm-type="search" @confirm="handleSearch" @input="handleInput" focus />
						<u-icon v-if="keyword" name="close" size="15" color="#999" @click="clearKeyword"></u-icon>
					</view>
					<text class="cancel-btn" @tap="handleCancel">取消</text>
				</view>
			</view>
		</view>

		<!-- 搜索历史 -->
		<scroll-view class="content" scroll-y>
			<block v-if="!keyword">
				<view class="history" v-if="searchHistory.length > 0">
					<view class="section-header">
						<text class="title">搜索历史</text>
						<view class="clear" @tap="clearHistory">
							<u-icon name="trash" size="32" color="#999"></u-icon>
							<text>清空</text>
						</view>
					</view>
					<view class="history-list">
						<view class="history-item" v-for="(item, index) in searchHistory" :key="index" @tap="useHistory(item)">
							<text>{{item}}</text>
						</view>
					</view>
				</view>

				<!-- 热门搜索 -->
				<view class="hot-search">
					<view class="section-header">
						<text class="title">热门搜索</text>
					</view>
					<view class="hot-list">
						<view class="hot-item" v-for="(item, index) in hotSearchList" :key="index" @tap="useHistory(item)">
							<text :class="{'hot': index < 3}">{{item}}</text>
						</view>
					</view>
				</view>
			</block>

			<!-- 搜索结 -->
			<block v-else>
				<view class="search-result" v-if="searchList.length > 0">
					<view class="result-item" v-for="(item, index) in searchList" :key="index" @tap="goToDetail(item)">
						<image class="item-image" :src="item.image" mode="aspectFill" />
						<view class="item-info">
							<text class="name">{{item.name}}</text>
							<text class="desc">{{item.desc}}</text>
							<view class="price">
								<view class="price-left">
									<text class="symbol">¥</text>
									<text class="value">{{item.price}}</text>
								</view>
								<view class="select-btn">
									<text>选规格</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 空状态 -->
				<view class="empty-state" v-else>
					<image class="empty-image" src="/static/search/empty.png" />
					<text class="empty-text">暂无相关商品</text>
				</view>
			</block>
		</scroll-view>

		<!-- 规格选择弹窗 -->
		<menu-open 
			v-if="showSpecSelector" 
			:product="selectedProduct"
			@close="closeSpecSelector"
			@add-to-cart="handleAddToCart"
		></menu-open>
	</view>
</template>

<script>
import MenuOpen from '../menu/menu_open.vue'

export default {
	components: {
		MenuOpen
	},
	
	data() {
		return {
			keyword: '',
			searchHistory: [],
			hotSearchList: [
				'柠檬茶',
				'奶茶',
				'果茶',
				'咖啡',
				'小吃',
				'招牌奶茶',
				'芒果茶',
				'鸭屎香'
			],
			searchList: [],
			productList: [{
				name: '招牌柠檬茶',
				desc: '精选柠檬片，清爽解腻',
				price: 18,
				image: '/static/menu/595f1342438e860b2989fb7a6b4614bc.png',
				spec: '600ml',
				description: '精选柠檬片，清爽解腻'
			},
			{
				name: '芒果果茶',
				desc: '新鲜芒果，香甜可口',
				price: 15,
				image: '/static/menu/51a6a8b5890fe982ce6e69b5bcc9499e.png',
				spec: '600ml',
				description: '新鲜芒果，香甜可口'
			}],
			showSpecSelector: false, // 控制规格选择器显示
			selectedProduct: null, // 当前选中的商品
		}
	},
	
	onLoad() {
		// 获取搜索历史
		this.searchHistory = uni.getStorageSync('searchHistory') || []
	},
	
	methods: {
		handleBack() {
			// 清除搜索框内容
			this.keyword = ''
			this.searchList = []
			// 返回上一页
			uni.navigateBack()
		},
		
		handleInput() {
			// 实时搜索
			this.searchProducts()
		},
		
		handleSearch() {
			if (!this.keyword) return
			
			// 保存搜索历史
			this.saveHistory()
			// 搜索商品
			this.searchProducts()
		},
		
		clearKeyword() {
			this.keyword = ''
			this.searchList = []
		},
		
		useHistory(keyword) {
			this.keyword = keyword
			this.handleSearch()
		},
		
		clearHistory() {
			uni.showModal({
				title: '提示',
				content: '确定要清空搜索历史吗？',
				success: (res) => {
					if (res.confirm) {
						this.searchHistory = []
						uni.removeStorageSync('searchHistory')
					}
				}
			})
		},
		
		saveHistory() {
			if (!this.searchHistory.includes(this.keyword)) {
				this.searchHistory.unshift(this.keyword)
				if (this.searchHistory.length > 10) {
					this.searchHistory.pop()
				}
				uni.setStorageSync('searchHistory', this.searchHistory)
			}
		},
		
		searchProducts() {
			if (!this.keyword) {
				this.searchList = []
				return
			}
			
			// 模拟搜索
			this.searchList = this.productList.filter(item =>
				item.name.toLowerCase().includes(this.keyword.toLowerCase())
			)
		},
		
		goToDetail(item) {
			this.selectSpec(item)
		},
		
		// 选择规格
		selectSpec(product) {
			// 构建完整的商品数据
			this.selectedProduct = {
				...product,
				totalPrice: product.price, // 添加总价字段
				number: 1, // 添加数量字段，改为number而非count
				property: [
					{
						name: '规格',
						values: [
							{
								value: product.spec || '常规',
								price: product.price,
								is_default: true
							}
						]
					},
					{
						name: '温度',
						values: [
							{ value: '常温', is_default: true },
							{ value: '热', is_default: false },
							{ value: '冰', is_default: false }
						]
					},
					{
						name: '糖度',
						values: [
							{ value: '标准糖', is_default: true },
							{ value: '少糖', is_default: false },
							{ value: '无糖', is_default: false }
						]
					}
				]
			}
			this.showSpecSelector = true
		},
		
		// 关闭规格选择器
		closeSpecSelector() {
			this.showSpecSelector = false
			this.selectedProduct = null
		},
		
		// 处理添加购物车
		handleAddToCart(item) {
			// 构建购物车商品数据
			const cartItem = {
				...item,
				count: item.count || item.number || 1,
				totalPrice: (item.price || 0) * (item.count || item.number || 1),
				specSelected: true
			}
			
			let cartData = uni.getStorageSync('cartData') || { list: [], total: 0, price: 0 }
			let cartList = cartData.list || []
			
			// 检查购物车是否已有相同商品（包括规格）
			const existingItem = cartList.find(cartItem => 
				cartItem.name === item.name && 
				cartItem.props_text === item.props_text
			)
			
			if (existingItem) {
				existingItem.count += (item.count || item.number || 1)
				existingItem.totalPrice = existingItem.price * existingItem.count
			} else {
				cartList.push(cartItem)
			}
			
			// 计算总数和总价
			const total = cartList.reduce((sum, item) => sum + (item.count || 0), 0)
			const price = cartList.reduce((sum, item) => sum + (item.totalPrice || 0), 0)
			
			// 更新购物车数据
			const newCartData = {
				list: cartList,
				total,
				price
			}
			uni.setStorageSync('cartData', newCartData)
			
			// 关闭规格选择器
			this.closeSpecSelector()
			
			// 显示添加成功提示
			uni.showToast({
				title: '已加入购物车',
				icon: 'none'
			})
		},
		
		// 修改取消按钮点击事件
		handleCancel() {
			// 只清除搜索框内容和搜索结果
			this.keyword = ''
			this.searchList = []
		}
	}
}
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		background: #f8f8f8;
		display: flex;
		flex-direction: column;
	}

	.header {
		background: #fff;
		padding-top: 120rpx;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 100;
		
		.nav-bar {
			position: relative;
			display: flex;
			align-items: center;
			padding: 20rpx 30rpx;
			
			.back-icon {
				width: 48rpx;
				height: 48rpx;
				padding: 10rpx;
				margin-right: 10rpx;
			}
			
			.search-bar {
				flex: 1;
				display: flex;
				align-items: center;
				
				.search-box {
					flex: 1;
					height: 72rpx;
					background: #f5f5f5;
					border-radius: 36rpx;
					padding: 0 24rpx;
					display: flex;
					align-items: center;
					
					.search-input {
						flex: 1;
						height: 100%;
						font-size: 28rpx;
						color: #333;
						margin: 0 16rpx;
					}
				}
				
				.cancel-btn {
					padding: 20rpx 30rpx;
					font-size: 28rpx;
					color: #666;
				}
			}
		}
	}

	.content {
		flex: 1;
		margin-top: 280rpx;
		height: calc(100vh - 280rpx);
	}

	.history {
		padding: 30rpx;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			
			.title {
				font-size: 30rpx;
				color: #333;
				font-weight: bold;
			}
			
			.clear {
				display: flex;
				align-items: center;
				padding: 10rpx;
				
				text {
					font-size: 26rpx;
					color: #999;
					margin-left: 8rpx;
				}
			}
		}
		
		.history-list {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			
			.history-item {
				padding: 12rpx 32rpx;
				background: #f5f5f5;
				border-radius: 100rpx;
				
				text {
					font-size: 26rpx;
					color: #666;
				}
			}
		}
	}

	.hot-search {
		padding: 30rpx;
		
		.section-header {
			margin-bottom: 20rpx;
			
			.title {
				font-size: 30rpx;
				color: #333;
				font-weight: bold;
			}
		}
		
		.hot-list {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			
			.hot-item {
				padding: 12rpx 32rpx;
				background: #f5f5f5;
				border-radius: 100rpx;
				
				text {
					font-size: 26rpx;
					color: #666;
					
					&.hot {
						color: #ff4444;
					}
				}
			}
		}
	}

	.search-result {
		padding: 20rpx;
		
		.result-item {
			background: #fff;
			border-radius: 16rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
			display: flex;
			
			.item-image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 12rpx;
				margin-right: 20rpx;
			}
			
			.item-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				
				.name {
					font-size: 28rpx;
					color: #333;
					font-weight: 500;
					margin-bottom: 8rpx;
				}
				
				.desc {
					font-size: 24rpx;
					color: #999;
					margin-bottom: 16rpx;
				}
				
				.price {
					display: flex;
					justify-content: space-between;
					align-items: center;
					
					.price-left {
						display: flex;
						align-items: baseline;
						
						.symbol {
							font-size: 24rpx;
							color: #ff4444;
						}
						
						.value {
							font-size: 32rpx;
							color: #ff4444;
							font-weight: bold;
						}
					}
					
					.select-btn {
						padding: 8rpx 24rpx;
						background: #8cd548;
						border-radius: 100rpx;
						
						text {
							font-size: 24rpx;
							color: #fff;
						}
					}
				}
			}
		}
	}

	.empty-state {
		padding-top: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.empty-image {
			width: 280rpx;
			height: 280rpx;
			margin-bottom: 30rpx;
		}
		
		.empty-text {
			font-size: 28rpx;
			color: #999;
		}
	}

	.placeholder {
		color: #999;
	}
</style>