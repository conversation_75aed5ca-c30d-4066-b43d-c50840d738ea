<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">{{isEdit ? '编辑地址' : '新增地址'}}</text>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form">
      <view class="form-item">
        <text class="label">收货人</text>
        <input 
          class="input"
          v-model="form.name"
          placeholder="请输入收货人姓名"
          placeholder-class="placeholder"
        />
      </view>
      
      <view class="form-item">
        <text class="label">手机号码</text>
        <input 
          class="input"
          type="number"
          v-model="form.phone"
          placeholder="请输入手机号码"
          placeholder-class="placeholder"
          maxlength="11"
        />
      </view>
      
      <view class="form-item">
        <text class="label">所在地区</text>
        <view class="region-picker" @tap="showRegionPicker">
          <text :class="['region-text', regionSelected ? '' : 'placeholder']">
            {{ regionSelected ? `${form.province} ${form.city} ${form.district}` : '请选择所在地区' }}
          </text>
          <u-icon name="arrow-right" color="#999" size="32"></u-icon>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">详细地址</text>
        <textarea 
          class="textarea"
          v-model="form.address"
          placeholder="请输入详细地址"
          placeholder-class="placeholder"
        />
      </view>
      
      <view class="form-item">
        <text class="label">标签</text>
        <view class="tag-list">
          <view 
            v-for="(tag, index) in tags"
            :key="index"
            :class="['tag', form.tag === tag ? 'active' : '']"
            @tap="selectTag(tag)"
          >
            {{tag}}
          </view>
        </view>
      </view>
      
      <view class="form-item switch">
        <text class="label">设为默认地址</text>
        <switch 
          :checked="form.status == '1'"
          color="#8cd548"
          @change="switchDefault"
        />
      </view>
    </view>

    <!-- 底部保存按钮 -->
    <view class="save-btn" @tap="saveAddress">
      <text>保存</text>
    </view>

    <!-- 地区选择器 -->
    <area-picker
      :show.sync="showPicker"
      title="选择地区"
      @confirm="confirmRegion"
      @cancel="cancelRegion"
    ></area-picker>
  </view>
</template>

<script>
import { addAddress, editAddress, getAddressDetail } from '@/api/address.js'
import AreaPicker from '@/components/area-picker/index.vue'

export default {
  components: {
    AreaPicker
  },
  data() {
    return {
      id: 0,
      isEdit: false,
      regionSelected: false,
      form: {
        name: '',
        phone: '',
        province: '',
        city: '',
        district: '',
        address: '',
        tag: '',
        status: '0'
      },
      tags: ['家', '公司', '学校'],
      showPicker: false
    }
  },
  
  onLoad(options) {
    // 获取地址ID，如果有则是编辑模式
    console.log('地址编辑页面 - 接收参数:', JSON.stringify(options));
    
    if(options && options.id) {
      // 确保ID是字符串类型
      const safeId = String(options.id || '');
      if (!safeId) {
        console.error('编辑地址失败: 收到的ID转换后为空');
        uni.showToast({
          title: '操作失败: 无效的地址ID',
          icon: 'none'
        });
        setTimeout(() => uni.navigateBack(), 1500);
        return;
      }
      
      console.log('编辑地址 - 使用ID:', safeId);
      this.id = safeId;
      this.isEdit = true;
      this.getAddressDetail(safeId);
    } else {
      console.log('新增地址模式');
    }
  },
  
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    
    // 获取地址详情
    async getAddressDetail(id) {
      console.log('获取地址详情 - ID:', id);
      
      // 验证ID参数
      if (!id) {
        console.error('获取地址详情失败: ID为空');
        uni.showToast({
          title: '获取地址失败: 无效的ID',
          icon: 'none'
        });
        setTimeout(() => uni.navigateBack(), 1500);
        return;
      }
      
      try {
        uni.showLoading({
          title: '加载中...'
        })
        
        // 使用API获取地址详情
        console.log('调用getAddressDetail API，参数:', { id });
        const res = await getAddressDetail(id)
        
        if (res.code === 1 && res.data) {
          this.form = { 
            name: res.data.name,
            phone: res.data.phone,
            province: res.data.province,
            city: res.data.city,
            district: res.data.district,
            address: res.data.address,
            tag: res.data.tag || '',
            status: res.data.status
          }
          
          // 设置地区已选中标志
          this.regionSelected = !!(this.form.province && this.form.city && this.form.district)
        } else {
          uni.showToast({
            title: res.msg || '获取地址详情失败',
            icon: 'none'
          })
        }
      } catch (e) {
        console.error('获取地址详情失败:', JSON.stringify(e))
        uni.showToast({
          title: '获取地址详情失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    showRegionPicker() {
      this.showPicker = true
    },
    
    selectTag(tag) {
      this.form.tag = this.form.tag === tag ? '' : tag
    },
    
    switchDefault(e) {
      this.form.status = e.detail.value ? '1' : '0'
    },
    
    confirmRegion(result) {
      console.log('地区选择结果:', result);
      if (result && result.province && result.city && result.district) {
        this.form.province = result.province.name;
        this.form.city = result.city.name;
        this.form.district = result.district.name;
        this.regionSelected = true;
      }
    },
    
    cancelRegion() {
      console.log('取消地区选择');
    },
    
    async saveAddress() {
      // 表单验证
      if(!this.form.name) {
        return uni.showToast({
          title: '请输入收货人姓名',
          icon: 'none'
        })
      }
      if(!this.form.phone) {
        return uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        })
      }
      if(!/^1[3-9]\d{9}$/.test(this.form.phone)) {
        return uni.showToast({
          title: '手机号码格式不正确',
          icon: 'none'
        })
      }
      if(!this.form.province || !this.form.city || !this.form.district) {
        return uni.showToast({
          title: '请选择所在地区',
          icon: 'none'
        })
      }
      if(!this.form.address) {
        return uni.showToast({
          title: '请输入详细地址',
          icon: 'none'
        })
      }
      
      try {
        uni.showLoading({
          title: '保存中...'
        })
        
        // 根据接口文档格式化参数
        const data = {
          name: this.form.name,
          phone: this.form.phone,
          province: this.form.province,
          city: this.form.city,
          district: this.form.district,
          address: this.form.address,
          tag: this.form.tag || '',
          is_default: this.form.status === '1' ? 1 : 0
        }
        
        // 打印请求数据，帮助调试
        console.log('地址数据请求参数:', JSON.stringify(data))
        
        let res
        if (this.isEdit) {
          // 编辑模式
          data.id = this.id
          res = await editAddress(data)
        } else {
          // 新增模式
          res = await addAddress(data)
        }
        
        // 打印响应数据，帮助调试
        console.log('地址保存响应:', JSON.stringify(res))
        
        if (res.code === 1) {
          uni.showToast({
            title: '保存成功',
            icon: 'none',
            success: () => {
              setTimeout(() => {
                uni.navigateBack()
              }, 1500)
            }
          })
        } else {
          // 显示详细错误信息
          uni.showToast({
            title: '保存失败: ' + (res.msg || ''),
            icon: 'none',
            duration: 3000
          })
        }
      } catch (e) {
        console.error('保存地址异常:', JSON.stringify(e))
        uni.showToast({
          title: '保存失败: ' + (e.msg || e.message || ''),
          icon: 'none',
          duration: 3000
        })
      } finally {
        uni.hideLoading()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
  }
}

.form {
  margin-top: 20rpx;
  background: #fff;
  padding: 0 30rpx;
  
  .form-item {
    padding: 30rpx 0;
    border-bottom: 1px solid #f5f5f5;
    display: flex;
    align-items: flex-start;
    
    .label {
      width: 160rpx;
      font-size: 28rpx;
      color: #333;
      padding-top: 6rpx;
    }
    
    .input {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
    
    .textarea {
      flex: 1;
      height: 160rpx;
      font-size: 28rpx;
      color: #333;
    }
    
    .region-picker {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .region-text {
        font-size: 28rpx;
        color: #333;
        
        &.placeholder {
          color: #999;
        }
      }
    }
    
    .tag-list {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;
      
      .tag {
        padding: 12rpx 32rpx;
        background: #f8f8f8;
        border-radius: 100rpx;
        font-size: 26rpx;
        color: #666;
        
        &.active {
          background: rgba(140, 213, 72, 0.1);
          color: #8cd548;
        }
      }
    }
    
    &.switch {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .label {
        padding-top: 0;
      }
    }
  }
}

.placeholder {
  color: #999;
}

.save-btn {
  position: fixed;
  left: 40rpx;
  right: 40rpx;
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 500;
  }
  
  &:active {
    transform: scale(0.98);
  }
}
</style> 