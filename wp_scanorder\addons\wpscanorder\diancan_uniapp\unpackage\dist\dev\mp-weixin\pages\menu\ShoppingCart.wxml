<view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="cart-popup data-v-41ea784f" catchtap="__e"><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="mask data-v-41ea784f" bindtap="__e"></view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="content data-v-41ea784f" catchtap="__e"><view class="header data-v-41ea784f"><block wx:if="{{$root.g0>0}}"><view data-event-opts="{{[['tap',[['handleClear',['$event']]]]]}}" class="clear-btn data-v-41ea784f" bindtap="__e"><text class="data-v-41ea784f">清空</text></view></block><text class="title data-v-41ea784f">购物车</text><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="close-btn data-v-41ea784f" bindtap="__e"><text class="close-icon data-v-41ea784f">×</text></view></view><scroll-view class="cart-list data-v-41ea784f" style="{{'max-height:'+($root.g1>0?'60vh':'30vh')+';'}}" scroll-y="{{true}}"><block wx:if="{{$root.g2===0}}"><view class="empty-cart data-v-41ea784f"><text class="empty-text data-v-41ea784f">购物车是空的</text><text class="empty-tip data-v-41ea784f">快去选购喜欢的商品吧~</text></view></block><block wx:else><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cart-item data-v-41ea784f"><view class="item-info data-v-41ea784f"><image class="item-image data-v-41ea784f" src="{{item.$orig.image}}" mode="aspectFill"></image><view class="item-details data-v-41ea784f"><text class="item-name data-v-41ea784f">{{item.$orig.name}}</text><block wx:if="{{item.g3}}"><view class="item-spec data-v-41ea784f">{{item.$orig.specs}}</view></block><view class="item-price data-v-41ea784f"><text class="price-symbol data-v-41ea784f">¥</text><text class="price-value data-v-41ea784f">{{item.$orig.price}}</text></view></view></view><view class="quantity-control data-v-41ea784f"><view data-event-opts="{{[['tap',[['decreaseQuantity',[index]]]]]}}" class="{{['quantity-btn','minus-btn','data-v-41ea784f',(item.$orig.count===1)?'delete-state':'']}}" bindtap="__e"><text class="data-v-41ea784f">-</text></view><text class="number data-v-41ea784f">{{item.$orig.count}}</text><view data-event-opts="{{[['tap',[['increaseQuantity',[index]]]]]}}" class="quantity-btn plus-btn data-v-41ea784f" bindtap="__e"><text class="data-v-41ea784f">+</text></view></view></view></block></block></scroll-view><view class="footer data-v-41ea784f"><view class="total-info data-v-41ea784f"><text class="total-label data-v-41ea784f">合计:</text><view class="total-price data-v-41ea784f"><text class="price-symbol data-v-41ea784f">¥</text><text class="price-value data-v-41ea784f">{{$root.g4}}</text></view></view><view data-event-opts="{{[['tap',[['handleCheckout',['$event']]]]]}}" class="checkout-btn data-v-41ea784f" bindtap="__e"><text class="data-v-41ea784f">去结算</text></view></view></view></view>