@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-032f11f4 {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}
.header.data-v-032f11f4 {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-032f11f4 {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-032f11f4 {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-032f11f4 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.tab-container.data-v-032f11f4 {
  display: flex;
  background: #fff;
  padding: 0 40rpx 16rpx;
  position: relative;
}
.tab-container.data-v-032f11f4::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #f5f5f5;
  -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
}
.tab-container .tab-item.data-v-032f11f4 {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  position: relative;
}
.tab-container .tab-item .tab-text.data-v-032f11f4 {
  font-size: 28rpx;
  color: #666;
  position: relative;
  padding: 0 6rpx 10rpx;
  line-height: 1.4;
}
.tab-container .tab-item .tab-text.data-v-032f11f4::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 4rpx;
  border-radius: 2rpx;
  background: transparent;
  transition: background-color 0.3s;
}
.tab-container .tab-item.active .tab-text.data-v-032f11f4 {
  color: #8cd548;
  font-weight: 500;
}
.tab-container .tab-item.active .tab-text.data-v-032f11f4::after {
  background: #8cd548;
}
.coupon-scroll.data-v-032f11f4 {
  flex: 1;
}
.coupon-list.data-v-032f11f4 {
  padding: 30rpx 20rpx;
}
.coupon-list .coupon-item.data-v-032f11f4 {
  position: relative;
  margin-bottom: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  display: flex;
}
.coupon-list .coupon-item.disabled.data-v-032f11f4 {
  opacity: 0.8;
}
.coupon-list .coupon-item.disabled .coupon-amount.data-v-032f11f4 {
  background: #f0f0f0;
}
.coupon-list .coupon-item.disabled .coupon-amount .symbol.data-v-032f11f4, .coupon-list .coupon-item.disabled .coupon-amount .value.data-v-032f11f4 {
  color: #999;
}
.coupon-list .coupon-item.expired .coupon-amount.data-v-032f11f4 {
  background: #f0f0f0;
}
.coupon-list .coupon-item.expired .coupon-amount .symbol.data-v-032f11f4, .coupon-list .coupon-item.expired .coupon-amount .value.data-v-032f11f4, .coupon-list .coupon-item.expired .coupon-amount .limit.data-v-032f11f4 {
  color: #999;
}
.coupon-list .coupon-item.expired .coupon-info.data-v-032f11f4 {
  opacity: 0.7;
}
.coupon-list .coupon-item .coupon-amount.data-v-032f11f4 {
  width: 200rpx;
  background: #8cd548;
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.coupon-list .coupon-item .coupon-amount.data-v-032f11f4::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 16rpx;
  background-image: radial-gradient(circle at 0 16rpx, transparent 0, transparent 8rpx, #fff 8rpx, #fff 16rpx), radial-gradient(circle at 0 16rpx, transparent 0, transparent 8rpx, #fff 8rpx, #fff 16rpx);
  background-size: 16rpx 32rpx;
  background-position: 0 0, 0 16rpx;
  background-repeat: repeat-y;
}
.coupon-list .coupon-item .coupon-amount .symbol.data-v-032f11f4 {
  font-size: 28rpx;
  color: #fff;
}
.coupon-list .coupon-item .coupon-amount .value.data-v-032f11f4 {
  font-size: 60rpx;
  line-height: 1;
  color: #fff;
  font-weight: bold;
  margin: 6rpx 0;
}
.coupon-list .coupon-item .coupon-amount .limit.data-v-032f11f4 {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 8rpx;
}
.coupon-list .coupon-item .coupon-info.data-v-032f11f4 {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.coupon-list .coupon-item .coupon-info .info-top.data-v-032f11f4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.coupon-list .coupon-item .coupon-info .info-top .name.data-v-032f11f4 {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  padding-right: 20rpx;
  line-height: 1.4;
}
.coupon-list .coupon-item .coupon-info .info-top .status-tag.data-v-032f11f4 {
  font-size: 22rpx;
  padding: 2rpx 12rpx;
  border-radius: 20rpx;
  white-space: nowrap;
  display: flex;
  align-items: center;
  height: 32rpx;
}
.coupon-list .coupon-item .coupon-info .info-top .status-tag.unused.data-v-032f11f4, .coupon-list .coupon-item .coupon-info .info-top .status-tag.valid.data-v-032f11f4 {
  color: #8cd548;
  background: rgba(140, 213, 72, 0.1);
}
.coupon-list .coupon-item .coupon-info .info-top .status-tag.expired.data-v-032f11f4 {
  color: #999;
  background: #f5f5f5;
}
.coupon-list .coupon-item .coupon-info .desc.data-v-032f11f4 {
  font-size: 24rpx;
  color: #999;
  margin-bottom: auto;
  line-height: 1.4;
}
.coupon-list .coupon-item .coupon-info .info-bottom.data-v-032f11f4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}
.coupon-list .coupon-item .coupon-info .info-bottom .date.data-v-032f11f4 {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}
.coupon-list .coupon-item .coupon-info .info-bottom .use-btn.data-v-032f11f4 {
  background: #8cd548;
  border-radius: 30rpx;
  padding: 6rpx 20rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.coupon-list .coupon-item .coupon-info .info-bottom .use-btn text.data-v-032f11f4 {
  font-size: 22rpx;
  color: #fff;
  line-height: 1;
}
.coupon-list .coupon-item .unavailable-tag.data-v-032f11f4 {
  position: absolute;
  right: 24rpx;
  top: 24rpx;
  background: #ff5b5b;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  z-index: 1;
  height: 32rpx;
  display: flex;
  align-items: center;
}
.coupon-list .coupon-item .unavailable-tag text.data-v-032f11f4 {
  font-size: 22rpx;
  color: #fff;
  line-height: 1;
}
.coupon-list .coupon-item .expired-watermark.data-v-032f11f4 {
  position: absolute;
  right: 30rpx;
  top: 50%;
  -webkit-transform: translateY(-50%) rotate(-30deg);
          transform: translateY(-50%) rotate(-30deg);
}
.coupon-list .coupon-item .expired-watermark text.data-v-032f11f4 {
  font-size: 80rpx;
  color: rgba(153, 153, 153, 0.2);
  font-weight: bold;
}
.loading.data-v-032f11f4 {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading .loading-circle.data-v-032f11f4 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #8cd548;
  border-radius: 50%;
  -webkit-animation: spin-data-v-032f11f4 1s linear infinite;
          animation: spin-data-v-032f11f4 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading .loading-text.data-v-032f11f4 {
  font-size: 28rpx;
  color: #999;
}
.error-state.data-v-032f11f4 {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.error-state .error-text.data-v-032f11f4 {
  font-size: 28rpx;
  color: #ff5b5b;
  margin-bottom: 30rpx;
}
.error-state .retry-btn.data-v-032f11f4 {
  padding: 16rpx 60rpx;
  background: #8cd548;
  border-radius: 100rpx;
}
.error-state .retry-btn text.data-v-032f11f4 {
  font-size: 28rpx;
  color: #ffffff;
}
.empty-state.data-v-032f11f4 {
  padding-top: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-state .empty-image.data-v-032f11f4 {
  width: 300rpx;
  height: 225rpx;
  margin-bottom: 40rpx;
}
.empty-state .empty-text.data-v-032f11f4 {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}
.empty-state .action-btn.data-v-032f11f4 {
  padding: 20rpx 60rpx;
  background: #8cd548;
  border-radius: 100rpx;
}
.empty-state .action-btn text.data-v-032f11f4 {
  font-size: 28rpx;
  color: #fff;
}
.loading-more.data-v-032f11f4 {
  padding: 20rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.loading-more .loading-dot.data-v-032f11f4 {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid rgba(140, 213, 72, 0.2);
  border-top: 3rpx solid #8cd548;
  border-radius: 50%;
  -webkit-animation: spin-data-v-032f11f4 1s linear infinite;
          animation: spin-data-v-032f11f4 1s linear infinite;
  margin-right: 10rpx;
}
.loading-more text.data-v-032f11f4 {
  font-size: 24rpx;
  color: #999;
}
.bottom-tip.data-v-032f11f4 {
  padding: 30rpx 0 50rpx;
  text-align: center;
}
.bottom-tip text.data-v-032f11f4 {
  font-size: 24rpx;
  color: #999;
}
@-webkit-keyframes spin-data-v-032f11f4 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-032f11f4 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}

