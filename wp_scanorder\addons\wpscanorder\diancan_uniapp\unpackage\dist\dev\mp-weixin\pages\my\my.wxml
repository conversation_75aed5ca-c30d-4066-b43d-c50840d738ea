<view class="wp_-flex-col page data-v-0be17cc6"><view class="header-section data-v-0be17cc6"><view class="status-bar data-v-0be17cc6" style="{{'height:'+(statusBarHeight+'px')+';'}}"></view><view class="section data-v-0be17cc6"><view class="user-info data-v-0be17cc6"><block wx:if="{{!isLogin}}"><view data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" class="user-content data-v-0be17cc6" bindtap="__e"><view class="user-left data-v-0be17cc6"><image class="avatar data-v-0be17cc6" src="/static/my/default-avatar.png" mode="aspectFill"></image><view class="info-wrap data-v-0be17cc6"><text class="nickname data-v-0be17cc6">点击授权登录</text><text class="desc data-v-0be17cc6">登录后享受更多权益</text></view></view></view></block><block wx:else><view class="user-content data-v-0be17cc6"><view class="user-left data-v-0be17cc6"><image class="avatar data-v-0be17cc6" src="{{userInfo.avatarUrl||'/static/my/default-avatar.png'}}" mode="aspectFill"></image><view class="info-wrap data-v-0be17cc6"><text class="nickname data-v-0be17cc6">{{userInfo.nickName}}</text><text class="desc data-v-0be17cc6">{{userInfo.phone}}</text></view></view><view data-event-opts="{{[['tap',[['goToSetting',['$event']]]]]}}" class="setting-btn data-v-0be17cc6" bindtap="__e"><custom-icon vue-id="704e9d00-1" name="setting-fill" size="32" color="#a3a3a3" class="data-v-0be17cc6" bind:__l="__l"></custom-icon></view></view></block></view></view></view><block wx:if="{{isLogin}}"><view class="assets-box data-v-0be17cc6"><view data-event-opts="{{[['tap',[['goToPoints',['$event']]]]]}}" class="asset-item data-v-0be17cc6" bindtap="__e"><text class="asset-value data-v-0be17cc6">{{userAssets.points}}</text><text class="asset-label data-v-0be17cc6">积分</text></view><view data-event-opts="{{[['tap',[['goToCoupons',['$event']]]]]}}" class="asset-item data-v-0be17cc6" bindtap="__e"><text class="asset-value data-v-0be17cc6">{{userAssets.coupons}}</text><text class="asset-label data-v-0be17cc6">优惠券</text></view><view data-event-opts="{{[['tap',[['goToRecharge',['$event']]]]]}}" class="asset-item data-v-0be17cc6" bindtap="__e"><text class="asset-value data-v-0be17cc6">{{"¥"+userAssets.balance}}</text><text class="asset-label data-v-0be17cc6">余额</text></view></view></block><view class="feature-box data-v-0be17cc6"><view class="feature-title data-v-0be17cc6">我的服务</view><view class="feature-list data-v-0be17cc6"><block wx:for="{{features}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-0be17cc6"><block wx:if="{{item.type!=='service'}}"><view data-event-opts="{{[['tap',[['handleFeatureClick',['$0'],[[['features','',index]]]]]]]}}" class="feature-item data-v-0be17cc6" bindtap="__e"><view class="item-left data-v-0be17cc6"><u-icon vue-id="{{'704e9d00-2-'+index}}" name="{{item.icon}}" size="30" color="#333" class="data-v-0be17cc6" bind:__l="__l"></u-icon><text class="feature-name data-v-0be17cc6">{{item.name}}</text></view><u-icon vue-id="{{'704e9d00-3-'+index}}" name="arrow-right" color="#999" size="15" class="data-v-0be17cc6" bind:__l="__l"></u-icon></view></block><block wx:else><button class="feature-item contact-feature-btn data-v-0be17cc6" open-type="contact"><view class="item-left data-v-0be17cc6"><u-icon vue-id="{{'704e9d00-4-'+index}}" name="{{item.icon}}" size="30" color="#333" class="data-v-0be17cc6" bind:__l="__l"></u-icon><text class="feature-name data-v-0be17cc6">{{item.name}}</text></view><u-icon vue-id="{{'704e9d00-5-'+index}}" name="arrow-right" color="#999" size="15" class="data-v-0be17cc6" bind:__l="__l"></u-icon></button></block></block></block></view></view></view>