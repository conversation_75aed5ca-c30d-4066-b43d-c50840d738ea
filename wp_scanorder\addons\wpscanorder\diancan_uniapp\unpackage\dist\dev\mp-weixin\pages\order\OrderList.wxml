<view class="order-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goToDetail',['$0'],[[['processedOrderList','',index]]]]]]]}}" class="order-card" bindtap="__e"><view class="order-header"><view class="left"><text class="order-no">{{"订单号: "+(item.$orig.order_no||item.$orig.orderNo)}}</text><text class="order-time">{{item.$orig.createtime_text||item.$orig.createTime}}</text></view><view class="{{['status-tag',(item.$orig.status==='pending')?'status-pending':'',(item.$orig.status==='paid')?'status-paid':'',(item.$orig.status==='cooking')?'status-cooking':'',(item.$orig.status==='cooked')?'status-cooked':'',(item.$orig.status==='delivering')?'status-delivering':'',(item.$orig.status==='processing')?'status-processing':'',(item.$orig.status==='completed')?'status-completed':'',(item.$orig.status==='cancelled')?'status-cancelled':'',(!item.g0)?'status-default':'']}}">{{''+item.m0+''}}</view></view><view class="product-list"><block wx:for="{{item.$orig.products}}" wx:for-item="product" wx:for-index="pIndex" wx:key="pIndex"><view class="product-item"><view class="product-info"><text class="product-name">{{product.name}}</text><block wx:if="{{product.specs}}"><text class="product-specs">{{product.specs}}</text></block></view><view class="product-price"><text class="price">{{"¥"+product.price}}</text><text class="count">{{"×"+product.count}}</text></view></view></block></view><view class="order-footer"><view class="total"><text class="count-text">{{"共"+item.m1+"件商品"}}</text><text class="price">实付<text class="price-value">{{"¥"+(item.$orig.final_price||item.$orig.totalPrice)}}</text></text></view><view class="actions"><view data-event-opts="{{[['tap',[['reorder',['$0'],[[['processedOrderList','',index]]]]]]]}}" class="action-btn outline" catchtap="__e">再来一单</view><block wx:if="{{item.g1}}"><view data-event-opts="{{[['tap',[['cancelOrder',['$0'],[[['processedOrderList','',index]]]]]]]}}" class="action-btn outline" catchtap="__e">取消订单</view></block><block wx:if="{{item.$orig.status==='pending'}}"><view data-event-opts="{{[['tap',[['payOrder',['$0'],[[['processedOrderList','',index]]]]]]]}}" class="action-btn primary" catchtap="__e">去支付</view></block><block wx:if="{{item.$orig.status==='paid'||item.$orig.status==='cooking'||item.$orig.status==='cooked'||item.$orig.status==='delivering'}}"><view data-event-opts="{{[['tap',[['checkOrder',['$0'],[[['processedOrderList','',index]]]]]]]}}" class="action-btn primary" catchtap="__e">查看进度</view></block><block wx:if="{{item.$orig.status==='completed'}}"><view data-event-opts="{{[['tap',[['goToComment',['$0'],[[['processedOrderList','',index]]]]]]]}}" hidden="{{!(false)}}" class="action-btn primary" catchtap="__e">评价订单</view></block></view></view></view></block></view>