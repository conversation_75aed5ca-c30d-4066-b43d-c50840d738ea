<template>
  <view class="navbar">
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <view class="navbar-content" :class="[bgClass]">
      <!-- 返回按钮（非tabbar页面显示） -->
      <view v-if="!isTabbar" class="navbar-left" @tap="goBack">
        <custom-icon name="arrow-left" :color="textColor" :size="36"></custom-icon>
      </view>
      <view v-else class="navbar-left"></view>
      
      <!-- 页面标题 -->
      <text class="navbar-title" :style="{color: textColor}">{{title}}</text>
      
      <!-- 右侧操作区域（可自定义） -->
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'NavBar',
  props: {
    title: {
      type: String,
      default: ''
    },
    isTabbar: {
      type: Boolean,
      default: false
    },
    textColor: {
      type: String,
      default: '#333'
    },
    background: {
      type: String,
      default: '' // 可选：primary, transparent, white
    }
  },
  data() {
    return {
      statusBarHeight: 20
    }
  },
  computed: {
    bgClass() {
      if (this.background === 'primary') {
        return 'bg-primary';
      } else if (this.background === 'transparent') {
        return 'bg-transparent';
      } else if (this.background === 'white') {
        return 'bg-white';
      }
      return 'bg-white';
    }
  },
  created() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.switchTab({
            url: '/pages/home/<USER>'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss">
@import '@/styles/theme.scss';

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  
  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    
    .navbar-left, .navbar-right {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .navbar-title {
      font-size: $font-size-lg;
      font-weight: bold;
      flex: 1;
      text-align: center;
    }
    
    &.bg-primary {
      background: $gradient-primary;
    }
    
    &.bg-transparent {
      background-color: transparent;
    }
    
    &.bg-white {
      background-color: #fff;
      border-bottom: 1px solid $border-color;
    }
  }
}
</style> 