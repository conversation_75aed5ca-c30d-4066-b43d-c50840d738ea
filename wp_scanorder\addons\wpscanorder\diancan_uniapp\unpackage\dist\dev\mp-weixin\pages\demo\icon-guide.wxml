<view class="page"><nav-bar vue-id="0d301b7b-1" title="图标使用指南" isTabbar="{{false}}" background="primary" textColor="#fff" bind:__l="__l"></nav-bar><view class="navbar-placeholder" style="{{'height:'+(statusBarHeight+88+'px')+';'}}"></view><scroll-view class="content-container" scroll-y="{{true}}"><ui-card vue-id="0d301b7b-2" title="图标使用说明" bind:__l="__l" vue-slots="{{['default']}}"><view class="description"><text>应用使用两种图标系统：</text><view class="list-item"><text>1. uView 内置图标库（推荐使用）</text></view><view class="list-item"><text>2. 自定义图标（位于 static/icons 目录）</text></view><text class="tip">使用统一图标组件 custom-icon 可以同时支持这两种图标</text></view></ui-card><ui-card vue-id="0d301b7b-3" title="uView 内置图标（常用）" bind:__l="__l" vue-slots="{{['default']}}"><view class="icon-grid"><block wx:for="{{commonIcons}}" wx:for-item="icon" wx:for-index="index" wx:key="index"><view class="icon-grid-item"><custom-icon vue-id="{{('0d301b7b-4-'+index)+','+('0d301b7b-3')}}" name="{{icon}}" size="{{40}}" color="#333" bind:__l="__l"></custom-icon><text class="icon-text">{{icon}}</text></view></block></view></ui-card><ui-card vue-id="0d301b7b-5" title="自定义图标" bind:__l="__l" vue-slots="{{['default']}}"><view class="icon-grid"><block wx:for="{{customIcons}}" wx:for-item="icon" wx:for-index="index" wx:key="index"><view class="icon-grid-item"><custom-icon vue-id="{{('0d301b7b-6-'+index)+','+('0d301b7b-5')}}" name="{{icon}}" size="{{40}}" custom="{{true}}" bind:__l="__l"></custom-icon><text class="icon-text">{{icon}}</text></view></block></view></ui-card><ui-card vue-id="0d301b7b-7" title="图标使用示例" bind:__l="__l" vue-slots="{{['default']}}"><view class="example-section"><view class="example-title">基本使用</view><view class="example-content"><custom-icon vue-id="{{('0d301b7b-8')+','+('0d301b7b-7')}}" name="home" size="32" color="#8cd548" bind:__l="__l"></custom-icon><view class="code-block"><text class="code"><custom-icon name="home" size="32" color="#8cd548"></custom-icon></text></view></view></view><view class="example-section"><view class="example-title">图标 + 文字</view><view class="example-content"><view class="icon-text"><custom-icon class="icon" vue-id="{{('0d301b7b-9')+','+('0d301b7b-7')}}" name="home" size="28" color="#8cd548" bind:__l="__l"></custom-icon><text class="text">首页</text></view><view class="code-block"><text class="code"><view class="icon-text">
  <custom-icon name="home" size="28" color="#8cd548" class="icon"></custom-icon>
  <text class="text">首页</text>
</view></text></view></view></view><view class="example-section"><view class="example-title">按钮中的图标</view><view class="example-content"><custom-button vue-id="{{('0d301b7b-10')+','+('0d301b7b-7')}}" type="primary" icon="plus" bind:__l="__l" vue-slots="{{['default']}}">添加</custom-button><view class="code-block"><text class="code"><custom-button type="primary" icon="plus">添加</custom-button></text></view></view></view></ui-card></scroll-view></view>