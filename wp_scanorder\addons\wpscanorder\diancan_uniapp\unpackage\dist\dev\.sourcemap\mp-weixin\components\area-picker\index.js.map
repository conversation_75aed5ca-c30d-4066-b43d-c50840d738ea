{"version": 3, "sources": ["webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/area-picker/index.vue?64b5", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/area-picker/index.vue?7d42", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/area-picker/index.vue?fa06", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/area-picker/index.vue?c8d3", "uni-app:///components/area-picker/index.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/area-picker/index.vue?10cf", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/area-picker/index.vue?781b"], "names": ["name", "props", "show", "type", "default", "title", "data", "provinces", "cities", "districts", "provinceIndex", "cityIndex", "districtIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCity", "selectedDistrict", "loading", "loadTimer", "retryCount", "maxRetries", "provinceScrollTop", "cityScrollTop", "districtScrollTop", "itemHeight", "watch", "console", "mounted", "methods", "preloadProvinceData", "res", "initAreaData", "uni", "mask", "provinceRes", "cityRes", "districtRes", "provincesCount", "citiesCount", "districtsCount", "debounceLoad", "args", "clearTimeout", "fn", "result", "resolve", "loadProvinces", "icon", "setTimeout", "loadCities", "pid", "loadDistricts", "scrollToIndex", "selectProvince", "index", "selectCity", "selectDistrict", "onConfirm", "province", "city", "district", "onCancel", "onClose", "reset"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6FlrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;IACA;EACA;EACAC;IACAtB;MACA;QACA;QACAuB;QACA;UACAA;QACA;MACA;IACA;EACA;EACAC;IACAD;IACA;IACA;MACA;MACA;QACAA;MACA;IACA;EACA;EACAE;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAH;gBAAA;gBAAA,OACA;cAAA;gBAAAI;gBACA;kBACA;kBACAJ;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;kBACA1B;kBACA2B;gBACA;gBAEAP;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAQ;gBACAR;gBAAA,MAEAQ;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAEAR;gBACAM;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAG;gBACAT;gBAAA,MAEAS;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAV;gBAEA;kBACA;kBACA;kBACA;gBACA;kBACAA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAA;gBACA;gBACA;gBACA;gBACA;cAAA;gBAIAA;kBACAW;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAb;cAAA;gBAAA;gBAEA;gBACAM;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MAAA;MAAA;QAAAC;MAAA;MACA;QACAC;MACA;MAEA;QACA;UAAA;UAAA;YAAA;cAAA;gBAAA;kBAAA;kBAAA;kBAAA,OAEAC;gBAAA;kBAAAC;kBACAC;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA;kBAEAnB;kBACAmB;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CAEA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACApB;gBAAA;cAAA;gBAAA;gBAKA;gBACA;gBACAM;kBAAA1B;gBAAA;gBAEAoB;gBAAA;gBAAA,OACA;cAAA;gBAAAI;gBACAJ;gBAAA,MAEAI;kBAAA;kBAAA;gBAAA;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAJ;gBACA;gBACA;gBAEAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEAA;cAAA;gBAAA;gBAAA;cAAA;gBAGAM;kBACA1B;kBACAyC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBACAsB;kBACA;kBACA;gBACA;gBAAA;cAAA;gBAIAhB;kBACA1B;kBACAyC;gBACA;cAAA;gBAAA;gBAEA;gBACAf;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBACAxB;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;gBAEAA;gBAAA;gBAAA,OACA;cAAA;gBAAAI;gBACAJ;gBAAA,MAEAI;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAJ;gBACA;gBACA;gBAEAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACAA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACA;gBACA;gBACA;gBACAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBACAsB;kBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAD;kBAAA;kBAAA;gBAAA;gBACAxB;gBAAA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAAA;gBAKA;gBACA;gBAEAA;gBAAA;gBAAA,OACA;cAAA;gBAAAI;gBACAJ;gBAEA;kBACA;kBAEA;oBACAA;oBACA;oBACA;oBACAA;kBACA;oBACA;oBACAA;kBACA;gBACA;kBACA;kBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBACAsB;kBACA;kBACA;gBACA;gBAAA;cAAA;gBAIA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAI;MACA;MAEA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MAAA;IAEA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACA5B;gBAEA;gBAEA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA;gBAGA;gBAAA;gBAAA,OAEA;cAAA;gBAAAS;gBACAT;gBAAA,MAEAS;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAV;gBAEA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACA;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACA5B;gBAEA;gBAEA;gBACA;gBACA;gBAAA;gBAGA;gBAAA;gBAAA,OAEA;cAAA;gBAAAU;gBACAV;gBAEA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA8B;MACA;MAEA;MACA;MACA9B;MAEA;IACA;IAEA+B;MACA;QACAC;QACAC;QACAC;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtlBA;AAAA;AAAA;AAAA;AAAixC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAryC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/area-picker/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=36ea85b4&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=36ea85b4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"36ea85b4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/area-picker/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=36ea85b4&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loading && _vm.provinces.length === 0\n  var g1 = !_vm.loading && _vm.provinces.length === 0\n  var g2 = _vm.loading && _vm.cities.length === 0\n  var g3 = !_vm.loading && _vm.selectedProvince && _vm.cities.length === 0\n  var g4 = _vm.loading && _vm.districts.length === 0\n  var g5 = !_vm.loading && _vm.selectedCity && _vm.districts.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"area-picker\">\r\n    <u-popup :show=\"show\" @close=\"onClose\" mode=\"bottom\">\r\n      <view class=\"area-picker-header\">\r\n        <text class=\"cancel-btn\" @tap=\"onCancel\">取消</text>\r\n        <text class=\"title\">{{title}}</text>\r\n        <text class=\"confirm-btn\" @tap=\"onConfirm\">确定</text>\r\n      </view>\r\n      \r\n      <view class=\"area-picker-body\">\r\n        <view class=\"area-column\">\r\n          <scroll-view \r\n            scroll-y \r\n            class=\"area-scroll\" \r\n            :scroll-top=\"provinceScrollTop\"\r\n            :scroll-with-animation=\"true\"\r\n            :id=\"'province-scroll'\"\r\n          >\r\n            <view class=\"area-loading\" v-if=\"loading && provinces.length === 0\">\r\n              <text>加载中...</text>\r\n            </view>\r\n            <view \r\n              v-for=\"(item, index) in provinces\" \r\n              :key=\"item.id\"\r\n              :class=\"['area-item', provinceIndex === index ? 'active' : '']\"\r\n              @tap=\"selectProvince(index)\"\r\n              :id=\"'province-item-' + index\"\r\n            >\r\n              {{item.name}}\r\n            </view>\r\n            <view class=\"area-empty\" v-if=\"!loading && provinces.length === 0\">\r\n              <text>暂无数据</text>\r\n            </view>\r\n          </scroll-view>\r\n        </view>\r\n        \r\n        <view class=\"area-column\">\r\n          <scroll-view \r\n            scroll-y \r\n            class=\"area-scroll\" \r\n            :scroll-top=\"cityScrollTop\"\r\n            :scroll-with-animation=\"true\"\r\n            :id=\"'city-scroll'\"\r\n          >\r\n            <view class=\"area-loading\" v-if=\"loading && cities.length === 0\">\r\n              <text>加载中...</text>\r\n            </view>\r\n            <view \r\n              v-for=\"(item, index) in cities\" \r\n              :key=\"item.id\"\r\n              :class=\"['area-item', cityIndex === index ? 'active' : '']\"\r\n              @tap=\"selectCity(index)\"\r\n              :id=\"'city-item-' + index\"\r\n            >\r\n              {{item.name}}\r\n            </view>\r\n            <view class=\"area-empty\" v-if=\"!loading && selectedProvince && cities.length === 0\">\r\n              <text>暂无数据</text>\r\n            </view>\r\n          </scroll-view>\r\n        </view>\r\n        \r\n        <view class=\"area-column\">\r\n          <scroll-view \r\n            scroll-y \r\n            class=\"area-scroll\" \r\n            :scroll-top=\"districtScrollTop\"\r\n            :scroll-with-animation=\"true\"\r\n            :id=\"'district-scroll'\"\r\n          >\r\n            <view class=\"area-loading\" v-if=\"loading && districts.length === 0\">\r\n              <text>加载中...</text>\r\n            </view>\r\n            <view \r\n              v-for=\"(item, index) in districts\" \r\n              :key=\"item.id\"\r\n              :class=\"['area-item', districtIndex === index ? 'active' : '']\"\r\n              @tap=\"selectDistrict(index)\"\r\n              :id=\"'district-item-' + index\"\r\n            >\r\n              {{item.name}}\r\n            </view>\r\n            <view class=\"area-empty\" v-if=\"!loading && selectedCity && districts.length === 0\">\r\n              <text>暂无数据</text>\r\n            </view>\r\n          </scroll-view>\r\n        </view>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getChildrenByPid } from '@/api/area.js';\r\n\r\nexport default {\r\n  name: 'area-picker',\r\n  props: {\r\n    show: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: '选择地区'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 存储省市区数据\r\n      provinces: [],\r\n      cities: [],\r\n      districts: [],\r\n      \r\n      // 选中的索引\r\n      provinceIndex: 0,\r\n      cityIndex: 0,\r\n      districtIndex: 0,\r\n      \r\n      // 已选择的值\r\n      selectedProvince: null,\r\n      selectedCity: null,\r\n      selectedDistrict: null,\r\n      \r\n      // 数据加载标志\r\n      loading: false,\r\n      \r\n      // 防抖计时器\r\n      loadTimer: null,\r\n      \r\n      // 错误重试计数\r\n      retryCount: 0,\r\n      maxRetries: 3,\r\n      \r\n      // 滚动位置\r\n      provinceScrollTop: 0,\r\n      cityScrollTop: 0,\r\n      districtScrollTop: 0,\r\n      \r\n      // 项目高度\r\n      itemHeight: 80\r\n    };\r\n  },\r\n  watch: {\r\n    show(val) {\r\n      if (val) {\r\n        // 当组件显示时，强制加载完整的三级数据\r\n        console.log('地区选择器显示，立即初始化数据');\r\n        this.initAreaData().catch(err => {\r\n          console.error('显示时初始化数据失败:', err);\r\n        });\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    console.log('地区选择器组件已挂载');\r\n    // 组件挂载后预加载省级数据\r\n    if (this.provinces.length === 0) {\r\n      // 使用 Promise 捕获可能的错误\r\n      this.preloadProvinceData().catch(err => {\r\n        console.error('挂载时预加载数据失败:', err);\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    // 预加载省级数据\r\n    async preloadProvinceData() {\r\n      try {\r\n        console.log('预加载省级数据');\r\n        const res = await getChildrenByPid(0);\r\n        if (res && res.code === 1 && Array.isArray(res.data)) {\r\n          this.provinces = res.data;\r\n          console.log(`预加载完成，获取到${this.provinces.length}个省份`);\r\n        } else {\r\n          console.warn('预加载省份数据格式不正确:', res);\r\n        }\r\n      } catch (error) {\r\n        console.error('预加载省级数据失败:', error);\r\n      }\r\n    },\r\n    async initAreaData() {\r\n      try {\r\n        uni.showLoading({\r\n          title: '加载地区数据...',\r\n          mask: true\r\n        });\r\n        \r\n        console.log('开始初始化省市区数据');\r\n        this.loading = true;\r\n        \r\n        if (this.provinces.length === 0) {\r\n          const provinceRes = await getChildrenByPid(0);\r\n          console.log('获取省份数据结果:', provinceRes);\r\n          \r\n          if (provinceRes.code === 1 && Array.isArray(provinceRes.data) && provinceRes.data.length > 0) {\r\n            this.provinces = provinceRes.data;\r\n          } else {\r\n            console.warn('没有获取到省份数据或数据格式不正确');\r\n            uni.hideLoading();\r\n            return;\r\n          }\r\n        }\r\n        \r\n        if (this.provinces.length > 0) {\r\n          this.provinceIndex = 0;\r\n          this.selectedProvince = this.provinces[0];\r\n          \r\n          const cityRes = await getChildrenByPid(this.selectedProvince.id);\r\n          console.log('获取城市数据结果:', cityRes);\r\n          \r\n          if (cityRes.code === 1 && Array.isArray(cityRes.data) && cityRes.data.length > 0) {\r\n            this.cities = cityRes.data;\r\n            this.cityIndex = 0;\r\n            this.selectedCity = this.cities[0];\r\n            \r\n            const districtRes = await getChildrenByPid(this.selectedCity.id);\r\n            console.log('获取区县数据结果:', districtRes);\r\n            \r\n            if (districtRes.code === 1 && Array.isArray(districtRes.data) && districtRes.data.length > 0) {\r\n              this.districts = districtRes.data;\r\n              this.districtIndex = 0;\r\n              this.selectedDistrict = this.districts[0];\r\n            } else {\r\n              console.warn('没有获取到区县数据或数据格式不正确');\r\n              this.districts = [];\r\n              this.selectedDistrict = null;\r\n            }\r\n          } else {\r\n            console.warn('没有获取到城市数据或数据格式不正确');\r\n            this.cities = [];\r\n            this.selectedCity = null;\r\n            this.districts = [];\r\n            this.selectedDistrict = null;\r\n          }\r\n        }\r\n        \r\n        console.log('省市区数据初始化完成:', {\r\n          provincesCount: this.provinces.length,\r\n          citiesCount: this.cities.length,\r\n          districtsCount: this.districts.length\r\n        });\r\n      } catch (error) {\r\n        console.error('初始化省市区数据失败:', error);\r\n      } finally {\r\n        this.loading = false;\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n    // 防抖函数\r\n    debounceLoad(fn, ...args) {\r\n      if (this.loadTimer) {\r\n        clearTimeout(this.loadTimer);\r\n      }\r\n      \r\n      return new Promise((resolve) => {\r\n        this.loadTimer = setTimeout(async () => {\r\n          try {\r\n            const result = await fn.apply(this, args);\r\n            resolve(result);\r\n          } catch (e) {\r\n            console.error('执行加载函数异常:', e);\r\n            resolve(null);\r\n          }\r\n        }, 100);\r\n      });\r\n    },\r\n    \r\n    async loadProvinces() {\r\n      if (this.loading) {\r\n        console.log('正在加载中，跳过此次请求');\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        this.loading = true;\r\n        this.retryCount = 0;\r\n        uni.showLoading({ title: '加载中...' });\r\n        \r\n        console.log('发起获取省份请求');\r\n        const res = await getChildrenByPid(0);\r\n        console.log('省份数据响应:', res);\r\n        \r\n        if (res.code === 1 && Array.isArray(res.data)) {\r\n          this.provinces = res.data;\r\n          \r\n          if (this.provinces.length > 0) {\r\n            console.log(`成功获取${this.provinces.length}个省份数据`);\r\n            this.provinceIndex = 0;\r\n            this.selectedProvince = this.provinces[0];\r\n            \r\n            console.log('选中省份:', this.selectedProvince.name);\r\n            await this.debounceLoad(this.loadCities, this.selectedProvince.id);\r\n          } else {\r\n            console.warn('获取到的省份数据为空');\r\n          }\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '获取省份数据失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (e) {\r\n        console.error('加载省份数据失败:', e);\r\n        \r\n        if (this.retryCount < this.maxRetries) {\r\n          this.retryCount++;\r\n          console.log(`省份数据加载失败，第${this.retryCount}次重试`);\r\n          setTimeout(() => {\r\n            this.loading = false;\r\n            this.loadProvinces();\r\n          }, 1000);\r\n          return;\r\n        }\r\n        \r\n        uni.showToast({\r\n          title: '加载省份数据失败',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n    async loadCities(pid) {\r\n      if (!pid) {\r\n        console.error('加载城市数据失败: 无效的父级ID');\r\n        return;\r\n      }\r\n      \r\n      if (this.loading) {\r\n        console.log('正在加载中，跳过加载城市请求');\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        this.loading = true;\r\n        this.retryCount = 0;\r\n        \r\n        console.log('发起获取城市请求, 父级ID:', pid);\r\n        const res = await getChildrenByPid(pid);\r\n        console.log('城市数据响应:', res);\r\n        \r\n        if (res.code === 1 && Array.isArray(res.data)) {\r\n          this.cities = res.data;\r\n          this.districts = [];\r\n          \r\n          if (this.cities.length > 0) {\r\n            console.log(`成功获取${this.cities.length}个城市数据`);\r\n            this.cityIndex = 0;\r\n            this.selectedCity = this.cities[0];\r\n            \r\n            console.log('选中城市:', this.selectedCity.name);\r\n            await this.debounceLoad(this.loadDistricts, this.selectedCity.id);\r\n          } else {\r\n            this.selectedCity = null;\r\n            this.selectedDistrict = null;\r\n            console.log('该省份下没有城市数据');\r\n          }\r\n        } else {\r\n          this.cities = [];\r\n          this.districts = [];\r\n          this.selectedCity = null;\r\n          this.selectedDistrict = null;\r\n          console.error('获取城市数据失败:', res.msg || '未知错误');\r\n        }\r\n      } catch (e) {\r\n        console.error('加载城市数据失败:', e);\r\n        \r\n        if (this.retryCount < this.maxRetries) {\r\n          this.retryCount++;\r\n          console.log(`城市数据加载失败，第${this.retryCount}次重试`);\r\n          setTimeout(() => {\r\n            this.loading = false;\r\n            this.loadCities(pid);\r\n          }, 1000);\r\n          return;\r\n        }\r\n        \r\n        this.cities = [];\r\n        this.districts = [];\r\n        this.selectedCity = null;\r\n        this.selectedDistrict = null;\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    async loadDistricts(pid) {\r\n      if (!pid) {\r\n        console.error('加载区县数据失败: 无效的父级ID');\r\n        return;\r\n      }\r\n      \r\n      if (this.loading) {\r\n        console.log('正在加载中，跳过加载区县请求');\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        this.loading = true;\r\n        this.retryCount = 0;\r\n        \r\n        console.log('发起获取区县请求, 父级ID:', pid);\r\n        const res = await getChildrenByPid(pid);\r\n        console.log('区县数据响应:', res);\r\n        \r\n        if (res.code === 1 && Array.isArray(res.data)) {\r\n          this.districts = res.data;\r\n          \r\n          if (this.districts.length > 0) {\r\n            console.log(`成功获取${this.districts.length}个区县数据`);\r\n            this.districtIndex = 0;\r\n            this.selectedDistrict = this.districts[0];\r\n            console.log('选中区县:', this.selectedDistrict.name);\r\n          } else {\r\n            this.selectedDistrict = null;\r\n            console.log('该城市下没有区县数据');\r\n          }\r\n        } else {\r\n          this.districts = [];\r\n          this.selectedDistrict = null;\r\n          console.error('获取区县数据失败:', res.msg || '未知错误');\r\n        }\r\n      } catch (e) {\r\n        console.error('加载区县数据失败:', e);\r\n        \r\n        if (this.retryCount < this.maxRetries) {\r\n          this.retryCount++;\r\n          console.log(`区县数据加载失败，第${this.retryCount}次重试`);\r\n          setTimeout(() => {\r\n            this.loading = false;\r\n            this.loadDistricts(pid);\r\n          }, 1000);\r\n          return;\r\n        }\r\n        \r\n        this.districts = [];\r\n        this.selectedDistrict = null;\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    scrollToIndex(type, index) {\r\n      const scrollTop = index * this.itemHeight;\r\n      \r\n      switch(type) {\r\n        case 'province':\r\n          this.provinceScrollTop = scrollTop;\r\n          break;\r\n        case 'city':\r\n          this.cityScrollTop = scrollTop;\r\n          break;\r\n        case 'district':\r\n          this.districtScrollTop = scrollTop;\r\n          break;\r\n      }\r\n    },\r\n    \r\n    async selectProvince(index) {\r\n      if (index === this.provinceIndex) return;\r\n      \r\n      this.provinceIndex = index;\r\n      this.selectedProvince = this.provinces[index];\r\n      console.log('手动选择省份:', this.selectedProvince.name);\r\n      \r\n      this.scrollToIndex('province', index);\r\n      \r\n      this.cityIndex = 0;\r\n      this.districtIndex = 0;\r\n      this.cityScrollTop = 0;\r\n      this.districtScrollTop = 0;\r\n      this.cities = [];\r\n      this.districts = [];\r\n      \r\n      try {\r\n        this.loading = true;\r\n        \r\n        const cityRes = await getChildrenByPid(this.selectedProvince.id);\r\n        console.log('选择省份后获取城市数据结果:', cityRes);\r\n        \r\n        if (cityRes.code === 1 && Array.isArray(cityRes.data) && cityRes.data.length > 0) {\r\n          this.cities = cityRes.data;\r\n          this.cityIndex = 0;\r\n          this.selectedCity = this.cities[0];\r\n          \r\n          const districtRes = await getChildrenByPid(this.selectedCity.id);\r\n          console.log('选择省份后获取区县数据结果:', districtRes);\r\n          \r\n          if (districtRes.code === 1 && Array.isArray(districtRes.data) && districtRes.data.length > 0) {\r\n            this.districts = districtRes.data;\r\n            this.districtIndex = 0;\r\n            this.selectedDistrict = this.districts[0];\r\n          } else {\r\n            this.districts = [];\r\n            this.selectedDistrict = null;\r\n          }\r\n        } else {\r\n          this.cities = [];\r\n          this.selectedCity = null;\r\n          this.districts = [];\r\n          this.selectedDistrict = null;\r\n        }\r\n      } catch (error) {\r\n        console.error('选择省份加载子级数据失败:', error);\r\n        this.cities = [];\r\n        this.selectedCity = null;\r\n        this.districts = [];\r\n        this.selectedDistrict = null;\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    async selectCity(index) {\r\n      if (index === this.cityIndex) return;\r\n      \r\n      this.cityIndex = index;\r\n      this.selectedCity = this.cities[index];\r\n      console.log('手动选择城市:', this.selectedCity.name);\r\n      \r\n      this.scrollToIndex('city', index);\r\n      \r\n      this.districtIndex = 0;\r\n      this.districtScrollTop = 0;\r\n      this.districts = [];\r\n      \r\n      try {\r\n        this.loading = true;\r\n        \r\n        const districtRes = await getChildrenByPid(this.selectedCity.id);\r\n        console.log('选择城市后获取区县数据结果:', districtRes);\r\n        \r\n        if (districtRes.code === 1 && Array.isArray(districtRes.data) && districtRes.data.length > 0) {\r\n          this.districts = districtRes.data;\r\n          this.districtIndex = 0;\r\n          this.selectedDistrict = this.districts[0];\r\n        } else {\r\n          this.districts = [];\r\n          this.selectedDistrict = null;\r\n        }\r\n      } catch (error) {\r\n        console.error('选择城市加载区县数据失败:', error);\r\n        this.districts = [];\r\n        this.selectedDistrict = null;\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    selectDistrict(index) {\r\n      if (index === this.districtIndex) return;\r\n      \r\n      this.districtIndex = index;\r\n      this.selectedDistrict = this.districts[index];\r\n      console.log('手动选择区县:', this.selectedDistrict.name);\r\n      \r\n      this.scrollToIndex('district', index);\r\n    },\r\n    \r\n    onConfirm() {\r\n      const result = {\r\n        province: this.selectedProvince,\r\n        city: this.selectedCity,\r\n        district: this.selectedDistrict\r\n      };\r\n      \r\n      this.$emit('confirm', result);\r\n      this.onClose();\r\n    },\r\n    \r\n    onCancel() {\r\n      this.$emit('cancel');\r\n      this.onClose();\r\n    },\r\n    \r\n    onClose() {\r\n      this.$emit('update:show', false);\r\n    },\r\n    \r\n    // 重置选择器\r\n    async reset() {\r\n      this.provinceIndex = 0;\r\n      this.cityIndex = 0;\r\n      this.districtIndex = 0;\r\n      \r\n      if (this.provinces.length > 0) {\r\n        this.selectedProvince = this.provinces[0];\r\n        try {\r\n          await this.loadCities(this.selectedProvince.id);\r\n        } catch (error) {\r\n          console.error('重置时加载城市数据失败:', error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.area-picker {\r\n  &-header {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 90rpx;\r\n    padding: 0 30rpx;\r\n    border-bottom: 1px solid #f5f5f5;\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: 500;\r\n    }\r\n    \r\n    .cancel-btn, .confirm-btn {\r\n      font-size: 28rpx;\r\n      padding: 20rpx 0;\r\n    }\r\n    \r\n    .cancel-btn {\r\n      color: #999;\r\n    }\r\n    \r\n    .confirm-btn {\r\n      color: #8cd548;\r\n    }\r\n  }\r\n  \r\n  &-body {\r\n    display: flex;\r\n    height: 500rpx;\r\n    \r\n    .area-column {\r\n      flex: 1;\r\n      height: 100%;\r\n      \r\n      .area-scroll {\r\n        height: 100%;\r\n        \r\n        .area-item {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          height: 80rpx;\r\n          font-size: 28rpx;\r\n          color: #666;\r\n          \r\n          &.active {\r\n            color: #8cd548;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n        \r\n        .area-loading, .area-empty {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          height: 80rpx;\r\n          \r\n          text {\r\n            font-size: 26rpx;\r\n            color: #999;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=36ea85b4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=36ea85b4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948310090\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}