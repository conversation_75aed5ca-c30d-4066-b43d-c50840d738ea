<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">领取优惠券</text>
      </view>
    </view>

    <!-- 优惠券列表 -->
    <view class="coupon-list">
      <view class="coupon-item" v-for="(item, index) in couponList" :key="index">
        <view class="left">
          <view class="amount">
            <text class="symbol">¥</text>
            <text class="value">{{item.amount}}</text>
          </view>
          <view class="limit">满{{item.limit}}元可用</view>
        </view>
        <view class="right">
          <text class="name">{{item.name}}</text>
          <text class="date">有效期至{{item.endDate}}</text>
          <view class="receive-btn" :class="{disabled: item.received}" @tap="receiveCoupon(index)">
            <text>{{item.received ? '已领取' : '立即领取'}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      couponList: [
        {
          amount: 10,
          limit: 50,
          name: '新人专享券',
          endDate: '2024-12-31',
          received: false
        },
        {
          amount: 5,
          limit: 30,
          name: '满减优惠券',
          endDate: '2024-12-31',
          received: false
        }
      ]
    }
  },
  
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    
    receiveCoupon(index) {
      if(this.couponList[index].received) return
      
      this.couponList[index].received = true
      uni.showToast({
        title: '领取成功',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
  }
}

.coupon-list {
  padding: 20rpx;
  
  .coupon-item {
    display: flex;
    background: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    
    .left {
      width: 220rpx;
      background: #8cd548;
      padding: 30rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      .amount {
        display: flex;
        align-items: baseline;
        color: #fff;
        
        .symbol {
          font-size: 32rpx;
        }
        
        .value {
          font-size: 60rpx;
          font-weight: bold;
        }
      }
      
      .limit {
        font-size: 24rpx;
        color: rgba(255,255,255,0.9);
        margin-top: 8rpx;
      }
    }
    
    .right {
      flex: 1;
      padding: 30rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .name {
        font-size: 30rpx;
        color: #333;
        font-weight: 500;
      }
      
      .date {
        font-size: 24rpx;
        color: #999;
      }
      
      .receive-btn {
        align-self: flex-end;
        padding: 12rpx 32rpx;
        background: #8cd548;
        border-radius: 100rpx;
        
        text {
          font-size: 26rpx;
          color: #fff;
        }
        
        &.disabled {
          background: #ccc;
        }
      }
    }
  }
}
</style> 