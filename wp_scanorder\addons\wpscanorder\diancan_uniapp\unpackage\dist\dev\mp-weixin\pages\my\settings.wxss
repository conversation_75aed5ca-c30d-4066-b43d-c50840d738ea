@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-3176ae3d {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: 40rpx;
}
.header.data-v-3176ae3d {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-3176ae3d {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-3176ae3d {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-3176ae3d {
  font-size: 34rpx;
  color: #000;
  font-weight: 500;
}
.content-container.data-v-3176ae3d {
  padding-bottom: 40rpx;
  /* 已移动到nickname-section中 */
}
.content-container .nickname-popup.data-v-3176ae3d {
  padding: 40rpx 30rpx;
}
.content-container .nickname-popup .popup-title.data-v-3176ae3d {
  text-align: center;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.content-container .nickname-popup .popup-content.data-v-3176ae3d {
  margin-bottom: 40rpx;
}
.content-container .nickname-popup .popup-content .nickname-popup-input.data-v-3176ae3d {
  width: 100%;
  height: 80rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.content-container .nickname-popup .popup-buttons.data-v-3176ae3d {
  display: flex;
  border-top: 1px solid #f5f5f5;
}
.content-container .nickname-popup .popup-buttons .popup-btn.data-v-3176ae3d {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
}
.content-container .nickname-popup .popup-buttons .popup-btn.data-v-3176ae3d:active {
  background-color: #f9f9f9;
}
.content-container .nickname-popup .popup-buttons .cancel-btn.data-v-3176ae3d {
  color: #666;
  border-right: 1px solid #f5f5f5;
}
.content-container .nickname-popup .popup-buttons .normal-btn.data-v-3176ae3d {
  color: #2196F3;
  font-weight: 500;
  border-right: 1px solid #f5f5f5;
}
.content-container .nickname-popup .popup-buttons .confirm-btn.data-v-3176ae3d {
  color: #07c160;
  font-weight: 500;
}
.content-container .nickname-popup .popup-tip.data-v-3176ae3d {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-bottom: 20rpx;
}
.content-container .avatar-section.data-v-3176ae3d {
  position: relative;
  padding: 40rpx 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-bottom: 1px solid #f5f5f5;
}
.content-container .avatar-section .avatar.data-v-3176ae3d {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  border: 2rpx solid #f5f5f5;
}
.content-container .avatar-section .avatar-button.data-v-3176ae3d {
  position: absolute;
  bottom: 30rpx;
  right: 50%;
  -webkit-transform: translateX(60rpx);
          transform: translateX(60rpx);
  width: 60rpx;
  height: 60rpx;
  padding: 0;
  margin: 0;
  border: none;
  background: transparent;
}
.content-container .avatar-section .avatar-button.data-v-3176ae3d::after {
  border: none;
}
.content-container .avatar-section .avatar-overlay.data-v-3176ae3d {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.content-container .form-list.data-v-3176ae3d {
  margin-top: 20rpx;
  background: #fff;
}
.content-container .form-list .form-item.data-v-3176ae3d {
  display: flex;
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f5f5f5;
}
.content-container .form-list .form-item.data-v-3176ae3d:last-child {
  border-bottom: none;
}
.content-container .form-list .form-item .form-label.data-v-3176ae3d {
  width: 160rpx;
  font-size: 32rpx;
  color: #333;
  padding-top: 8rpx;
}
.content-container .form-list .form-item .form-content.data-v-3176ae3d {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content-container .form-list .form-item .form-content .form-input.data-v-3176ae3d {
  flex: 1;
  height: 80rpx;
  font-size: 32rpx;
  color: #333;
  padding: 0 10rpx;
}
.content-container .form-list .form-item .form-content .form-input.data-v-3176ae3d::-webkit-input-placeholder {
  color: #999;
}
.content-container .form-list .form-item .form-content .form-input.data-v-3176ae3d::placeholder {
  color: #999;
}
.content-container .form-list .form-item .form-content .form-text.data-v-3176ae3d {
  font-size: 32rpx;
  color: #333;
}
.content-container .form-list .form-item .form-content .form-right-btn.data-v-3176ae3d {
  margin-left: 20rpx;
}
.content-container .form-list .form-item .form-content .form-right-btn .btn-text.data-v-3176ae3d {
  color: #576b95;
  font-size: 32rpx;
}
.content-container .form-list .form-item .form-content .form-right-tip.data-v-3176ae3d {
  margin-left: 20rpx;
}
.content-container .form-list .form-item .form-content .form-right-tip .tip-text.data-v-3176ae3d {
  color: #999;
  font-size: 26rpx;
}
.content-container .form-list .form-item .form-content .gender-group.data-v-3176ae3d {
  display: flex;
  flex: 1;
}
.content-container .form-list .form-item .form-content .gender-group .gender-option.data-v-3176ae3d {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
}
.content-container .form-list .form-item .form-content .gender-group .gender-option .gender-radio.data-v-3176ae3d {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}
.content-container .form-list .form-item .form-content .gender-group .gender-option .gender-radio .radio-inner.data-v-3176ae3d {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background: #07c160;
}
.content-container .form-list .form-item .form-content .gender-group .gender-option .gender-text.data-v-3176ae3d {
  font-size: 32rpx;
  color: #333;
}
.content-container .form-list .form-item .form-content .gender-group .gender-option.active .gender-radio.data-v-3176ae3d {
  border-color: #07c160;
}
.content-container .sync-wx-profile.data-v-3176ae3d {
  background: #07c160;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2);
}
.content-container .sync-wx-profile text.data-v-3176ae3d {
  font-size: 28rpx;
  color: #fff;
  margin-left: 10rpx;
}
.content-container .sync-wx-profile.data-v-3176ae3d:active {
  opacity: 0.9;
}
.form-list.data-v-3176ae3d {
  margin: 30rpx;
}
.form-list .form-section.data-v-3176ae3d {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.form-list .form-section .form-item.data-v-3176ae3d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}
.form-list .form-section .form-item.data-v-3176ae3d:last-child {
  border-bottom: none;
}
.form-list .form-section .form-item .item-left.data-v-3176ae3d {
  display: flex;
  align-items: center;
}
.form-list .form-section .form-item .item-left .label.data-v-3176ae3d {
  font-size: 30rpx;
  color: #333;
  margin-left: 16rpx;
}
.form-list .form-section .form-item .item-right.data-v-3176ae3d {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  /* 已移除昵称按钮相关样式 */
}
.form-list .form-section .form-item .item-right .value.data-v-3176ae3d {
  font-size: 30rpx;
  color: #999;
  margin-right: 16rpx;
}
.form-list .form-section .form-item.data-v-3176ae3d:active {
  background-color: #f9f9f9;
}
.privacy-tip.data-v-3176ae3d {
  padding: 30rpx 40rpx;
}
.privacy-tip .tip-text.data-v-3176ae3d {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
}
.save-btn.data-v-3176ae3d {
  margin: 40rpx 30rpx;
  height: 90rpx;
  background: #e64340;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.save-btn text.data-v-3176ae3d {
  font-size: 34rpx;
  color: #fff;
  font-weight: 500;
}
.save-btn.data-v-3176ae3d:active {
  opacity: 0.9;
}
.about-container.data-v-3176ae3d {
  margin-top: 40rpx;
  text-align: center;
}
.about-btn.data-v-3176ae3d {
  display: inline-block;
  padding: 20rpx 40rpx;
}
.about-btn text.data-v-3176ae3d {
  font-size: 28rpx;
  color: #999;
}
.about-btn.data-v-3176ae3d:active {
  opacity: 0.7;
}
.logout-container.data-v-3176ae3d {
  margin-top: 20rpx;
  text-align: center;
}
.logout-btn.data-v-3176ae3d {
  display: inline-block;
  padding: 20rpx 40rpx;
}
.logout-btn text.data-v-3176ae3d {
  font-size: 32rpx;
  color: #576b95;
}
.logout-btn.data-v-3176ae3d:active {
  opacity: 0.7;
}
.avatar-popup.data-v-3176ae3d {
  padding: 30rpx;
}
.avatar-popup .popup-title.data-v-3176ae3d {
  text-align: center;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 40rpx;
}
.avatar-popup .popup-options.data-v-3176ae3d {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}
.avatar-popup .popup-options .popup-option.data-v-3176ae3d {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}
.avatar-popup .popup-options .popup-option text.data-v-3176ae3d {
  font-size: 28rpx;
  color: #333;
  margin-top: 16rpx;
}
.avatar-popup .popup-options .popup-option .wx-button.data-v-3176ae3d {
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  border: none;
  line-height: normal;
}
.avatar-popup .popup-options .popup-option .wx-button.data-v-3176ae3d::after {
  border: none;
}
.avatar-popup .popup-options .popup-option .wx-button text.data-v-3176ae3d {
  font-size: 28rpx;
  color: #333;
  margin-top: 16rpx;
}
.avatar-popup .popup-options .popup-option.data-v-3176ae3d:active {
  opacity: 0.7;
}
.avatar-popup .popup-cancel.data-v-3176ae3d {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  color: #333;
  border-top: 1px solid #eee;
}
.avatar-popup .popup-cancel.data-v-3176ae3d:active {
  background-color: #f5f5f5;
}
.nickname-popup.data-v-3176ae3d {
  padding: 40rpx 30rpx;
}
.nickname-popup .popup-title.data-v-3176ae3d {
  text-align: center;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.nickname-popup .popup-content.data-v-3176ae3d {
  margin-bottom: 40rpx;
}
.nickname-popup .popup-content .nickname-popup-input.data-v-3176ae3d {
  width: 100%;
  height: 80rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
/* 微信昵称按钮样式 */
.wx-nickname-btn.data-v-3176ae3d {
  background: transparent;
  padding: 0;
  margin: 0 0 0 10rpx;
  line-height: normal;
  border: none;
  display: inline-block;
}
.wx-nickname-btn.data-v-3176ae3d::after {
  border: none;
}
/* 昵称弹窗样式 */
/* 昵称弹窗 - 简约风格 */
.nickname-popup-container.data-v-3176ae3d {
  width: 580rpx;
  padding: 40rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  -webkit-animation: fadeIn-data-v-3176ae3d 0.3s ease-out;
          animation: fadeIn-data-v-3176ae3d 0.3s ease-out;
}
/* 标题区 */
.nickname-popup-container .popup-header.data-v-3176ae3d {
  text-align: center;
  margin-bottom: 30rpx;
}
.nickname-popup-container .popup-header .popup-title.data-v-3176ae3d {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
/* 输入区 */
.nickname-popup-container .input-container.data-v-3176ae3d {
  margin-bottom: 30rpx;
}
.nickname-popup-container .input-container .input-wrapper.data-v-3176ae3d {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 4rpx 16rpx;
  margin-bottom: 12rpx;
}
.nickname-popup-container .input-container .input-wrapper .input-icon.data-v-3176ae3d {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.nickname-popup-container .input-container .input-wrapper .nickname-edit-input.data-v-3176ae3d {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  background: transparent;
}
.nickname-popup-container .input-container .input-tip.data-v-3176ae3d {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
/* 按钮区 */
.nickname-popup-container .action-btns.data-v-3176ae3d {
  margin-top: 20rpx;
}
.nickname-popup-container .action-btns .cancel-btn.data-v-3176ae3d {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  color: #07c160;
  background: #f8f8f8;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}
.nickname-popup-container .action-btns .cancel-btn.data-v-3176ae3d:active {
  background-color: #f2f2f2;
}
.popup-buttons.data-v-3176ae3d {
  display: flex;
  border-top: 1px solid #f5f5f5;
}
.popup-buttons .popup-btn.data-v-3176ae3d {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
}
.popup-buttons .popup-btn.data-v-3176ae3d:active {
  background-color: #f9f9f9;
}
.popup-buttons .cancel-btn.data-v-3176ae3d {
  color: #666;
  border-right: 1px solid #f5f5f5;
}
.popup-buttons .confirm-btn.data-v-3176ae3d {
  color: #07c160;
  font-weight: 500;
}
/* 动画效果 */
@-webkit-keyframes popIn-data-v-3176ae3d {
0% {
    opacity: 0;
    -webkit-transform: scale(0.9);
            transform: scale(0.9);
}
70% {
    -webkit-transform: scale(1.03);
            transform: scale(1.03);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes popIn-data-v-3176ae3d {
0% {
    opacity: 0;
    -webkit-transform: scale(0.9);
            transform: scale(0.9);
}
70% {
    -webkit-transform: scale(1.03);
            transform: scale(1.03);
}
100% {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
/* 简单的淡入动画 */
@-webkit-keyframes fadeIn-data-v-3176ae3d {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeIn-data-v-3176ae3d {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
/* 日期选择器样式 */
.birthday-picker-modal.data-v-3176ae3d {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}
.birthday-picker-mask.data-v-3176ae3d {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}
.date-picker-content.data-v-3176ae3d {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 12rpx;
  border-top-right-radius: 12rpx;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);
  z-index: 10000;
  -webkit-animation: slideUp-data-v-3176ae3d 0.3s ease-out forwards;
          animation: slideUp-data-v-3176ae3d 0.3s ease-out forwards;
}
.date-picker-header.data-v-3176ae3d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  height: 88rpx;
  font-size: 32rpx;
  background-color: #f8f8f8;
  border-bottom: 1rpx solid #eee;
}
.date-picker-cancel.data-v-3176ae3d {
  color: #888;
  font-size: 28rpx;
}
.date-picker-title.data-v-3176ae3d {
  color: #333;
  font-size: 32rpx;
  font-weight: 500;
}
.date-picker-confirm.data-v-3176ae3d {
  color: #07c160;
  font-weight: 500;
  font-size: 28rpx;
}
.date-picker-view.data-v-3176ae3d {
  width: 100%;
  height: 400rpx;
  text-align: center;
}
.picker-item.data-v-3176ae3d {
  line-height: 50px;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}
@-webkit-keyframes slideUp-data-v-3176ae3d {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp-data-v-3176ae3d {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
/* 修正后新增的样式 */
.picker-view-container.data-v-3176ae3d {
  width: 100%;
  height: 300rpx;
  background-color: #fff;
  margin-top: 10rpx;
}
.date-picker-view.data-v-3176ae3d {
  width: 100%;
  height: 100%;
  text-align: center;
}
.picker-item.data-v-3176ae3d {
  line-height: 50px;
  text-align: center;
  font-size: 32rpx;
  color: #333;
}

