<view class="empty-state"><image class="empty-state-image" src="{{image||'/static/common/empty-data.png'}}" mode="aspectFit"></image><text class="empty-state-text">{{text||'暂无数据'}}</text><block wx:if="{{showAction}}"><view class="empty-state-action"><block wx:if="{{$slots.action}}"><slot name="action"></slot></block><block wx:else><custom-button vue-id="3833ae49-1" type="{{actionType}}" size="sm" round="{{true}}" data-event-opts="{{[['^click',[['onActionClick']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">{{actionText}}</custom-button></block></view></block></view>