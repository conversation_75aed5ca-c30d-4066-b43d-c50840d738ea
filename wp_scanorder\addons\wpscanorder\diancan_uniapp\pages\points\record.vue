<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">兑换记录</text>
        <view class="empty-placeholder"></view>
      </view>
    </view>

    <!-- 记录列表 -->
    <view class="record-list" v-if="!loading && recordList.length > 0">
      <view class="record-item" v-for="(item, index) in recordList" :key="index">
        <view class="item-left">
          <image class="item-image" :src="item.image || '/static/images/placeholder.png'" @error="handleImageError" mode="aspectFill" />
        </view>
        
        <view class="item-center">
          <view class="item-name">{{item.name}}</view>
          
          <view class="item-tag" :class="{'coupon-tag': item.type === 'coupon', 'product-tag': item.type === 'product'}">
            <text>{{item.type === 'coupon' ? '优惠券' : '实物商品'}}</text>
          </view>
          
          <view class="item-time">{{formatTime(item.createtime)}}</view>
        </view>
        
        <view class="item-right">
          <view class="item-status" :class="{'success': item.status === 'success', 'pending': item.status === 'pending', 'failed': item.status === 'failed'}">
            <text>{{getStatusText(item.status)}}</text>
          </view>
          <view class="item-points">
            <text>-{{item.points}}积分</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载中状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text>正在加载...</text>
    </view>
    
    <!-- 空状态 -->
    <view v-if="!loading && recordList.length === 0" class="empty-container">
      <image class="empty-image" src="/static/order/e186e04e8774da64b58c96a8bb479840.png" mode="aspectFit" />
      <text>暂无兑换记录</text>
      <text class="empty-tips">去积分商城兑换商品吧</text>
    </view>
    
    <!-- 底部提示 -->
    <view v-if="!loading && recordList.length > 0 && !hasMore" class="bottom-tip">
      <text>—— 已经到底了 ——</text>
    </view>
    
    <!-- 上拉加载更多提示 -->
    <view v-if="!loading && recordList.length > 0 && hasMore && showLoadingMore" class="bottom-tip">
      <view class="loading-more">
        <view class="loading-dot"></view>
        <text>正在加载更多...</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getMyExchanges } from '@/api/mall';

export default {
  data() {
    return {
      recordList: [],
      loading: true,
      isRefreshing: false,
      page: 1,
      limit: 10,
      hasMore: true,
      total: 0,
      showLoadingMore: false,
      isLoadingMore: false
    }
  },
  
  onLoad() {
    this.getRecordList();
  },
  
  // 添加下拉刷新
  onPullDownRefresh() {
    this.handlePullRefresh();
  },
  
  // 添加上拉加载更多
  onReachBottom() {
    this.handleReachBottom();
  },
  
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    
    // 处理下拉刷新
    async handlePullRefresh() {
      if (this.isRefreshing) return;
      
      this.isRefreshing = true;
      
      try {
        // 重置页码
        this.page = 1;
        
        // 请求新数据
        await this.getRecordList();
        
        uni.showToast({
          title: '刷新成功',
          icon: 'none',
          duration: 1000
        });
      } catch (error) {
        console.error('下拉刷新异常:', error);
        uni.showToast({
          title: '刷新失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isRefreshing = false;
        uni.stopPullDownRefresh();
      }
    },
    
    // 处理上拉加载更多
    async handleReachBottom() {
      if (this.hasMore && !this.loading && !this.isLoadingMore) {
        this.isLoadingMore = true;
        this.showLoadingMore = true;
        
        try {
          this.page++;
          await this.getRecordList();
        } catch (error) {
          console.error('上拉加载更多异常:', error);
          // 加载失败时恢复页码
          this.page--;
        } finally {
          this.isLoadingMore = false;
          // 延迟隐藏加载提示，提升用户体验
          setTimeout(() => {
            this.showLoadingMore = false;
          }, 500);
        }
      }
    },
    
    // 获取兑换记录列表
    async getRecordList() {
      try {
        if (this.page === 1) {
          this.loading = true;
        }
        
        const params = {
          page: this.page,
          limit: this.limit
        };
        
        const res = await getMyExchanges(params);
        
        if (res.code === 1) {
          const data = res.data;
          
          // 更新总记录数
          this.total = data.total || 0;
          
          // 处理记录列表
          const records = data.rows || [];
          
          // 添加状态字段（接口未返回，默认为成功）
          const processedRecords = records.map(item => ({
            ...item,
            status: 'success' // 默认状态，实际项目中应根据接口返回的状态字段设置
          }));
          
          // 如果是第一页，替换列表，否则追加
          if (this.page === 1) {
            this.recordList = processedRecords;
          } else {
            this.recordList = [...this.recordList, ...processedRecords];
          }
          
          // 判断是否还有更多数据
          this.hasMore = this.recordList.length < this.total;
        } else {
          uni.showToast({
            title: res.msg || '获取记录失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取兑换记录异常:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 获取状态文本
    getStatusText(status) {
      switch(status) {
        case 'success':
          return '兑换成功';
        case 'pending':
          return '处理中';
        case 'failed':
          return '兑换失败';
        default:
          return '未知状态';
      }
    },
    
    // 格式化时间戳
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp * 1000);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    
    // 处理图片加载错误
    handleImageError(e) {
      // 替换为默认图片
      const target = e.target || e.currentTarget;
      if (target) {
        target.src = '/static/my/default-avatar.png';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
}

.header {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding-top: 88rpx;
  padding-bottom: 30rpx;
  position: relative;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    
    .back-icon {
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #fff;
      font-weight: bold;
    }
    
    .empty-placeholder {
      width: 48rpx;
      height: 48rpx;
    }
  }
}

.record-list {
  padding: 0 20rpx;
  
  .record-item {
    display: flex;
    align-items: center;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    padding: 30rpx 20rpx;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-left {
      margin-right: 20rpx;
      
      .item-image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
        object-fit: cover;
      }
    }
    
    .item-center {
      flex: 1;
      overflow: hidden;
      
      .item-name {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .item-tag {
        display: inline-flex;
        align-items: center;
        height: 32rpx;
        padding: 0 12rpx;
        border-radius: 4rpx;
        margin-bottom: 10rpx;
        min-width: 80rpx;
        
        text {
          font-size: 20rpx;
          white-space: nowrap;
        }
        
        &.coupon-tag {
          background: rgba(255, 68, 68, 0.1);
          
          text {
            color: #ff4444;
          }
        }
        
        &.product-tag {
          background: rgba(106, 181, 46, 0.1);
          
          text {
            color: #6ab52e;
          }
        }
      }
      
      .item-time {
        font-size: 24rpx;
        color: #999;
      }
    }
    
    .item-right {
      text-align: right;
      
      .item-status {
        margin-bottom: 10rpx;
        
        text {
          font-size: 26rpx;
        }
        
        &.success {
          text {
            color: #6ab52e;
          }
        }
        
        &.pending {
          text {
            color: #ff9900;
          }
        }
        
        &.failed {
          text {
            color: #ff4444;
          }
        }
      }
      
      .item-points {
        text {
          font-size: 26rpx;
          color: #ff4444;
        }
      }
    }
  }
}

.loading-container {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(140, 213, 72, 0.2);
    border-left: 4rpx solid #8cd548;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}

.empty-container {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 30rpx;
    color: #666;
    
    &.empty-tips {
      font-size: 24rpx;
      color: #999;
      margin-top: 10rpx;
    }
  }
}

.bottom-tip {
  padding: 40rpx 0 80rpx;
  text-align: center;
  
  text {
    font-size: 26rpx;
    color: #999;
  }
  
  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .loading-dot {
      width: 30rpx;
      height: 30rpx;
      border: 3rpx solid rgba(140, 213, 72, 0.2);
      border-left: 3rpx solid #8cd548;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 10rpx;
    }
    
    text {
      font-size: 26rpx;
      color: #999;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 