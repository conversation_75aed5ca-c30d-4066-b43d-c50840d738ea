{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/login/login.vue?f2a9", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/login/login.vue?04cc", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/login/login.vue?af14", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/login/login.vue?a38d", "uni-app:///pages/login/login.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/login/login.vue?92ac", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/login/login.vue?b45b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isAgree", "merchantInfo", "name", "logo", "redirectUrl", "loginParams", "onLoad", "methods", "getMerchantData", "res", "console", "toggleAgreement", "viewPrivacyPolicy", "uni", "title", "icon", "viewUserAgreement", "getPhoneNumber", "e", "loginResult", "codeResult", "code", "duration", "response", "userInfo", "nick<PERSON><PERSON>", "avatarUrl", "handleLoginSuccess", "userData", "token", "phone", "userId", "gender", "birthday", "thirdData", "openid", "session_key", "redirectAfterLogin", "setTimeout", "url", "success", "fail", "handleBack", "delta", "skip<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACuDlrB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;MACA;IACA;MACA;MACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;oBACAP;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAO;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAH;QACAC;QACAC;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAJ;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAKAG;kBAAA;kBAAA;gBAAA;gBACAL;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAKAF;kBAAAC;gBAAA;;gBAEA;gBAAA;gBAAA;gBAAA,OAGAD;cAAA;gBAAAM;gBACAT;;gBAEA;gBACAU,0BAEA;gBACA;kBACAA;kBACAV;gBACA;gBAAA,IAEAU;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAIAA;kBAAA;kBAAA;gBAAA;gBACAV;gBAAA,MACA;cAAA;gBAGAW;gBACAX;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAG;gBACAA;kBACAC;kBACAC;kBACAO;gBACA;gBAAA;cAAA;gBAIA;gBACAjB;kBAAAgB;gBAAA;gBACAX;;gBAEA;gBACA;kBACAL;kBACAA;kBACAK;gBACA;;gBAEA;gBACAA;gBAAA;gBAAA,OACA;cAAA;gBAAAa;gBACAb;gBAAA,IAEAa;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAGAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAhB;gBACAG;kBACAC;kBACAC;kBACAO;gBACA;cAAA;gBAAA;gBAEAT;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAc;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBACAC;gBAAA,IAEAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAGA;gBACAhB;;gBAEA;gBACAW;kBACAE;kBACAD;kBACAK;kBACAC;kBACAC;kBACAC;gBACA;gBAEApB;gBACAH;;gBAEA;gBACAwB;gBACA;kBACArB;oBACAsB;oBACAC;kBACA;kBACA1B;gBACA;;gBAEA;gBACAG;kBAAAC;kBAAAC;gBAAA;gBAAA,kCACA;cAAA;gBAAA;gBAAA;gBAGAL;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACA2B;MAAA;MACAC;QACA;UACA5B;;UAEA;UACA;YACA;YACA,mBACA,oBACA,oBACA,sBACA,eACA;YAEA;cACAA;cACAG;gBACA0B;gBACAC;kBACA9B;gBACA;gBACA+B;kBACA/B;kBACA;kBACAG;oBACA0B;kBACA;gBACA;cACA;YACA;cACA7B;cACAG;gBACA0B;gBACAC;kBACA9B;gBACA;gBACA+B;kBACA/B;kBACA;kBACAG;oBACA0B;kBACA;gBACA;cACA;YACA;;YAEA;YACA1B;UACA;YACA;YACAH;YACAG;cACA0B;cACAC;gBACA9B;cACA;cACA+B;gBACA/B;cACA;YACA;UACA;QACA;UACAA;UACA;UACAG;YACA0B;UACA;QACA;MACA;IACA;IAEA;IACAG;MACA;MACA;;MAEA;MACA;QACA7B;UACA0B;QACA;MACA;QACA;QACA1B;UACA8B;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA/B;UACA0B;QACA;MACA;QACA;QACA1B;UACA8B;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvYA;AAAA;AAAA;AAAA;AAAyvC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACA7wC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"login-container\">\n\t\t<!-- 返回按钮 -->\n\t\t<view class=\"back-button\" @tap=\"handleBack\">\n\t\t\t<image class=\"back-icon\" src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" />\n\t\t</view>\n\t\t\n\t\t<!-- 商家名称标题 -->\n\t\t<view class=\"merchant-name\">\n\t\t\t<text>{{merchantInfo.name || '点餐小程序'}}</text>\n\t\t</view>\n\t\t\n\t\t<!-- 商家LOGO -->\n\t\t<view class=\"logo-container\">\n\t\t\t<image class=\"logo-image\" :src=\"merchantInfo.logo || '/static/my/default-avatar.png'\" mode=\"aspectFill\" />\n\t\t</view>\n\t\t\n\t\t<!-- 商家名称 -->\n\t\t<view class=\"merchant-title\">\n\t\t\t<text>{{merchantInfo.name || '点餐小程序'}}</text>\n\t\t</view>\n\t\t\n\t\t<!-- 会员权益宣传 -->\n\t\t<view class=\"benefit-text\">\n\t\t\t<text>成为会员，立享更多优惠福利</text>\n\t\t</view>\n\t\t\n\t\t<!-- 授权说明 -->\n\t\t<view class=\"auth-desc\">\n\t\t\t<text>授权绑定手机号 为您提供更好的服务</text>\n\t\t</view>\n\t\t\n\t\t<!-- 登录按钮 -->\n\t\t<view class=\"login-btn-wrap\">\n\t\t\t<button class=\"login-btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">微信一键登录</button>\n\t\t</view>\n\t\t\n\t\t<!-- 用户协议选项 -->\n\t\t<view class=\"agreement-wrap\">\n\t\t\t<view class=\"checkbox\" @tap=\"toggleAgreement\">\n\t\t\t\t<view class=\"checkbox-inner\" :class=\"{'checked': isAgree}\"></view>\n\t\t\t</view>\n\t\t\t<text class=\"agreement-text\">已阅读并同意</text>\n\t\t\t<text class=\"agreement-link\" @tap=\"viewPrivacyPolicy\">《{{merchantInfo.name || '点餐小程序'}}个人信息保护政策》</text>\n\t\t\t<text class=\"agreement-link\" @tap=\"viewUserAgreement\">《{{merchantInfo.name || '点餐小程序'}}用户服务协议》</text>\n\t\t</view>\n\t\t\n\t\t<!-- 暂不登录 -->\n\t\t<view class=\"skip-login\" @tap=\"skipLogin\">\n\t\t\t<text>暂不登录</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { thirdLogin, thirdLoginFullPath } from '@/api/user';\nimport { getMerchantInfo } from '@/api/merchant';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisAgree: false,\n\t\t\tmerchantInfo: {\n\t\t\t\tname: '',\n\t\t\t\tlogo: ''\n\t\t\t},\n\t\t\tredirectUrl: '',\n\t\t\tloginParams: null\n\t\t}\n\t},\n\tonLoad(options) {\n\t\t// 获取商家信息\n\t\tthis.getMerchantData();\n\t\t\n\t\t// 保存重定向URL\n\t\tif (options.redirect) {\n\t\t\tthis.redirectUrl = decodeURIComponent(options.redirect);\n\t\t} else {\n\t\t\t// 获取之前可能存储的重定向URL\n\t\t\tconst storedRedirect = uni.getStorageSync('redirect_url');\n\t\t\tif (storedRedirect) {\n\t\t\t\tthis.redirectUrl = storedRedirect;\n\t\t\t}\n\t\t}\n\t},\n\tmethods: {\n\t\t// 获取商家信息\n\t\tasync getMerchantData() {\n\t\t\ttry {\n\t\t\t\tconst res = await getMerchantInfo();\n\t\t\t\tif (res && res.code === 1 && res.data) {\n\t\t\t\t\tthis.merchantInfo = {\n\t\t\t\t\t\tname: res.data.name || '点餐小程序',\n\t\t\t\t\t\tlogo: res.data.logo_image || '/static/my/default-avatar.png'\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('获取商家信息失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换协议同意状态\n\t\ttoggleAgreement() {\n\t\t\tthis.isAgree = !this.isAgree;\n\t\t},\n\t\t\n\t\t// 查看隐私政策\n\t\tviewPrivacyPolicy() {\n\t\t\t// 跳转到隐私政策页面\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '暂未开放',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 查看用户协议\n\t\tviewUserAgreement() {\n\t\t\t// 跳转到用户协议页面\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '暂未开放',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取手机号\n\t\tasync getPhoneNumber(e) {\n\t\t\t// 检查是否同意协议\n\t\t\tif (!this.isAgree) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请先同意用户协议和隐私政策',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 判断是否拒绝授权\n\t\t\tif (e.detail.errMsg && e.detail.errMsg.indexOf('deny') > -1) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '您已拒绝授权',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tuni.showLoading({ title: '登录中...' });\n\t\t\t\t\n\t\t\t\t// 获取微信登录凭证\n\t\t\t\tlet code;\n\t\t\t\ttry {\n\t\t\t\t\tconst loginResult = await uni.login();\n\t\t\t\t\tconsole.log('uni.login() 返回结果：', JSON.stringify(loginResult));\n\t\t\t\t\t\n\t\t\t\t\t// 处理返回结果格式，可能是数组或对象\n\t\t\t\t\tlet codeResult = loginResult;\n\t\t\t\t\t\n\t\t\t\t\t// 如果返回的是数组格式 [null, {errMsg, code}]\n\t\t\t\t\tif (Array.isArray(loginResult) && loginResult.length > 1 && loginResult[1]) {\n\t\t\t\t\t\tcodeResult = loginResult[1]; // 取第二个元素作为结果对象\n\t\t\t\t\t\tconsole.log('检测到数组格式返回，使用索引1的元素:', JSON.stringify(codeResult));\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (!codeResult) {\n\t\t\t\t\t\tthrow new Error('uni.login()返回空结果');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 检查返回值格式\n\t\t\t\t\tif (!codeResult.code) {\n\t\t\t\t\t\tconsole.error('登录凭证格式异常：', codeResult);\n\t\t\t\t\t\tthrow new Error(`获取登录凭证失败: ${codeResult.errMsg || '未知错误'}`);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tcode = codeResult.code;\n\t\t\t\t\tconsole.log('成功获取登录凭证，code:', code);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('登录凭证获取失败:', error);\n\t\t\t\t\tuni.hideLoading(); // 确保登录失败时关闭加载状态\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: error.message || '获取登录凭证失败，请检查网络后重试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\treturn; // 中止登录流程\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 构造登录参数\n\t\t\t\tconst loginParams = { code };\n\t\t\t\tconsole.log('构造登录参数:', JSON.stringify(loginParams));\n\t\t\t\t\n\t\t\t\t// 如果有手机号加密数据，增加到请求参数中\n\t\t\t\tif (e.detail.encryptedData && e.detail.iv) {\n\t\t\t\t\tloginParams.encryptedData = e.detail.encryptedData;\n\t\t\t\t\tloginParams.iv = e.detail.iv;\n\t\t\t\t\tconsole.log('添加手机号加密数据');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 调用登录接口\n\t\t\t\tconsole.log('调用登录接口，参数:', JSON.stringify(loginParams));\n\t\t\t\tconst response = await thirdLogin(loginParams);\n\t\t\t\tconsole.log('登录接口返回:', JSON.stringify(response));\n\t\t\t\t\n\t\t\t\tif (!response) {\n\t\t\t\t\tthrow new Error('服务器无响应');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (response.code !== 1) {\n\t\t\t\t\tthrow new Error(response.msg || '登录失败');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (!response.data) {\n\t\t\t\t\tthrow new Error('服务器返回数据为空');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理登录成功\n\t\t\t\tconst userInfo = { \n\t\t\t\t\tnickName: this.merchantInfo.name ? this.merchantInfo.name + '用户' : '新用户',\n\t\t\t\t\tavatarUrl: '/static/my/default-avatar.png'\n\t\t\t\t};\n\t\t\t\tawait this.handleLoginSuccess(response.data, userInfo);\n\t\t\t\t\n\t\t\t\t// 跳转到目标页面\n\t\t\t\tthis.redirectAfterLogin();\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('登录失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: error.message || '登录失败',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.hideLoading();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 处理登录成功\n\t\tasync handleLoginSuccess(data, originalUserInfo) {\n\t\t\ttry {\n\t\t\t\t// 提取核心数据\n\t\t\t\tconst userData = data.userinfo || data.user || {};\n\t\t\t\tconst token = userData.token || userData.accessToken || userData.access_token || data.token;\n\t\t\t\t\n\t\t\t\tif (!token) {\n\t\t\t\t\tthrow new Error('登录凭证无效');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 保存必要信息\n\t\t\t\tuni.setStorageSync('token', token);\n\t\t\t\t\n\t\t\t\t// 保存用户信息 - 优先使用服务器返回数据，兼容多种字段名\n\t\t\t\tconst userInfo = {\n\t\t\t\t\tavatarUrl: userData.avatar || userData.avatarurl || originalUserInfo?.avatarUrl || '/static/my/default-avatar.png',\n\t\t\t\t\tnickName: userData.nickname || userData.name || originalUserInfo?.nickName || '用户',\n\t\t\t\t\tphone: userData.mobile || userData.phone || '',\n\t\t\t\t\tuserId: userData.id || userData.user_id || '',\n\t\t\t\t\tgender: userData.gender || '',\n\t\t\t\t\tbirthday: userData.birthday || '',\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tuni.setStorageSync('userInfo', userInfo);\n\t\t\t\tconsole.log('已保存用户信息:', JSON.stringify(userInfo));\n\t\t\t\t\n\t\t\t\t// 保存第三方数据（如果有）\n\t\t\t\tconst thirdData = data.thirdinfo || data.third;\n\t\t\t\tif (thirdData?.openid || thirdData?.session_key) {\n\t\t\t\t\tuni.setStorageSync('thirdData', {\n\t\t\t\t\t\topenid: thirdData.openid || thirdData.open_id || '',\n\t\t\t\t\t\tsession_key: thirdData.session_key || thirdData.sessionKey || ''\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('已保存第三方数据');\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示成功提示\n\t\t\t\tuni.showToast({ title: '登录成功', icon: 'none' });\n\t\t\t\treturn true;\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('登录数据处理失败:', error);\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到目标页面\n\t\tredirectAfterLogin() {\n\t\t\tsetTimeout(() => {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('准备跳转，目标地址:', this.redirectUrl || '/pages/my/my');\n\t\t\t\t\t\n\t\t\t\t\t// 判断是否有重定向地址\n\t\t\t\t\tif (this.redirectUrl) {\n\t\t\t\t\t\t// 检查是否是tabBar页面\n\t\t\t\t\t\tconst tabBarPages = [\n\t\t\t\t\t\t\t'/pages/home/<USER>',\n\t\t\t\t\t\t\t'/pages/menu/menu',\n\t\t\t\t\t\t\t'/pages/order/order',\n\t\t\t\t\t\t\t'/pages/my/my'\n\t\t\t\t\t\t];\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (tabBarPages.includes(this.redirectUrl)) {\n\t\t\t\t\t\t\tconsole.log('检测到Tab页面，使用switchTab跳转');\n\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\turl: this.redirectUrl,\n\t\t\t\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t\t\t\tconsole.log('switchTab跳转成功');\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t\t\t\tconsole.error('switchTab跳转失败:', err);\n\t\t\t\t\t\t\t\t\t// 失败时尝试用redirectTo\n\t\t\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\t\t\turl: '/pages/my/my'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log('检测到普通页面，使用redirectTo跳转');\n\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\turl: this.redirectUrl,\n\t\t\t\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t\t\t\tconsole.log('redirectTo跳转成功');\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t\t\t\tconsole.error('redirectTo跳转失败:', err);\n\t\t\t\t\t\t\t\t\t// 失败时尝试用switchTab跳转到我的页面\n\t\t\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\t\t\turl: '/pages/my/my'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 清除存储的重定向URL\n\t\t\t\t\t\tuni.removeStorageSync('redirect_url');\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 默认跳转到我的页面\n\t\t\t\t\t\tconsole.log('无重定向地址，默认跳转到我的页面');\n\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\turl: '/pages/my/my',\n\t\t\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t\t\tconsole.log('默认跳转成功');\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail(err) {\n\t\t\t\t\t\t\t\tconsole.error('默认跳转失败:', err);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('跳转过程出错:', error);\n\t\t\t\t\t// 出错时尝试最基本的跳转\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl: '/pages/my/my'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, 1500);\n\t\t},\n\t\t\n\t\t// 返回上一页\n\t\thandleBack() {\n\t\t\t// 获取当前页面栈\n\t\t\tconst pages = getCurrentPages();\n\t\t\t\n\t\t\t// 如果当前只有登录页面一个页面，则跳转到首页\n\t\t\tif (pages.length <= 1) {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/pages/home/<USER>'\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 返回上一页\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 暂不登录\n\t\tskipLogin() {\n\t\t\t// 获取当前页面栈\n\t\t\tconst pages = getCurrentPages();\n\t\t\t\n\t\t\t// 如果当前只有登录页面一个页面，则跳转到首页\n\t\t\tif (pages.length <= 1) {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/pages/home/<USER>'\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\t// 返回上一页\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.login-container {\n\tmin-height: 100vh;\n\tbackground-color: #FFFFFF;\n\tposition: relative;\n\tpadding: 0 40rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.back-button {\n\tposition: absolute;\n\tleft: 30rpx;\n\ttop: 80rpx;\n\tz-index: 10;\n}\n\n.back-icon {\n\twidth: 40rpx;\n\theight: 40rpx;\n}\n\n.merchant-name {\n\tmargin-top: 80rpx;\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\ttext-align: center;\n\twidth: 100%;\n\tpadding: 20rpx 0;\n}\n\n.logo-container {\n\tmargin-top: 60rpx;\n\tmargin-bottom: 30rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.logo-image {\n\twidth: 180rpx;\n\theight: 180rpx;\n\tborder-radius: 50%;\n\tbackground-color: #f5f5f5;\n}\n\n.merchant-title {\n\tfont-size: 34rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 20rpx;\n}\n\n.benefit-text {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 80rpx;\n}\n\n.auth-desc {\n\tfont-size: 28rpx;\n\tcolor: #666666;\n\tmargin-bottom: 40rpx;\n}\n\n.login-btn-wrap {\n\twidth: 100%;\n\tpadding: 0 20rpx;\n\tmargin-bottom: 40rpx;\n}\n\n.login-btn {\n\theight: 90rpx;\n\tline-height: 90rpx;\n\tbackground-color: #78c238;\n\tcolor: #FFFFFF;\n\tfont-size: 32rpx;\n\tborder-radius: 45rpx;\n\tfont-weight: bold;\n}\n\n.agreement-wrap {\n\tdisplay: flex;\n\tflex-direction: row;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-bottom: 60rpx;\n\tflex-wrap: wrap;\n\tpadding: 0 20rpx;\n}\n\n.checkbox {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tborder: 1px solid #CCCCCC;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tmargin-right: 10rpx;\n}\n\n.checkbox-inner {\n\twidth: 20rpx;\n\theight: 20rpx;\n\tborder-radius: 50%;\n\tbackground-color: transparent;\n\t\n\t&.checked {\n\t\tbackground-color: #78c238;\n\t}\n}\n\n.agreement-text {\n\tfont-size: 26rpx;\n\tcolor: #666666;\n}\n\n.agreement-link {\n\tfont-size: 26rpx;\n\tcolor: #FF0000;\n}\n\n.skip-login {\n\tmargin-top: 40rpx;\n\tpadding: 20rpx 40rpx;\n}\n\n.skip-login text {\n\tfont-size: 28rpx;\n\tcolor: #999999;\n}\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309959\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}