<template>
	<view class="page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
			<view class="navbar-content">
				<view class="navbar-left" @tap="goBack">
					<u-icon name="arrow-left" size="20" color="#333"></u-icon>
				</view>
				<view class="navbar-title">版权声明</view>
				<view class="navbar-right"></view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="content">
			<!-- 页面标题 -->
			<view class="page-title">版权声明</view>
			<view class="page-subtitle">Copyright Notice</view>

			<!-- 第三方作品授权信息 -->
			<view class="section">
				<view class="section-title" @tap="toggleSection('thirdParty')">
					<view class="title-left">
						<u-icon name="file-text" size="18" color="#FF6B35"></u-icon>
						<text>第三方作品授权信息</text>
					</view>
					<u-icon
						:name="expandedSections.thirdParty ? 'arrow-up' : 'arrow-down'"
						size="16"
						color="#666">
					</u-icon>
				</view>

				<view class="work-list" v-show="expandedSections.thirdParty">
					<view class="work-item" v-for="(work, index) in thirdPartyWorks" :key="index">
						<view class="work-header">
							<text class="work-name">{{ work.name }}</text>
							<view class="license-badge" :class="[
								work.license === 'MIT' ? 'license-mit' : '',
								work.license === 'CC BY 4.0' ? 'license-cc' : '',
								work.license === 'Apache 2.0' ? 'license-apache' : '',
								(!work.license || (work.license !== 'MIT' && work.license !== 'CC BY 4.0' && work.license !== 'Apache 2.0')) ? 'license-default' : ''
							]">{{ work.license }}</view>
						</view>

						<view class="work-description" v-if="work.description">
							<text>{{ work.description }}</text>
						</view>

						<view class="work-details">
							<view class="detail-row">
								<text class="label">原作者：</text>
								<text class="value">{{ work.author }}</text>
							</view>
							<view class="detail-row">
								<text class="label">作品类型：</text>
								<text class="value">{{ work.type }}</text>
							</view>
							<view class="detail-row">
								<text class="label">来源链接：</text>
								<text class="link" @tap="openLink(work.sourceUrl)">点击查看原作品</text>
							</view>
							<view class="detail-row">
								<text class="label">授权协议：</text>
								<text class="link" @tap="openLink(work.licenseUrl)">{{ work.license }} 协议详情</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 原创内容声明 -->
			<view class="section">
				<view class="section-title" @tap="toggleSection('original')">
					<view class="title-left">
						<u-icon name="shield" size="18" color="#4CAF50"></u-icon>
						<text>原创内容声明</text>
					</view>
					<u-icon
						:name="expandedSections.original ? 'arrow-up' : 'arrow-down'"
						size="16"
						color="#666">
					</u-icon>
				</view>

				<view class="original-content" v-show="expandedSections.original">
					<view class="copyright-text">
						<text>本小程序的原创内容（包括但不限于界面设计、功能逻辑、文案内容等）版权归 <text class="highlight">{{ appInfo.developer }}</text> 所有。</text>
					</view>
					<view class="copyright-text">
						<text>未经书面许可，任何个人或组织不得擅自复制、修改、分发或用于商业用途。</text>
					</view>
					<view class="copyright-text">
						<text>© {{ currentYear }} {{ appInfo.developer }} 保留所有权利。</text>
					</view>
				</view>
			</view>

			<!-- CC BY 4.0 协议介绍 -->
			<view class="section">
				<view class="section-title" @tap="toggleSection('ccLicense')">
					<view class="title-left">
						<u-icon name="bookmark" size="18" color="#FF9800"></u-icon>
						<text>CC BY 4.0 协议介绍</text>
					</view>
					<u-icon
						:name="expandedSections.ccLicense ? 'arrow-up' : 'arrow-down'"
						size="16"
						color="#666">
					</u-icon>
				</view>

				<view class="cc-license" v-show="expandedSections.ccLicense">
					<view class="license-intro">
						<text class="intro-text">知识共享署名 4.0 国际许可协议（CC BY 4.0）是一种开放的版权许可协议，允许他人分发、重新混合、调整和基于您的作品进行创作，甚至是在商业性使用的情况下，只要他们给您署名。</text>
					</view>

					<view class="license-features">
						<view class="feature-title">
							<text>您可以自由地：</text>
						</view>
						<view class="feature-item">
							<u-icon name="checkmark-circle" size="16" color="#4CAF50"></u-icon>
							<text class="feature-text"><text class="feature-bold">分享</text> — 在任何媒介以任何形式复制、发行本作品</text>
						</view>
						<view class="feature-item">
							<u-icon name="checkmark-circle" size="16" color="#4CAF50"></u-icon>
							<text class="feature-text"><text class="feature-bold">演绎</text> — 修改、转换或以本作品为基础进行创作</text>
						</view>
						<view class="feature-item">
							<u-icon name="checkmark-circle" size="16" color="#4CAF50"></u-icon>
							<text class="feature-text"><text class="feature-bold">商业性使用</text> — 在任何用途下，甚至商业目的</text>
						</view>
					</view>

					<view class="license-conditions">
						<view class="condition-title">
							<text>惟须遵守下列条件：</text>
						</view>
						<view class="condition-item">
							<u-icon name="info-circle" size="16" color="#FF6B35"></u-icon>
							<text class="condition-text"><text class="condition-bold">署名</text> — 您必须给出适当的署名，提供指向本许可协议的链接，同时标明是否（对原始作品）作了修改。您可以用任何合理的方式来署名，但是不得以任何方式暗示许可人为您或您的使用背书。</text>
						</view>
					</view>

					<view class="license-links">
						<view class="link-item" @tap="openLink('https://creativecommons.org/licenses/by/4.0/deed.zh')">
							<u-icon name="external-link" size="14" color="#1976d2"></u-icon>
							<text class="link-text">查看完整协议条款</text>
						</view>
						<view class="link-item" @tap="openLink('https://js.design/community?category=detail&type=resource&id=66b1e3824979a82bdd2c417d')">
							<u-icon name="external-link" size="14" color="#1976d2"></u-icon>
							<text class="link-text">CC BY 4.0 协议详细说明</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 免责声明 -->
			<view class="section">
				<view class="section-title" @tap="toggleSection('disclaimer')">
					<view class="title-left">
						<u-icon name="info-circle" size="18" color="#2196F3"></u-icon>
						<text>免责声明</text>
					</view>
					<u-icon
						:name="expandedSections.disclaimer ? 'arrow-up' : 'arrow-down'"
						size="16"
						color="#666">
					</u-icon>
				</view>

				<view class="disclaimer" v-show="expandedSections.disclaimer">
					<view class="disclaimer-text">
						<text>1. 本页面所列第三方作品的版权归原作者所有，我们已按照相应开源协议的要求进行署名。</text>
					</view>
					<view class="disclaimer-text">
						<text>2. 如发现任何版权问题或侵权行为，请及时联系我们，我们将在收到通知后立即处理。</text>
					</view>
					<view class="disclaimer-text">
						<text>3. 联系方式：{{ appInfo.contact }}</text>
					</view>
				</view>
			</view>

			<!-- 更新信息 -->
			<view class="update-info">
				<text>最后更新：{{ updateDate }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0,
			currentYear: new Date().getFullYear(),
			updateDate: '2025-01-14',
			appInfo: {
				developer: '扫码点餐系统开发团队',
				contact: '<EMAIL>'
			},
			// 第三方作品列表 - 根据实际使用情况填写
			thirdPartyWorks: [
				{
					name: 'uView UI 组件库',
					author: 'uView团队',
					type: 'UI组件库',
					license: 'MIT',
					sourceUrl: 'https://github.com/umicro/uView2.0',
					licenseUrl: 'https://opensource.org/licenses/MIT',
					description: '基于Vue.js的移动端UI组件库，提供丰富的组件和工具'
				},
				{
					name: 'Feather Icons',
					author: 'Cole Bemis',
					type: '图标素材',
					license: 'MIT',
					sourceUrl: 'https://feathericons.com/',
					licenseUrl: 'https://opensource.org/licenses/MIT',
					description: '简洁美观的开源图标集，用于界面图标显示'
				},
				{
					name: 'CC BY 4.0 设计素材',
					author: '即时设计社区',
					type: '设计素材',
					license: 'CC BY 4.0',
					sourceUrl: 'https://js.design/community?category=detail&type=resource&id=66b1e3824979a82bdd2c417d',
					licenseUrl: 'https://creativecommons.org/licenses/by/4.0/',
					description: '遵循CC BY 4.0协议的高质量设计素材，可自由使用和修改'
				},
				{
					name: 'Uni-App框架',
					author: 'DCloud团队',
					type: '开发框架',
					license: 'Apache 2.0',
					sourceUrl: 'https://uniapp.dcloud.io/',
					licenseUrl: 'https://www.apache.org/licenses/LICENSE-2.0',
					description: '跨平台应用开发框架，支持多端发布'
				}
				// 可以根据实际使用的第三方资源继续添加
			],
			// 控制折叠状态
			expandedSections: {
				thirdParty: true,
				original: true,
				ccLicense: true,
				disclaimer: true
			}
		}
	},
	onLoad() {
		// 获取状态栏高度
		const systemInfo = uni.getSystemInfoSync()
		this.statusBarHeight = systemInfo.statusBarHeight
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 打开外部链接
		openLink(url) {
			if (!url) {
				uni.showToast({
					title: '链接不可用',
					icon: 'none'
				})
				return
			}

			// 复制链接到剪贴板（小程序限制无法直接打开外部链接）
			uni.setClipboardData({
				data: url,
				success: () => {
					uni.showToast({
						title: '链接已复制到剪贴板',
						icon: 'success'
					})
				}
			})
		},

		// 切换章节展开/折叠状态
		toggleSection(section) {
			this.expandedSections[section] = !this.expandedSections[section]
		},


	}
}
</script>

<style lang="scss" scoped>
.page {
	background-color: #f8f9fa;
	min-height: 100vh;
}

// 自定义导航栏
.custom-navbar {
	background-color: #fff;
	border-bottom: 1px solid #eee;
	
	.navbar-content {
		height: 44px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16px;
		
		.navbar-left, .navbar-right {
			width: 60px;
		}
		
		.navbar-title {
			font-size: 18px;
			font-weight: 600;
			color: #333;
		}
	}
}

// 页面内容
.content {
	padding: 20px 16px;
}

// 页面标题
.page-title {
	font-size: 28px;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 8px;
}

.page-subtitle {
	font-size: 14px;
	color: #666;
	text-align: center;
	margin-bottom: 32px;
}

// 章节样式
.section {
	background-color: #fff;
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 16px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16px;
	padding: 8px 0;
	cursor: pointer;

	.title-left {
		display: flex;
		align-items: center;

		text {
			font-size: 18px;
			font-weight: 600;
			color: #333;
			margin-left: 8px;
		}
	}
}

// 第三方作品列表
.work-list {
	.work-item {
		border: 1px solid #e0e0e0;
		border-radius: 8px;
		padding: 16px;
		margin-bottom: 12px;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}

.work-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 12px;

	.work-name {
		font-size: 16px;
		font-weight: 600;
		color: #333;
	}

	.license-badge {
		padding: 4px 8px;
		border-radius: 4px;
		font-size: 12px;
		background-color: #f5f5f5;
		color: #666;

		&.license-mit {
			background-color: #e8f5e8;
			color: #2e7d32;
		}

		&.license-cc {
			background-color: #fff3e0;
			color: #f57c00;
		}

		&.license-apache {
			background-color: #e3f2fd;
			color: #1976d2;
		}
	}
}

.work-description {
	margin-bottom: 12px;
	padding: 8px 12px;
	background-color: #f8f9fa;
	border-radius: 6px;
	border-left: 3px solid #FF6B35;

	text {
		font-size: 13px;
		color: #666;
		line-height: 1.5;
	}
}

.work-details {
	.detail-row {
		display: flex;
		margin-bottom: 8px;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.label {
			font-size: 14px;
			color: #666;
			width: 80px;
			flex-shrink: 0;
		}
		
		.value {
			font-size: 14px;
			color: #333;
			flex: 1;
		}
		
		.link {
			font-size: 14px;
			color: #1976d2;
			text-decoration: underline;
		}
	}
}

// 原创内容声明
.original-content {
	.copyright-text {
		margin-bottom: 12px;
		line-height: 1.6;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		text {
			font-size: 14px;
			color: #333;
		}
		
		.highlight {
			color: #FF6B35;
			font-weight: 600;
		}
	}
}

// CC BY 4.0 协议介绍
.cc-license {
	.license-intro {
		margin-bottom: 20px;
		padding: 16px;
		background-color: #fff3e0;
		border-radius: 8px;
		border-left: 4px solid #FF9800;

		.intro-text {
			font-size: 14px;
			color: #333;
			line-height: 1.6;
		}
	}

	.license-features, .license-conditions {
		margin-bottom: 20px;

		.feature-title, .condition-title {
			margin-bottom: 12px;

			text {
				font-size: 16px;
				font-weight: 600;
				color: #333;
			}
		}

		.feature-item, .condition-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 10px;

			&:last-child {
				margin-bottom: 0;
			}

			.feature-text, .condition-text {
				font-size: 14px;
				color: #333;
				line-height: 1.5;
				margin-left: 8px;
				flex: 1;
			}

			.feature-bold, .condition-bold {
				font-weight: 600;
				color: #FF6B35;
			}
		}
	}

	.license-links {
		border-top: 1px solid #eee;
		padding-top: 16px;

		.link-item {
			display: flex;
			align-items: center;
			margin-bottom: 12px;
			padding: 8px 0;

			&:last-child {
				margin-bottom: 0;
			}

			.link-text {
				font-size: 14px;
				color: #1976d2;
				margin-left: 8px;
				text-decoration: underline;
			}

			&:active {
				opacity: 0.7;
			}
		}
	}
}

// 免责声明
.disclaimer {
	.disclaimer-text {
		margin-bottom: 12px;
		line-height: 1.6;

		&:last-child {
			margin-bottom: 0;
		}

		text {
			font-size: 14px;
			color: #333;
		}
	}
}

// 更新信息
.update-info {
	text-align: center;
	margin-top: 32px;
	padding-top: 16px;
	border-top: 1px solid #eee;
	
	text {
		font-size: 12px;
		color: #999;
	}
}
</style>
