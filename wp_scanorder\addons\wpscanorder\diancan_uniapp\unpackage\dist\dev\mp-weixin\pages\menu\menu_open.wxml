<view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="spec-popup data-v-483f7033" catchtap="__e"><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="mask data-v-483f7033" bindtap="__e"></view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="content data-v-483f7033" catchtap="__e"><view class="product-info data-v-483f7033"><image class="product-image data-v-483f7033" src="{{product.image}}" mode="aspectFill"></image><view class="info data-v-483f7033"><text class="name data-v-483f7033">{{product.name}}</text><view class="price data-v-483f7033"><text class="symbol data-v-483f7033">¥</text><text class="value data-v-483f7033">{{currentPrice}}</text></view></view><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="close-btn data-v-483f7033" bindtap="__e"><text class="close-icon data-v-483f7033">×</text></view></view><scroll-view class="specs-container data-v-483f7033" scroll-y="{{true}}"><block wx:for="{{specGroups}}" wx:for-item="group" wx:for-index="groupIndex" wx:key="groupIndex"><view class="spec-group data-v-483f7033"><view class="group-title data-v-483f7033"><text class="group-name data-v-483f7033">{{group.name}}</text></view><view class="spec-options data-v-483f7033"><block wx:for="{{group.values}}" wx:for-item="item" wx:for-index="itemIndex" wx:key="itemIndex"><view data-event-opts="{{[['tap',[['handleSpecSelect',[groupIndex,itemIndex]]]]]}}" class="{{['spec-item','data-v-483f7033',(item.is_default)?'active':'',(selectedAnimation.groupIndex===groupIndex&&selectedAnimation.itemIndex===itemIndex)?'animate-select':'']}}" bindtap="__e"><text class="data-v-483f7033">{{item.value}}</text><block wx:if="{{group.name==='规格'}}"><text class="price-tag data-v-483f7033">{{'¥'+item.price+''}}</text></block></view></block></view></view></block><view class="selected-summary data-v-483f7033"><text class="summary-title data-v-483f7033">已选:</text><view class="summary-content data-v-483f7033"><block wx:for="{{selectedSpecsSummary}}" wx:for-item="spec" wx:for-index="index" wx:key="index"><text class="summary-item data-v-483f7033">{{''+spec.name+":"+spec.value+''}}<block wx:if="{{index<$root.g0-1}}"><text class="data-v-483f7033">、</text></block></text></block></view></view></scroll-view><view class="bottom-action data-v-483f7033"><view class="quantity-control data-v-483f7033"><view data-event-opts="{{[['tap',[['decreaseQuantity',['$event']]]]]}}" class="{{['quantity-btn','minus-btn','data-v-483f7033',(quantity<=1)?'disabled':'']}}" bindtap="__e"><text class="data-v-483f7033">-</text></view><text class="number data-v-483f7033">{{quantity}}</text><view data-event-opts="{{[['tap',[['increaseQuantity',['$event']]]]]}}" class="quantity-btn plus-btn data-v-483f7033" bindtap="__e"><text class="data-v-483f7033">+</text></view></view><view data-event-opts="{{[['tap',[['handleAddToCart',['$event']]]]]}}" class="add-btn data-v-483f7033" bindtap="__e"><text class="data-v-483f7033">加入购物车</text></view></view></view></view>