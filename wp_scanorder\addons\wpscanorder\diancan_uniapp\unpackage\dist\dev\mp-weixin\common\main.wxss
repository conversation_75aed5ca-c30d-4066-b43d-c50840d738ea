@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView基础样式 */
.u-line-1 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical !important;
}
.u-line-2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-border {
  border-width: 0.5px !important;
  border-color: #dadbde !important;
  border-style: solid;
}
.u-border-top {
  border-top-width: 0.5px !important;
  border-color: #dadbde !important;
  border-top-style: solid;
}
.u-border-left {
  border-left-width: 0.5px !important;
  border-color: #dadbde !important;
  border-left-style: solid;
}
.u-border-right {
  border-right-width: 0.5px !important;
  border-color: #dadbde !important;
  border-right-style: solid;
}
.u-border-bottom {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
.u-border-top-bottom {
  border-top-width: 0.5px !important;
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-top-style: solid;
  border-bottom-style: solid;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/************************************************************
** 请将全局样式拷贝到项目的全局 CSS 文件或者当前页面的顶部 **
** 否则页面将无法正常显示                                  **
************************************************************/
page {
  font-size: 16px;
}
page {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
view,
image,
text {
  box-sizing: border-box;
  flex-shrink: 0;
}
#app {
  width: 100vw;
  height: 100vh;
}
.wp_-flex-row {
  display: flex;
  flex-direction: row;
}
.wp_-flex-col {
  display: flex;
  flex-direction: column;
}
.wp_-justify-start {
  justify-content: flex-start;
}
.wp_-justify-end {
  justify-content: flex-end;
}
.wp_-justify-center {
  justify-content: center;
}
.wp_-justify-between {
  justify-content: space-between;
}
.wp_-justify-around {
  justify-content: space-around;
}
.wp_-justify-evenly {
  justify-content: space-evenly;
}
.wp_-items-start {
  align-items: flex-start;
}
.wp_-items-end {
  align-items: flex-end;
}
.wp_-items-center {
  align-items: center;
}
.wp_-items-baseline {
  align-items: baseline;
}
.wp_-items-stretch {
  align-items: stretch;
}
.wp_-self-start {
  align-self: flex-start;
}
.wp_-self-end {
  align-self: flex-end;
}
.wp_-self-center {
  align-self: center;
}
.wp_-self-baseline {
  align-self: baseline;
}
.wp_-self-stretch {
  align-self: stretch;
}
.wp_-flex-1 {
  flex: 1 1 0%;
}
.wp_-flex-auto {
  flex: 1 1 auto;
}
.wp_-grow {
  flex-grow: 1;
}
.wp_-grow-0 {
  flex-grow: 0;
}
.wp_-shrink {
  flex-shrink: 1;
}
.wp_-shrink-0 {
  flex-shrink: 0;
}
.wp_-relative {
  position: relative;
}
.wp_-ml-2 {
  margin-left: 4rpx;
}
.wp_-mt-2 {
  margin-top: 4rpx;
}
.wp_-ml-4 {
  margin-left: 8rpx;
}
.wp_-mt-4 {
  margin-top: 8rpx;
}
.wp_-ml-6 {
  margin-left: 12rpx;
}
.wp_-mt-6 {
  margin-top: 12rpx;
}
.wp_-ml-8 {
  margin-left: 16rpx;
}
.wp_-mt-8 {
  margin-top: 16rpx;
}
.wp_-ml-10 {
  margin-left: 20rpx;
}
.wp_-mt-10 {
  margin-top: 20rpx;
}
.wp_-ml-12 {
  margin-left: 24rpx;
}
.wp_-mt-12 {
  margin-top: 24rpx;
}
.wp_-ml-14 {
  margin-left: 28rpx;
}
.wp_-mt-14 {
  margin-top: 28rpx;
}
.wp_-ml-16 {
  margin-left: 32rpx;
}
.wp_-mt-16 {
  margin-top: 32rpx;
}
.wp_-ml-18 {
  margin-left: 36rpx;
}
.wp_-mt-18 {
  margin-top: 36rpx;
}
.wp_-ml-20 {
  margin-left: 40rpx;
}
.wp_-mt-20 {
  margin-top: 40rpx;
}
.wp_-ml-22 {
  margin-left: 44rpx;
}
.wp_-mt-22 {
  margin-top: 44rpx;
}
.wp_-ml-24 {
  margin-left: 48rpx;
}
.wp_-mt-24 {
  margin-top: 48rpx;
}
.wp_-ml-26 {
  margin-left: 52rpx;
}
.wp_-mt-26 {
  margin-top: 52rpx;
}
.wp_-ml-28 {
  margin-left: 56rpx;
}
.wp_-mt-28 {
  margin-top: 56rpx;
}
.wp_-ml-30 {
  margin-left: 60rpx;
}
.wp_-mt-30 {
  margin-top: 60rpx;
}
.wp_-ml-32 {
  margin-left: 64rpx;
}
.wp_-mt-32 {
  margin-top: 64rpx;
}
.wp_-ml-34 {
  margin-left: 68rpx;
}
.wp_-mt-34 {
  margin-top: 68rpx;
}
.wp_-ml-36 {
  margin-left: 72rpx;
}
.wp_-mt-36 {
  margin-top: 72rpx;
}
.wp_-ml-38 {
  margin-left: 76rpx;
}
.wp_-mt-38 {
  margin-top: 76rpx;
}
.wp_-ml-40 {
  margin-left: 80rpx;
}
.wp_-mt-40 {
  margin-top: 80rpx;
}
.wp_-ml-42 {
  margin-left: 84rpx;
}
.wp_-mt-42 {
  margin-top: 84rpx;
}
.wp_-ml-44 {
  margin-left: 88rpx;
}
.wp_-mt-44 {
  margin-top: 88rpx;
}
.wp_-ml-46 {
  margin-left: 92rpx;
}
.wp_-mt-46 {
  margin-top: 92rpx;
}
.wp_-ml-48 {
  margin-left: 96rpx;
}
.wp_-mt-48 {
  margin-top: 96rpx;
}
.wp_-ml-50 {
  margin-left: 100rpx;
}
.wp_-mt-50 {
  margin-top: 100rpx;
}
.wp_-ml-52 {
  margin-left: 104rpx;
}
.wp_-mt-52 {
  margin-top: 104rpx;
}
.wp_-ml-54 {
  margin-left: 108rpx;
}
.wp_-mt-54 {
  margin-top: 108rpx;
}
.wp_-ml-56 {
  margin-left: 112rpx;
}
.wp_-mt-56 {
  margin-top: 112rpx;
}
.wp_-ml-58 {
  margin-left: 116rpx;
}
.wp_-mt-58 {
  margin-top: 116rpx;
}
.wp_-ml-60 {
  margin-left: 120rpx;
}
.wp_-mt-60 {
  margin-top: 120rpx;
}
.wp_-ml-62 {
  margin-left: 124rpx;
}
.wp_-mt-62 {
  margin-top: 124rpx;
}
.wp_-ml-64 {
  margin-left: 128rpx;
}
.wp_-mt-64 {
  margin-top: 128rpx;
}
.wp_-ml-66 {
  margin-left: 132rpx;
}
.wp_-mt-66 {
  margin-top: 132rpx;
}
.wp_-ml-68 {
  margin-left: 136rpx;
}
.wp_-mt-68 {
  margin-top: 136rpx;
}
.wp_-ml-70 {
  margin-left: 140rpx;
}
.wp_-mt-70 {
  margin-top: 140rpx;
}
.wp_-ml-72 {
  margin-left: 144rpx;
}
.wp_-mt-72 {
  margin-top: 144rpx;
}
.wp_-ml-74 {
  margin-left: 148rpx;
}
.wp_-mt-74 {
  margin-top: 148rpx;
}
.wp_-ml-76 {
  margin-left: 152rpx;
}
.wp_-mt-76 {
  margin-top: 152rpx;
}
.wp_-ml-78 {
  margin-left: 156rpx;
}
.wp_-mt-78 {
  margin-top: 156rpx;
}
.wp_-ml-80 {
  margin-left: 160rpx;
}
.wp_-mt-80 {
  margin-top: 160rpx;
}
.wp_-ml-82 {
  margin-left: 164rpx;
}
.wp_-mt-82 {
  margin-top: 164rpx;
}
.wp_-ml-84 {
  margin-left: 168rpx;
}
.wp_-mt-84 {
  margin-top: 168rpx;
}
.wp_-ml-86 {
  margin-left: 172rpx;
}
.wp_-mt-86 {
  margin-top: 172rpx;
}
.wp_-ml-88 {
  margin-left: 176rpx;
}
.wp_-mt-88 {
  margin-top: 176rpx;
}
.wp_-ml-90 {
  margin-left: 180rpx;
}
.wp_-mt-90 {
  margin-top: 180rpx;
}
.wp_-ml-92 {
  margin-left: 184rpx;
}
.wp_-mt-92 {
  margin-top: 184rpx;
}
.wp_-ml-94 {
  margin-left: 188rpx;
}
.wp_-mt-94 {
  margin-top: 188rpx;
}
.wp_-ml-96 {
  margin-left: 192rpx;
}
.wp_-mt-96 {
  margin-top: 192rpx;
}
.wp_-ml-98 {
  margin-left: 196rpx;
}
.wp_-mt-98 {
  margin-top: 196rpx;
}
.wp_-ml-100 {
  margin-left: 200rpx;
}
.wp_-mt-100 {
  margin-top: 200rpx;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.icon-text {
  display: inline-flex;
  align-items: center;
}
.icon-text.right-icon {
  flex-direction: row-reverse;
}
.icon-text.right-icon .icon {
  margin-left: 10rpx;
  margin-right: 0;
}
.icon-text .icon {
  margin-right: 10rpx;
}
.icon-text .text {
  font-size: 28rpx;
}
.icon-grid {
  display: flex;
  flex-wrap: wrap;
}
.icon-grid .icon-grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx;
}
.icon-grid .icon-grid-item .icon-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
.page {
  min-height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}
.content-container {
  flex: 1;
  padding: 30rpx;
}
.card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.card-header {
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
  border-bottom: 1px solid #f5f5f5;
}
.card-header .card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}
.card-footer {
  padding-top: 20rpx;
  margin-top: 20rpx;
  border-top: 1px solid #f5f5f5;
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #fff;
  background-color: #8cd548;
}
.btn-primary {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
}
.btn-outline {
  background-color: transparent;
  border: 1px solid #8cd548;
  color: #8cd548;
}
.btn-disabled {
  background-color: #cccccc;
  opacity: 0.7;
}
.btn-block {
  width: 100%;
}
.btn-sm {
  height: 60rpx;
  font-size: 28rpx;
}
.btn-lg {
  height: 100rpx;
  font-size: 36rpx;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-item-label {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}
.form-item-input {
  height: 80rpx;
  background-color: #f7f7f7;
  border-radius: 8rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
}
.footer-bar {
  padding: 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
}
.empty-state-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-state-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 30rpx;
}
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f5f5f5;
}
.list-item:last-child {
  border-bottom: none;
}
.list-item-icon {
  margin-right: 30rpx;
}
.list-item-content {
  flex: 1;
}
.list-item-title {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 10rpx;
}
.list-item-subtitle {
  font-size: 28rpx;
  color: #999999;
}
.list-item-right {
  margin-left: 20rpx;
}

