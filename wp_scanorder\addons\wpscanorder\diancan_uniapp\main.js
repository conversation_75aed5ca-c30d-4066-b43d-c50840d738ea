import Vue from 'vue';
import App from './App';
import uView from "uview-ui";

// 引入全局样式
import './styles/common.scss';

// 注册全局组件
import NavBar from './components/common/NavBar.vue';
import Card from './components/common/Card.vue';
import CustomButton from './components/common/Button.vue';
import EmptyState from './components/common/EmptyState.vue';
import CustomIcon from './components/common/Icon.vue';

// 使用uView UI
Vue.use(uView);

// 注册自定义全局组件
Vue.component('nav-bar', NavBar);
Vue.component('ui-card', Card);
Vue.component('custom-button', CustomButton);
Vue.component('empty-state', EmptyState);
Vue.component('custom-icon', CustomIcon);

Vue.config.productionTip = false;

App.mpType = 'app';

const app = new Vue({
  ...App,
});
app.$mount();