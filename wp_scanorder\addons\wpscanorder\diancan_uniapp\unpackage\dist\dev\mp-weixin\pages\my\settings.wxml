<view class="page data-v-3176ae3d"><view class="header data-v-3176ae3d"><view class="nav-bar data-v-3176ae3d"><image class="back-icon data-v-3176ae3d" src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" bindtap="__e"></image><text class="title data-v-3176ae3d">个人资料</text></view></view><view class="content-container data-v-3176ae3d"><view class="avatar-section data-v-3176ae3d"><image class="avatar data-v-3176ae3d" src="{{userInfo.avatarUrl||'/static/my/default-avatar.png'}}" mode="aspectFill"></image><button class="avatar-button data-v-3176ae3d" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><view class="avatar-overlay data-v-3176ae3d"><view class="overlay-icon data-v-3176ae3d"><u-icon vue-id="0540cb2e-1" name="camera-fill" color="#ffffff" size="24" class="data-v-3176ae3d" bind:__l="__l"></u-icon></view></view></button></view><view class="form-list data-v-3176ae3d"><view class="form-item data-v-3176ae3d"><view class="form-label data-v-3176ae3d">昵称</view><view class="form-content data-v-3176ae3d"><input class="form-input data-v-3176ae3d" type="nickname" placeholder="请输入昵称" data-event-opts="{{[['nicknamereview',[['onNicknameReview',['$event']]]],['input',[['onNicknameInput',['$event']]]],['blur',[['onNicknameBlur',['$event']]]]]}}" value="{{userInfo.nickName}}" bindnicknamereview="__e" bindinput="__e" bindblur="__e"/><block wx:if="{{!userInfo.nickName}}"><view class="form-right-btn data-v-3176ae3d"><text class="btn-text data-v-3176ae3d">设置昵称</text></view></block></view></view><view class="form-item data-v-3176ae3d"><view class="form-label data-v-3176ae3d">手机</view><view class="form-content data-v-3176ae3d"><text class="form-text data-v-3176ae3d">{{$root.m0}}</text><view data-event-opts="{{[['tap',[['changePhone',['$event']]]]]}}" hidden="{{!(false)}}" class="form-right-btn data-v-3176ae3d" bindtap="__e"><text class="btn-text data-v-3176ae3d">修改</text></view></view></view><view data-event-opts="{{[['tap',[['showGenderPicker',['$event']]]]]}}" class="form-item data-v-3176ae3d" bindtap="__e"><view class="form-label data-v-3176ae3d">性别</view><view class="form-content data-v-3176ae3d"><text class="form-text data-v-3176ae3d">{{userInfo.gender=='1'?'男':userInfo.gender=='2'?'女':'未设置'}}</text><view class="form-right-btn data-v-3176ae3d"><text class="btn-text data-v-3176ae3d">修改</text></view></view></view><view data-event-opts="{{[['tap',[['changeBirthday',['$event']]]]]}}" class="form-item data-v-3176ae3d" bindtap="__e"><view class="form-label data-v-3176ae3d">生日</view><view class="form-content data-v-3176ae3d"><text class="form-text data-v-3176ae3d">{{userInfo.birthday||userInfo.birthdayOne||'未设置'}}</text><block wx:if="{{userInfo.birthday}}"><view class="form-right-tip data-v-3176ae3d"><text class="tip-text data-v-3176ae3d">(仅支持修改一次)</text></view></block><block wx:else><view class="form-right-btn data-v-3176ae3d"><text class="btn-text data-v-3176ae3d">修改</text></view></block></view></view></view><view class="privacy-tip data-v-3176ae3d"><text class="tip-text data-v-3176ae3d">根据未成年人保护相关法律规定，我们不主动处理14周岁以下未成年人的个人信息。如果您为14周岁以下的用户，请勿填写您的个人资料。</text></view><view data-event-opts="{{[['tap',[['saveUserProfile',['$event']]]]]}}" class="save-btn data-v-3176ae3d" bindtap="__e"><text class="data-v-3176ae3d">保存</text></view><view class="about-container data-v-3176ae3d"><view data-event-opts="{{[['tap',[['goToCopyright',['$event']]]]]}}" class="about-btn data-v-3176ae3d" bindtap="__e"><text class="data-v-3176ae3d">版权声明</text></view></view><view class="logout-container data-v-3176ae3d"><view data-event-opts="{{[['tap',[['handleLogout',['$event']]]]]}}" class="logout-btn data-v-3176ae3d" bindtap="__e"><text class="data-v-3176ae3d">退出登录</text></view></view></view><u-popup bind:input="__e" vue-id="0540cb2e-2" mode="bottom" border-radius="16" value="{{showAvatarPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showAvatarPopup','$event',[]]]]]]}}" class="data-v-3176ae3d" bind:__l="__l" vue-slots="{{['default']}}"><view class="avatar-popup data-v-3176ae3d"><view class="popup-title data-v-3176ae3d">设置头像</view><view class="popup-options data-v-3176ae3d"><view data-event-opts="{{[['tap',[['chooseAvatar',['$event']]]]]}}" class="popup-option data-v-3176ae3d" bindtap="__e"><u-icon vue-id="{{('0540cb2e-3')+','+('0540cb2e-2')}}" name="photo" size="32" color="#333" class="data-v-3176ae3d" bind:__l="__l"></u-icon><text class="data-v-3176ae3d">从相册选择</text></view><view class="popup-option data-v-3176ae3d"><button class="wx-button data-v-3176ae3d" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><u-icon vue-id="{{('0540cb2e-4')+','+('0540cb2e-2')}}" name="weixin-fill" size="32" color="#07c160" class="data-v-3176ae3d" bind:__l="__l"></u-icon><text class="data-v-3176ae3d">使用微信头像</text></button></view></view><view data-event-opts="{{[['tap',[['closeAvatarPopup',['$event']]]]]}}" class="popup-cancel data-v-3176ae3d" bindtap="__e">取消</view></view></u-popup><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" hidden="{{!(showBirthdayPicker)}}" class="birthday-picker-modal data-v-3176ae3d" catchtap="__e"><view data-event-opts="{{[['tap',[['closeBirthdayPicker',['$event']]]]]}}" class="birthday-picker-mask data-v-3176ae3d" catchtap="__e"></view><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="date-picker-content data-v-3176ae3d" catchtap="__e"><view class="date-picker-header data-v-3176ae3d"><text data-event-opts="{{[['tap',[['closeBirthdayPicker',['$event']]]]]}}" class="date-picker-cancel data-v-3176ae3d" bindtap="__e">取消</text><text class="date-picker-title data-v-3176ae3d">选择生日</text><text data-event-opts="{{[['tap',[['confirmBirthday',['$event']]]]]}}" class="date-picker-confirm data-v-3176ae3d" bindtap="__e">确认</text></view><view class="picker-view-container data-v-3176ae3d"><picker-view class="date-picker-view data-v-3176ae3d" value="{{defaultBirthdayIndex}}" indicator-style="height: 50px;" immediate-change="{{true}}" data-event-opts="{{[['change',[['onPickerChange',['$event']]]]]}}" bindchange="__e"><picker-view-column class="data-v-3176ae3d"><block wx:for="{{birthdayColumns[0]}}" wx:for-item="year" wx:for-index="index" wx:key="index"><view class="picker-item data-v-3176ae3d">{{''+year+''}}</view></block></picker-view-column><picker-view-column class="data-v-3176ae3d"><block wx:for="{{birthdayColumns[1]}}" wx:for-item="month" wx:for-index="index" wx:key="index"><view class="picker-item data-v-3176ae3d">{{''+month+''}}</view></block></picker-view-column><picker-view-column class="data-v-3176ae3d"><block wx:for="{{birthdayColumns[2]}}" wx:for-item="day" wx:for-index="index" wx:key="index"><view class="picker-item data-v-3176ae3d">{{''+day+''}}</view></block></picker-view-column></picker-view></view></view></view></view>