<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">会员充值</text>
      </view>
      
      <!-- 账户余额 -->
      <view class="balance-info">
        <text class="label">当前余额</text>
        <view class="amount">
          <text class="symbol">¥</text>
          <text class="value">{{userBalance}}</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 充值金额卡片 -->
      <view class="recharge-card">
        <view class="card-header">
          <text class="card-title">选择充值金额</text>
          <view class="record-link" @tap="goToRecord">
            <text>充值记录</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <!-- 金额选择网格 -->
        <view class="amount-grid">
          <view 
            v-for="(item, index) in amounts" 
            :key="index"
            :class="['amount-item', {'active': item.active || selectedAmount === item.value}]"
            @tap="selectAmount(item)"
          >
            <view class="amount">
              <text class="symbol">¥</text>
              <text class="value">{{item.value}}</text>
            </view>
            <text class="gift">赠送{{item.gift}}元</text>
          </view>
        </view>
      </view>

      <!-- 充值说明 -->
      <view class="notice">
        <text class="notice-title">充值说明</text>
        <text class="notice-item">1、本次充值可用于平台上消费，各地区跨站点皆可使用。</text>
        <text class="notice-item">2、若遇到充值未到账，请联系客服。</text>
      </view>
    </view>

    <!-- 底部充值按钮 -->
    <view class="bottom-bar">
      <view class="recharge-btn" @tap="handleRecharge" :class="{'disabled': loading}">
        <text>{{loading ? '处理中...' : '立即充值'}}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserBalance } from '@/api/user.js';
import { createRecharge, getRechargeConfigs } from '@/api/recharge.js';

  export default {
    components: {},
    props: {},
    data() {
      return {
      userBalance: '0.00', // 用户余额
        selectedAmount: 0,
        selectedId: '', // 选中的充值套餐ID
      loading: false,
      // 充值金额配置（将从接口获取）
        amounts: []
      };
    },
  
  onShow() {
    // 获取余额
    this.loadBalance();
    this.loadRechargeConfigs();
  },

    methods: {
    // 加载余额
    async loadBalance() {
      try {
        console.log('开始获取用户余额...');
        const res = await getUserBalance();
        console.log('余额API响应完整数据:', JSON.stringify(res));
        
        if (res.code === 1) {
          console.log('余额API响应数据类型:', typeof res.data);
          console.log('余额API响应数据值:', res.data);
          
          // 根据数据类型处理
          if (typeof res.data === 'object' && res.data !== null) {
            // 如果是对象，尝试获取balance字段
            console.log('余额对象字段:', Object.keys(res.data));
            const balance = res.data.balance || res.data.money || res.data.user_money || 0;
            this.userBalance = parseFloat(balance).toFixed(2);
          } else if (typeof res.data === 'number') {
            // 如果直接是数值
            this.userBalance = parseFloat(res.data).toFixed(2);
          } else if (typeof res.data === 'string') {
            // 如果是字符串
            this.userBalance = parseFloat(res.data).toFixed(2);
          }
          
          console.log('解析后的余额:', this.userBalance);
        } else {
          console.error('获取余额失败:', res.msg);
        }
      } catch (error) {
        console.error('获取余额请求异常:', error);
      }
    },
    
    // 加载充值套餐配置
    async loadRechargeConfigs() {
      try {
        uni.showLoading({
          title: '加载中...'
        });
        
        const res = await getRechargeConfigs();
        console.log('充值套餐API响应:', JSON.stringify(res));
        
        if (res.code === 1 && res.data) {
          // 检查返回数据格式
          let configList = [];
          
          if (Array.isArray(res.data)) {
            // 如果直接返回数组
            configList = res.data;
          } else if (res.data.list && Array.isArray(res.data.list)) {
            // 如果返回的是包含list的对象
            configList = res.data.list;
          } else if (typeof res.data === 'object') {
            // 如果是对象格式，尝试转换为数组
            configList = Object.values(res.data).filter(item => typeof item === 'object');
          }
          
          if (configList.length > 0) {
            // 从接口获取充值配置
            this.amounts = configList.map((item, index) => {
              return {
                id: item.id || item.recharge_id || '',
                value: parseFloat(item.amount || item.value || 0),
                gift: parseFloat(item.send_amount || item.gift || 0),
                active: index === 0 // 默认选中第一个
              };
            });
            
            // 默认选中第一个充值选项
            if (this.amounts.length > 0) {
              this.selectedAmount = this.amounts[0].value;
              this.selectedId = this.amounts[0].id || '';
            }
            
            console.log('充值套餐配置获取成功:', this.amounts);
            return;
          }
        }
        
        // 如果接口没有返回有效数据，显示提示
        console.log('未获取到充值套餐配置');
        this.amounts = [];
        this.selectedAmount = 0;
        
        uni.showToast({
          title: '获取充值套餐失败',
          icon: 'none'
        });
      } catch (error) {
        console.error('获取充值套餐配置失败:', error);
        // 接口异常时显示提示
        this.amounts = [];
        this.selectedAmount = 0;
        
        uni.showToast({
          title: '获取充值套餐失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
      selectAmount(item) {
        this.selectedAmount = item.value
        this.selectedId = item.id || ''
      
      // 更新活动状态
      this.amounts.forEach(amountItem => {
        amountItem.active = amountItem.value === item.value
      })
      },
    

    
    // 处理充值操作
    async handleRecharge() {
      // 验证金额
      if (this.selectedAmount <= 0) {
        return uni.showToast({
          title: '请选择充值金额',
          icon: 'none'
        });
      }
      
      // 避免重复提交
      if (this.loading) return;
      
        uni.showModal({
          title: '确认充值',
          content: `确认充值${this.selectedAmount}元？`,
        success: async (res) => {
            if(res.confirm) {
            this.loading = true;
            
            try {
              uni.showLoading({
                title: '充值中...'
              });
              
              // 准备充值参数
              let rechargeParams = {};
              
              // 如果有选中的充值套餐ID，则使用ID
              if (this.selectedId) {
                rechargeParams.recharge_id = this.selectedId;
              } else {
                // 否则使用自定义金额
                rechargeParams.amount = this.selectedAmount;
              }
              
              console.log('充值参数:', rechargeParams);
              
              // 调用充值接口
              const result = await createRecharge(rechargeParams);
              
              console.log('充值结果:', JSON.stringify(result));
              
              // 判断充值结果
              if (result.code === 1) {
                // 获取充值记录详情，包括赠送金额
                const rechargeData = result.data;
                console.log('充值数据:', JSON.stringify(rechargeData));
                
                // 检查是否需要调起微信支付
                if (rechargeData.payment && rechargeData.payment.type === 'wechat' && rechargeData.payment.params) {
                  // 调起微信支付
                  this.handleWechatPay(rechargeData);
                  return;
                }
                
                // 非微信支付情况，直接显示充值成功
                // 立即刷新余额
                await this.loadBalance();
                
                // 显示成功提示，加上赠送金额提示
                let toastMsg = '充值成功';
                if (rechargeData.send_amount && parseFloat(rechargeData.send_amount) > 0) {
                  toastMsg += `，赠送${rechargeData.send_amount}元`;
                }
                
                uni.showToast({
                  title: toastMsg,
                  icon: 'none',
                  duration: 2000
                });
                
                // 延时返回
                    setTimeout(() => {
                  uni.navigateBack();
                }, 2000);
              } else {
                // 充值失败
                uni.showToast({
                  title: result.msg || '充值失败，请重试',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('充值请求异常:', error);
              uni.showToast({
                title: '充值失败，请重试',
                icon: 'none'
              });
            } finally {
              uni.hideLoading();
              this.loading = false;
            }
            }
          }
      });
    },
    
      handleBack() {
        uni.navigateBack()
      },
    
      goToRecord() {
        uni.navigateTo({
          url: '/pages/my/recharge-record'
        })
      },
      
    // 处理微信支付
    handleWechatPay(rechargeData) {
      console.log('调起微信支付:', rechargeData.payment.params);
      
      // 解构参数
      const payParams = rechargeData.payment.params;
      
      // 调用微信支付
      uni.requestPayment({
        provider: 'wxpay',
        ...payParams,
        success: async (res) => {
          console.log('微信支付成功:', res);
          
          // 支付成功后刷新余额
          await this.loadBalance();
          
          // 计算实际到账金额
          const totalAmount = parseFloat(rechargeData.amount) + 
                              parseFloat(rechargeData.send_amount || 0);
                              
          // 显示成功消息
          uni.showToast({
            title: `充值成功，到账${totalAmount}元`,
            icon: 'none',
            duration: 2000
          });
          
          // 延时返回
          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        },
        fail: (err) => {
          console.error('微信支付失败:', err);
          
          // 判断是否是用户取消
          if (err.errMsg === 'requestPayment:fail cancel') {
            uni.showToast({
              title: '支付已取消',
              icon: 'none'
            });
          } else {
            uni.showToast({
              title: '支付失败，请重试',
              icon: 'none'
            });
          }
          
          this.loading = false;
        },
        complete: () => {
          uni.hideLoading();
        }
      });
    }
    },
  };
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
}

.header {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding-top: 88rpx;
  padding-bottom: 60rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #fff;
      font-weight: bold;
    }
  }
  
  .balance-info {
    text-align: center;
    margin-top: 40rpx;
    
    .label {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 16rpx;
      display: block;
    }
    
    .amount {
      display: flex;
      align-items: baseline;
      justify-content: center;
      
      .symbol {
        font-size: 32rpx;
        color: #fff;
        margin-right: 8rpx;
      }
      
      .value {
        font-size: 60rpx;
        color: #fff;
        font-weight: bold;
        font-family: 'DIN';
      }
    }
  }
}

.content {
  margin-top: -40rpx;
  padding: 0 30rpx;
  padding-bottom: 120rpx;
  
  .recharge-card {
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .card-title {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
      }
      
      .record-link {
        display: flex;
        align-items: center;
        padding: 6rpx 12rpx;
        
        text {
          font-size: 26rpx;
          color: #333;
        }
        
        .arrow {
          margin-left: 6rpx;
          font-size: 22rpx;
        }
        
        &:active {
          opacity: 0.7;
        }
      }
    }
    
    .amount-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;
      margin-top: 10rpx;
      
      .amount-item {
        background: #f8f8f8;
        border-radius: 16rpx;
        padding: 30rpx 20rpx;
        text-align: center;
        border: 2rpx solid transparent;
        
        .amount {
          margin-bottom: 12rpx;
          
          .symbol {
            font-size: 24rpx;
            color: #333;
          }
          
          .value {
            font-size: 36rpx;
            color: #333;
            font-weight: bold;
          }
        }
        
        .gift {
          font-size: 24rpx;
          color: #999;
        }
        
        &.active {
          background: rgba(140, 213, 72, 0.1);
          border-color: #8cd548;
          
          .symbol, .value {
            color: #8cd548;
          }
          
          .gift {
            color: #8cd548;
          }
        }
      }
    }
  }
}

.notice {
  padding: 0 20rpx;
  
  .notice-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
  }
  
  .notice-item {
    font-size: 24rpx;
    color: #999;
    line-height: 1.6;
    display: block;
  }
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: #fff;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
  
  .recharge-btn {
    height: 88rpx;
    background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
      font-size: 32rpx;
      color: #fff;
      font-weight: 500;
    }
    
    &:active {
      transform: scale(0.98);
    }
    
    &.disabled {
      opacity: 0.7;
      pointer-events: none;
    }
  }
}
</style>