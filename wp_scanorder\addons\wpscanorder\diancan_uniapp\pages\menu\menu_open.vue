<template>
	<view class="spec-popup" @click.stop>
		<view class="mask" @click="handleClose"></view>
		<view class="content" @click.stop>
			<!-- 商品基本信息 -->
			<view class="product-info">
				<image class="product-image" :src="product.image" mode="aspectFill" />
				<view class="info">
					<text class="name">{{product.name}}</text>
					<view class="price">
						<text class="symbol">¥</text>
						<text class="value">{{currentPrice}}</text>
					</view>
				</view>
				<view class="close-btn" @click="handleClose">
					<text class="close-icon">×</text>
				</view>
			</view>
			
			<!-- 规格选择区域 -->
			<scroll-view scroll-y class="specs-container">
				<view 
					class="spec-group" 
					v-for="(group, groupIndex) in specGroups" 
					:key="groupIndex"
				>
					<view class="group-title">
						<text class="group-name">{{group.name}}</text>
					</view>
					<view class="spec-options">
						<view 
							class="spec-item"
							:class="{
								'active': item.is_default,
								'animate-select': selectedAnimation.groupIndex === groupIndex && selectedAnimation.itemIndex === itemIndex
							}"
							v-for="(item, itemIndex) in group.values"
							:key="itemIndex"
							@click="handleSpecSelect(groupIndex, itemIndex)"
						>
							<text>{{item.value}}</text>
							<text class="price-tag" v-if="group.name === '规格'">
								¥{{item.price}}
							</text>
						</view>
					</view>
				</view>
				
				<!-- 添加已选规格摘要 -->
				<view class="selected-summary">
					<text class="summary-title">已选:</text>
					<view class="summary-content">
						<text v-for="(spec, index) in selectedSpecsSummary" :key="index" class="summary-item">
							{{spec.name}}:{{spec.value}}
							<text v-if="index < selectedSpecsSummary.length - 1">、</text>
						</text>
					</view>
				</view>
			</scroll-view>
			
			<!-- 底部操作区 -->
			<view class="bottom-action">
				<view class="quantity-control">
					<view 
						class="quantity-btn minus-btn" 
						:class="{'disabled': quantity <= 1}"
						@click="decreaseQuantity"
					>
						<text>-</text>
					</view>
					<text class="number">{{quantity}}</text>
					<view 
						class="quantity-btn plus-btn"
						@click="increaseQuantity"
					>
						<text>+</text>
					</view>
				</view>
				<view class="add-btn" @click="handleAddToCart">
					<text>加入购物车</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		product: {
			type: Object,
			required: true
		}
	},
	data() {
		return {
			specGroups: [],
			quantity: 1, // 使用本地数据存储数量
			selectedAnimation: {
				groupIndex: -1,
				itemIndex: -1
			} // 选中动画控制
		}
	},
	created() {
		// 深拷贝规格数据,避免直接修改props
		try {
			if (this.product && this.product.property) {
				this.specGroups = JSON.parse(JSON.stringify(this.product.property));
				
				// 初始化每个规格组的第一个选项为默认选中
				this.specGroups.forEach(group => {
					if (group.values && group.values.length > 0) {
						// 先重置所有选项
						group.values.forEach(item => item.is_default = false);
						// 设置第一个为默认选中
						group.values[0].is_default = true;
					}
				});
			} else {
				// 如果property不存在，初始化为空数组
				this.specGroups = [];
			}
			
			// 初始化数量
			this.quantity = this.product.number || 1;
		} catch (error) {
			console.error('解析规格数据错误:', error);
			this.specGroups = [];
			this.quantity = 1;
		}
	},
	computed: {
		// 计算当前选中规格对应的价格
		currentPrice() {
			const sizeGroup = this.specGroups.find(group => group.name === '规格')
			if (sizeGroup) {
				const selectedSize = sizeGroup.values.find(item => item.is_default)
				return selectedSize ? selectedSize.price : this.product.price
			}
			return this.product.price
		},
		
		// 计算已选规格摘要
		selectedSpecsSummary() {
			return this.specGroups.map(group => {
				const selected = group.values.find(item => item.is_default);
				return {
					name: group.name,
					value: selected ? selected.value : ''
				};
			}).filter(item => item.value);
		}
	},
	methods: {
		// 关闭弹窗
		handleClose() {
			this.$emit('close')
		},
		
		// 选择规格
		handleSpecSelect(groupIndex, itemIndex) {
			const group = this.specGroups[groupIndex]
			// 取消该组其他选项的选中状态
			group.values.forEach(item => item.is_default = false)
			// 设置当前选项为选中状态
			group.values[itemIndex].is_default = true
			
			// 触发选中动画
			this.selectedAnimation = {
				groupIndex,
				itemIndex
			}
			
			// 300ms后重置动画状态
			setTimeout(() => {
				this.selectedAnimation = {
					groupIndex: -1,
					itemIndex: -1
				}
			}, 300)
		},
		
		// 减少数量
		decreaseQuantity() {
			console.log('减少数量')
			if (this.quantity <= 1) return
			this.quantity -= 1
		},
		
		// 增加数量
		increaseQuantity() {
			console.log('增加数量')
			this.quantity += 1
		},
		
		// 添加到购物车
		handleAddToCart() {
			// 获取所有选中的规格
			const selectedSpecs = this.specGroups.map(group => {
				const selected = group.values.find(item => item.is_default)
				return {
					name: group.name,
					value: selected ? selected.value : ''
				}
			})
			
			// 将选中的规格格式化为字符串
			const specText = selectedSpecs.map(spec => `${spec.name}:${spec.value}`).join('，');
			console.log('多规格商品选择结果:', specText);
			
			// 构建购物车商品数据
			const cartItem = {
				id: this.product.id,
				name: this.product.name,
				price: this.currentPrice,
				image: this.product.image,
				count: this.quantity, // 使用本地数量
				totalPrice: this.currentPrice * this.quantity,
				spec_type: 'multi',
				specs: specText,
				props_text: specText
			}
			
			// 输出调试信息
			console.log('添加到购物车的商品数据:', JSON.stringify(cartItem));
			
			// 发出添加到购物车事件
			this.$emit('add-to-cart', cartItem);
			
			// 自动关闭弹窗
			this.handleClose();
		}
	}
}
</script>

<style lang="scss" scoped>
.spec-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	
	.mask {
		position: absolute;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.6);
	}
	
	.content {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
		background: #fff;
		border-radius: 32rpx 32rpx 0 0;
		padding-bottom: env(safe-area-inset-bottom);
		box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
		animation: slideUp 0.3s ease-out;
		
		.product-info {
			padding: 40rpx 30rpx 30rpx;
			display: flex;
			align-items: flex-start;
			position: relative;
			border-bottom: 1rpx solid #f2f2f2;
			background: linear-gradient(to bottom, #f9f9f9, #ffffff);
			border-radius: 32rpx 32rpx 0 0;
			
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 30rpx;
				right: 30rpx;
				height: 1rpx;
				background: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));
			}
			
			.product-image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 16rpx;
				box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
				object-fit: cover;
				border: 2rpx solid #ffffff;
			}
			
			.info {
				flex: 1;
				margin-left: 30rpx;
				padding-top: 10rpx;
				
				.name {
					font-size: 34rpx;
					color: #333;
					font-weight: bold;
					line-height: 1.4;
					margin-bottom: 16rpx;
				}
				
				.price {
					margin-top: 30rpx;
					color: #ff5722;
					display: flex;
					align-items: baseline;
					
					.symbol {
						font-size: 28rpx;
						font-weight: bold;
					}
					
					.value {
						font-size: 40rpx;
						font-weight: bold;
					}
				}
			}
			
			.close-btn {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				width: 64rpx;
				height: 64rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: rgba(0, 0, 0, 0.05);
				
				.close-icon {
					font-size: 40rpx;
					color: #666;
					line-height: 1;
				}
				
				&:active {
					background: rgba(0, 0, 0, 0.1);
				}
			}
		}
		
		.specs-container {
			max-height: 60vh;
			padding: 20rpx 20rpx;
			overflow-y: auto;
			
			.spec-group {
				margin-bottom: 30rpx;
				padding: 20rpx;
				position: relative;
				background: #FFFFFF;
				border-radius: 16rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
				border: 1rpx solid rgba(0, 0, 0, 0.03);
				overflow: hidden;
				
				// 添加左侧装饰条
				&::before {
					content: '';
					position: absolute;
					left: 0;
					top: 0;
					bottom: 0;
					width: 6rpx;
					background: linear-gradient(to bottom, #8cd548, #6ab52e);
					border-radius: 0 3rpx 3rpx 0;
				}
				
				&:last-child {
					margin-bottom: 20rpx;
				}
				
				.group-title {
					position: relative;
					margin-bottom: 20rpx;
					padding-left: 16rpx;
					display: flex;
					align-items: center;
					
					.group-name {
						font-size: 30rpx;
						color: #333;
						font-weight: 600;
					}
					
					// 添加标题右侧装饰线
					&::after {
						content: '';
						flex: 1;
						height: 1rpx;
						background: linear-gradient(to right, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0.02) 100%);
						margin-left: 16rpx;
					}
				}
				
				.spec-options {
					display: flex;
					flex-wrap: wrap;
					gap: 16rpx;
					padding: 0 10rpx;
					
					.spec-item {
						position: relative;
						min-width: 130rpx;
						padding: 16rpx 24rpx;
						margin-bottom: 10rpx;
						background: #f8f8f8;
						border-radius: 100rpx;
						font-size: 28rpx;
						color: #666;
						transition: all 0.25s;
						text-align: center;
						border: 1rpx solid transparent;
						box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
						overflow: hidden;
						
						// 添加轻微的渐变背景
						&::before {
							content: '';
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							height: 50%;
							background: linear-gradient(to bottom, rgba(255,255,255,0.5), rgba(255,255,255,0));
							border-radius: 100rpx 100rpx 0 0;
							z-index: 1;
							pointer-events: none;
						}
						
						// 文字内容相对定位
						text {
							position: relative;
							z-index: 2;
						}
						
						&.active {
							background: rgba(140, 213, 72, 0.1);
							color: #8cd548;
							border-color: #8cd548;
							font-weight: 500;
							box-shadow: 0 2rpx 10rpx rgba(140, 213, 72, 0.2);
							
							// 活跃状态下的渐变背景
							&::before {
								background: linear-gradient(to bottom, rgba(140, 213, 72, 0.2), rgba(140, 213, 72, 0.05));
							}
							
							.price-tag {
								color: #8cd548;
							}
						}
						
						&:active {
							transform: scale(0.95);
							opacity: 0.8;
						}
						
						&.btn-hover {
							transform: scale(0.95);
							opacity: 0.8;
							background: #e0e0e0;
						}
						
						// 选中动画
						&.animate-select {
							animation: pulse 0.3s ease-out;
						}
						
						.price-tag {
							margin-left: 8rpx;
							color: #ff5722;
							font-size: 24rpx;
							font-weight: 500;
							position: relative;
							z-index: 2;
						}
					}
				}
			}
			
			// 已选规格摘要
			.selected-summary {
				margin: 20rpx 0 40rpx;
				padding: 20rpx;
				background: #f9f9f9;
				border-radius: 16rpx;
				display: flex;
				align-items: flex-start;
				
				.summary-title {
					font-size: 28rpx;
					color: #999;
					margin-right: 16rpx;
					white-space: nowrap;
				}
				
				.summary-content {
					flex: 1;
					font-size: 28rpx;
					color: #333;
					line-height: 1.5;
					
					.summary-item {
						display: inline;
					}
				}
			}
		}
		
		.bottom-action {
			padding: 20rpx 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-top: 1rpx solid #f2f2f2;
			background: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
			position: relative;
			
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 30rpx;
				right: 30rpx;
				height: 1rpx;
				background: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));
			}
			
			.quantity-control {
				display: flex;
				align-items: center;
				background: #f8f8f8;
				border-radius: 100rpx;
				padding: 6rpx;
				box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
				
				.quantity-btn {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
					background: #ffffff;
					display: flex;
					align-items: center;
					justify-content: center;
					transition: all 0.2s;
					position: relative;
					box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
					
					text {
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						line-height: 1;
					}
					
					&:active {
						transform: scale(0.95);
						opacity: 0.8;
						background: #f5f5f5;
					}
					
					&.btn-hover {
						transform: scale(0.95);
						opacity: 0.8;
						background: #f5f5f5;
					}
					
					&.disabled {
						opacity: 0.5;
						pointer-events: none;
					}
					
					/* 增加点击区域 */
					&::after {
						content: '';
						position: absolute;
						top: -20rpx;
						left: -20rpx;
						right: -20rpx;
						bottom: -20rpx;
					}
					
					&.minus-btn {
						text {
							color: #999;
						}
					}
					
					&.plus-btn {
						background: #8cd548;
						
						text {
							color: #fff;
						}
						
						&:active {
							background: #7bc53a;
						}
					}
				}
				
				.number {
					margin: 0 24rpx;
					font-size: 32rpx;
					font-weight: bold;
					min-width: 50rpx;
					text-align: center;
				}
			}
			
			.add-btn {
				flex: 1;
				margin-left: 30rpx;
				height: 80rpx;
				background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				font-size: 32rpx;
				font-weight: bold;
				transition: all 0.2s;
				box-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);
				position: relative;
				overflow: hidden;
				
				// 添加闪光效果
				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 50%;
					height: 100%;
					background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
					transform: skewX(-25deg);
					animation: shine 3s infinite;
				}
				
				&:active {
					transform: scale(0.98);
					opacity: 0.9;
				}
				
				text {
					position: relative;
					z-index: 2;
				}
			}
		}
	}
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

@keyframes shine {
	0% {
		left: -100%;
	}
	20% {
		left: 100%;
	}
	100% {
		left: 100%;
	}
}

@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(0.95);
	}
	100% {
		transform: scale(1);
	}
}
</style>