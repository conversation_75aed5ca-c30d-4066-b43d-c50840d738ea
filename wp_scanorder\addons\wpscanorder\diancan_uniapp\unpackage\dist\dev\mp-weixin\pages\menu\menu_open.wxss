@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.spec-popup.data-v-483f7033 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}
.spec-popup .mask.data-v-483f7033 {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}
.spec-popup .content.data-v-483f7033 {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #fff;
  border-radius: 32rpx 32rpx 0 0;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
  -webkit-animation: slideUp-data-v-483f7033 0.3s ease-out;
          animation: slideUp-data-v-483f7033 0.3s ease-out;
}
.spec-popup .content .product-info.data-v-483f7033 {
  padding: 40rpx 30rpx 30rpx;
  display: flex;
  align-items: flex-start;
  position: relative;
  border-bottom: 1rpx solid #f2f2f2;
  background: linear-gradient(to bottom, #f9f9f9, #ffffff);
  border-radius: 32rpx 32rpx 0 0;
}
.spec-popup .content .product-info.data-v-483f7033::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30rpx;
  right: 30rpx;
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
}
.spec-popup .content .product-info .product-image.data-v-483f7033 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  object-fit: cover;
  border: 2rpx solid #ffffff;
}
.spec-popup .content .product-info .info.data-v-483f7033 {
  flex: 1;
  margin-left: 30rpx;
  padding-top: 10rpx;
}
.spec-popup .content .product-info .info .name.data-v-483f7033 {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
  line-height: 1.4;
  margin-bottom: 16rpx;
}
.spec-popup .content .product-info .info .price.data-v-483f7033 {
  margin-top: 30rpx;
  color: #ff5722;
  display: flex;
  align-items: baseline;
}
.spec-popup .content .product-info .info .price .symbol.data-v-483f7033 {
  font-size: 28rpx;
  font-weight: bold;
}
.spec-popup .content .product-info .info .price .value.data-v-483f7033 {
  font-size: 40rpx;
  font-weight: bold;
}
.spec-popup .content .product-info .close-btn.data-v-483f7033 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
}
.spec-popup .content .product-info .close-btn .close-icon.data-v-483f7033 {
  font-size: 40rpx;
  color: #666;
  line-height: 1;
}
.spec-popup .content .product-info .close-btn.data-v-483f7033:active {
  background: rgba(0, 0, 0, 0.1);
}
.spec-popup .content .specs-container.data-v-483f7033 {
  max-height: 60vh;
  padding: 20rpx 20rpx;
  overflow-y: auto;
}
.spec-popup .content .specs-container .spec-group.data-v-483f7033 {
  margin-bottom: 30rpx;
  padding: 20rpx;
  position: relative;
  background: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
  border: 1rpx solid rgba(0, 0, 0, 0.03);
  overflow: hidden;
}
.spec-popup .content .specs-container .spec-group.data-v-483f7033::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: linear-gradient(to bottom, #8cd548, #6ab52e);
  border-radius: 0 3rpx 3rpx 0;
}
.spec-popup .content .specs-container .spec-group.data-v-483f7033:last-child {
  margin-bottom: 20rpx;
}
.spec-popup .content .specs-container .spec-group .group-title.data-v-483f7033 {
  position: relative;
  margin-bottom: 20rpx;
  padding-left: 16rpx;
  display: flex;
  align-items: center;
}
.spec-popup .content .specs-container .spec-group .group-title .group-name.data-v-483f7033 {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}
.spec-popup .content .specs-container .spec-group .group-title.data-v-483f7033::after {
  content: '';
  flex: 1;
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
  margin-left: 16rpx;
}
.spec-popup .content .specs-container .spec-group .spec-options.data-v-483f7033 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 0 10rpx;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.data-v-483f7033 {
  position: relative;
  min-width: 130rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 10rpx;
  background: #f8f8f8;
  border-radius: 100rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.25s;
  text-align: center;
  border: 1rpx solid transparent;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.data-v-483f7033::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
  border-radius: 100rpx 100rpx 0 0;
  z-index: 1;
  pointer-events: none;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item text.data-v-483f7033 {
  position: relative;
  z-index: 2;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.active.data-v-483f7033 {
  background: rgba(140, 213, 72, 0.1);
  color: #8cd548;
  border-color: #8cd548;
  font-weight: 500;
  box-shadow: 0 2rpx 10rpx rgba(140, 213, 72, 0.2);
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.active.data-v-483f7033::before {
  background: linear-gradient(to bottom, rgba(140, 213, 72, 0.2), rgba(140, 213, 72, 0.05));
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.active .price-tag.data-v-483f7033 {
  color: #8cd548;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.data-v-483f7033:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.8;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.btn-hover.data-v-483f7033 {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.8;
  background: #e0e0e0;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item.animate-select.data-v-483f7033 {
  -webkit-animation: pulse-data-v-483f7033 0.3s ease-out;
          animation: pulse-data-v-483f7033 0.3s ease-out;
}
.spec-popup .content .specs-container .spec-group .spec-options .spec-item .price-tag.data-v-483f7033 {
  margin-left: 8rpx;
  color: #ff5722;
  font-size: 24rpx;
  font-weight: 500;
  position: relative;
  z-index: 2;
}
.spec-popup .content .specs-container .selected-summary.data-v-483f7033 {
  margin: 20rpx 0 40rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 16rpx;
  display: flex;
  align-items: flex-start;
}
.spec-popup .content .specs-container .selected-summary .summary-title.data-v-483f7033 {
  font-size: 28rpx;
  color: #999;
  margin-right: 16rpx;
  white-space: nowrap;
}
.spec-popup .content .specs-container .selected-summary .summary-content.data-v-483f7033 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}
.spec-popup .content .specs-container .selected-summary .summary-content .summary-item.data-v-483f7033 {
  display: inline;
}
.spec-popup .content .bottom-action.data-v-483f7033 {
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1rpx solid #f2f2f2;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
  position: relative;
}
.spec-popup .content .bottom-action.data-v-483f7033::before {
  content: '';
  position: absolute;
  top: 0;
  left: 30rpx;
  right: 30rpx;
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.02), rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
}
.spec-popup .content .bottom-action .quantity-control.data-v-483f7033 {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 100rpx;
  padding: 6rpx;
  box-shadow: inset 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.data-v-483f7033 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  /* 增加点击区域 */
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn text.data-v-483f7033 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.data-v-483f7033:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.8;
  background: #f5f5f5;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.btn-hover.data-v-483f7033 {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.8;
  background: #f5f5f5;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.disabled.data-v-483f7033 {
  opacity: 0.5;
  pointer-events: none;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.data-v-483f7033::after {
  content: '';
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.minus-btn text.data-v-483f7033 {
  color: #999;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.plus-btn.data-v-483f7033 {
  background: #8cd548;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.plus-btn text.data-v-483f7033 {
  color: #fff;
}
.spec-popup .content .bottom-action .quantity-control .quantity-btn.plus-btn.data-v-483f7033:active {
  background: #7bc53a;
}
.spec-popup .content .bottom-action .quantity-control .number.data-v-483f7033 {
  margin: 0 24rpx;
  font-size: 32rpx;
  font-weight: bold;
  min-width: 50rpx;
  text-align: center;
}
.spec-popup .content .bottom-action .add-btn.data-v-483f7033 {
  flex: 1;
  margin-left: 30rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s;
  box-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);
  position: relative;
  overflow: hidden;
}
.spec-popup .content .bottom-action .add-btn.data-v-483f7033::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0) 100%);
  -webkit-transform: skewX(-25deg);
          transform: skewX(-25deg);
  -webkit-animation: shine-data-v-483f7033 3s infinite;
          animation: shine-data-v-483f7033 3s infinite;
}
.spec-popup .content .bottom-action .add-btn.data-v-483f7033:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  opacity: 0.9;
}
.spec-popup .content .bottom-action .add-btn text.data-v-483f7033 {
  position: relative;
  z-index: 2;
}
@-webkit-keyframes slideUp-data-v-483f7033 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp-data-v-483f7033 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@-webkit-keyframes shine-data-v-483f7033 {
0% {
    left: -100%;
}
20% {
    left: 100%;
}
100% {
    left: 100%;
}
}
@keyframes shine-data-v-483f7033 {
0% {
    left: -100%;
}
20% {
    left: 100%;
}
100% {
    left: 100%;
}
}
@-webkit-keyframes pulse-data-v-483f7033 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes pulse-data-v-483f7033 {
0% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
}
100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}

