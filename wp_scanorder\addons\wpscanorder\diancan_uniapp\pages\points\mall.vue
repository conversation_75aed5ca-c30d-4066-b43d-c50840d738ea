<template>
  <view class="page">
    <!-- 顶部导航和积分展示 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">积分商城</text>
        <view class="empty-placeholder"></view>
      </view>
      
      <!-- 积分信息卡片 -->
      <view class="points-card">
        <view class="points-info">
          <text class="label">我的积分</text>
          <view class="value-container">
            <text class="value">{{userPoints}}</text>
            <view class="detail-btn" @tap="goToPointsDetail">
              <text>兑换记录</text>
              <view class="arrow-right"></view>
            </view>
          </view>
        </view>
        
        <view class="points-tips">
          <text>订单完成后可获得积分</text>
        </view>
      </view>
    </view>

    <!-- 分类选项卡 -->
    <view class="category-tabs">
      <view class="tab-item" :class="{active: activeTab === 'all'}" @tap="switchTab('all')">
        <text>全部商品</text>
      </view>
      <view class="tab-item" :class="{active: activeTab === 'coupon'}" @tap="switchTab('coupon')">
        <text>优惠券</text>
      </view>
      <view class="tab-item" :class="{active: activeTab === 'product'}" @tap="switchTab('product')">
        <text>实物商品</text>
      </view>
    </view>

    <!-- 商品列表 -->
    <view class="goods-list" v-if="!loading && filteredGoodsList.length > 0">
      <view class="goods-card" v-for="(item, index) in filteredGoodsList" :key="item.id" @tap="exchange(item)">
        <image class="goods-image" :src="item.image || '/static/images/placeholder.png'" @error="handleImageError" mode="aspectFill" />
        
        <view class="goods-info">
          <text class="goods-name">{{item.name}}</text>
          
          <!-- 优惠券标签 -->
          <view class="tags-row" v-if="item.type === 'coupon' && item.coupons && item.coupons.length > 0">
            <view class="tag" v-for="(coupon, cIndex) in item.coupons" :key="cIndex" v-if="cIndex < 2">
              <text v-if="coupon.min_amount > 0">满{{coupon.min_amount}}减{{coupon.amount}}</text>
              <text v-else>{{coupon.amount}}元无门槛</text>
            </view>
            <view class="tag more-tag" v-if="item.coupons.length > 2">
              <text>+{{item.coupons.length - 2}}张</text>
            </view>
          </view>
          
          <!-- 商品标签 -->
          <view class="tags-row" v-if="item.type !== 'coupon' && item.products && item.products.length > 0">
            <view class="tag product-tag" v-for="(product, pIndex) in item.products" :key="pIndex" v-if="pIndex < 2">
              <text>{{product.name}}</text>
            </view>
            <view class="tag more-tag" v-if="item.products && item.products.length > 2">
              <text>+{{item.products.length - 2}}件</text>
            </view>
          </view>
          
          <!-- 商品分类 -->
          <view class="category-info" v-if="item.type !== 'coupon' && item.category_text">
            <text>{{item.category_text}}</text>
          </view>
          
          <!-- 有效期显示 -->
          <view class="validity" v-if="item.type === 'coupon' && item.coupons && item.coupons.length > 0">
            <text>领取后{{item.coupons[0].valid_day}}天内有效</text>
          </view>
          
          <view class="bottom-row">
            <view class="points-price">
              <text class="price-value">{{item.points}}</text>
              <text class="price-unit">积分</text>
            </view>
            
            <view class="exchange-btn" :class="{disabled: item.points > userPoints || item.stock <= 0}">
              <text>{{item.stock <= 0 ? '已售罄' : (item.points > userPoints ? '积分不足' : '立即兑换')}}</text>
            </view>
          </view>
        </view>
        
        <!-- 商品类型标签 -->
        <view class="type-tag" :class="{'coupon-tag': item.type === 'coupon', 'product-tag': item.type !== 'coupon'}">
          <text>{{item.type === 'coupon' ? '券' : '品'}}</text>
        </view>
        
        <!-- 库存标签 -->
        <view class="stock-tag" v-if="item.stock !== undefined && item.stock <= 10 && item.stock > 0">
          <text>仅剩{{item.stock}}{{item.type === 'coupon' ? '张' : '件'}}</text>
        </view>
      </view>
    </view>
    
    <!-- 加载中状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text>正在加载...</text>
    </view>
    
    <!-- 空状态 -->
    <view v-if="!loading && filteredGoodsList.length === 0" class="empty-container">
      <image class="empty-image" src="/static/my/c04474c2444f5692feea25109359ae0f.png" mode="aspectFit" />
      <text>暂无商品</text>
      <text class="empty-tips">敬请期待更多商品上架</text>
    </view>
    
    <!-- 加载更多 -->
    <view v-if="!loading && filteredGoodsList.length > 0" class="load-more" @tap="loadMore">
      <text v-if="hasMore">点击加载更多</text>
      <text v-else>—— 已经到底了 ——</text>
    </view>
  </view>
</template>

<script>
import { getUserProfile } from '@/api/user';
import { getPointMallList, exchangePointMallItem } from '@/api/mall';

export default {
  data() {
    return {
      userPoints: 0,
      goodsList: [],
      activeTab: 'all', // 默认显示全部商品
      page: 1,
      limit: 10,
      loading: true,
      hasMore: true,
      isRefreshing: false
    }
  },
  
  computed: {
    // 根据当前选中的标签过滤商品列表
    filteredGoodsList() {
      if (this.activeTab === 'all') {
        return this.goodsList;
      } else if (this.activeTab === 'coupon') {
        return this.goodsList.filter(item => item.type === 'coupon');
      } else if (this.activeTab === 'product') {
        return this.goodsList.filter(item => item.type !== 'coupon');
      }
      return this.goodsList;
    }
  },
  
  onLoad() {
    this.getUserInfo();
    this.getGoodsList();
  },
  
  // 添加下拉刷新
  onPullDownRefresh() {
    this.handlePullRefresh();
  },
  
  methods: {
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab;
    },
    
    // 跳转到兑换记录页面
    goToPointsDetail() {
      uni.navigateTo({
        url: '/pages/points/record'
      });
    },
    
    handleBack() {
      uni.navigateBack()
    },
    
    // 处理下拉刷新
    async handlePullRefresh() {
      if (this.isRefreshing) return;
      
      this.isRefreshing = true;
      
      try {
        // 重置页码
        this.page = 1;
        
        // 并行请求用户信息和商品列表
        await Promise.all([
          this.getUserInfo(),
          this.getGoodsList()
        ]);
        
        uni.showToast({
          title: '刷新成功',
          icon: 'none',
          duration: 1000
        });
      } catch (error) {
        console.error('下拉刷新异常:', error);
        uni.showToast({
          title: '刷新失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isRefreshing = false;
        uni.stopPullDownRefresh();
      }
    },
    
    // 获取用户信息包含积分
    async getUserInfo() {
      try {
        const res = await getUserProfile();
        console.log('用户信息完整数据:', JSON.stringify(res));
        
        if (res.code === 1) {
          // 打印出用户信息数据结构
          console.log('用户信息数据结构:', JSON.stringify(res.data));
          
          // 从返回的数据中获取积分值
          // 根据my.vue页面的代码，应该使用score字段而不是points
          let points = 0;
          if (res.data && res.data.score !== undefined) {
            points = parseInt(res.data.score || 0);
            console.log('从data.score获取积分:', points);
          } else if (res.data && res.data.points !== undefined) {
            points = parseInt(res.data.points || 0);
            console.log('从data.points获取积分:', points);
          } else if (res.data && res.data.user && res.data.user.score !== undefined) {
            points = parseInt(res.data.user.score || 0);
            console.log('从data.user.score获取积分:', points);
          } else if (res.data && res.data.assets && res.data.assets.points !== undefined) {
            points = parseInt(res.data.assets.points || 0);
            console.log('从data.assets.points获取积分:', points);
          }
          
          this.userPoints = points || 0;
          
          // 同时更新本地存储
          uni.setStorageSync('points', this.userPoints);
        } else {
          console.error('获取用户信息失败:', res.msg);
          
          // 如果无法获取用户信息，尝试从本地存储获取
          const points = uni.getStorageSync('points');
          if (points !== '' && points !== undefined) {
            this.userPoints = parseInt(points);
            console.log('从本地存储获取积分:', this.userPoints);
          } else {
            // 尝试从用户信息中获取
            const userInfo = uni.getStorageSync('userInfo');
            console.log('从本地存储获取用户信息:', JSON.stringify(userInfo));
          }
        }
      } catch (error) {
        console.error('获取用户信息异常:', error);
        
        // 如果请求出错，尝试从本地存储获取
        const points = uni.getStorageSync('points');
        if (points !== '' && points !== undefined) {
          this.userPoints = parseInt(points);
          console.log('从本地存储获取积分:', this.userPoints);
        }
      }
    },
    
    // 获取商品列表
    async getGoodsList() {
      try {
        this.loading = true;
        const params = {
          page: this.page,
          limit: this.limit
        };
        
        const res = await getPointMallList(params);
        if (res.code === 1) {
          // 检查API返回的数据结构
          console.log('积分商城数据:', res.data);
          
          // 根据实际返回的数据结构获取商品列表
          const list = res.data.rows || res.data.list || [];
          
          // 处理商品数据，确保中文显示
          const processedList = list.map(item => {
            // 如果category_text不存在，根据type设置中文类别
            if (!item.category_text) {
              item.category_text = item.type === 'coupon' ? '优惠券' : '实物商品';
            }
            
            // 处理优惠券数据，确保没有多余元素
            if (item.type === 'coupon' && item.coupons && item.coupons.length > 0) {
              // 只保留必要的优惠券数据，并处理无门槛优惠券
              item.coupons = item.coupons.map(coupon => {
                return {
                  min_amount: parseFloat(coupon.min_amount || 0),
                  amount: parseFloat(coupon.amount || 0),
                  valid_day: parseInt(coupon.valid_day || 30),
                  name: coupon.name || ''
                };
              });
              
              // 根据优惠券类型添加描述
              item.couponDesc = this.getCouponDescription(item.coupons);
            }
            
            // 处理实物商品数据
            if (item.type !== 'coupon' && item.products && item.products.length > 0) {
              // 处理商品数据
              item.products = item.products.map(product => {
                return {
                  id: product.id || 0,
                  name: product.name || '',
                  image: product.image || ''
                };
              });
              
              // 添加商品描述
              item.productDesc = this.getProductDescription(item.products);
            }
            
            return item;
          });
          
          // 如果是第一页，替换列表，否则追加
          if (this.page === 1) {
            this.goodsList = processedList;
          } else {
            this.goodsList = [...this.goodsList, ...processedList];
          }
          
          this.hasMore = list.length >= this.limit;
        } else {
          uni.showToast({
            title: res.msg || '获取商品列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取商品列表异常:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    // 获取优惠券描述
    getCouponDescription(coupons) {
      if (!coupons || coupons.length === 0) return '';
      
      // 找出最大优惠的优惠券
      const maxCoupon = coupons.reduce((max, current) => {
        return current.amount > max.amount ? current : max;
      }, coupons[0]);
      
      if (maxCoupon.min_amount > 0) {
        return `满${maxCoupon.min_amount}减${maxCoupon.amount}`;
      } else {
        return `${maxCoupon.amount}元无门槛`;
      }
    },
    
    // 获取商品描述
    getProductDescription(products) {
      if (!products || products.length === 0) return '';
      
      if (products.length === 1) {
        return products[0].name;
      } else {
        return `${products[0].name}等${products.length}件商品`;
      }
    },
    
    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++;
        this.loading = true;
        this.getGoodsList();
      }
    },
    
    // 兑换商品
    exchange(item) {
      if (item.points > this.userPoints) {
        uni.showToast({
          title: '积分不足',
          icon: 'none'
        });
        return;
      }
      
      if (item.stock <= 0) {
        uni.showToast({
          title: '商品已售罄',
          icon: 'none'
        });
        return;
      }
      
      // 如果是优惠券类型并且有多张优惠券，显示详情弹窗
      if (item.type === 'coupon' && item.coupons && item.coupons.length > 1) {
        this.showCouponDetail(item);
        return;
      }
      
      // 如果是实物商品并且有多个产品，显示详情弹窗
      if (item.type !== 'coupon' && item.products && item.products.length > 1) {
        this.showProductDetail(item);
        return;
      }
      
      uni.showModal({
        title: '确认兑换',
        content: `确定使用${item.points}积分兑换${item.name}吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({
                title: '兑换中...',
                mask: true
              });
              
              const data = {
                id: item.id
              };
              
              const result = await exchangePointMallItem(data);
              
              if (result.code === 1) {
                // 更新本地数据，无需完全重载
                this.userPoints -= item.points;
                
                // 更新商品库存
                const updatedItem = this.goodsList.find(i => i.id === item.id);
                if (updatedItem) {
                  updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);
                }
                
                // 先隐藏加载框，再显示成功提示
                uni.hideLoading();
                uni.showToast({
                  title: '兑换成功',
                  icon: 'none'
                });
                
                // 在后台更新用户信息
                this.getUserInfo().then(() => {
                  // 静默更新，不影响用户体验
                  console.log('用户信息已更新');
                });
                
                // 如果库存为零，可能需要刷新列表以获取最新数据
                if (updatedItem && updatedItem.stock <= 0) {
                  setTimeout(() => {
                    // 延迟刷新列表，保证提示消息显示
                    this.refreshGoodsList();
                  }, 1500);
                }
              } else {
                uni.hideLoading();
                uni.showToast({
                  title: result.msg || '兑换失败',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('兑换商品异常:', error);
              uni.hideLoading();
              uni.showToast({
                title: '网络异常，请稍后重试',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    
    // 显示优惠券详情
    showCouponDetail(item) {
      // 构建优惠券详情内容
      let content = '';
      if (item.coupons && item.coupons.length > 0) {
        content = item.coupons.map((coupon, index) => {
          const desc = coupon.min_amount > 0 ? 
            `满${coupon.min_amount}减${coupon.amount}` : 
            `${coupon.amount}元无门槛`;
          return `${index + 1}. ${coupon.name || desc}（${coupon.valid_day}天有效）`;
        }).join('\n');
      }
      
      uni.showModal({
        title: '优惠券详情',
        content: content || '暂无详情',
        confirmText: '立即兑换',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确认，继续兑换流程
            uni.showModal({
              title: '确认兑换',
              content: `确定使用${item.points}积分兑换${item.name}吗？`,
              success: async (confirmRes) => {
                if (confirmRes.confirm) {
                  // 执行兑换操作
                  try {
                    uni.showLoading({
                      title: '兑换中...',
                      mask: true
                    });
                    
                    const data = {
                      id: item.id
                    };
                    
                    const result = await exchangePointMallItem(data);
                    
                    if (result.code === 1) {
                      // 更新本地数据
                      this.userPoints -= item.points;
                      
                      // 更新商品库存
                      const updatedItem = this.goodsList.find(i => i.id === item.id);
                      if (updatedItem) {
                        updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);
                      }
                      
                      uni.hideLoading();
                      uni.showToast({
                        title: '兑换成功',
                        icon: 'none'
                      });
                      
                      // 更新用户信息
                      this.getUserInfo();
                    } else {
                      uni.hideLoading();
                      uni.showToast({
                        title: result.msg || '兑换失败',
                        icon: 'none'
                      });
                    }
                  } catch (error) {
                    console.error('兑换商品异常:', error);
                    uni.hideLoading();
                    uni.showToast({
                      title: '网络异常，请稍后重试',
                      icon: 'none'
                    });
                  }
                }
              }
            });
          }
        }
      });
    },
    
    // 显示商品详情
    showProductDetail(item) {
      // 构建商品详情内容
      let content = '';
      if (item.products && item.products.length > 0) {
        content = item.products.map((product, index) => {
          return `${index + 1}. ${product.name}`;
        }).join('\n');
      }
      
      uni.showModal({
        title: '商品详情',
        content: content || '暂无详情',
        confirmText: '立即兑换',
        success: (res) => {
          if (res.confirm) {
            // 用户点击确认，继续兑换流程
            uni.showModal({
              title: '确认兑换',
              content: `确定使用${item.points}积分兑换${item.name}吗？`,
              success: async (confirmRes) => {
                if (confirmRes.confirm) {
                  // 执行兑换操作
                  try {
                    uni.showLoading({
                      title: '兑换中...',
                      mask: true
                    });
                    
                    const data = {
                      id: item.id
                    };
                    
                    const result = await exchangePointMallItem(data);
                    
                    if (result.code === 1) {
                      // 更新本地数据
                      this.userPoints -= item.points;
                      
                      // 更新商品库存
                      const updatedItem = this.goodsList.find(i => i.id === item.id);
                      if (updatedItem) {
                        updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);
                      }
                      
                      uni.hideLoading();
                      uni.showToast({
                        title: '兑换成功',
                        icon: 'none'
                      });
                      
                      // 更新用户信息
                      this.getUserInfo();
                    } else {
                      uni.hideLoading();
                      uni.showToast({
                        title: result.msg || '兑换失败',
                        icon: 'none'
                      });
                    }
                  } catch (error) {
                    console.error('兑换商品异常:', error);
                    uni.hideLoading();
                    uni.showToast({
                      title: '网络异常，请稍后重试',
                      icon: 'none'
                    });
                  }
                }
              }
            });
          }
        }
      });
    },
    
    // 平滑刷新商品列表
    refreshGoodsList() {
      // 先保存滚动位置
      const currentScrollTop = uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      
      // 重置页码
      this.page = 1;
      
      // 请求新数据
      const params = {
        page: this.page,
        limit: this.limit
      };
      
      this.loading = true;
      
      getPointMallList(params).then(res => {
        if (res.code === 1) {
          // 获取新列表
          const list = res.data.rows || res.data.list || [];
          
          // 处理商品数据
          const processedList = list.map(item => {
            if (!item.category_text) {
              item.category_text = item.type === 'coupon' ? '优惠券' : '实物商品';
            }
            
            // 处理优惠券数据
            if (item.type === 'coupon' && item.coupons && item.coupons.length > 0) {
              item.coupons = item.coupons.map(coupon => {
                return {
                  min_amount: coupon.min_amount || 0,
                  amount: coupon.amount || 0
                };
              });
            }
            
            return item;
          });
          
          // 平滑更新列表
          this.goodsList = processedList;
          this.hasMore = list.length >= this.limit;
        }
      }).catch(err => {
        console.error('刷新列表异常:', err);
      }).finally(() => {
        this.loading = false;
        
        // 恢复滚动位置
        setTimeout(() => {
          uni.pageScrollTo({
            scrollTop: currentScrollTop || 0,
            duration: 0
          });
        }, 50);
      });
    },
    
    // 处理图片加载错误
    handleImageError(e) {
      // 替换为默认图片
      e.target.src = '/static/images/placeholder.png';
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
}

.header {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding-top: 88rpx;
  padding-bottom: 30rpx;
  position: relative;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    
    .back-icon {
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #fff;
      font-weight: bold;
    }
    
    .empty-placeholder {
      width: 48rpx;
      height: 48rpx;
    }
  }
  
  .points-card {
    margin: 30rpx 40rpx 10rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20rpx;
    padding: 30rpx;
    backdrop-filter: blur(5px);
    
    .points-info {
      .label {
        font-size: 28rpx;
        color: rgba(255,255,255,0.9);
        display: block;
      }
      
      .value-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10rpx;
        
        .value {
          font-size: 52rpx;
          color: #fff;
          font-weight: bold;
        }
        
        .detail-btn {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.3);
          padding: 10rpx 20rpx;
          border-radius: 100rpx;
          
          text {
            font-size: 24rpx;
            color: #fff;
          }
          
          .arrow-right {
            width: 14rpx;
            height: 14rpx;
            border-top: 2rpx solid #fff;
            border-right: 2rpx solid #fff;
            transform: rotate(45deg);
            margin-left: 10rpx;
          }
        }
      }
    }
    
    .points-tips {
      margin-top: 20rpx;
      
      text {
        font-size: 24rpx;
        color: rgba(255,255,255,0.8);
      }
    }
  }
}

.category-tabs {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin: -20rpx 20rpx 20rpx;
  padding: 20rpx;
  position: relative;
  z-index: 1;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  
  .tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    position: relative;
    
    text {
      font-size: 28rpx;
      color: #666;
      position: relative;
      z-index: 1;
    }
    
    &.active {
      text {
        color: #8cd548;
        font-weight: bold;
      }
      
      &::after {
        content: '';
        position: absolute;
        bottom: 10rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 6rpx;
        background: #8cd548;
        border-radius: 6rpx;
      }
    }
  }
}

.goods-list {
  padding: 0 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  
  .goods-card {
    width: calc(50% - 10rpx);
    margin-bottom: 20rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
    position: relative;
    
    .goods-image {
      width: 100%;
      height: 240rpx;
      display: block;
      object-fit: cover;
    }
    
    .goods-info {
      padding: 20rpx;
      
      .goods-name {
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
        height: 80rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .tags-row {
        margin: 16rpx 0;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        min-height: 40rpx;
        
        .tag {
          background: rgba(255, 68, 68, 0.1);
          border-radius: 8rpx;
          padding: 0 12rpx;
          margin-right: 10rpx;
          margin-bottom: 10rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 32rpx;
          box-sizing: border-box;
          min-width: 60rpx;
          
          text {
            font-size: 20rpx;
            color: #ff4444;
            line-height: 1;
            padding: 0;
            margin: 0;
            white-space: nowrap;
          }
          
          &.product-tag {
            background: rgba(106, 181, 46, 0.1);
            
            text {
              color: #6ab52e;
            }
          }
          
          &.more-tag {
            background: rgba(106, 181, 46, 0.1);
            
            text {
              color: #6ab52e;
            }
          }
        }
      }
      
      .category-info {
        margin-top: 8rpx;
        
        text {
          font-size: 22rpx;
          color: #6ab52e;
        }
      }
      
      .validity {
        margin-top: 8rpx;
        
        text {
          font-size: 22rpx;
          color: #999;
        }
      }
      
      .bottom-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;
        
        .points-price {
          display: flex;
          align-items: baseline;
          
          .price-value {
            font-size: 32rpx;
            color: #ff4444;
            font-weight: bold;
          }
          
          .price-unit {
            font-size: 24rpx;
            color: #ff4444;
            margin-left: 4rpx;
          }
        }
        
        .exchange-btn {
          padding: 0 20rpx;
          background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
          border-radius: 100rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 48rpx;
          box-sizing: border-box;
          min-width: 120rpx;
          
          text {
            font-size: 24rpx;
            color: #fff;
            line-height: 1;
            padding: 0;
            margin: 0;
          }
          
          &.disabled {
            background: linear-gradient(135deg, #ccc 0%, #999 100%);
          }
        }
      }
    }
    
    .type-tag {
      position: absolute;
      top: 20rpx;
      left: 20rpx;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.coupon-tag {
        background: #ff4444;
      }
      
      &.product-tag {
        background: #6ab52e;
      }
      
      text {
        font-size: 22rpx;
        color: #fff;
        font-weight: bold;
      }
    }
    
    .stock-tag {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      padding: 4rpx 12rpx;
      background: rgba(255, 68, 68, 0.8);
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32rpx;
      box-sizing: border-box;
      
      text {
        font-size: 20rpx;
        color: #fff;
        line-height: 1;
        padding: 0;
        margin: 0;
        white-space: nowrap;
      }
    }
  }
}

.loading-container {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid rgba(140, 213, 72, 0.2);
    border-left: 4rpx solid #8cd548;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}

.empty-container {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .empty-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }
  
  text {
    font-size: 30rpx;
    color: #666;
    
    &.empty-tips {
      font-size: 24rpx;
      color: #999;
      margin-top: 10rpx;
    }
  }
}

.load-more {
  padding: 40rpx 0 80rpx;
  text-align: center;
  
  text {
    font-size: 26rpx;
    color: #999;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 