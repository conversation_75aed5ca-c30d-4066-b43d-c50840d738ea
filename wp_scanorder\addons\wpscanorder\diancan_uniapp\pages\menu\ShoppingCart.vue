<template>
	<view class="cart-popup" @click.stop>
		<view class="mask" @click="handleClose"></view>
		<view class="content" @click.stop>
			<!-- 顶部标题栏 -->
			<view class="header">
				<!-- 清空按钮 -->
				<view class="clear-btn" @click="handleClear" v-if="cartList.length > 0">
					<text>清空</text>
				</view>
				<text class="title">购物车</text>
				<view class="close-btn" @click="handleClose">
					<text class="close-icon">×</text>
				</view>
			</view>
			
			<!-- 购物车列表 -->
			<scroll-view 
				scroll-y 
				class="cart-list"
				:style="{ maxHeight: cartList.length > 0 ? '60vh' : '30vh' }"
			>
				<view v-if="cartList.length === 0" class="empty-cart">
					<text class="empty-text">购物车是空的</text>
					<text class="empty-tip">快去选购喜欢的商品吧~</text>
				</view>
				<view 
					v-else
					class="cart-item"
					v-for="(item, index) in cartList"
					:key="index"
				>
					<view class="item-info">
						<image class="item-image" :src="item.image" mode="aspectFill" />
						<view class="item-details">
							<text class="item-name">{{item.name}}</text>
							<view class="item-spec" v-if="item.specs && item.specs.trim()">{{item.specs}}</view>
							<view class="item-price">
								<text class="price-symbol">¥</text>
								<text class="price-value">{{item.price}}</text>
							</view>
						</view>
					</view>
					<view class="quantity-control">
						<view 
							class="quantity-btn minus-btn"
							:class="{'delete-state': item.count === 1}"
							@click="decreaseQuantity(index)"
						>
							<text>-</text>
						</view>
						<text class="number">{{item.count}}</text>
						<view 
							class="quantity-btn plus-btn"
							@click="increaseQuantity(index)"
						>
							<text>+</text>
						</view>
					</view>
				</view>
			</scroll-view>
			
			<!-- 底部结算栏 -->
			<view class="footer">
				<view class="total-info">
					<text class="total-label">合计:</text>
					<view class="total-price">
						<text class="price-symbol">¥</text>
						<text class="price-value">{{totalPrice.toFixed(2)}}</text>
					</view>
				</view>
				<view class="checkout-btn" @click="handleCheckout">
					<text>去结算</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		cartList: {
			type: Array,
			default: () => []
		},
		totalPrice: {
			type: [Number, String],
			default: 0
		}
	},
	methods: {
		// 关闭购物车弹窗
		handleClose() {
			this.$emit('close')
		},
		
		// 清空购物车 (保留方法但不在界面显示按钮)
		handleClear() {
			if (this.cartList.length === 0) return
			
			uni.showModal({
				title: '提示',
				content: '确定要清空购物车吗？',
				success: (res) => {
					if (res.confirm) {
						// 清空购物车
						this.$emit('update', {
							list: [],
							total: 0,
							price: 0
						})
						
						// 发送清空购物车事件
						uni.$emit('clearCart')
						
						// 显示提示
						uni.showToast({
							title: '购物车已清空',
							icon: 'none'
						})
					}
				}
			})
		},
		
		// 减少商品数量
		decreaseQuantity(index) {
			const item = this.cartList[index]
			if (item.count <= 1) {
				// 数量为1时，删除该商品
				this.cartList.splice(index, 1)
				
				// 显示删除提示
				uni.showToast({
					title: '已删除商品',
					icon: 'none'
				})
				
				// 如果购物车为空，关闭弹窗
				if (this.cartList.length === 0) {
					this.$emit('close')
				}
			} else {
				// 减少数量
				item.count -= 1
				item.totalPrice = Number(item.price) * item.count
			}
			
			// 更新购物车
			this.updateCart()
		},
		
		// 增加商品数量
		increaseQuantity(index) {
			const item = this.cartList[index]
			
			// 增加数量
			item.count += 1
			item.totalPrice = Number(item.price) * item.count
			
			// 更新购物车
			this.updateCart()
		},
		
		// 更新购物车
		updateCart() {
			// 计算总数和总价
			const total = this.cartList.reduce((sum, item) => sum + item.count, 0)
			const price = this.cartList.reduce((sum, item) => sum + item.totalPrice, 0)
			
			// 更新购物车数据
			this.$emit('update', {
				list: this.cartList,
				total,
				price
			})
		},
		
		// 去结算
		handleCheckout() {
			if (this.cartList.length === 0) {
				uni.showToast({
					title: '购物车为空',
					icon: 'none'
				})
				return
			}
			
			// 保存购物车数据
			uni.setStorageSync('cartData', {
				list: this.cartList,
				total: this.cartList.reduce((sum, item) => sum + item.count, 0),
				price: this.totalPrice
			})
			
			// 关闭购物车弹窗
			this.handleClose()
			
			// 跳转到订单提交页面
			uni.navigateTo({
				url: '/pages/order/orderSubmit'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.cart-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	
	.mask {
		position: absolute;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.6);
		animation: fadeIn 0.3s ease-out;
	}
	
	.content {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
		background: #fff;
		border-radius: 32rpx 32rpx 0 0;
		padding-bottom: env(safe-area-inset-bottom);
		box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.12);
		animation: slideUp 0.3s ease-out;
		width: 100%;
		box-sizing: border-box;
		
		.header {
			position: relative;
			padding: 30rpx 40rpx;
			border-bottom: 1rpx solid #f2f2f2;
			display: flex;
			align-items: center;
			justify-content: center;
			background: linear-gradient(to bottom, #f9f9f9, #ffffff);
			border-radius: 32rpx 32rpx 0 0;
			
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 30rpx;
				right: 30rpx;
				height: 1rpx;
				background: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));
			}
			
			.title {
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					bottom: -10rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background: linear-gradient(to right, #8cd548, #6ab52e);
					border-radius: 2rpx;
				}
			}
			
			.clear-btn {
				position: absolute;
				left: 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 10rpx;
				
				.clear-icon {
					width: 24rpx;
					height: 24rpx;
					margin-right: 6rpx;
					opacity: 0.6;
				}
				
				text {
					font-size: 24rpx;
					color: #999;
					transition: all 0.2s;
				}
				
				&:active {
					text {
						color: #ff5722;
					}
					.clear-icon {
						opacity: 0.8;
					}
				}
			}
			
			.close-btn {
				position: absolute;
				right: 30rpx;
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background: rgba(0, 0, 0, 0.05);
				transition: all 0.2s;
				
				.close-icon {
					font-size: 40rpx;
					color: #666;
					line-height: 1;
				}
				
				&:active {
					background: rgba(0, 0, 0, 0.1);
					transform: scale(0.95);
				}
			}
		}
		
		.cart-list {
			padding: 20rpx 20rpx;
			
			.empty-cart {
				padding: 80rpx 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				
				.empty-icon {
					width: 140rpx;
					height: 140rpx;
					margin-bottom: 30rpx;
					opacity: 0.5;
				}
				
				.empty-text {
					font-size: 32rpx;
					color: #666;
					font-weight: 500;
					margin-bottom: 16rpx;
				}
				
				.empty-tip {
					font-size: 28rpx;
					color: #999;
				}
			}
			
			.cart-item {
				display: flex;
				align-items: center;
				justify-content: space-around;
				padding: 24rpx 0;
				border-bottom: 1rpx solid #f5f5f5;
				position: relative;
				width: 100%;
				box-sizing: border-box;
				
				&:last-child {
					border-bottom: none;
				}
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 0;
					right: 0;
					height: 1rpx;
					background: linear-gradient(to right, rgba(0,0,0,0.01), rgba(0,0,0,0.03), rgba(0,0,0,0.01));
				}
				
				.item-info {
					display: flex;
					align-items: flex-start;
					flex: 1;
					max-width: 55%;
					overflow: hidden;
					padding-right: 10rpx;
					
					.item-image {
						width: 80rpx;
						height: 80rpx;
						border-radius: 12rpx;
						background-color: #f5f5f5;
						object-fit: cover;
						box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
						border: 2rpx solid #fff;
						flex-shrink: 0;
					}
					
					.item-details {
						margin-left: 16rpx;
						flex: 1;
						min-width: 0;
						overflow: hidden;
						display: flex;
						flex-direction: column;
						
						.item-name {
							font-size: 24rpx;
							color: #333;
							margin-bottom: 6rpx;
							font-weight: 500;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							max-width: 100%;
						}
						
						.item-spec {
							font-size: 18rpx;
							color: #999;
							margin-bottom: 6rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							padding: 2rpx 8rpx;
							border-radius: 20rpx;
							display: inline-block;
							max-width: 100%;
						}
						
						.item-price {
							display: flex;
							align-items: baseline;
							margin-top: 6rpx;
							
							.price-symbol {
								font-size: 18rpx;
								color: #ff5722;
								font-weight: bold;
							}
							
							.price-value {
								font-size: 24rpx;
								color: #ff5722;
								font-weight: bold;
							}
						}
					}
				}
				
				.quantity-control {
					display: flex;
					align-items: center;
					flex-shrink: 0;
					background: rgba(0, 0, 0, 0.02);
					border-radius: 100rpx;
					padding: 2rpx;
					margin-left: auto;
					width: 110rpx;
					justify-content: space-between;
					margin-right: 40rpx;
					
					.quantity-btn {
						width: 32rpx;
						height: 32rpx;
						border-radius: 50%;
						background: #fff;
						display: flex;
						align-items: center;
						justify-content: center;
						transition: all 0.2s;
						box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
						flex-shrink: 0;
						
						text {
							font-size: 18rpx;
							font-weight: bold;
							color: #333;
							line-height: 1;
						}
						
						&:active {
							transform: scale(0.95);
							opacity: 0.8;
						}
						
						&.minus-btn {
							text {
								color: #999;
							}
							
							// 数量为1时，显示删除状态
							&.delete-state {
								background: #ffebee;
								border: 1rpx solid #ffcdd2;
								
								text {
									color: #f44336;
								}
								
								&:active {
									background: #ffcdd2;
								}
							}
						}
						
						&.plus-btn {
							background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
							
							text {
								color: #fff;
							}
							
							&:active {
								background: linear-gradient(135deg, #7bc53a 0%, #5aa41e 100%);
							}
						}
					}
					
					.number {
						font-size: 18rpx;
						font-weight: bold;
						min-width: 20rpx;
						text-align: center;
					}
				}
			}
		}
		
		.footer {
			padding: 24rpx 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-top: 1rpx solid #f2f2f2;
			background: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
			position: relative;
			
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 30rpx;
				right: 30rpx;
				height: 1rpx;
				background: linear-gradient(to right, rgba(0,0,0,0.02), rgba(0,0,0,0.05), rgba(0,0,0,0.02));
			}
			
			.total-info {
				display: flex;
				align-items: baseline;
				
				.total-label {
					font-size: 30rpx;
					color: #333;
					margin-right: 10rpx;
					font-weight: 500;
				}
				
				.total-price {
					display: flex;
					align-items: baseline;
					
					.price-symbol {
						font-size: 26rpx;
						color: #ff5722;
						font-weight: bold;
					}
					
					.price-value {
						font-size: 40rpx;
						color: #ff5722;
						font-weight: bold;
					}
				}
			}
			
			.checkout-btn {
				padding: 0 50rpx;
				height: 88rpx;
				background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);
				position: relative;
				overflow: hidden;
				
				// 添加闪光效果
				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 50%;
					height: 100%;
					background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
					transform: skewX(-25deg);
					animation: shine 3s infinite;
				}
				
				text {
					font-size: 34rpx;
					color: #fff;
					font-weight: bold;
					position: relative;
					z-index: 2;
				}
				
				&:active {
					transform: scale(0.98);
					opacity: 0.9;
					background: linear-gradient(135deg, #7bc53a 0%, #5aa41e 100%);
				}
			}
		}
	}
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}
	to {
		transform: translateY(0);
	}
}

@keyframes shine {
	0% {
		left: -100%;
	}
	20% {
		left: 100%;
	}
	100% {
		left: 100%;
	}
}
</style>