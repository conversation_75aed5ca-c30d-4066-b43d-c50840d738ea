<view class="navbar"><view class="status-bar" style="{{'height:'+(statusBarHeight+'px')+';'}}"></view><view class="{{['navbar-content',bgClass]}}"><block wx:if="{{!isTabbar}}"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="navbar-left" bindtap="__e"><custom-icon vue-id="bed956c6-1" name="arrow-left" color="{{textColor}}" size="{{36}}" bind:__l="__l"></custom-icon></view></block><block wx:else><view class="navbar-left"></view></block><text class="navbar-title" style="{{'color:'+(textColor)+';'}}">{{title}}</text><view class="navbar-right"><slot name="right"></slot></view></view></view>