import request from '@/utils/request.js';

/**
 * 获取所有地区列表
 */
export function getAllAreas() {
  return request({
    url: '/area/index',
    method: 'GET'
  });
}

/**
 * 获取省市区三级结构列表
 */
export function getAreasList() {
  return request({
    url: '/area/getList',
    method: 'GET'
  });
}

/**
 * 根据父级ID获取子地区
 * @param {Number|String} pid - 父级地区ID，0表示获取所有省级地区
 */
export function getChildrenByPid(pid) {
  // 记录请求参数和时间，方便调试
  const requestTime = new Date().toISOString();
  console.log(`[${requestTime}] 发起getChildrenByPid请求，pid:`, pid);
  
  return request({
    url: '/area/getChildrenByPid',
    method: 'GET',
    params: { pid }
  });
}

/**
 * 获取地区详情
 * @param {Number|String} id - 地区ID
 */
export function getAreaDetail(id) {
  return request({
    url: '/area/detail',
    method: 'GET',
    params: { id }
  });
}
