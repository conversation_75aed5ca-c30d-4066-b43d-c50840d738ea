import request from '@/utils/request';
import { baseUrl } from '@/utils/request';

/**
 * 微信小程序登录接口
 * @param {Object} data - 登录参数
 * @param {string} data.code - 微信小程序登录code（必需）
 * @param {Object} [data.userInfo] - 用户信息（可选）
 * @param {string} [data.encryptedData] - 手机号加密数据（可选）
 * @param {string} [data.iv] - 手机号加密向量（可选）
 * @returns {Promise} 登录响应
 */
export const thirdLogin = (data) => {
    return request({
        url: '/user/wxLogin',
        method: 'POST',
        data
    });
};

/**
 * 使用备用路径调用微信小程序登录接口
 * @param {Object} data - 登录参数，同上thirdLogin
 * @returns {Promise} 登录响应
 */
export const thirdLoginFullPath = (data) => {
    return request({
        url: '/user/wxLogin',
        method: 'POST',
        data
    });
};

// 获取用户信息
export const getUserInfo = () => {
    return request({
        url: '/user/info',
        method: 'GET'
    });
};

// 获取用户完整信息（包含资产数据）
export const getUserProfile = () => {
    return request({
        url: '/user/index',
        method: 'GET'
    });
};

// 获取用户资产信息
export const getUserAssets = () => {
    return request({
        url: '/user/assets',
        method: 'GET'
    });
};

// 获取用户优惠券
export const getUserCoupons = (params) => {
    return request({
        url: '/user/coupons',
        method: 'GET',
        params
    });
};

// 获取我的优惠券列表
export const getMyCoupons = (params) => {
    return request({
        url: '/coupon/mycoupons',
        method: 'GET',
        params
    });
};

// 获取用户余额
export const getUserBalance = () => {
    return request({
        url: '/user/balance',
        method: 'GET'
    });
};

// 修改用户个人信息
export const updateUserProfile = (data) => {
    return request({
        url: '/user/profile',
        method: 'POST',
        data
    });
};

// 上传图片
export const uploadImage = (filePath, name = 'file') => {
    return new Promise((resolve, reject) => {
        const token = uni.getStorageSync('token');
        if (!token) {
            return reject({
                code: 401,
                msg: '未登录或登录已过期'
            });
        }
        
        // 使用从request.js导入的baseUrl
        uni.uploadFile({
            url: baseUrl + '/upload/image',
            filePath,
            name,
            header: {
                token
            },
            success: (res) => {
                if (res.statusCode === 200) {
                    try {
                        const data = JSON.parse(res.data);
                        if (data.code === 1 && data.data && data.data.url) {
                            resolve(data);
                        } else {
                            reject(data || { code: 0, msg: '上传失败' });
                        }
                    } catch (e) {
                        reject({
                            code: 0,
                            msg: '解析响应数据失败'
                        });
                    }
                } else {
                    reject({
                        code: res.statusCode,
                        msg: '上传失败，状态码：' + res.statusCode
                    });
                }
            },
            fail: (err) => {
                reject({
                    code: 0,
                    msg: err.errMsg || '网络请求失败'
                });
            }
        });
    });
};