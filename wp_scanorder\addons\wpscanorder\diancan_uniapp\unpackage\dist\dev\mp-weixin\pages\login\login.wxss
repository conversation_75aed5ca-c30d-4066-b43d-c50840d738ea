@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container {
  min-height: 100vh;
  background-color: #FFFFFF;
  position: relative;
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.back-button {
  position: absolute;
  left: 30rpx;
  top: 80rpx;
  z-index: 10;
}
.back-icon {
  width: 40rpx;
  height: 40rpx;
}
.merchant-name {
  margin-top: 80rpx;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  width: 100%;
  padding: 20rpx 0;
}
.logo-container {
  margin-top: 60rpx;
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
}
.logo-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
}
.merchant-title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.benefit-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 80rpx;
}
.auth-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}
.login-btn-wrap {
  width: 100%;
  padding: 0 20rpx;
  margin-bottom: 40rpx;
}
.login-btn {
  height: 90rpx;
  line-height: 90rpx;
  background-color: #78c238;
  color: #FFFFFF;
  font-size: 32rpx;
  border-radius: 45rpx;
  font-weight: bold;
}
.agreement-wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  flex-wrap: wrap;
  padding: 0 20rpx;
}
.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 1px solid #CCCCCC;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10rpx;
}
.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: transparent;
}
.checkbox-inner.checked {
  background-color: #78c238;
}
.agreement-text {
  font-size: 26rpx;
  color: #666666;
}
.agreement-link {
  font-size: 26rpx;
  color: #FF0000;
}
.skip-login {
  margin-top: 40rpx;
  padding: 20rpx 40rpx;
}
.skip-login text {
  font-size: 28rpx;
  color: #999999;
}

