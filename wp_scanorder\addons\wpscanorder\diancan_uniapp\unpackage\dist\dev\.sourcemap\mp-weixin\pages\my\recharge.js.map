{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge.vue?0fb0", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge.vue?02bf", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge.vue?24b0", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge.vue?422f", "uni-app:///pages/my/recharge.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge.vue?ba3b", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge.vue?bb37"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "props", "data", "userBalance", "selectedAmount", "selectedId", "loading", "amounts", "onShow", "methods", "loadBalance", "console", "res", "balance", "loadRechargeConfigs", "uni", "title", "configList", "id", "value", "gift", "active", "icon", "selectAmount", "amountItem", "handleRecharge", "content", "success", "rechargeParams", "result", "rechargeData", "toastMsg", "duration", "setTimeout", "handleBack", "goToRecord", "url", "handleWechatPay", "provider", "payParams", "totalAmount", "parseFloat", "fail", "complete"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsErrB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAD;gBAEA;kBACAA;kBACAA;;kBAEA;kBACA;oBACA;oBACAA;oBACAE;oBACA;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBAEAF;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAJ;gBACAD;gBAAA,MAEAC;kBAAA;kBAAA;gBAAA;gBACA;gBACAK;gBAEA;kBACA;kBACAA;gBACA;kBACA;kBACAA;gBACA;kBACA;kBACAA;oBAAA;kBAAA;gBACA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;kBACA;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;gBACA;gBAEAV;gBAAA;cAAA;gBAKA;gBACAA;gBACA;gBACA;gBAEAI;kBACAC;kBACAM;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;gBACA;gBACA;gBACA;gBAEAI;kBACAC;kBACAM;gBACA;cAAA;gBAAA;gBAEAP;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAQ;MACA;MACA;;MAEA;MACA;QACAC;MACA;IACA;IAIA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAV;kBACAC;kBACAM;gBACA;cAAA;gBAAA,KAIA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAP;kBACAC;kBACAU;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAf;gCAAA;gCAAA;8BAAA;8BACA;8BAAA;8BAGAG;gCACAC;8BACA;;8BAEA;8BACAY,qBAEA;8BACA;gCACAA;8BACA;gCACA;gCACAA;8BACA;8BAEAjB;;8BAEA;8BAAA;8BAAA,OACA;4BAAA;8BAAAkB;8BAEAlB;;8BAEA;8BAAA,MACAkB;gCAAA;gCAAA;8BAAA;8BACA;8BACAC;8BACAnB;;8BAEA;8BAAA,MACAmB;gCAAA;gCAAA;8BAAA;8BACA;8BACA;8BAAA;4BAAA;8BAAA;8BAAA,OAMA;4BAAA;8BAEA;8BACAC;8BACA;gCACAA;8BACA;8BAEAhB;gCACAC;gCACAM;gCACAU;8BACA;;8BAEA;8BACAC;gCACAlB;8BACA;8BAAA;8BAAA;4BAAA;8BAEA;8BACAA;gCACAC;gCACAM;8BACA;4BAAA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAGAX;8BACAI;gCACAC;gCACAM;8BACA;4BAAA;8BAAA;8BAEAP;8BACA;8BAAA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAmB;MACAnB;IACA;IAEAoB;MACApB;QACAqB;MACA;IACA;IAEA;IACAC;MAAA;MACA1B;;MAEA;MACA;;MAEA;MACAI;QACAuB;MAAA,GACAC;QACAZ;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAhB;;oBAEA;oBAAA;oBAAA,OACA;kBAAA;oBAEA;oBACA6B,gDACAC,2CAEA;oBACA1B;sBACAC;sBACAM;sBACAU;oBACA;;oBAEA;oBACAC;sBACAlB;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;QACA2B;UACA/B;;UAEA;UACA;YACAI;cACAC;cACAM;YACA;UACA;YACAP;cACAC;cACAM;YACA;UACA;UAEA;QACA;QACAqB;UACA5B;QACA;MAAA,GACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxXA;AAAA;AAAA;AAAA;AAAoxC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAxyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/recharge.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/recharge.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./recharge.vue?vue&type=template&id=dd0ce27e&scoped=true&\"\nvar renderjs\nimport script from \"./recharge.vue?vue&type=script&lang=js&\"\nexport * from \"./recharge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recharge.vue?vue&type=style&index=0&id=dd0ce27e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dd0ce27e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/recharge.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=template&id=dd0ce27e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">会员充值</text>\r\n      </view>\r\n      \r\n      <!-- 账户余额 -->\r\n      <view class=\"balance-info\">\r\n        <text class=\"label\">当前余额</text>\r\n        <view class=\"amount\">\r\n          <text class=\"symbol\">¥</text>\r\n          <text class=\"value\">{{userBalance}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <view class=\"content\">\r\n      <!-- 充值金额卡片 -->\r\n      <view class=\"recharge-card\">\r\n        <view class=\"card-header\">\r\n          <text class=\"card-title\">选择充值金额</text>\r\n          <view class=\"record-link\" @tap=\"goToRecord\">\r\n            <text>充值记录</text>\r\n            <text class=\"arrow\">></text>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 金额选择网格 -->\r\n        <view class=\"amount-grid\">\r\n          <view \r\n            v-for=\"(item, index) in amounts\" \r\n            :key=\"index\"\r\n            :class=\"['amount-item', {'active': item.active || selectedAmount === item.value}]\"\r\n            @tap=\"selectAmount(item)\"\r\n          >\r\n            <view class=\"amount\">\r\n              <text class=\"symbol\">¥</text>\r\n              <text class=\"value\">{{item.value}}</text>\r\n            </view>\r\n            <text class=\"gift\">赠送{{item.gift}}元</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 充值说明 -->\r\n      <view class=\"notice\">\r\n        <text class=\"notice-title\">充值说明</text>\r\n        <text class=\"notice-item\">1、本次充值可用于平台上消费，各地区跨站点皆可使用。</text>\r\n        <text class=\"notice-item\">2、若遇到充值未到账，请联系客服。</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部充值按钮 -->\r\n    <view class=\"bottom-bar\">\r\n      <view class=\"recharge-btn\" @tap=\"handleRecharge\" :class=\"{'disabled': loading}\">\r\n        <text>{{loading ? '处理中...' : '立即充值'}}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getUserBalance } from '@/api/user.js';\r\nimport { createRecharge, getRechargeConfigs } from '@/api/recharge.js';\r\n\r\n  export default {\r\n    components: {},\r\n    props: {},\r\n    data() {\r\n      return {\r\n      userBalance: '0.00', // 用户余额\r\n        selectedAmount: 0,\r\n        selectedId: '', // 选中的充值套餐ID\r\n      loading: false,\r\n      // 充值金额配置（将从接口获取）\r\n        amounts: []\r\n      };\r\n    },\r\n  \r\n  onShow() {\r\n    // 获取余额\r\n    this.loadBalance();\r\n    this.loadRechargeConfigs();\r\n  },\r\n\r\n    methods: {\r\n    // 加载余额\r\n    async loadBalance() {\r\n      try {\r\n        console.log('开始获取用户余额...');\r\n        const res = await getUserBalance();\r\n        console.log('余额API响应完整数据:', JSON.stringify(res));\r\n        \r\n        if (res.code === 1) {\r\n          console.log('余额API响应数据类型:', typeof res.data);\r\n          console.log('余额API响应数据值:', res.data);\r\n          \r\n          // 根据数据类型处理\r\n          if (typeof res.data === 'object' && res.data !== null) {\r\n            // 如果是对象，尝试获取balance字段\r\n            console.log('余额对象字段:', Object.keys(res.data));\r\n            const balance = res.data.balance || res.data.money || res.data.user_money || 0;\r\n            this.userBalance = parseFloat(balance).toFixed(2);\r\n          } else if (typeof res.data === 'number') {\r\n            // 如果直接是数值\r\n            this.userBalance = parseFloat(res.data).toFixed(2);\r\n          } else if (typeof res.data === 'string') {\r\n            // 如果是字符串\r\n            this.userBalance = parseFloat(res.data).toFixed(2);\r\n          }\r\n          \r\n          console.log('解析后的余额:', this.userBalance);\r\n        } else {\r\n          console.error('获取余额失败:', res.msg);\r\n        }\r\n      } catch (error) {\r\n        console.error('获取余额请求异常:', error);\r\n      }\r\n    },\r\n    \r\n    // 加载充值套餐配置\r\n    async loadRechargeConfigs() {\r\n      try {\r\n        uni.showLoading({\r\n          title: '加载中...'\r\n        });\r\n        \r\n        const res = await getRechargeConfigs();\r\n        console.log('充值套餐API响应:', JSON.stringify(res));\r\n        \r\n        if (res.code === 1 && res.data) {\r\n          // 检查返回数据格式\r\n          let configList = [];\r\n          \r\n          if (Array.isArray(res.data)) {\r\n            // 如果直接返回数组\r\n            configList = res.data;\r\n          } else if (res.data.list && Array.isArray(res.data.list)) {\r\n            // 如果返回的是包含list的对象\r\n            configList = res.data.list;\r\n          } else if (typeof res.data === 'object') {\r\n            // 如果是对象格式，尝试转换为数组\r\n            configList = Object.values(res.data).filter(item => typeof item === 'object');\r\n          }\r\n          \r\n          if (configList.length > 0) {\r\n            // 从接口获取充值配置\r\n            this.amounts = configList.map((item, index) => {\r\n              return {\r\n                id: item.id || item.recharge_id || '',\r\n                value: parseFloat(item.amount || item.value || 0),\r\n                gift: parseFloat(item.send_amount || item.gift || 0),\r\n                active: index === 0 // 默认选中第一个\r\n              };\r\n            });\r\n            \r\n            // 默认选中第一个充值选项\r\n            if (this.amounts.length > 0) {\r\n              this.selectedAmount = this.amounts[0].value;\r\n              this.selectedId = this.amounts[0].id || '';\r\n            }\r\n            \r\n            console.log('充值套餐配置获取成功:', this.amounts);\r\n            return;\r\n          }\r\n        }\r\n        \r\n        // 如果接口没有返回有效数据，显示提示\r\n        console.log('未获取到充值套餐配置');\r\n        this.amounts = [];\r\n        this.selectedAmount = 0;\r\n        \r\n        uni.showToast({\r\n          title: '获取充值套餐失败',\r\n          icon: 'none'\r\n        });\r\n      } catch (error) {\r\n        console.error('获取充值套餐配置失败:', error);\r\n        // 接口异常时显示提示\r\n        this.amounts = [];\r\n        this.selectedAmount = 0;\r\n        \r\n        uni.showToast({\r\n          title: '获取充值套餐失败',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        uni.hideLoading();\r\n      }\r\n    },\r\n    \r\n      selectAmount(item) {\r\n        this.selectedAmount = item.value\r\n        this.selectedId = item.id || ''\r\n      \r\n      // 更新活动状态\r\n      this.amounts.forEach(amountItem => {\r\n        amountItem.active = amountItem.value === item.value\r\n      })\r\n      },\r\n    \r\n\r\n    \r\n    // 处理充值操作\r\n    async handleRecharge() {\r\n      // 验证金额\r\n      if (this.selectedAmount <= 0) {\r\n        return uni.showToast({\r\n          title: '请选择充值金额',\r\n          icon: 'none'\r\n        });\r\n      }\r\n      \r\n      // 避免重复提交\r\n      if (this.loading) return;\r\n      \r\n        uni.showModal({\r\n          title: '确认充值',\r\n          content: `确认充值${this.selectedAmount}元？`,\r\n        success: async (res) => {\r\n            if(res.confirm) {\r\n            this.loading = true;\r\n            \r\n            try {\r\n              uni.showLoading({\r\n                title: '充值中...'\r\n              });\r\n              \r\n              // 准备充值参数\r\n              let rechargeParams = {};\r\n              \r\n              // 如果有选中的充值套餐ID，则使用ID\r\n              if (this.selectedId) {\r\n                rechargeParams.recharge_id = this.selectedId;\r\n              } else {\r\n                // 否则使用自定义金额\r\n                rechargeParams.amount = this.selectedAmount;\r\n              }\r\n              \r\n              console.log('充值参数:', rechargeParams);\r\n              \r\n              // 调用充值接口\r\n              const result = await createRecharge(rechargeParams);\r\n              \r\n              console.log('充值结果:', JSON.stringify(result));\r\n              \r\n              // 判断充值结果\r\n              if (result.code === 1) {\r\n                // 获取充值记录详情，包括赠送金额\r\n                const rechargeData = result.data;\r\n                console.log('充值数据:', JSON.stringify(rechargeData));\r\n                \r\n                // 检查是否需要调起微信支付\r\n                if (rechargeData.payment && rechargeData.payment.type === 'wechat' && rechargeData.payment.params) {\r\n                  // 调起微信支付\r\n                  this.handleWechatPay(rechargeData);\r\n                  return;\r\n                }\r\n                \r\n                // 非微信支付情况，直接显示充值成功\r\n                // 立即刷新余额\r\n                await this.loadBalance();\r\n                \r\n                // 显示成功提示，加上赠送金额提示\r\n                let toastMsg = '充值成功';\r\n                if (rechargeData.send_amount && parseFloat(rechargeData.send_amount) > 0) {\r\n                  toastMsg += `，赠送${rechargeData.send_amount}元`;\r\n                }\r\n                \r\n                uni.showToast({\r\n                  title: toastMsg,\r\n                  icon: 'none',\r\n                  duration: 2000\r\n                });\r\n                \r\n                // 延时返回\r\n                    setTimeout(() => {\r\n                  uni.navigateBack();\r\n                }, 2000);\r\n              } else {\r\n                // 充值失败\r\n                uni.showToast({\r\n                  title: result.msg || '充值失败，请重试',\r\n                  icon: 'none'\r\n                });\r\n              }\r\n            } catch (error) {\r\n              console.error('充值请求异常:', error);\r\n              uni.showToast({\r\n                title: '充值失败，请重试',\r\n                icon: 'none'\r\n              });\r\n            } finally {\r\n              uni.hideLoading();\r\n              this.loading = false;\r\n            }\r\n            }\r\n          }\r\n      });\r\n    },\r\n    \r\n      handleBack() {\r\n        uni.navigateBack()\r\n      },\r\n    \r\n      goToRecord() {\r\n        uni.navigateTo({\r\n          url: '/pages/my/recharge-record'\r\n        })\r\n      },\r\n      \r\n    // 处理微信支付\r\n    handleWechatPay(rechargeData) {\r\n      console.log('调起微信支付:', rechargeData.payment.params);\r\n      \r\n      // 解构参数\r\n      const payParams = rechargeData.payment.params;\r\n      \r\n      // 调用微信支付\r\n      uni.requestPayment({\r\n        provider: 'wxpay',\r\n        ...payParams,\r\n        success: async (res) => {\r\n          console.log('微信支付成功:', res);\r\n          \r\n          // 支付成功后刷新余额\r\n          await this.loadBalance();\r\n          \r\n          // 计算实际到账金额\r\n          const totalAmount = parseFloat(rechargeData.amount) + \r\n                              parseFloat(rechargeData.send_amount || 0);\r\n                              \r\n          // 显示成功消息\r\n          uni.showToast({\r\n            title: `充值成功，到账${totalAmount}元`,\r\n            icon: 'none',\r\n            duration: 2000\r\n          });\r\n          \r\n          // 延时返回\r\n          setTimeout(() => {\r\n            uni.navigateBack();\r\n          }, 2000);\r\n        },\r\n        fail: (err) => {\r\n          console.error('微信支付失败:', err);\r\n          \r\n          // 判断是否是用户取消\r\n          if (err.errMsg === 'requestPayment:fail cancel') {\r\n            uni.showToast({\r\n              title: '支付已取消',\r\n              icon: 'none'\r\n            });\r\n          } else {\r\n            uni.showToast({\r\n              title: '支付失败，请重试',\r\n              icon: 'none'\r\n            });\r\n          }\r\n          \r\n          this.loading = false;\r\n        },\r\n        complete: () => {\r\n          uni.hideLoading();\r\n        }\r\n      });\r\n    }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n  padding-top: 88rpx;\r\n  padding-bottom: 60rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .back-icon {\r\n      position: absolute;\r\n      left: 30rpx;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #fff;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n  \r\n  .balance-info {\r\n    text-align: center;\r\n    margin-top: 40rpx;\r\n    \r\n    .label {\r\n      font-size: 28rpx;\r\n      color: rgba(255, 255, 255, 0.9);\r\n      margin-bottom: 16rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .amount {\r\n      display: flex;\r\n      align-items: baseline;\r\n      justify-content: center;\r\n      \r\n      .symbol {\r\n        font-size: 32rpx;\r\n        color: #fff;\r\n        margin-right: 8rpx;\r\n      }\r\n      \r\n      .value {\r\n        font-size: 60rpx;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        font-family: 'DIN';\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content {\r\n  margin-top: -40rpx;\r\n  padding: 0 30rpx;\r\n  padding-bottom: 120rpx;\r\n  \r\n  .recharge-card {\r\n    background: #fff;\r\n    border-radius: 20rpx;\r\n    padding: 30rpx;\r\n    margin-bottom: 30rpx;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n    \r\n    .card-header {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      margin-bottom: 20rpx;\r\n      \r\n      .card-title {\r\n        font-size: 30rpx;\r\n        color: #333;\r\n        font-weight: bold;\r\n      }\r\n      \r\n      .record-link {\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 6rpx 12rpx;\r\n        \r\n        text {\r\n          font-size: 26rpx;\r\n          color: #333;\r\n        }\r\n        \r\n        .arrow {\r\n          margin-left: 6rpx;\r\n          font-size: 22rpx;\r\n        }\r\n        \r\n        &:active {\r\n          opacity: 0.7;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .amount-grid {\r\n      display: grid;\r\n      grid-template-columns: repeat(3, 1fr);\r\n      gap: 20rpx;\r\n      margin-top: 10rpx;\r\n      \r\n      .amount-item {\r\n        background: #f8f8f8;\r\n        border-radius: 16rpx;\r\n        padding: 30rpx 20rpx;\r\n        text-align: center;\r\n        border: 2rpx solid transparent;\r\n        \r\n        .amount {\r\n          margin-bottom: 12rpx;\r\n          \r\n          .symbol {\r\n            font-size: 24rpx;\r\n            color: #333;\r\n          }\r\n          \r\n          .value {\r\n            font-size: 36rpx;\r\n            color: #333;\r\n            font-weight: bold;\r\n          }\r\n        }\r\n        \r\n        .gift {\r\n          font-size: 24rpx;\r\n          color: #999;\r\n        }\r\n        \r\n        &.active {\r\n          background: rgba(140, 213, 72, 0.1);\r\n          border-color: #8cd548;\r\n          \r\n          .symbol, .value {\r\n            color: #8cd548;\r\n          }\r\n          \r\n          .gift {\r\n            color: #8cd548;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.notice {\r\n  padding: 0 20rpx;\r\n  \r\n  .notice-title {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .notice-item {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n    line-height: 1.6;\r\n    display: block;\r\n  }\r\n}\r\n\r\n.bottom-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  padding: 20rpx 40rpx;\r\n  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\r\n  background: #fff;\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .recharge-btn {\r\n    height: 88rpx;\r\n    background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n    border-radius: 44rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    text {\r\n      font-size: 32rpx;\r\n      color: #fff;\r\n      font-weight: 500;\r\n    }\r\n    \r\n    &:active {\r\n      transform: scale(0.98);\r\n    }\r\n    \r\n    &.disabled {\r\n      opacity: 0.7;\r\n      pointer-events: none;\r\n    }\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=style&index=0&id=dd0ce27e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge.vue?vue&type=style&index=0&id=dd0ce27e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309925\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}