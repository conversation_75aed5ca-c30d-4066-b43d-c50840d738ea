<view class="page data-v-7b3dceb8"><view class="custom-navbar data-v-7b3dceb8" style="{{'padding-top:'+(statusBarHeight+'px')+';'}}"><view class="navbar-content data-v-7b3dceb8"><view data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" class="back-btn data-v-7b3dceb8" bindtap="__e"><u-icon vue-id="2706ed64-1" name="arrow-left" color="#333" size="20" class="data-v-7b3dceb8" bind:__l="__l"></u-icon></view><text class="page-title data-v-7b3dceb8">商户详情</text><view class="placeholder data-v-7b3dceb8"></view></view></view><view class="navbar-placeholder data-v-7b3dceb8" style="{{'height:'+(navbarHeight+'px')+';'}}"></view><view class="store-header data-v-7b3dceb8"><view class="store-info-wrap data-v-7b3dceb8"><image class="store-logo data-v-7b3dceb8" src="{{storeInfo.image||'/static/store/default.png'}}" mode="aspectFill"></image><view class="store-brief data-v-7b3dceb8"><text class="store-name data-v-7b3dceb8">{{storeInfo.name}}</text><view class="store-stats data-v-7b3dceb8"><view class="rating-wrap data-v-7b3dceb8"><text class="rating data-v-7b3dceb8">{{storeInfo.rating+"分"}}</text><view class="rating-stars data-v-7b3dceb8"><block wx:for="{{5}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><u-icon vue-id="{{'2706ed64-2-'+__i0__}}" name="{{i<=$root.g0?'star-fill':'star'}}" color="{{i<=$root.g1?'#ff9900':'#ddd'}}" size="24" class="data-v-7b3dceb8" bind:__l="__l"></u-icon></block></view></view><text class="divider data-v-7b3dceb8">|</text><text class="sales data-v-7b3dceb8">{{"月售"+storeInfo.monthlySales+"单"}}</text></view></view></view></view><view class="store-info data-v-7b3dceb8"><view class="info-card business-card data-v-7b3dceb8"><view class="card-header data-v-7b3dceb8"><u-icon vue-id="2706ed64-3" name="info-circle" size="32" color="#333" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="title data-v-7b3dceb8">营业信息</text></view><view class="business-info data-v-7b3dceb8"><view class="time-item data-v-7b3dceb8"><view class="time-header data-v-7b3dceb8"><view class="header-left data-v-7b3dceb8"><u-icon vue-id="2706ed64-4" name="clock" size="28" color="#8cd548" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="time-label data-v-7b3dceb8">营业时间</text></view><view class="{{['status-tag','data-v-7b3dceb8',(!isOpen)?'closed':'']}}"><u-icon vue-id="2706ed64-5" name="{{isOpen?'checkmark-circle':'close-circle'}}" size="24" color="{{isOpen?'#8cd548':'#ff6b6b'}}" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="data-v-7b3dceb8">{{isOpen?'营业中':'已打烊'}}</text></view></view><view class="time-periods data-v-7b3dceb8"><text class="time-value data-v-7b3dceb8">{{storeInfo.businessHours}}</text><text class="time-tips data-v-7b3dceb8">*最后下单时间为打烊前30分钟</text></view></view><block wx:if="{{storeInfo.notice}}"><view class="notice-item data-v-7b3dceb8"><u-icon vue-id="2706ed64-6" name="volume" size="28" color="#ff9900" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><view class="notice-content data-v-7b3dceb8"><text class="notice-label data-v-7b3dceb8">商家公告</text><text class="notice-text data-v-7b3dceb8">{{storeInfo.notice}}</text></view></view></block></view></view><view class="info-card address-info data-v-7b3dceb8"><view class="card-header data-v-7b3dceb8"><u-icon vue-id="2706ed64-7" name="map-fill" size="32" color="#ff6b6b" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="title data-v-7b3dceb8">商家地址</text></view><view class="address-content data-v-7b3dceb8"><view class="location-info data-v-7b3dceb8"><view class="address-header data-v-7b3dceb8"><view class="distance-tag data-v-7b3dceb8"><u-icon vue-id="2706ed64-8" name="map" size="24" color="#ff6b6b" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="data-v-7b3dceb8">{{"距您"+storeInfo.distance+"km"}}</text></view><view data-event-opts="{{[['tap',[['openMap',['$event']]]]]}}" class="nav-btn data-v-7b3dceb8" bindtap="__e"><u-icon vue-id="2706ed64-9" size="28" color="#ff6b6b" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="data-v-7b3dceb8">去导航</text></view></view></view><view class="address-detail data-v-7b3dceb8"><u-icon vue-id="2706ed64-10" name="home" size="28" color="#666" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="address-text data-v-7b3dceb8">{{storeInfo.address}}</text></view></view></view><block wx:if="{{$root.g2}}"><view class="info-card qualification data-v-7b3dceb8"><view class="card-header data-v-7b3dceb8"><u-icon vue-id="2706ed64-11" name="file-text" size="32" color="#333" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="title data-v-7b3dceb8">商家资质</text></view><view class="qual-content data-v-7b3dceb8"><scroll-view class="qual-scroll data-v-7b3dceb8" scroll-x="{{true}}" show-scrollbar="false" enhanced="{{true}}"><block wx:for="{{storeInfo.qualifications}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" class="qual-item data-v-7b3dceb8" bindtap="__e"><image class="qual-image data-v-7b3dceb8" src="{{item.image}}" mode="aspectFill"></image><text class="qual-tips data-v-7b3dceb8">点击查看</text></view></block></scroll-view></view></view></block></view><view class="bottom-bar data-v-7b3dceb8"><view data-event-opts="{{[['tap',[['contactStore',['$event']]]]]}}" class="contact data-v-7b3dceb8" bindtap="__e"><u-icon vue-id="2706ed64-12" name="phone-fill" size="32" color="#666" class="data-v-7b3dceb8" bind:__l="__l"></u-icon><text class="data-v-7b3dceb8">联系商家</text></view><view data-event-opts="{{[['tap',[['goToMenu',['$event']]]]]}}" class="order-btn data-v-7b3dceb8" bindtap="__e"><u-icon class="cart-icon data-v-7b3dceb8" vue-id="2706ed64-13" name="shopping-cart" size="32" color="#fff" bind:__l="__l"></u-icon><text class="data-v-7b3dceb8">去点餐</text></view></view></view>