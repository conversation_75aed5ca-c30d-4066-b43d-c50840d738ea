(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/order/orderSubmit"],{

/***/ 206:
/*!*********************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/main.js?{"page":"pages%2Forder%2ForderSubmit"} ***!
  \*********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _orderSubmit = _interopRequireDefault(__webpack_require__(/*! ./pages/order/orderSubmit.vue */ 207));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_orderSubmit.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 207:
/*!**************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./orderSubmit.vue?vue&type=template&id=5c101a1c&scoped=true& */ 208);
/* harmony import */ var _orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./orderSubmit.vue?vue&type=script&lang=js& */ 210);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _orderSubmit_vue_vue_type_style_index_0_id_5c101a1c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./orderSubmit.vue?vue&type=style&index=0&id=5c101a1c&lang=scss&scoped=true& */ 213);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "5c101a1c",
  null,
  false,
  _orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/order/orderSubmit.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 208:
/*!*********************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?vue&type=template&id=5c101a1c&scoped=true& ***!
  \*********************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=template&id=5c101a1c&scoped=true& */ 209);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_template_id_5c101a1c_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 209:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?vue&type=template&id=5c101a1c&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uIcon: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-icon/u-icon.vue */ 372))
    },
    uPopup: function () {
      return Promise.all(/*! import() | node-modules/uview-ui/components/u-popup/u-popup */[__webpack_require__.e("common/vendor"), __webpack_require__.e("node-modules/uview-ui/components/u-popup/u-popup")]).then(__webpack_require__.bind(null, /*! uview-ui/components/u-popup/u-popup.vue */ 402))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 =
    _vm.pickupType === "takeout" && _vm.selectedAddress
      ? _vm.formatAddress(_vm.selectedAddress)
      : null
  var g0 = (_vm.userBalance || 0).toFixed(2)
  var g1 = (_vm.totalPrice - _vm.discountAmount).toFixed(2)
  var g2 = _vm.tempRemark.length
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        g0: g0,
        g1: g1,
        g2: g2,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 210:
/*!***************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=script&lang=js& */ 211);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 211:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 55));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 57));
var _order = __webpack_require__(/*! @/api/order.js */ 195);
var _user = __webpack_require__(/*! @/api/user.js */ 166);
var _address = __webpack_require__(/*! @/api/address.js */ 212);
var _merchant = __webpack_require__(/*! @/api/merchant.js */ 177);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      statusBarHeight: 0,
      cartList: [],
      // 购物车商品列表
      totalPrice: 0,
      // 总价
      discountAmount: 0,
      // 优惠金额
      finalPrice: 0,
      // 最终支付金额
      pickupType: 'dine-in',
      // 取餐方式：dine-in(堂食) / takeout(外带)
      remark: '',
      // 备注
      paymentMethod: 'wechat',
      // 支付方式：wechat(微信) / balance(余额)
      storeInfo: {
        name: '',
        pickupTime: '尽快取餐'
      },
      selectedTime: '',
      // 选中的预约时间
      timeSlots: [],
      // 可选时间段列表
      showTimePicker: false,
      // 控制时间选择弹窗显示
      tempSelectedTime: '',
      // 临时存储选中的时间
      showRemarkPopup: false,
      // 控制备注弹窗显示
      tempRemark: '',
      // 临时存储备注信息
      selectedAddress: null,
      // 选中的收货地址
      userBalance: 0,
      // 用户余额
      selectedCoupon: null,
      // 选中的优惠券
      isSubmitting: false // 是否正在提交订单，防止重复点击
    };
  },

  computed: {
    // 计算最终支付金额
    finalAmount: function finalAmount() {
      return (this.totalPrice - this.discountAmount).toFixed(2);
    }
  },
  onLoad: function onLoad() {
    var _this = this;
    // 获取状态栏高度
    var systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;

    // 获取购物车数据
    var cartData = uni.getStorageSync('cartData');
    if (cartData) {
      this.cartList = cartData.list;
      this.totalPrice = cartData.price;
      this.finalPrice = cartData.price;
    }

    // 获取用餐方式
    var diningType = uni.getStorageSync('diningType');
    if (diningType) {
      this.pickupType = diningType;
      // 如果是外卖模式，生成时间段列表
      if (diningType === 'takeout') {
        this.generateTimeSlots();
      }
    }

    // 恢复暂存的订单数据
    var tempOrderData = uni.getStorageSync('tempOrderData');
    if (tempOrderData) {
      if (tempOrderData.selectedAddress) {
        this.selectedAddress = tempOrderData.selectedAddress;
      }
      if (tempOrderData.selectedCoupon) {
        this.selectedCoupon = tempOrderData.selectedCoupon;
        this.calculateDiscount();
      }
    }

    // 监听地址选择结果
    uni.$on('addressSelected', function (address) {
      _this.selectedAddress = address;
    });

    // 监听优惠券选择结果
    uni.$on('couponSelected', function (coupon) {
      console.log('选择的优惠券:', coupon);
      _this.selectedCoupon = coupon;
      _this.calculateDiscount();
    });

    // 获取用户余额信息
    this.getUserBalanceInfo();

    // 获取商户信息
    this.getMerchantDetail();
  },
  onUnload: function onUnload() {
    // 移除事件监听
    uni.$off('addressSelected');
    uni.$off('couponSelected');
  },
  methods: {
    // 返回上一页
    handleBack: function handleBack() {
      uni.navigateBack();
    },
    // 继续点单
    goToMenu: function goToMenu() {
      // 保存当前订单信息到缓存
      uni.setStorageSync('tempOrderData', {
        pickupType: this.pickupType,
        remark: this.remark,
        paymentMethod: this.paymentMethod
      });

      // 保存当前的用餐方式到菜单页面使用
      uni.setStorageSync('diningType', this.pickupType);

      // 跳转到点餐页面
      uni.switchTab({
        url: '/pages/menu/menu'
      });
    },
    // 切换取餐方式
    switchPickupType: function switchPickupType(type) {
      this.pickupType = type;
      if (type === 'takeout') {
        this.generateTimeSlots();
      }
    },
    // 显示时间选择器
    showTimeSelector: function showTimeSelector() {
      this.generateTimeSlots();
      this.tempSelectedTime = this.selectedTime;
      this.showTimePicker = true;
    },
    // 关闭时间选择器
    closeTimeSelector: function closeTimeSelector() {
      this.showTimePicker = false;
      this.tempSelectedTime = this.selectedTime;
    },
    // 选择立即取餐
    selectImmediatePickup: function selectImmediatePickup() {
      this.tempSelectedTime = '';
    },
    // 选择时间
    selectTime: function selectTime(item) {
      this.tempSelectedTime = item.time;
    },
    // 确认时间选择
    confirmTimeSelect: function confirmTimeSelect() {
      // 如果是立即取餐，则清空selectedTime
      if (this.tempSelectedTime === '') {
        this.selectedTime = '';
      } else if (this.tempSelectedTime) {
        this.selectedTime = this.tempSelectedTime;
      } else {
        uni.showToast({
          title: '请选择取餐时间',
          icon: 'none'
        });
        return;
      }
      this.showTimePicker = false;
    },
    // 生成时间段列表
    generateTimeSlots: function generateTimeSlots() {
      var slots = [];
      var now = new Date();
      var startTime = new Date(now.setMinutes(now.getMinutes() + 15));

      // 生成从现在开始到晚上10点的时间段
      for (var i = 0; i < 40; i++) {
        var time = new Date(startTime.getTime() + i * 15 * 60000);
        var hours = time.getHours();

        // 只显示营业时间内的时间段（假设营业时间到22:00）
        if (hours < 22) {
          slots.push({
            time: this.formatTime(time),
            desc: i === 0 ? '最早可取餐时间' : ''
          });
        }
      }
      this.timeSlots = slots;
    },
    // 格式化时间
    formatTime: function formatTime(date) {
      var hours = date.getHours().toString().padStart(2, '0');
      var minutes = date.getMinutes().toString().padStart(2, '0');
      return "".concat(hours, ":").concat(minutes);
    },
    // 选择优惠券
    selectCoupon: function selectCoupon() {
      // 保存当前订单信息到缓存
      uni.setStorageSync('tempOrderData', {
        pickupType: this.pickupType,
        remark: this.remark,
        paymentMethod: this.paymentMethod,
        selectedCoupon: this.selectedCoupon,
        selectedAddress: this.selectedAddress
      });

      // 跳转到优惠券选择页面，并传递订单金额
      uni.navigateTo({
        url: "/pages/coupon/coupon?amount=".concat(this.totalPrice),
        animationType: 'slide-in-right',
        animationDuration: 300
      });
    },
    // 计算优惠金额
    calculateDiscount: function calculateDiscount() {
      if (!this.selectedCoupon) {
        this.discountAmount = 0;
      } else {
        // 获取优惠券金额和限制金额
        var couponAmount = parseFloat(this.selectedCoupon.amount || 0);
        var couponLimit = parseFloat(this.selectedCoupon.limit || 0);

        // 检查订单金额是否满足优惠券使用条件
        if (this.totalPrice >= couponLimit) {
          this.discountAmount = couponAmount;
        } else {
          // 如果不满足条件，提示用户并清除优惠券选择
          uni.showToast({
            title: "\u8BA2\u5355\u91D1\u989D\u9700\u6EE1".concat(couponLimit, "\u5143\u624D\u80FD\u4F7F\u7528\u6B64\u4F18\u60E0\u5238"),
            icon: 'none',
            duration: 2000
          });
          this.selectedCoupon = null;
          this.discountAmount = 0;
        }
      }

      // 计算最终价格
      this.finalPrice = Math.max(0, this.totalPrice - this.discountAmount).toFixed(2);
    },
    // 添加备注
    addRemark: function addRemark() {
      this.showRemarkPopup = true;
    },
    // 切换支付方式
    switchPayment: function switchPayment(method) {
      this.paymentMethod = method;
    },
    // 选择地址
    selectAddress: function selectAddress() {
      // 保存当前订单信息到缓存
      uni.setStorageSync('tempOrderData', {
        pickupType: this.pickupType,
        remark: this.remark,
        paymentMethod: this.paymentMethod,
        selectedTime: this.selectedTime,
        selectedAddress: this.selectedAddress
      });

      // 跳转到地址选择页面
      uni.navigateTo({
        url: '/pages/address/address'
      });
    },
    // 提交订单
    submitOrder: function submitOrder() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        var orderData, res, orderNo, orderList, newOrder, payRes, isSuccess;
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!_this2.isSubmitting) {
                  _context.next = 4;
                  break;
                }
                console.log('订单正在提交中，请勿重复点击');
                uni.showToast({
                  title: '请勿重复提交',
                  icon: 'none'
                });
                return _context.abrupt("return");
              case 4:
                // 设置提交状态为true
                _this2.isSubmitting = true;
                _context.prev = 5;
                if (!(_this2.pickupType === 'takeout' && !_this2.selectedAddress)) {
                  _context.next = 9;
                  break;
                }
                _this2.isSubmitting = false; // 重置提交状态
                return _context.abrupt("return", uni.showToast({
                  title: '请选择收货地址',
                  icon: 'none'
                }));
              case 9:
                // 检查并确保购物车中的商品都有规格信息
                _this2.cartList.forEach(function (item) {
                  if (!item.props_text && item.specs) {
                    item.props_text = item.specs;
                    console.log('提交前修复商品规格信息:', item.name, item.props_text);
                  } else if (!item.props_text) {
                    item.props_text = '默认规格';
                    item.specs = '默认规格';
                    console.log('提交前设置商品默认规格:', item.name);
                  }
                });

                // 构建订单数据
                orderData = {
                  products: _this2.cartList.map(function (item) {
                    console.log('商品规格信息:', item.name, 'props_text:', item.props_text, 'specs:', item.specs);
                    return {
                      id: item.id,
                      name: item.name,
                      price: item.price,
                      count: item.count,
                      totalPrice: item.totalPrice,
                      specs: item.props_text || item.specs || '默认规格',
                      image: item.image || '',
                      spec_type: item.spec_type || 'single',
                      props_text: item.props_text || item.specs || '默认规格'
                    };
                  }),
                  totalPrice: _this2.totalPrice,
                  finalPrice: _this2.finalPrice,
                  discountAmount: _this2.discountAmount,
                  diningType: _this2.pickupType,
                  remark: _this2.remark,
                  address: _this2.selectedAddress ? {
                    name: _this2.selectedAddress.name || '',
                    phone: _this2.selectedAddress.phone || '',
                    address: _this2.formatAddress(_this2.selectedAddress)
                  } : null,
                  // 添加地址信息
                  phone: _this2.selectedAddress ? _this2.selectedAddress.phone : '',
                  payment_method: _this2.paymentMethod,
                  // 添加支付方式
                  coupon: _this2.selectedCoupon ? {
                    id: _this2.selectedCoupon.id,
                    coupon_id: _this2.selectedCoupon.coupon_id || _this2.selectedCoupon.id,
                    amount: _this2.selectedCoupon.amount,
                    name: _this2.selectedCoupon.name
                  } : null // 添加优惠券信息
                }; // 调用添加订单接口

                console.log('提交订单数据:', JSON.stringify(orderData));
                _context.next = 14;
                return (0, _order.addOrder)(orderData);
              case 14:
                res = _context.sent;
                console.log('订单提交响应:', JSON.stringify(res));

                // 检查响应中是否包含订单号

                // 尝试从不同位置获取订单号
                if (res.data && res.data.order_no) {
                  orderNo = res.data.order_no;
                } else if (res.data && res.data.orderNo) {
                  orderNo = res.data.orderNo;
                } else if (res.order_no) {
                  orderNo = res.order_no;
                } else if (res.orderNo) {
                  orderNo = res.orderNo;
                } else if (res.data && res.data.id) {
                  // 有些API可能使用id作为订单号
                  orderNo = res.data.id;
                } else {
                  // 如果找不到订单号，使用时间戳作为临时订单号
                  orderNo = 'temp_' + new Date().getTime();
                  console.warn('未找到有效订单号，使用临时ID:', orderNo);
                }

                // 获取正确的订单号
                console.log('最终使用的订单号:', orderNo);
                console.log('获取到的订单号:', orderNo);

                // 保存订单到本地存储，用于订单详情页显示
                orderList = uni.getStorageSync('orderList') || [];
                newOrder = _objectSpread(_objectSpread({}, orderData), {}, {
                  order_no: orderNo,
                  // 使用后端API返回的订单号
                  orderNo: orderNo,
                  // 保留兼容性
                  createTime: new Date().toLocaleString(),
                  status: 'processing',
                  pay_status: 0,
                  // 初始支付状态为未支付
                  type: _this2.pickupType,
                  dining_type: _this2.pickupType,
                  // 添加与API一致的字段
                  products: orderData.products.map(function (item) {
                    return _objectSpread(_objectSpread({}, item), {}, {
                      specs: item.props_text || ''
                    });
                  })
                });
                orderList.unshift(newOrder);
                uni.setStorageSync('orderList', orderList);

                // 如果用户选择了余额支付，调用余额支付API
                if (!(_this2.paymentMethod === 'balance')) {
                  _context.next = 51;
                  break;
                }
                // 显示加载提示
                uni.showLoading({
                  title: '余额支付中...'
                });
                _context.prev = 25;
                // 调用余额支付API
                console.log('调用余额支付API，订单号:', orderNo);
                _context.next = 29;
                return (0, _order.payOrderWithBalance)(orderNo);
              case 29:
                payRes = _context.sent;
                console.log('余额支付响应:', JSON.stringify(payRes));

                // 关闭加载提示
                uni.hideLoading();

                // 检查支付结果 - 支持多种响应格式
                isSuccess = payRes.code === 1;
                if (!isSuccess) {
                  _context.next = 39;
                  break;
                }
                // 支付成功
                _this2.handlePaymentSuccess(newOrder, orderList);

                // 刷新用户余额
                _context.next = 37;
                return _this2.getUserBalanceInfo();
              case 37:
                _context.next = 41;
                break;
              case 39:
                // 如果为2 跳转充值页面
                if (payRes.code === 2) {
                  // 显示确认对话框，让用户选择是否跳转到充值页面
                  uni.showModal({
                    title: '余额不足',
                    content: '您的余额不足，是否前往充值？',
                    confirmText: '去充值',
                    cancelText: '取消',
                    success: function success(res) {
                      if (res.confirm) {
                        // 用户点击确认，跳转到充值页面
                        uni.navigateTo({
                          url: '/pages/my/recharge'
                        });
                      }
                    }
                  });
                }
                // 支付失败
                uni.showToast({
                  title: payRes.msg || payRes.message || '余额支付失败',
                  icon: 'none'
                });
              case 41:
                _context.next = 49;
                break;
              case 43:
                _context.prev = 43;
                _context.t0 = _context["catch"](25);
                // 关闭加载提示
                uni.hideLoading();
                console.error('余额支付过程中出错:', _context.t0);
                uni.showToast({
                  title: _context.t0.message || _context.t0.msg || '余额支付失败',
                  icon: 'none'
                });

                // 出错后，仍然跳转到订单详情页
                setTimeout(function () {
                  uni.redirectTo({
                    url: "/pages/order/detail?order_no=".concat(orderNo)
                  });
                }, 1500);
              case 49:
                _context.next = 52;
                break;
              case 51:
                // 微信支付处理
                _this2.handleWxPay(orderNo, newOrder, orderList);
              case 52:
                _context.next = 58;
                break;
              case 54:
                _context.prev = 54;
                _context.t1 = _context["catch"](5);
                console.error('提交订单失败:', _context.t1);
                uni.showToast({
                  title: _context.t1.message || _context.t1.msg || '下单失败',
                  icon: 'none'
                });
              case 58:
                _context.prev = 58;
                // 无论成功或失败，都要重置提交状态
                setTimeout(function () {
                  _this2.isSubmitting = false;
                  console.log('重置订单提交状态');
                }, 2000); // 延迟2秒重置，防止快速连续点击
                return _context.finish(58);
              case 61:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[5, 54, 58, 61], [25, 43]]);
      }))();
    },
    // 关闭备注弹窗
    closeRemarkPopup: function closeRemarkPopup() {
      this.showRemarkPopup = false;
    },
    // 确认备注
    confirmRemark: function confirmRemark() {
      if (this.tempRemark) {
        this.remark = this.tempRemark;
        this.showRemarkPopup = false;
        uni.showToast({
          title: '备注已添加',
          icon: 'none'
        });
      }
    },
    // 添加获取用户余额的方法
    getUserBalanceInfo: function getUserBalanceInfo() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return (0, _user.getUserBalance)();
              case 3:
                res = _context2.sent;
                console.log('获取余额返回数据:', JSON.stringify(res));

                // 根据接口返回格式解析余额数据
                // {"code":1,"msg":"Get balance successful","time":"1750841022","data":"1000.00"}
                if (res.code === 1 && res.data) {
                  // data直接是余额字符串
                  if (typeof res.data === 'string') {
                    _this3.userBalance = parseFloat(res.data);
                    console.log('从字符串中解析余额:', _this3.userBalance);
                  } else if (typeof res.data === 'number') {
                    _this3.userBalance = res.data;
                    console.log('获取到数字余额:', _this3.userBalance);
                  } else if ((0, _typeof2.default)(res.data) === 'object' && res.data.balance) {
                    _this3.userBalance = parseFloat(res.data.balance);
                    console.log('从对象中解析余额:', _this3.userBalance);
                  }
                } else {
                  console.warn('API返回格式不符合预期:', res);
                  _this3.userBalance = 0;
                }

                // 确保余额是有效数字
                if (isNaN(_this3.userBalance)) {
                  _this3.userBalance = 0;
                }
                console.log('最终用户余额:', _this3.userBalance);
                _context2.next = 14;
                break;
              case 10:
                _context2.prev = 10;
                _context2.t0 = _context2["catch"](0);
                console.error('获取用户余额失败:', _context2.t0);
                _this3.userBalance = 0;
              case 14:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 10]]);
      }))();
    },
    // 格式化地址
    formatAddress: function formatAddress(address) {
      if (!address) return '';

      // 构建省市区字符串
      var region = [address.province || '', address.city || '', address.district || ''].filter(Boolean).join(' ');

      // 添加详细地址
      return "".concat(region, " ").concat(address.address || '').trim();
    },
    // 微信支付处理
    handleWxPay: function handleWxPay(orderNo, newOrder, orderList) {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var payParamsRes, payParams;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.prev = 0;
                _context4.next = 3;
                return (0, _order.getWxPayParams)(orderNo);
              case 3:
                payParamsRes = _context4.sent;
                console.log('获取微信支付参数返回:', JSON.stringify(payParamsRes));

                // 检查API返回是否成功
                if (!(payParamsRes.code !== 1 || !payParamsRes.data)) {
                  _context4.next = 7;
                  break;
                }
                throw new Error(payParamsRes.msg || '获取支付参数失败');
              case 7:
                // 提取支付参数
                payParams = payParamsRes.data; // 发起微信支付
                uni.requestPayment({
                  provider: 'wxpay',
                  timeStamp: payParams.timeStamp,
                  nonceStr: payParams.nonceStr,
                  package: payParams.package,
                  signType: payParams.signType,
                  paySign: payParams.paySign,
                  success: function () {
                    var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(res) {
                      var queryRes, isPaid;
                      return _regenerator.default.wrap(function _callee3$(_context3) {
                        while (1) {
                          switch (_context3.prev = _context3.next) {
                            case 0:
                              console.log('微信支付成功:', JSON.stringify(res));

                              // 查询支付结果（有些系统可能需要后端确认支付状态）
                              _context3.prev = 1;
                              uni.showLoading({
                                title: '确认支付结果...'
                              });
                              _context3.next = 5;
                              return (0, _order.queryWxPayResult)(orderNo);
                            case 5:
                              queryRes = _context3.sent;
                              uni.hideLoading();

                              // 根据pay_status判断支付状态，0=未支付，1=已支付
                              isPaid = queryRes.code === 1 && queryRes.data && (queryRes.data.paid === true || queryRes.data.order && queryRes.data.order.pay_status === 1 || queryRes.data.pay_status === 1);
                              if (isPaid) {
                                // 支付成功处理
                                _this4.handlePaymentSuccess(newOrder, orderList);
                              } else {
                                // 虽然微信返回成功，但后端查询失败的情况
                                console.warn('支付确认失败:', JSON.stringify(queryRes));
                                // 仍然按支付成功处理，等待后端异步通知
                                _this4.handlePaymentSuccess(newOrder, orderList);
                              }
                              _context3.next = 16;
                              break;
                            case 11:
                              _context3.prev = 11;
                              _context3.t0 = _context3["catch"](1);
                              uni.hideLoading();
                              console.error('查询支付结果失败:', _context3.t0);
                              // 仍然按支付成功处理，等待后端异步通知
                              _this4.handlePaymentSuccess(newOrder, orderList);
                            case 16:
                            case "end":
                              return _context3.stop();
                          }
                        }
                      }, _callee3, null, [[1, 11]]);
                    }));
                    function success(_x) {
                      return _success.apply(this, arguments);
                    }
                    return success;
                  }(),
                  fail: function fail(err) {
                    console.error('微信支付失败:', JSON.stringify(err));

                    // 处理用户取消支付的情况
                    if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
                      uni.showToast({
                        title: '支付已取消',
                        icon: 'none'
                      });
                    } else {
                      uni.showToast({
                        title: '支付失败: ' + (err.errMsg || '未知错误'),
                        icon: 'none',
                        duration: 2000
                      });
                    }

                    // 支付失败后，仍然跳转到订单详情页
                    setTimeout(function () {
                      uni.redirectTo({
                        url: "/pages/order/detail?order_no=".concat(orderNo)
                      });
                    }, 1500);
                  },
                  complete: function complete() {
                    // 支付流程完成
                    console.log('微信支付流程完成');
                  }
                });
                _context4.next = 17;
                break;
              case 11:
                _context4.prev = 11;
                _context4.t0 = _context4["catch"](0);
                uni.hideLoading();
                console.error('微信支付处理异常:', _context4.t0);
                uni.showToast({
                  title: _context4.t0.message || '支付失败，请稍后再试',
                  icon: 'none',
                  duration: 2000
                });

                // 异常情况下，也跳转到订单详情页
                setTimeout(function () {
                  uni.redirectTo({
                    url: "/pages/order/detail?order_no=".concat(orderNo)
                  });
                }, 1500);
              case 17:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4, null, [[0, 11]]);
      }))();
    },
    // 处理支付成功逻辑
    handlePaymentSuccess: function handlePaymentSuccess(newOrder, orderList) {
      // 显示支付成功提示
      uni.showToast({
        title: '支付成功',
        icon: 'none'
      });

      // 更新订单状态
      newOrder.status = 'paid';
      newOrder.pay_status = 1; // 设置支付状态为已支付
      orderList[0] = newOrder;
      uni.setStorageSync('orderList', orderList);

      // 清空购物车数据
      uni.removeStorageSync('cartData');
      uni.removeStorageSync('cartList');
      uni.removeStorageSync('cartTotal');
      uni.removeStorageSync('diningType');
      uni.removeStorageSync('tempOrderData');

      // 通知菜单页面清空购物车
      uni.$emit('clearCart');

      // 跳转到订单详情页
      setTimeout(function () {
        uni.redirectTo({
          url: "/pages/order/detail?order_no=".concat(newOrder.order_no)
        });
      }, 1500);
    },
    // 获取商户信息
    getMerchantDetail: function getMerchantDetail() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.prev = 0;
                _context5.next = 3;
                return (0, _merchant.getMerchantInfo)();
              case 3:
                res = _context5.sent;
                console.log('获取商户信息返回:', JSON.stringify(res));
                if (res.code === 1 && res.data) {
                  // 更新商户信息
                  _this5.storeInfo = {
                    name: res.data.name || res.data.store_name || res.data.title || '',
                    address: res.data.address || '',
                    phone: res.data.phone || res.data.tel || '',
                    pickupTime: '尽快取餐'
                  };
                } else {
                  console.warn('商户信息获取失败:', res);
                }
                _context5.next = 12;
                break;
              case 8:
                _context5.prev = 8;
                _context5.t0 = _context5["catch"](0);
                console.error('获取商户信息异常:', _context5.t0);
                // 设置默认值，防止页面显示异常
                _this5.storeInfo = {
                  name: 'KKmall京基店2楼188号',
                  pickupTime: '尽快取餐'
                };
              case 12:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5, null, [[0, 8]]);
      }))();
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 213:
/*!************************************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?vue&type=style&index=0&id=5c101a1c&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_style_index_0_id_5c101a1c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./orderSubmit.vue?vue&type=style&index=0&id=5c101a1c&lang=scss&scoped=true& */ 214);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_style_index_0_id_5c101a1c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_style_index_0_id_5c101a1c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_style_index_0_id_5c101a1c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_style_index_0_id_5c101a1c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_orderSubmit_vue_vue_type_style_index_0_id_5c101a1c_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 214:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/orderSubmit.vue?vue&type=style&index=0&id=5c101a1c&lang=scss&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[206,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/orderSubmit.js.map