@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-30c7cdd4 {
  background-color: #f8f9fa;
  min-height: 100vh;
}
.custom-navbar.data-v-30c7cdd4 {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}
.custom-navbar .navbar-content.data-v-30c7cdd4 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}
.custom-navbar .navbar-content .navbar-left.data-v-30c7cdd4, .custom-navbar .navbar-content .navbar-right.data-v-30c7cdd4 {
  width: 60px;
}
.custom-navbar .navbar-content .navbar-title.data-v-30c7cdd4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.content.data-v-30c7cdd4 {
  padding: 20px 16px;
}
.page-title.data-v-30c7cdd4 {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 8px;
}
.page-subtitle.data-v-30c7cdd4 {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-bottom: 32px;
}
.section.data-v-30c7cdd4 {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
.section-title.data-v-30c7cdd4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 8px 0;
  cursor: pointer;
}
.section-title .title-left.data-v-30c7cdd4 {
  display: flex;
  align-items: center;
}
.section-title .title-left text.data-v-30c7cdd4 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-left: 8px;
}
.work-list .work-item.data-v-30c7cdd4 {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}
.work-list .work-item.data-v-30c7cdd4:last-child {
  margin-bottom: 0;
}
.work-header.data-v-30c7cdd4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.work-header .work-name.data-v-30c7cdd4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.work-header .license-badge.data-v-30c7cdd4 {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background-color: #f5f5f5;
  color: #666;
}
.work-header .license-badge.license-mit.data-v-30c7cdd4 {
  background-color: #e8f5e8;
  color: #2e7d32;
}
.work-header .license-badge.license-cc.data-v-30c7cdd4 {
  background-color: #fff3e0;
  color: #f57c00;
}
.work-header .license-badge.license-apache.data-v-30c7cdd4 {
  background-color: #e3f2fd;
  color: #1976d2;
}
.work-description.data-v-30c7cdd4 {
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #FF6B35;
}
.work-description text.data-v-30c7cdd4 {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}
.work-details .detail-row.data-v-30c7cdd4 {
  display: flex;
  margin-bottom: 8px;
}
.work-details .detail-row.data-v-30c7cdd4:last-child {
  margin-bottom: 0;
}
.work-details .detail-row .label.data-v-30c7cdd4 {
  font-size: 14px;
  color: #666;
  width: 80px;
  flex-shrink: 0;
}
.work-details .detail-row .value.data-v-30c7cdd4 {
  font-size: 14px;
  color: #333;
  flex: 1;
}
.work-details .detail-row .link.data-v-30c7cdd4 {
  font-size: 14px;
  color: #1976d2;
  text-decoration: underline;
}
.original-content .copyright-text.data-v-30c7cdd4 {
  margin-bottom: 12px;
  line-height: 1.6;
}
.original-content .copyright-text.data-v-30c7cdd4:last-child {
  margin-bottom: 0;
}
.original-content .copyright-text text.data-v-30c7cdd4 {
  font-size: 14px;
  color: #333;
}
.original-content .copyright-text .highlight.data-v-30c7cdd4 {
  color: #FF6B35;
  font-weight: 600;
}
.cc-license .license-intro.data-v-30c7cdd4 {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff3e0;
  border-radius: 8px;
  border-left: 4px solid #FF9800;
}
.cc-license .license-intro .intro-text.data-v-30c7cdd4 {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}
.cc-license .license-features.data-v-30c7cdd4, .cc-license .license-conditions.data-v-30c7cdd4 {
  margin-bottom: 20px;
}
.cc-license .license-features .feature-title.data-v-30c7cdd4, .cc-license .license-features .condition-title.data-v-30c7cdd4, .cc-license .license-conditions .feature-title.data-v-30c7cdd4, .cc-license .license-conditions .condition-title.data-v-30c7cdd4 {
  margin-bottom: 12px;
}
.cc-license .license-features .feature-title text.data-v-30c7cdd4, .cc-license .license-features .condition-title text.data-v-30c7cdd4, .cc-license .license-conditions .feature-title text.data-v-30c7cdd4, .cc-license .license-conditions .condition-title text.data-v-30c7cdd4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
.cc-license .license-features .feature-item.data-v-30c7cdd4, .cc-license .license-features .condition-item.data-v-30c7cdd4, .cc-license .license-conditions .feature-item.data-v-30c7cdd4, .cc-license .license-conditions .condition-item.data-v-30c7cdd4 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
}
.cc-license .license-features .feature-item.data-v-30c7cdd4:last-child, .cc-license .license-features .condition-item.data-v-30c7cdd4:last-child, .cc-license .license-conditions .feature-item.data-v-30c7cdd4:last-child, .cc-license .license-conditions .condition-item.data-v-30c7cdd4:last-child {
  margin-bottom: 0;
}
.cc-license .license-features .feature-item .feature-text.data-v-30c7cdd4, .cc-license .license-features .feature-item .condition-text.data-v-30c7cdd4, .cc-license .license-features .condition-item .feature-text.data-v-30c7cdd4, .cc-license .license-features .condition-item .condition-text.data-v-30c7cdd4, .cc-license .license-conditions .feature-item .feature-text.data-v-30c7cdd4, .cc-license .license-conditions .feature-item .condition-text.data-v-30c7cdd4, .cc-license .license-conditions .condition-item .feature-text.data-v-30c7cdd4, .cc-license .license-conditions .condition-item .condition-text.data-v-30c7cdd4 {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-left: 8px;
  flex: 1;
}
.cc-license .license-features .feature-item .feature-bold.data-v-30c7cdd4, .cc-license .license-features .feature-item .condition-bold.data-v-30c7cdd4, .cc-license .license-features .condition-item .feature-bold.data-v-30c7cdd4, .cc-license .license-features .condition-item .condition-bold.data-v-30c7cdd4, .cc-license .license-conditions .feature-item .feature-bold.data-v-30c7cdd4, .cc-license .license-conditions .feature-item .condition-bold.data-v-30c7cdd4, .cc-license .license-conditions .condition-item .feature-bold.data-v-30c7cdd4, .cc-license .license-conditions .condition-item .condition-bold.data-v-30c7cdd4 {
  font-weight: 600;
  color: #FF6B35;
}
.cc-license .license-links.data-v-30c7cdd4 {
  border-top: 1px solid #eee;
  padding-top: 16px;
}
.cc-license .license-links .link-item.data-v-30c7cdd4 {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}
.cc-license .license-links .link-item.data-v-30c7cdd4:last-child {
  margin-bottom: 0;
}
.cc-license .license-links .link-item .link-text.data-v-30c7cdd4 {
  font-size: 14px;
  color: #1976d2;
  margin-left: 8px;
  text-decoration: underline;
}
.cc-license .license-links .link-item.data-v-30c7cdd4:active {
  opacity: 0.7;
}
.disclaimer .disclaimer-text.data-v-30c7cdd4 {
  margin-bottom: 12px;
  line-height: 1.6;
}
.disclaimer .disclaimer-text.data-v-30c7cdd4:last-child {
  margin-bottom: 0;
}
.disclaimer .disclaimer-text text.data-v-30c7cdd4 {
  font-size: 14px;
  color: #333;
}
.update-info.data-v-30c7cdd4 {
  text-align: center;
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}
.update-info text.data-v-30c7cdd4 {
  font-size: 12px;
  color: #999;
}

