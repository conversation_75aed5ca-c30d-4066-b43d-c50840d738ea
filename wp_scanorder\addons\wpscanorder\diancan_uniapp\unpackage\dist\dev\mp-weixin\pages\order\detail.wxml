<view class="page data-v-57d42baa"><view class="header data-v-57d42baa"><view class="nav-bar data-v-57d42baa"><view data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" class="left data-v-57d42baa" bindtap="__e"><u-icon vue-id="a2b0478a-1" name="arrow-left" color="#333" size="20" class="data-v-57d42baa" bind:__l="__l"></u-icon></view><text class="title data-v-57d42baa">订单详情</text><view data-event-opts="{{[['tap',[['refreshOrderData',['$event']]]]]}}" class="right data-v-57d42baa" bindtap="__e"><u-icon vue-id="a2b0478a-2" name="reload" color="#333" size="20" class="data-v-57d42baa" bind:__l="__l"></u-icon></view></view></view><view class="{{['status-card','data-v-57d42baa',(!isPaid)?'unpaid-card':'',(order.status==='paid')?'paid-card':'',(order.status==='cooking')?'cooking-card':'',(order.status==='cooked')?'cooked-card':'',(order.status==='delivering')?'delivering-card':'',(order.status==='completed')?'completed-card':'',(order.status==='cancelled')?'cancelled-card':'']}}"><block wx:if="{{$root.g0}}"><view class="status-info data-v-57d42baa"><block wx:if="{{order.status==='pending'}}"><text class="status data-v-57d42baa">待支付</text></block><block wx:else><block wx:if="{{order.status==='paid'}}"><text class="status data-v-57d42baa">已支付</text></block><block wx:else><block wx:if="{{order.status==='cooking'}}"><text class="status data-v-57d42baa">制作中</text></block><block wx:else><block wx:if="{{order.status==='cooked'}}"><text class="status data-v-57d42baa">制作完成</text></block><block wx:else><block wx:if="{{order.status==='delivering'}}"><text class="status data-v-57d42baa">配送中</text></block><block wx:else><block wx:if="{{order.status==='completed'}}"><text class="status data-v-57d42baa">已完成</text></block><block wx:else><block wx:if="{{order.status==='cancelled'}}"><text class="status data-v-57d42baa">已取消</text></block><block wx:else><text class="status data-v-57d42baa">{{isPaid?'已支付':'待支付'}}</text></block></block></block></block></block></block></block><block wx:if="{{order.status==='pending'}}"><text class="desc data-v-57d42baa">请尽快完成支付，订单将在30分钟后自动取消</text></block><block wx:else><block wx:if="{{order.status==='cancelled'}}"><text class="desc data-v-57d42baa">订单已取消，请重新下单</text></block><block wx:else><text class="desc data-v-57d42baa">{{$root.m0}}</text></block></block><block wx:if="{{order.status!=='completed'&&order.status!=='cancelled'}}"><view class="auto-refresh-tip data-v-57d42baa"><text class="data-v-57d42baa">订单状态自动刷新中</text></view></block></view></block><block wx:else><view class="status-info data-v-57d42baa"><text class="status data-v-57d42baa">加载中...</text><text class="desc data-v-57d42baa">正在获取订单信息</text></view></block></view><view class="order-card data-v-57d42baa"><view class="card-title data-v-57d42baa">订单信息</view><view class="info-item data-v-57d42baa"><text class="label data-v-57d42baa">取餐号</text><text class="value data-v-57d42baa">{{order.pickup_code||order.pickup_code||''}}</text></view><view class="info-item data-v-57d42baa"><text class="label data-v-57d42baa">订单编号</text><text class="value data-v-57d42baa">{{order.order_no||order.orderNo}}</text></view><view class="info-item data-v-57d42baa"><text class="label data-v-57d42baa">下单时间</text><text class="value data-v-57d42baa">{{order.createtime_text||order.createTime}}</text></view><view class="info-item data-v-57d42baa"><text class="label data-v-57d42baa">用餐方式</text><text class="value data-v-57d42baa">{{(order.dining_type||order.type)==='dine-in'?'堂食':'外卖'}}</text></view><block wx:if="{{(order.dining_type||order.type)==='takeout'}}"><view class="info-item data-v-57d42baa"><text class="label data-v-57d42baa">配送地址</text><text class="value data-v-57d42baa">{{order.address?$root.g1:'未设置地址'}}</text></view></block><view class="info-item data-v-57d42baa"><text class="label data-v-57d42baa">备注信息</text><text class="value data-v-57d42baa">{{order.remark||'无'}}</text></view></view><view class="product-card data-v-57d42baa"><view class="card-title data-v-57d42baa">商品信息</view><view class="product-list data-v-57d42baa"><block wx:for="{{order.products}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="product-item data-v-57d42baa"><text class="product-name data-v-57d42baa">{{item.product_name||item.name}}</text><text class="product-specs data-v-57d42baa">{{item.props_text||item.specs||''}}</text><view class="product-price data-v-57d42baa"><text class="data-v-57d42baa">{{"¥"+(item.price||item.product_price||0)}}</text><text class="count data-v-57d42baa">{{"x"+(item.quantity||item.count||1)}}</text></view></view></block></view><view class="price-info data-v-57d42baa"><view class="price-item data-v-57d42baa"><text class="label data-v-57d42baa">商品小计</text><text class="value data-v-57d42baa">{{"¥"+$root.g2}}</text></view><view class="price-item data-v-57d42baa"><text class="label data-v-57d42baa">优惠金额</text><text class="value data-v-57d42baa">{{"-¥"+$root.g3}}</text></view><view class="price-item total data-v-57d42baa"><text class="label data-v-57d42baa">实付金额</text><text class="{{['value','data-v-57d42baa',(!isPaid)?'unpaid':'']}}">{{"¥"+$root.g4}}</text></view><view class="price-item payment-status data-v-57d42baa"><text class="label data-v-57d42baa">支付状态</text><text class="{{['value','data-v-57d42baa',(isPaid)?'paid':'',(!isPaid)?'unpaid':'']}}">{{isPaid?'已支付':'待支付'}}</text></view></view></view><view class="bottom-bar data-v-57d42baa"><view data-event-opts="{{[['tap',[['reorder',['$event']]]]]}}" class="btn outline data-v-57d42baa" bindtap="__e">再来一单</view><block wx:if="{{$root.g5}}"><block wx:if="{{$root.g6}}"><view data-event-opts="{{[['tap',[['cancelOrder',['$event']]]]]}}" class="btn outline data-v-57d42baa" bindtap="__e">取消订单</view></block><block wx:if="{{order.status==='pending'}}"><view data-event-opts="{{[['tap',[['goPay',['$event']]]]]}}" class="btn primary data-v-57d42baa" bindtap="__e">去支付</view></block><block wx:else><block wx:if="{{$root.g7}}"><view data-event-opts="{{[['tap',[['checkProgress',['$event']]]]]}}" class="btn primary data-v-57d42baa" bindtap="__e">查看进度</view></block><block wx:else><block wx:if="{{order.status==='completed'}}"><view data-event-opts="{{[['tap',[['goComment',['$event']]]]]}}" hidden="{{!(false)}}" class="btn primary data-v-57d42baa" bindtap="__e">评价订单</view></block></block></block></block></view></view>