{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/record.vue?8183", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/record.vue?50ec", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/record.vue?c4a8", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/record.vue?f068", "uni-app:///pages/points/record.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/record.vue?f23f", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/record.vue?de16"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "recordList", "loading", "isRefreshing", "page", "limit", "hasMore", "total", "showLoadingMore", "isLoadingMore", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "handleBack", "uni", "handlePullRefresh", "title", "icon", "duration", "console", "handleReachBottom", "setTimeout", "getRecordList", "params", "res", "records", "processedRecords", "item", "status", "getStatusText", "formatTime", "handleImageError", "target"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACwEnrB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAEAD;kBACAE;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAL;kBACAE;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACAH;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAM;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAGA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAD;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBACAE;kBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;kBACA;gBACA;gBAEAC;kBACApB;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAoB;gBAEA;kBACAzB,iBAEA;kBACA;;kBAEA;kBACA0B,2BAEA;kBACAC;oBAAA,uCACAC;sBACAC;oBAAA;kBAAA,CACA,GAEA;;kBACA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;kBACAd;oBACAE;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;gBACAL;kBACAE;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7PA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/points/record.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/points/record.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./record.vue?vue&type=template&id=18f9de58&scoped=true&\"\nvar renderjs\nimport script from \"./record.vue?vue&type=script&lang=js&\"\nexport * from \"./record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./record.vue?vue&type=style&index=0&id=18f9de58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18f9de58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/points/record.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=template&id=18f9de58&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && _vm.recordList.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.recordList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.formatTime(item.createtime)\n        var m1 = _vm.getStatusText(item.status)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n        }\n      })\n    : null\n  var g1 = !_vm.loading && _vm.recordList.length === 0\n  var g2 = !_vm.loading && _vm.recordList.length > 0 && !_vm.hasMore\n  var g3 =\n    !_vm.loading &&\n    _vm.recordList.length > 0 &&\n    _vm.hasMore &&\n    _vm.showLoadingMore\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">兑换记录</text>\r\n        <view class=\"empty-placeholder\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 记录列表 -->\r\n    <view class=\"record-list\" v-if=\"!loading && recordList.length > 0\">\r\n      <view class=\"record-item\" v-for=\"(item, index) in recordList\" :key=\"index\">\r\n        <view class=\"item-left\">\r\n          <image class=\"item-image\" :src=\"item.image || '/static/images/placeholder.png'\" @error=\"handleImageError\" mode=\"aspectFill\" />\r\n        </view>\r\n        \r\n        <view class=\"item-center\">\r\n          <view class=\"item-name\">{{item.name}}</view>\r\n          \r\n          <view class=\"item-tag\" :class=\"{'coupon-tag': item.type === 'coupon', 'product-tag': item.type === 'product'}\">\r\n            <text>{{item.type === 'coupon' ? '优惠券' : '实物商品'}}</text>\r\n          </view>\r\n          \r\n          <view class=\"item-time\">{{formatTime(item.createtime)}}</view>\r\n        </view>\r\n        \r\n        <view class=\"item-right\">\r\n          <view class=\"item-status\" :class=\"{'success': item.status === 'success', 'pending': item.status === 'pending', 'failed': item.status === 'failed'}\">\r\n            <text>{{getStatusText(item.status)}}</text>\r\n          </view>\r\n          <view class=\"item-points\">\r\n            <text>-{{item.points}}积分</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 加载中状态 -->\r\n    <view v-if=\"loading\" class=\"loading-container\">\r\n      <view class=\"loading-spinner\"></view>\r\n      <text>正在加载...</text>\r\n    </view>\r\n    \r\n    <!-- 空状态 -->\r\n    <view v-if=\"!loading && recordList.length === 0\" class=\"empty-container\">\r\n      <image class=\"empty-image\" src=\"/static/order/e186e04e8774da64b58c96a8bb479840.png\" mode=\"aspectFit\" />\r\n      <text>暂无兑换记录</text>\r\n      <text class=\"empty-tips\">去积分商城兑换商品吧</text>\r\n    </view>\r\n    \r\n    <!-- 底部提示 -->\r\n    <view v-if=\"!loading && recordList.length > 0 && !hasMore\" class=\"bottom-tip\">\r\n      <text>—— 已经到底了 ——</text>\r\n    </view>\r\n    \r\n    <!-- 上拉加载更多提示 -->\r\n    <view v-if=\"!loading && recordList.length > 0 && hasMore && showLoadingMore\" class=\"bottom-tip\">\r\n      <view class=\"loading-more\">\r\n        <view class=\"loading-dot\"></view>\r\n        <text>正在加载更多...</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getMyExchanges } from '@/api/mall';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      recordList: [],\r\n      loading: true,\r\n      isRefreshing: false,\r\n      page: 1,\r\n      limit: 10,\r\n      hasMore: true,\r\n      total: 0,\r\n      showLoadingMore: false,\r\n      isLoadingMore: false\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    this.getRecordList();\r\n  },\r\n  \r\n  // 添加下拉刷新\r\n  onPullDownRefresh() {\r\n    this.handlePullRefresh();\r\n  },\r\n  \r\n  // 添加上拉加载更多\r\n  onReachBottom() {\r\n    this.handleReachBottom();\r\n  },\r\n  \r\n  methods: {\r\n    handleBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 处理下拉刷新\r\n    async handlePullRefresh() {\r\n      if (this.isRefreshing) return;\r\n      \r\n      this.isRefreshing = true;\r\n      \r\n      try {\r\n        // 重置页码\r\n        this.page = 1;\r\n        \r\n        // 请求新数据\r\n        await this.getRecordList();\r\n        \r\n        uni.showToast({\r\n          title: '刷新成功',\r\n          icon: 'none',\r\n          duration: 1000\r\n        });\r\n      } catch (error) {\r\n        console.error('下拉刷新异常:', error);\r\n        uni.showToast({\r\n          title: '刷新失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.isRefreshing = false;\r\n        uni.stopPullDownRefresh();\r\n      }\r\n    },\r\n    \r\n    // 处理上拉加载更多\r\n    async handleReachBottom() {\r\n      if (this.hasMore && !this.loading && !this.isLoadingMore) {\r\n        this.isLoadingMore = true;\r\n        this.showLoadingMore = true;\r\n        \r\n        try {\r\n          this.page++;\r\n          await this.getRecordList();\r\n        } catch (error) {\r\n          console.error('上拉加载更多异常:', error);\r\n          // 加载失败时恢复页码\r\n          this.page--;\r\n        } finally {\r\n          this.isLoadingMore = false;\r\n          // 延迟隐藏加载提示，提升用户体验\r\n          setTimeout(() => {\r\n            this.showLoadingMore = false;\r\n          }, 500);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取兑换记录列表\r\n    async getRecordList() {\r\n      try {\r\n        if (this.page === 1) {\r\n          this.loading = true;\r\n        }\r\n        \r\n        const params = {\r\n          page: this.page,\r\n          limit: this.limit\r\n        };\r\n        \r\n        const res = await getMyExchanges(params);\r\n        \r\n        if (res.code === 1) {\r\n          const data = res.data;\r\n          \r\n          // 更新总记录数\r\n          this.total = data.total || 0;\r\n          \r\n          // 处理记录列表\r\n          const records = data.rows || [];\r\n          \r\n          // 添加状态字段（接口未返回，默认为成功）\r\n          const processedRecords = records.map(item => ({\r\n            ...item,\r\n            status: 'success' // 默认状态，实际项目中应根据接口返回的状态字段设置\r\n          }));\r\n          \r\n          // 如果是第一页，替换列表，否则追加\r\n          if (this.page === 1) {\r\n            this.recordList = processedRecords;\r\n          } else {\r\n            this.recordList = [...this.recordList, ...processedRecords];\r\n          }\r\n          \r\n          // 判断是否还有更多数据\r\n          this.hasMore = this.recordList.length < this.total;\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '获取记录失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('获取兑换记录异常:', error);\r\n        uni.showToast({\r\n          title: '网络异常，请稍后重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 获取状态文本\r\n    getStatusText(status) {\r\n      switch(status) {\r\n        case 'success':\r\n          return '兑换成功';\r\n        case 'pending':\r\n          return '处理中';\r\n        case 'failed':\r\n          return '兑换失败';\r\n        default:\r\n          return '未知状态';\r\n      }\r\n    },\r\n    \r\n    // 格式化时间戳\r\n    formatTime(timestamp) {\r\n      if (!timestamp) return '';\r\n      \r\n      const date = new Date(timestamp * 1000);\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    },\r\n    \r\n    // 处理图片加载错误\r\n    handleImageError(e) {\r\n      // 替换为默认图片\r\n      const target = e.target || e.currentTarget;\r\n      if (target) {\r\n        target.src = '/static/my/default-avatar.png';\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n  padding-top: 88rpx;\r\n  padding-bottom: 30rpx;\r\n  position: relative;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 30rpx;\r\n    \r\n    .back-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #fff;\r\n      font-weight: bold;\r\n    }\r\n    \r\n    .empty-placeholder {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.record-list {\r\n  padding: 0 20rpx;\r\n  \r\n  .record-item {\r\n    display: flex;\r\n    align-items: center;\r\n    background: #fff;\r\n    border-bottom: 1px solid #f0f0f0;\r\n    padding: 30rpx 20rpx;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .item-left {\r\n      margin-right: 20rpx;\r\n      \r\n      .item-image {\r\n        width: 80rpx;\r\n        height: 80rpx;\r\n        border-radius: 50%;\r\n        background-color: #f5f5f5;\r\n        object-fit: cover;\r\n      }\r\n    }\r\n    \r\n    .item-center {\r\n      flex: 1;\r\n      overflow: hidden;\r\n      \r\n      .item-name {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        font-weight: 500;\r\n        margin-bottom: 10rpx;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n      \r\n      .item-tag {\r\n        display: inline-flex;\r\n        align-items: center;\r\n        height: 32rpx;\r\n        padding: 0 12rpx;\r\n        border-radius: 4rpx;\r\n        margin-bottom: 10rpx;\r\n        min-width: 80rpx;\r\n        \r\n        text {\r\n          font-size: 20rpx;\r\n          white-space: nowrap;\r\n        }\r\n        \r\n        &.coupon-tag {\r\n          background: rgba(255, 68, 68, 0.1);\r\n          \r\n          text {\r\n            color: #ff4444;\r\n          }\r\n        }\r\n        \r\n        &.product-tag {\r\n          background: rgba(106, 181, 46, 0.1);\r\n          \r\n          text {\r\n            color: #6ab52e;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .item-time {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n    \r\n    .item-right {\r\n      text-align: right;\r\n      \r\n      .item-status {\r\n        margin-bottom: 10rpx;\r\n        \r\n        text {\r\n          font-size: 26rpx;\r\n        }\r\n        \r\n        &.success {\r\n          text {\r\n            color: #6ab52e;\r\n          }\r\n        }\r\n        \r\n        &.pending {\r\n          text {\r\n            color: #ff9900;\r\n          }\r\n        }\r\n        \r\n        &.failed {\r\n          text {\r\n            color: #ff4444;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .item-points {\r\n        text {\r\n          font-size: 26rpx;\r\n          color: #ff4444;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  padding: 40rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  .loading-spinner {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    border: 4rpx solid rgba(140, 213, 72, 0.2);\r\n    border-left: 4rpx solid #8cd548;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.empty-container {\r\n  padding: 100rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  .empty-image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 30rpx;\r\n    color: #666;\r\n    \r\n    &.empty-tips {\r\n      font-size: 24rpx;\r\n      color: #999;\r\n      margin-top: 10rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.bottom-tip {\r\n  padding: 40rpx 0 80rpx;\r\n  text-align: center;\r\n  \r\n  text {\r\n    font-size: 26rpx;\r\n    color: #999;\r\n  }\r\n  \r\n  .loading-more {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .loading-dot {\r\n      width: 30rpx;\r\n      height: 30rpx;\r\n      border: 3rpx solid rgba(140, 213, 72, 0.2);\r\n      border-left: 3rpx solid #8cd548;\r\n      border-radius: 50%;\r\n      animation: spin 1s linear infinite;\r\n      margin-right: 10rpx;\r\n    }\r\n    \r\n    text {\r\n      font-size: 26rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=style&index=0&id=18f9de58&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./record.vue?vue&type=style&index=0&id=18f9de58&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309382\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}