@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-4cedc0c6 {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}
.header.data-v-4cedc0c6 {
  background: #fff;
  padding-top: 120rpx;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}
.header .nav-bar.data-v-4cedc0c6 {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}
.header .nav-bar .back-icon.data-v-4cedc0c6 {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
  margin-right: 10rpx;
}
.header .nav-bar .search-bar.data-v-4cedc0c6 {
  flex: 1;
  display: flex;
  align-items: center;
}
.header .nav-bar .search-bar .search-box.data-v-4cedc0c6 {
  flex: 1;
  height: 72rpx;
  background: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
}
.header .nav-bar .search-bar .search-box .search-input.data-v-4cedc0c6 {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
  margin: 0 16rpx;
}
.header .nav-bar .search-bar .cancel-btn.data-v-4cedc0c6 {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
}
.content.data-v-4cedc0c6 {
  flex: 1;
  margin-top: 280rpx;
  height: calc(100vh - 280rpx);
}
.history.data-v-4cedc0c6 {
  padding: 30rpx;
}
.history .section-header.data-v-4cedc0c6 {
  display: flex;
  justify-content: space-between;
}
.history .section-header .title.data-v-4cedc0c6 {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.history .section-header .clear.data-v-4cedc0c6 {
  display: flex;
  align-items: center;
  padding: 10rpx;
}
.history .section-header .clear text.data-v-4cedc0c6 {
  font-size: 26rpx;
  color: #999;
  margin-left: 8rpx;
}
.history .history-list.data-v-4cedc0c6 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.history .history-list .history-item.data-v-4cedc0c6 {
  padding: 12rpx 32rpx;
  background: #f5f5f5;
  border-radius: 100rpx;
}
.history .history-list .history-item text.data-v-4cedc0c6 {
  font-size: 26rpx;
  color: #666;
}
.hot-search.data-v-4cedc0c6 {
  padding: 30rpx;
}
.hot-search .section-header.data-v-4cedc0c6 {
  margin-bottom: 20rpx;
}
.hot-search .section-header .title.data-v-4cedc0c6 {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.hot-search .hot-list.data-v-4cedc0c6 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.hot-search .hot-list .hot-item.data-v-4cedc0c6 {
  padding: 12rpx 32rpx;
  background: #f5f5f5;
  border-radius: 100rpx;
}
.hot-search .hot-list .hot-item text.data-v-4cedc0c6 {
  font-size: 26rpx;
  color: #666;
}
.hot-search .hot-list .hot-item text.hot.data-v-4cedc0c6 {
  color: #ff4444;
}
.search-result.data-v-4cedc0c6 {
  padding: 20rpx;
}
.search-result .result-item.data-v-4cedc0c6 {
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
}
.search-result .result-item .item-image.data-v-4cedc0c6 {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}
.search-result .result-item .item-info.data-v-4cedc0c6 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.search-result .result-item .item-info .name.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.search-result .result-item .item-info .desc.data-v-4cedc0c6 {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}
.search-result .result-item .item-info .price.data-v-4cedc0c6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.search-result .result-item .item-info .price .price-left.data-v-4cedc0c6 {
  display: flex;
  align-items: baseline;
}
.search-result .result-item .item-info .price .price-left .symbol.data-v-4cedc0c6 {
  font-size: 24rpx;
  color: #ff4444;
}
.search-result .result-item .item-info .price .price-left .value.data-v-4cedc0c6 {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}
.search-result .result-item .item-info .price .select-btn.data-v-4cedc0c6 {
  padding: 8rpx 24rpx;
  background: #8cd548;
  border-radius: 100rpx;
}
.search-result .result-item .item-info .price .select-btn text.data-v-4cedc0c6 {
  font-size: 24rpx;
  color: #fff;
}
.empty-state.data-v-4cedc0c6 {
  padding-top: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-state .empty-image.data-v-4cedc0c6 {
  width: 280rpx;
  height: 280rpx;
  margin-bottom: 30rpx;
}
.empty-state .empty-text.data-v-4cedc0c6 {
  font-size: 28rpx;
  color: #999;
}
.placeholder.data-v-4cedc0c6 {
  color: #999;
}

