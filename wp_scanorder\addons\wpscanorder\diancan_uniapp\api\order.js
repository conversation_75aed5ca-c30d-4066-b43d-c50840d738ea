import request from '@/utils/request';


// 取消订单
export const cancelOrder = (orderNo) => {
    console.log('调用取消订单API，订单号:', orderNo);
    return request({
        url: '/order/cancel',
        method: 'POST',
        data: { order_no: orderNo }
    }).then(res => {
        console.log('取消订单API返回:', JSON.stringify(res));
        return res;
    });
};


// 添加订单
export const addOrder = (data) => {
    console.log('调用添加订单API，数据:', JSON.stringify(data));
    return request({
        url: '/order/add',
        method: 'POST',
        data
    }).then(res => {
        console.log('添加订单API返回:', JSON.stringify(res));
        return res;
    });
};

// 获取我的订单列表
export const myOrders = (params = {}) => {
    console.log('调用获取我的订单API，参数:', JSON.stringify(params));
    return request({
        url: '/order/myOrders',
        method: 'GET',
        params
    }).then(res => {
        console.log('获取我的订单API返回:', JSON.stringify(res));
        return res;
    });
};

// 获取订单详情
export const getOrderDetail = (order_no) => {
    console.log('调用获取订单详情API，订单号:', order_no);
    return request({
        url: '/order/detail',
        method: 'GET',
        params: { order_no }
    }).then(res => {
        console.log('获取订单详情API返回:', JSON.stringify(res));
        return res;
    });
};

// 余额支付订单
export const payOrderWithBalance = (orderNo) => {
    console.log('调用余额支付API，订单号:', orderNo);
    return request({
        url: '/order/payWithBalance',
        method: 'POST',
        data: { order_no: orderNo }
    }).then(res => {
        console.log('余额支付API返回:', JSON.stringify(res));
        // 标准化响应结果
        if (res.code !== undefined && res.data !== undefined) {
            // 已经是标准格式
            return res;
        } else if (res.code !== undefined) {
            // 只有code，没有data
            return {
                code: res.code,
                msg: res.msg || '',
                data: {}
            };
        } else if (typeof res === 'object') {
            // 将非标准响应转换为标准格式
            return {
                code: 1, // 默认成功
                msg: '支付成功',
                data: res
            };
        } else {
            // 其他情况
            return {
                code: 1,
                msg: '支付成功',
                data: { result: res }
            };
        }
    });
};

// 获取微信支付参数
export const getWxPayParams = (orderNo) => {
    console.log('调用微信支付参数获取API，订单号:', orderNo);
    return request({
        url: '/wx_pay/create',
        method: 'POST',
        data: { order_no: orderNo }
    }).then(res => {
        console.log('微信支付参数获取API返回:', JSON.stringify(res));
        return res;
    });
};

// 查询微信支付结果
export const queryWxPayResult = (orderNo) => {
    console.log('调用微信支付结果查询API，订单号:', orderNo);
    return request({
        url: '/wx_pay/query',
        method: 'GET',
        params: { order_no: orderNo }
    }).then(res => {
        console.log('微信支付结果查询API返回:', JSON.stringify(res));
        return res;
    });
};