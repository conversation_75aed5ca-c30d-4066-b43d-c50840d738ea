<template>
	<view class="wp_-flex-col page">
		<!-- 顶部搜索和店铺信息 -->
		<view class="wp_-flex-col header">
			<!-- 搜索框 -->
			<view class="wp_-flex-row search-wrapper" @tap="goToSearch">
				<view class="wp_-flex-row wp_-items-center wp_-flex-1 search-input">
					<image class="search-icon" src="/static/menu/117dfdcfc62a8651f86aa56c1db6c74a.png" />
					<text class="search-placeholder">请输入商品名称</text>
				</view>
			</view>
			
			<!-- 店铺信息 -->
			<view class="wp_-flex-row wp_-justify-between store-info">
				<view class="wp_-flex-col wp_-self-center" @tap="goToStoreDetail">
					<view class="wp_-flex-row wp_-items-center">
						<text class="store-name">{{merchant.name}}</text>
					</view>
					<view class="wp_-mt-16 wp_-flex-row wp_-items-center">
						<image class="distance-icon" src="/static/menu/82a617e96551e74fc96528efc85baa41.png" />
						<text class="distance-text">月售{{merchant.monthly_sales}}单</text>
					</view>
				</view>
				<!-- 堂食/外卖切换 -->
				<view class="dining-type">
					<view 
						class="dining-type-item" 
						:class="{'dining-type-active': diningType === 'dine-in'}"
						@click="switchDiningType('dine-in')"
					>
						<text>堂食</text>
					</view>
					<view 
						class="dining-type-item"
						:class="{'dining-type-active': diningType === 'takeout'}"
						@click="switchDiningType('takeout')"
					>
						<text>外卖</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 商品列表区域 -->
		<view class="menu-content">
			<!-- 左侧分类菜单 -->
			<scroll-view scroll-y class="menu-sidebar">
				<view 
					v-for="(category, index) in categories" 
					:key="category.id"
					:class="['sidebar-item', currentCategory === index ? 'active' : '']"
					@click="switchCategory(index)"
				>
					<text>{{category.name}}</text>
				</view>
			</scroll-view>
			
			<!-- 右侧商品列表 -->
			<scroll-view scroll-y class="product-list-container">
				<!-- 分类标题 -->
				<text class="category-title">{{categories[currentCategory].name}}</text>
				
				<!-- 商品列 -->
				<view class="product-list">
					<view class="product-item" v-for="(item, index) in currentProducts" :key="index">
						<image class="product-image" :src="item.image || '/static/home/<USER>'" />
						<view class="product-info">
							<text class="product-name">{{item.name}}</text>
							<view class="product-bottom">
								<view class="price">
									<text class="price-symbol">¥</text>
									<text class="price-value">{{item.price || '0.00'}}</text>
								</view>
								<view class="select-btn" @tap="selectSpec(item)">
									<text v-if="item.spec_type === 'multi'" class="spec-btn">选规格</text>
									<view v-else class="add-btn">
										<image class="add-icon" src="/static/menu/add.png" />
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 规格选择弹窗 -->
		<menu-open 
			v-if="showSpecSelector" 
			:product="selectedProduct"
			@close="closeSpecSelector"
			@add-to-cart="handleAddToCart"
		></menu-open>

		<!-- 底部购物车栏 -->
		<view class="cart-bar" v-if="cartTotal > 0">
			<view class="wp_-flex-row wp_-items-center cart-content">
				<view class="cart-left" @tap="openCart">
					<image class="cart-icon" src="/static/menu/cart.png" />
					<view class="cart-badge">{{cartTotal}}</view>
				</view>
				<view class="wp_-flex-1 cart-center">
					<text class="price-symbol">¥</text>
					<text class="price-value">{{cartPrice.toFixed(2)}}</text>
				</view>
				<view class="checkout-btn" @tap="goToCheckout">
					<text class="btn-text">去结算</text>
				</view>
			</view>
		</view>

		<!-- 购物车弹窗 -->
		<shopping-cart 
			v-if="showCart"
			:cart-list="cartList"
			:total-price="cartPrice"
			@close="closeCart"
			@update="updateCart"
		/>
	</view>
</template>

<script>
import MenuOpen from '../menu/menu_open.vue'
import ShoppingCart from './ShoppingCart.vue'
import { getCategories, getProducts } from '@/api/menu'
import { getMerchantInfo } from '@/api/merchant'

export default {
	components: {
		MenuOpen,
		ShoppingCart
	},
	data() {
		return {
			showSpecSelector: false, // 控制规格选择器显示
			selectedProduct: null, // 当前选中的商品
			productList: [
				{
					name: '暴打凤梨鸭屎香柠檬茶',
					spec: '600ml',
					price: 18,
					image: '/static/menu/595f1342438e860b2989fb7a6b4614bc.png'
				},
				{
					name: '招牌暴打渣男柠檬茶',
					spec: '600ml',
					price: 18,
					image: '/static/menu/ade3ff4d605f82d824aedfaef156d804.png'
				},
				{
					name: '芒果柠檬茶',
					spec: '600ml',
					price: 15,
					image: '/static/menu/51a6a8b5890fe982ce6e69b5bcc9499e.png'
				}
			],
			storeInfo: {
				name: 'KKmall京基店2楼188号',
				distance: '300m'
			},
			currentCategory: 0, // 当前选中的分类
			categories: [], // 分类列表
			diningType: 'dine-in', // 用餐方式：dine-in(堂食) / takeout(外卖)
			cartTotal: 0, // 购物车商品总数
			cartPrice: 0, // 购物车总价
			cartList: [], // 购物车商品列表
			showCart: false, // 控制物弹窗显示
			currentProducts: [], // 当前分类的商品列表
			loading: false, // 加载状态
			merchant: {
				name: '',
				logo_image: '',
				address: '',
				phone: '',
				monthly_sales: 0,
				business_hours: '',
				rating: 5.0,
				announcement: ''
			},
			pendingProductInfo: null, // 存储待打开的商品信息
			allProducts: [] // 所有分类的商品缓存
		}
	},
	onLoad() {
		// 监听清空购物车事件
		uni.$on('clearCart', this.handleClearCart)
		
		// 监听从首页跳转过来时切换分类的事件
		uni.$on('switchCategory', this.handleSwitchCategory)
		
		// 初始化时就获取商户信息和分类数据
		this.getMerchantInfo()
		this.getCategories()
	},
	onUnload() {
		// 移除事件监听
		uni.$off('clearCart', this.handleClearCart)
		uni.$off('switchCategory', this.handleSwitchCategory)
	},
	methods: {
		// 获取分类列表
		async getCategories() {
			try {
				const res = await getCategories()
				
				// 根据接口返回的实际数据结构调整取值路径
				let categoriesData = []
				if (res && res.code === 1) {
					if (res.data && Array.isArray(res.data.list)) {
						categoriesData = res.data.list
					} else if (res.data && Array.isArray(res.data)) {
						categoriesData = res.data
					} else if (Array.isArray(res.list)) {
						categoriesData = res.list
					}
				}
				
				this.categories = categoriesData
				console.log('获取到的分类数据:', this.categories)
				
				// 如果有待处理的商品信息，检查对应的分类
				if (this.pendingProductInfo && this.pendingProductInfo.categoryId) {
					const categoryIndex = this.categories.findIndex(cat => cat.id === this.pendingProductInfo.categoryId)
					if (categoryIndex > -1 && categoryIndex !== this.currentCategory) {
						console.log('处理待打开商品的分类切换:', categoryIndex)
						this.currentCategory = categoryIndex
					}
				}
				
				// 如果有分类数据，自动获取第一个分类的商品
				if (this.categories.length > 0) {
					await this.getProductList()
				}
			} catch(e) {
				console.error('获取分类失败:', e)
				uni.showToast({
					title: '获取分类失败',
					icon: 'none'
				})
			}
		},
		// 搜索商品
		onSearch(keyword) {
			console.log('搜索:', keyword)
		},
		// 切换堂食/外卖
		switchDiningType(type) {
			this.diningType = type
			// 将当前选择的用餐方式保存到本地存储
			uni.setStorageSync('diningType', type)
		},
		// 选择商品规格
		selectSpec(product) {
			if (product.spec_type === 'single') {
				// 单规格商品直接加入购物车
				const price = parseFloat(product.price || 0);
				// 确保规格信息正确
				const specText = product.spec || product.props_text || '默认规格';
				console.log('单规格商品添加购物车，规格信息:', specText);
				
				this.handleAddToCart({
					id: product.id,
					name: product.name,
					price: price,
					image: product.image,
					count: 1,
					totalPrice: price,
					spec_type: 'single',
					specs: specText,
					props_text: specText
				})
			} else {
				// 多规格商品，打开选择器并传递规格数据
				const specConfig = product.spec_config || {}
				
				// 构建规格属性数据
				const properties = []
				
				// 添加规格选项
				if (specConfig.规格 && (Array.isArray(specConfig.规格) ? specConfig.规格.length > 0 : true)) {
					const specs = Array.isArray(specConfig.规格) ? specConfig.规格 : [specConfig.规格];
					properties.push({
						name: '规格',
						values: specs.map(item => ({
							value: item,
							is_default: false,
							price: (specConfig.价格 && specConfig.价格[item]) ? parseFloat(specConfig.价格[item]) : parseFloat(product.price || 0)
						}))
					})
				}
				
				// 添加温度选项
				if (specConfig.温度 && (Array.isArray(specConfig.温度) ? specConfig.温度.length > 0 : true)) {
					const temps = Array.isArray(specConfig.温度) ? specConfig.温度 : [specConfig.温度];
					properties.push({
						name: '温度',
						values: temps.map(item => ({
							value: item,
							is_default: false
						}))
					})
				}
				
				// 添加甜度选项
				if (specConfig.甜度 && (Array.isArray(specConfig.甜度) ? specConfig.甜度.length > 0 : true)) {
					const sweetness = Array.isArray(specConfig.甜度) ? specConfig.甜度 : [specConfig.甜度];
					properties.push({
						name: '甜度',
						values: sweetness.map(item => ({
							value: item,
							is_default: false
						}))
					})
				}
				
				// 设置选中商品数据
				this.selectedProduct = {
					...product,
					number: 1,
					property: properties,
					prices: specConfig.价格 || {}
				}
				
				// 设置每个属性的第一个选项为默认选中
				this.selectedProduct.property.forEach(prop => {
					if (prop.values.length > 0) {
						prop.values[0].is_default = true
					}
				})
				
				// 打开规格选择器
				this.showSpecSelector = true
			}
		},
		// 关闭规格择器
		closeSpecSelector() {
			this.showSpecSelector = false
			this.selectedProduct = null
		},
		// 切换分类
		async switchCategory(index) {
			if (this.currentCategory === index) return
			this.currentCategory = index
			
			// 清空当前商品列表，显示加载中
			this.currentProducts = []
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
			
			try {
				await this.getProductList()
			} catch (e) {
				console.error('切换分类加载商品失败:', e)
				// 如果失败，尝试重新加载一次
				setTimeout(async () => {
					try {
						await this.getProductList()
					} catch (error) {
						console.error('重试加载商品失败:', error)
					}
				}, 1000)
			} finally {
				uni.hideLoading()
			}
		},
		// 打开购物车
		openCart() {
			this.showCart = true
		},
		
		// 关闭购物车
		closeCart() {
			this.showCart = false
		},
		
		// 更新购物车
		updateCart(data) {
			this.cartList = data.list
			this.cartTotal = data.total
			this.cartPrice = data.price
			// 同步到本地存储
			uni.setStorageSync('cartData', data)
		},
		
		// 添加到购物车
		addToCart(product, spec) {
			// 添加商品到购物车
			const item = {
				...product,
				spec,
				count: 1
			}
			this.cartList.push(item)
			this.cartTotal++
			this.cartPrice += Number(product.price)
		},
		
		// 去结算
		goToCheckout() {
			if (this.cartTotal > 0) {
				// 保存购车数据
				uni.setStorageSync('cartData', {
					list: this.cartList,
					total: this.cartTotal,
					price: this.cartPrice
				})
				
				// 保存当前的用餐方式
				uni.setStorageSync('diningType', this.diningType)
				
				// 跳转到订单提交页面
				uni.navigateTo({
					url: '/pages/order/orderSubmit'
				})
			}
		},
		
		// 处理添加购物车
		handleAddToCart(item) {
			// 检查购物车是否已有相同商品（包括规格）
			const existingItem = this.cartList.find(cartItem => {
				if (cartItem.id !== item.id) return false
				if (item.spec_type === 'single') return true
				return cartItem.props_text === item.props_text
			})
			
			if (existingItem) {
				// 如果已存在相同规格的商品，增加数量
				existingItem.count += item.count
				existingItem.totalPrice = Number(existingItem.price) * existingItem.count
			} else {
				// 如果不存在，添加新商品
				this.cartList.push(item)
			}
			
			// 计算总数和总价
			this.updateCartTotal()
		},
		
		// 跳转到搜索页面
		goToSearch() {
			uni.navigateTo({
				url: '/pages/search/search',
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		},
		
		// 加载购物车数据
		loadCartData() {
			const cartData = uni.getStorageSync('cartData')
			if (cartData) {
				console.log('从本地加载购物车数据:', JSON.stringify(cartData));
				this.cartList = cartData.list || []
				this.cartTotal = cartData.total || 0
				this.cartPrice = cartData.price || 0
				
				// 检查并修复从本地加载的购物车数据中的规格信息
				if (this.cartList.length > 0) {
					let hasFixed = false;
					this.cartList.forEach(item => {
						if (!item.props_text && item.specs) {
							item.props_text = item.specs;
							console.log('修复加载的购物车商品规格:', item.name, item.props_text);
							hasFixed = true;
						} else if (!item.props_text) {
							item.props_text = '默认规格';
							item.specs = '默认规格';
							console.log('为加载的购物车商品设置默认规格:', item.name);
							hasFixed = true;
						}
					});
					
					// 如果有修复，更新到本地存储
					if (hasFixed) {
						console.log('更新修复后的购物车数据到本地存储');
						uni.setStorageSync('cartData', {
							list: this.cartList,
							total: this.cartTotal,
							price: this.cartPrice
						});
					}
				}
			}
		},
		
		// 添加跳转到店铺详情的方法
		goToStoreDetail() {
			uni.navigateTo({
				url: `/pages/store/detail?id=${this.merchant.id}`,
				animationType: 'slide-in-right',
				animationDuration: 300
			})
		},
		
		// 获取商品列表
		async getProductList() {
			try {
				this.loading = true
				const category_id = this.categories[this.currentCategory]?.id
				if (!category_id) return
				
				// 确保category_id是整数类型，与后端接口要求匹配
				const categoryIdNum = parseInt(category_id) || 0
				
				console.log('获取商品列表，原始分类ID:', category_id, '类型:', typeof category_id)
				console.log('获取商品列表，转换后分类ID:', categoryIdNum, '类型:', typeof categoryIdNum)
				
				const res = await getProducts({ category_id: categoryIdNum })
				
				// 根据接口返回的实际数据结构调整取值路径
				let productsData = []
				if (res && res.code === 1) {
					if (res.data && Array.isArray(res.data.list)) {
						productsData = res.data.list
					} else if (res.data && Array.isArray(res.data)) {
						productsData = res.data
					} else if (Array.isArray(res.list)) {
						productsData = res.list
					}
				}
				
				if (productsData.length === 0) {
					console.log('当前分类没有商品数据')
					this.currentProducts = []
					return
				}
				
				// 处理商品数据，只处理价格信息
				this.currentProducts = productsData.map(product => {
					// 确保价格为数字
					const price = parseFloat(product.price || 0).toFixed(2);
					
					// 保留规格信息但不显示
					let spec = '';
					if (product.spec) {
						spec = product.spec;
					} else if (product.props_text) {
						spec = product.props_text;
					} else if (product.spec_config && product.spec_config.规格) {
						spec = Array.isArray(product.spec_config.规格) ? product.spec_config.规格[0] : product.spec_config.规格;
					} else {
						spec = '默认规格';
					}
					
					return {
						...product,
						spec,
						price
					};
				});
				
				console.log('处理后的商品数据:', this.currentProducts)
				
				// 存储到所有商品缓存中
				this.allProducts = [...this.allProducts.filter(p => p.category_id !== categoryIdNum), ...this.currentProducts]
				
				// 如果有待打开的商品，检查是否在当前分类中
				this.tryOpenPendingProduct()
			} catch (e) {
				console.error('获取商品列表失败:', e)
				uni.showToast({
					title: '获取商品列表失败',
					icon: 'none'
				})
				// 网络错误时清空商品列表
				this.currentProducts = []
			} finally {
				this.loading = false
			}
		},
		
		// 尝试打开待处理的商品
		tryOpenPendingProduct() {
			if (!this.pendingProductInfo || !this.pendingProductInfo.productId) return
			
			// 当前分类的商品中查找
			let productToOpen = this.currentProducts.find(p => p.id == this.pendingProductInfo.productId)
			
			if (productToOpen) {
				console.log('在当前分类中找到待打开的商品:', productToOpen.name)
				
				// 确保购物车弹窗已关闭
				this.showCart = false
				
				// 延迟一点打开商品详情，确保UI渲染完成
				setTimeout(() => {
					// 打开商品详情
					this.selectSpec(productToOpen)
					// 清空待处理信息
					this.pendingProductInfo = null
				}, 200)
			} else {
				console.log('当前分类未找到商品ID:', this.pendingProductInfo.productId)
				
				// 尝试在所有已加载的商品中查找
				productToOpen = this.allProducts.find(p => p.id == this.pendingProductInfo.productId)
				
				if (productToOpen) {
					console.log('在其他分类中找到待打开的商品:', productToOpen.name)
					// 切换到对应分类
					const categoryIndex = this.categories.findIndex(cat => cat.id === productToOpen.category_id)
					if (categoryIndex > -1 && categoryIndex !== this.currentCategory) {
						console.log('切换到商品所在分类:', categoryIndex)
						this.currentCategory = categoryIndex
						// 获取分类商品并在回调中打开商品详情
						this.getProductList().then(() => {
							setTimeout(() => {
								// 确保购物车弹窗关闭
								this.showCart = false
								// 打开商品详情
								this.selectSpec(productToOpen)
								// 清空待处理信息
								this.pendingProductInfo = null
							}, 200)
						})
					}
				}
			}
		},
		
		// 修改模态框添加到购物车的方法
		handleAddToCartInModal() {
			// 获取选中的规格
			const selectedProps = this.selectedProduct.property.map(prop => {
				const selected = prop.values.find(v => v.is_default)
				return {
					name: prop.name,
					value: selected ? selected.value : ''
				}
			})

			// 获取规格对应的价格
			const size = selectedProps.find(p => p.name === '规格')?.value
			const price = this.selectedProduct.prices[size] || this.selectedProduct.price

			// 构建购物车商品数据
			const cartItem = {
				id: this.selectedProduct.id,
				name: this.selectedProduct.name,
				price: Number(price),
				image: this.selectedProduct.image,
				count: this.selectedProduct.number,
				totalPrice: Number(price) * this.selectedProduct.number,
				spec_type: 'multi',
				specs: selectedProps.map(p => `${p.name}:${p.value}`).join('，'),
				props_text: selectedProps.map(p => `${p.name}:${p.value}`).join('，')
			}

			this.handleAddToCart(cartItem)
			this.closeSpecSelector()
		},
		
		// 修改购物车商品匹配逻辑
		handleAddToCart(item) {
			// 确保规格信息正确设置
			console.log('添加购物车，商品数据:', JSON.stringify(item));
			
			// 确保props_text字段存在
			if (!item.props_text && item.specs) {
				item.props_text = item.specs;
				console.log('补充props_text字段:', item.props_text);
			}
			
			// 检查购物车是否已有相同商品（包括规格）
			const existingItem = this.cartList.find(cartItem => {
				if (cartItem.id !== item.id) return false
				if (item.spec_type === 'single') return true
				return cartItem.props_text === item.props_text
			})
			
			if (existingItem) {
				// 如果已存在相同规格的商品，增加数量
				existingItem.count += item.count
				existingItem.totalPrice = Number(existingItem.price) * existingItem.count
				// 确保existingItem的props_text字段
				if (!existingItem.props_text && existingItem.specs) {
					existingItem.props_text = existingItem.specs;
				}
				console.log('更新购物车已有商品:', JSON.stringify(existingItem));
			} else {
				// 如果不存在，添加新商品
				this.cartList.push(item)
				console.log('添加新商品到购物车:', JSON.stringify(item));
			}
			
			// 计算总数和总价
			this.updateCartTotal()
		},
		
		// 修改购物车商品匹配逻辑
		updateCartTotal() {
			// 先检查并修复购物车中的每个商品是否都有props_text字段
			this.cartList.forEach(item => {
				if (!item.props_text && item.specs) {
					item.props_text = item.specs;
					console.log('修复购物车商品缺失的props_text字段:', item.id, item.props_text);
				} else if (!item.props_text) {
					// 如果没有规格信息，设置为默认值
					item.props_text = '默认规格';
					item.specs = '默认规格';
					console.log('设置商品默认规格:', item.id);
				}
			});
			
			// 计算总数和总价
			const total = this.cartList.reduce((sum, item) => sum + item.count, 0)
			const price = this.cartList.reduce((sum, item) => sum + item.totalPrice, 0)
			
			// 更新购物车数据
			const newCartData = {
				list: this.cartList,
				total,
				price
			}
			
			// 输出完整的购物车数据用于调试
			console.log('保存到本地存储的购物车数据:', JSON.stringify(newCartData));
			
			// 更新本地存储
			uni.setStorageSync('cartData', newCartData)
			
			// 更新页面数据
			this.cartTotal = total
			this.cartPrice = price
			
			// 显示添加成功提示
			uni.showToast({
				title: '已加入购物车',
				icon: 'none'
			})
		},
		
		async getMerchantInfo() {
			try {
				const res = await getMerchantInfo()
				console.log('获取到的商户信息:', res)
				
				// 检查API返回的数据结构
				if (res && res.code === 1 && res.data) {
					const merchantData = res.data
					
					// 从data中提取商户信息
					this.merchant = {
						id: merchantData.id || 0,
						name: merchantData.name || '',
						logo_image: merchantData.logo_image || '',
						address: merchantData.address || '',
						phone: merchantData.phone || '',
						monthly_sales: merchantData.monthly_sales || 0,
						business_hours: merchantData.business_hours || '',
						rating: Number(merchantData.rating) || 5.0,
						announcement: merchantData.announcement || ''
					}
					
					console.log('处理后的商户信息:', this.merchant)
				} else {
					throw new Error('API返回数据格式错误')
				}
			} catch (e) {
				console.error('获取商户信息失败:', e)
				uni.showToast({
					title: '获取商户信息失败',
					icon: 'none'
				})
			}
		},
		// 清空购物车处理方法
		handleClearCart() {
			this.cartList = []
			this.cartTotal = 0
			this.cartPrice = 0
			// 更新商品数量状态
			this.productList.forEach(product => {
				product.number = 0
			})
		},
		// 处理从首页跳转切换分类的事件
		handleSwitchCategory(data) {
			if (!data || !data.categoryId) return
			
			console.log('接收到切换分类事件:', data)
			
			// 存储待打开的商品信息
			if (data.productId) {
				this.pendingProductInfo = {
					categoryId: data.categoryId,
					productId: data.productId
				}
				
				// 先关闭可能打开的购物车弹窗
				this.showCart = false
			}
			
			// 找到对应分类的索引
			const categoryIndex = this.categories.findIndex(cat => cat.id === data.categoryId)
			if (categoryIndex > -1) {
				// 切换到对应分类
				this.switchCategory(categoryIndex)
			} else {
				// 如果还没有获取到分类数据，先保存要切换的分类ID
				console.log('分类数据未加载，记录待切换的分类')
				// 在getCategories完成后会处理pendingProductInfo
			}
		}
	},
	async onShow() {
		// 检查是否有外卖选择状态
		const savedDiningType = uni.getStorageSync('diningType')
		if (savedDiningType) {
			this.diningType = savedDiningType
			// 清除保存的状态
			uni.removeStorageSync('diningType')
		}

		// 加载购物车数据
		this.loadCartData()
		
		// 检查本地存储中是否有待打开的商品ID
		const scrollToProductId = uni.getStorageSync('scrollToProductId')
		if (scrollToProductId) {
			console.log('从本地存储获取到待打开商品ID:', scrollToProductId)
			// 保存待打开的商品信息
			this.pendingProductInfo = {
				productId: scrollToProductId
			}
			// 关闭购物车弹窗，避免冲突
			this.showCart = false
			// 清除本地存储
			uni.removeStorageSync('scrollToProductId')
		}
		
		// 每次进入页面时都重新获取最新数据
		// 先获取商户信息
		await this.getMerchantInfo()
		
		// 获取分类数据
		await this.getCategories()
		
		// 如果有分类数据，不需要再次请求商品列表，因为getCategories方法中已经调用了getProductList
	}
}
</script>

<style scoped lang="scss">
.page {
	position: relative;
	z-index: 1;
	background-color: #f8fafb;
	width: 100%;
	height: 100%;
	overflow-y: auto;
	
	// 顶部区域
	.header {
		padding-top: 180rpx;
		background-image: url('/static/menu/a704b1fd76a14df640e66c4ad009de43.png');
		background-size: 100% 100%;
		background-repeat: no-repeat;
		
		// 搜索框
		.search-wrapper {
			padding: 20rpx 30rpx;
			
			.search-input {
				padding: 16rpx 24rpx;
				background-color: #ffffff;
				border-radius: 308rpx;
				height: 72rpx;
				
				.search-icon {
					width: 28rpx;
					height: 28rpx;
				}
				
				.search-placeholder {
					margin-left: 16rpx;
					color: #cccccc;
					font-size: 28rpx;
				}
			}
		}
		
		// 店铺信息
		.store-info {
			padding: 56rpx 40rpx 38rpx;
			
			.store-name {
				font-size: 30rpx;
				color: #333;
				font-weight: bold;
				position: relative;
				padding-right: 40rpx;
				
				&::after {
					content: '';
					position: absolute;
					right: 12rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 12rpx;
					height: 12rpx;
					border-top: 2rpx solid #999;
					border-right: 2rpx solid #999;
					transform: rotate(45deg);
				}
			}
			
			.distance-icon {
				width: 36rpx;
				height: 40rpx;
			}
			
			.distance-text {
				margin-left: 14rpx;
				font-size: 26rpx;
				color: #b3b3b3;
			}
			
			&:active {
				opacity: 0.8;
			}
		}
		
		// 堂食/外卖切换
		.dining-type {
			display: flex;
			align-items: center;
			background-color: #f6f6f6;
			border-radius: 308rpx;
			width: 186rpx;
			height: 56rpx;
			
			&-item {
				flex: 1;
				text-align: center;
				font-size: 24rpx;
				color: #b3b3b3;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s;
				
				&.dining-type-active {
					background-color: #8cd548;
					border-radius: 308rpx;
					color: #ffffff;
				}
				
				text {
					color: inherit;
				}
			}
		}
	}
	
	// 商品列表区域
	.menu-content {
		display: flex;
		height: calc(100vh - 300rpx);
		padding-bottom: 128rpx;
		
		// 左侧分类菜单
		.menu-sidebar {
			width: 160rpx;
			height: 100%;
			background-color: #f6f6f6;
			
			.sidebar-item {
				padding: 30rpx 20rpx;
				text-align: center;
				font-size: 26rpx;
				color: #666;
				position: relative;
				
				&.active {
					background-color: #fff;
					color: #8cd548;
					font-weight: bold;
					
					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 6rpx;
						height: 36rpx;
						background-color: #8cd548;
					}
				}
			}
		}
		
		// 右侧商品列表
		.product-list-container {
			flex: 1;
			height: 100%;
			padding: 0 30rpx;
			background-color: #fff;
			
			// 分类标题
			.category-title {
				font-size: 26rpx;
				color: #3b3b3b;
			}
			
			.product-list {
				.product-item {
					display: flex;
					margin-top: 30rpx;
					
					.product-image {
						width: 148rpx;
						height: 160rpx;
						border-radius: 16rpx;
						background-color: #f1f6f3;
					}
					
					.product-info {
						flex: 1;
						margin-left: 24rpx;
						
						.product-name {
							font-size: 30rpx;
							color: #3b3b3b;
							margin-top: 10rpx;
							margin-bottom: 20rpx;
						}
						
						.product-bottom {
							margin-top: 20rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;
							
							.price {
								display: flex;
								align-items: baseline;
								
								&-symbol {
									font-size: 24rpx;
								}
								
								&-value {
									font-size: 40rpx;
									margin-left: 6rpx;
								}
							}
							
							.select-btn {
								padding: 12rpx 0;
								border-radius: 308rpx;
								width: 120rpx;
								text-align: center;
								
								text {
									font-size: 24rpx;
									color: #ffffff;
								}
							}
						}
					}
				}
			}
		}
	}

	// 底部购物车栏
	.cart-bar {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		padding: 20rpx 40rpx;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
		z-index: 100;
		
		.cart-content {
			height: 88rpx;
			
			.cart-left {
				position: relative;
				padding: 10rpx;
				
				.cart-icon {
					width: 64rpx;
					height: 64rpx;
					transition: all 0.3s;
				}
				
				.cart-badge {
					position: absolute;
					top: -6rpx;
					right: -6rpx;
					min-width: 36rpx;
					height: 36rpx;
					padding: 0 8rpx;
					background: linear-gradient(135deg, #ff5722 0%, #ff7043 100%);
					border-radius: 18rpx;
					color: #fff;
					font-size: 24rpx;
					line-height: 36rpx;
					text-align: center;
					box-shadow: 0 2rpx 6rpx rgba(255, 87, 34, 0.3);
					font-weight: bold;
					border: 2rpx solid #fff;
					animation: bounce 0.5s;
				}
				
				&:active {
					.cart-icon {
						transform: scale(0.95);
						opacity: 0.8;
					}
				}
			}
			
			.cart-center {
				margin-left: 20rpx;
				
				.empty-text {
					font-size: 28rpx;
					color: #999;
				}
				
				.price-symbol {
					font-size: 24rpx;
					color: #ff5722;
					font-weight: bold;
				}
				
				.price-value {
					font-size: 36rpx;
					color: #ff5722;
					font-weight: bold;
					margin-left: 4rpx;
				}
			}
			
			.checkout-btn {
				padding: 0 40rpx;
				height: 88rpx;
				background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);
				transition: all 0.2s;
				position: relative;
				overflow: hidden;
				
				// 添加闪光效果
				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: -100%;
					width: 50%;
					height: 100%;
					background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
					transform: skewX(-25deg);
					animation: shine 3s infinite;
				}
				
				&.disabled {
					background-color: #ccc;
					pointer-events: none; // 禁用点击
				}
				
				.btn-text {
					font-size: 32rpx;
					color: #fff;
					font-weight: 500;
					position: relative;
					z-index: 2;
				}
				
				&:active {
					transform: scale(0.98);
					opacity: 0.9;
				}
			}
		}
	}
}

.product-bottom {
	.select-btn {
		// 多规格按钮样式
		.spec-btn {
			display: block;
			padding: 12rpx 24rpx;
			background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
			border-radius: 308rpx;
			color: #ffffff;
			font-size: 24rpx;
			box-shadow: 0 4rpx 8rpx rgba(140, 213, 72, 0.2);
			transition: all 0.2s;
			
			&:active {
				transform: scale(0.95);
				opacity: 0.9;
			}
		}
		
		// 单规格加号按钮样式
		.add-btn {
			width: 48rpx;
			height: 48rpx;
			background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 40rpx;
			box-shadow: 0 4rpx 8rpx rgba(140, 213, 72, 0.2);
			transition: all 0.2s;
			position: relative;
			
			.add-icon {
				color: #ffffff;
				font-size: 32rpx;
				line-height: 1;
				font-weight: bold;
				// 优化加号显示
				width: 32rpx;
				height: 32rpx;
				position: relative;
				
				&::before,
				&::after {
					content: '';
					position: absolute;
					background-color: #ffffff;
				}
				
				// 横线
				&::before {
					width: 20rpx;
					height: 2rpx;
					left: 6rpx;
					top: 15rpx;
				}
				
				// 竖线
				&::after {
					width: 2rpx;
					height: 20rpx;
					left: 15rpx;
					top: 6rpx;
				}
			}
			
			&:active {
				transform: scale(0.9);
				opacity: 0.8;
			}
		}
	}
}

@keyframes shine {
	0% {
		left: -100%;
	}
	20% {
		left: 100%;
	}
	100% {
		left: 100%;
	}
}

@keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-6rpx);
	}
	60% {
		transform: translateY(-3rpx);
	}
}
</style>