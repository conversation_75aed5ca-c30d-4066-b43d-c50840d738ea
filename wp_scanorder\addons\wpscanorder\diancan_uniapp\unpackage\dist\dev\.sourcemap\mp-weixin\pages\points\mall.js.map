{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?961e", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?ef3c", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?d109", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?682a", "uni-app:///pages/points/mall.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?4cf7", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?d697"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userPoints", "goodsList", "activeTab", "page", "limit", "loading", "hasMore", "isRefreshing", "computed", "filteredGoodsList", "onLoad", "onPullDownRefresh", "methods", "switchTab", "goToPointsDetail", "uni", "url", "handleBack", "handlePullRefresh", "Promise", "title", "icon", "duration", "console", "getUserInfo", "res", "points", "userInfo", "getGoodsList", "params", "list", "processedList", "item", "min_amount", "amount", "valid_day", "name", "id", "image", "getCouponDescription", "getProductDescription", "loadMore", "exchange", "content", "success", "mask", "result", "updatedItem", "setTimeout", "showCouponDetail", "coupon", "confirmText", "confirmRes", "showProductDetail", "refreshGoodsList", "scrollTop", "handleImageError", "e"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmIjrB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;MACA;QACA;UAAA;QAAA;MACA;QACA;UAAA;QAAA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEAC;MACAF;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA;gBAGA;gBACA;;gBAEA;gBAAA;gBAAA,OACAC,aACA,qBACA,qBACA;cAAA;gBAEAJ;kBACAK;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAR;kBACAK;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACAN;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAF;gBAEA;kBACA;kBACAA;;kBAEA;kBACA;kBACAG;kBACA;oBACAA;oBACAH;kBACA;oBACAG;oBACAH;kBACA;oBACAG;oBACAH;kBACA;oBACAG;oBACAH;kBACA;kBAEA;;kBAEA;kBACAR;gBACA;kBACAQ;;kBAEA;kBACAG;kBACA;oBACA;oBACAH;kBACA;oBACA;oBACAI;oBACAJ;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;;gBAEA;gBACAG;gBACA;kBACA;kBACAH;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;kBACA1B;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAqB;gBACA;kBACA;kBACAF;;kBAEA;kBACAO,6CAEA;kBACAC;oBACA;oBACA;sBACAC;oBACA;;oBAEA;oBACA;sBACA;sBACAA;wBACA;0BACAC;0BACAC;0BACAC;0BACAC;wBACA;sBACA;;sBAEA;sBACAJ;oBACA;;oBAEA;oBACA;sBACA;sBACAA;wBACA;0BACAK;0BACAD;0BACAE;wBACA;sBACA;;sBAEA;sBACAN;oBACA;oBAEA;kBACA,IAEA;kBACA;oBACA;kBACA;oBACA;kBACA;kBAEA;gBACA;kBACAjB;oBACAK;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;gBACAR;kBACAK;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkB;MACA;;MAEA;MACA;QACA;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA3B;UACAK;UACAC;QACA;QACA;MACA;MAEA;QACAN;UACAK;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;;MAEA;MACA;QACA;QACA;MACA;MAEAN;QACAK;QACAuB;QACAC;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAnB;sBAAA;sBAAA;oBAAA;oBAAA;oBAEAV;sBACAK;sBACAyB;oBACA;oBAEA9C;sBACAsC;oBACA;oBAAA;oBAAA,OAEA;kBAAA;oBAAAS;oBAEA;sBACA;sBACA;;sBAEA;sBACAC;wBAAA;sBAAA;sBACA;wBACAA;sBACA;;sBAEA;sBACAhC;sBACAA;wBACAK;wBACAC;sBACA;;sBAEA;sBACA;wBACA;wBACAE;sBACA;;sBAEA;sBACA;wBACAyB;0BACA;0BACA;wBACA;sBACA;oBACA;sBACAjC;sBACAA;wBACAK;wBACAC;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAE;oBACAR;oBACAA;sBACAK;sBACAC;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACA4B;MAAA;MACA;MACA;MACA;QACAN;UACA,mDACAO,+DACAA;UACA;QACA;MACA;MAEAnC;QACAK;QACAuB;QACAQ;QACAP;UACA;YACA;YACA7B;cACAK;cACAuB;cACAC;gBAAA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA,KACAQ;4BAAA;4BAAA;0BAAA;0BAAA;0BAGArC;4BACAK;4BACAyB;0BACA;0BAEA9C;4BACAsC;0BACA;0BAAA;0BAAA,OAEA;wBAAA;0BAAAS;0BAEA;4BACA;4BACA;;4BAEA;4BACAC;8BAAA;4BAAA;4BACA;8BACAA;4BACA;4BAEAhC;4BACAA;8BACAK;8BACAC;4BACA;;4BAEA;4BACA;0BACA;4BACAN;4BACAA;8BACAK;8BACAC;4BACA;0BACA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAEAE;0BACAR;0BACAA;4BACAK;4BACAC;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CAGA;gBAAA;kBAAA;gBAAA;gBAAA;cAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAgC;MAAA;MACA;MACA;MACA;QACAV;UACA;QACA;MACA;MAEA5B;QACAK;QACAuB;QACAQ;QACAP;UACA;YACA;YACA7B;cACAK;cACAuB;cACAC;gBAAA;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA,KACAQ;4BAAA;4BAAA;0BAAA;0BAAA;0BAGArC;4BACAK;4BACAyB;0BACA;0BAEA9C;4BACAsC;0BACA;0BAAA;0BAAA,OAEA;wBAAA;0BAAAS;0BAEA;4BACA;4BACA;;4BAEA;4BACAC;8BAAA;4BAAA;4BACA;8BACAA;4BACA;4BAEAhC;4BACAA;8BACAK;8BACAC;4BACA;;4BAEA;4BACA;0BACA;4BACAN;4BACAA;8BACAK;8BACAC;4BACA;0BACA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAEAE;0BACAR;0BACAA;4BACAK;4BACAC;0BACA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CAGA;gBAAA;kBAAA;gBAAA;gBAAA;cAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAiC;MAAA;MACA;MACA;QACAC;QACAjC;MACA;;MAEA;MACA;;MAEA;MACA;QACAnB;QACAC;MACA;MAEA;MAEA;QACA;UACA;UACA;;UAEA;UACA;YACA;cACA4B;YACA;;YAEA;YACA;cACAA;gBACA;kBACAC;kBACAC;gBACA;cACA;YACA;YAEA;UACA;;UAEA;UACA;UACA;QACA;MACA;QACAX;MACA;QACA;;QAEA;QACAyB;UACAjC;YACAwC;YACAjC;UACA;QACA;MACA;IACA;IAEA;IACAkC;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9sBA;AAAA;AAAA;AAAA;AAAgxC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;;ACApyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/points/mall.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/points/mall.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mall.vue?vue&type=template&id=163aafd7&scoped=true&\"\nvar renderjs\nimport script from \"./mall.vue?vue&type=script&lang=js&\"\nexport * from \"./mall.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mall.vue?vue&type=style&index=0&id=163aafd7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"163aafd7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/points/mall.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=template&id=163aafd7&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && _vm.filteredGoodsList.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.filteredGoodsList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g1 =\n          item.type === \"coupon\" && item.coupons && item.coupons.length > 0\n        var g2 = g1 ? item.coupons.length : null\n        var g3 = g1 && g2 > 2 ? item.coupons.length : null\n        var g4 =\n          item.type !== \"coupon\" && item.products && item.products.length > 0\n        var g5 = g4 ? item.products && item.products.length > 2 : null\n        var g6 = g4 && g5 ? item.products.length : null\n        var g7 =\n          item.type === \"coupon\" && item.coupons && item.coupons.length > 0\n        return {\n          $orig: $orig,\n          g1: g1,\n          g2: g2,\n          g3: g3,\n          g4: g4,\n          g5: g5,\n          g6: g6,\n          g7: g7,\n        }\n      })\n    : null\n  var g8 = !_vm.loading && _vm.filteredGoodsList.length === 0\n  var g9 = !_vm.loading && _vm.filteredGoodsList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g8: g8,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航和积分展示 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">积分商城</text>\r\n        <view class=\"empty-placeholder\"></view>\r\n      </view>\r\n      \r\n      <!-- 积分信息卡片 -->\r\n      <view class=\"points-card\">\r\n        <view class=\"points-info\">\r\n          <text class=\"label\">我的积分</text>\r\n          <view class=\"value-container\">\r\n            <text class=\"value\">{{userPoints}}</text>\r\n            <view class=\"detail-btn\" @tap=\"goToPointsDetail\">\r\n              <text>兑换记录</text>\r\n              <view class=\"arrow-right\"></view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <view class=\"points-tips\">\r\n          <text>订单完成后可获得积分</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 分类选项卡 -->\r\n    <view class=\"category-tabs\">\r\n      <view class=\"tab-item\" :class=\"{active: activeTab === 'all'}\" @tap=\"switchTab('all')\">\r\n        <text>全部商品</text>\r\n      </view>\r\n      <view class=\"tab-item\" :class=\"{active: activeTab === 'coupon'}\" @tap=\"switchTab('coupon')\">\r\n        <text>优惠券</text>\r\n      </view>\r\n      <view class=\"tab-item\" :class=\"{active: activeTab === 'product'}\" @tap=\"switchTab('product')\">\r\n        <text>实物商品</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 商品列表 -->\r\n    <view class=\"goods-list\" v-if=\"!loading && filteredGoodsList.length > 0\">\r\n      <view class=\"goods-card\" v-for=\"(item, index) in filteredGoodsList\" :key=\"item.id\" @tap=\"exchange(item)\">\r\n        <image class=\"goods-image\" :src=\"item.image || '/static/images/placeholder.png'\" @error=\"handleImageError\" mode=\"aspectFill\" />\r\n        \r\n        <view class=\"goods-info\">\r\n          <text class=\"goods-name\">{{item.name}}</text>\r\n          \r\n          <!-- 优惠券标签 -->\r\n          <view class=\"tags-row\" v-if=\"item.type === 'coupon' && item.coupons && item.coupons.length > 0\">\r\n            <view class=\"tag\" v-for=\"(coupon, cIndex) in item.coupons\" :key=\"cIndex\" v-if=\"cIndex < 2\">\r\n              <text v-if=\"coupon.min_amount > 0\">满{{coupon.min_amount}}减{{coupon.amount}}</text>\r\n              <text v-else>{{coupon.amount}}元无门槛</text>\r\n            </view>\r\n            <view class=\"tag more-tag\" v-if=\"item.coupons.length > 2\">\r\n              <text>+{{item.coupons.length - 2}}张</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 商品标签 -->\r\n          <view class=\"tags-row\" v-if=\"item.type !== 'coupon' && item.products && item.products.length > 0\">\r\n            <view class=\"tag product-tag\" v-for=\"(product, pIndex) in item.products\" :key=\"pIndex\" v-if=\"pIndex < 2\">\r\n              <text>{{product.name}}</text>\r\n            </view>\r\n            <view class=\"tag more-tag\" v-if=\"item.products && item.products.length > 2\">\r\n              <text>+{{item.products.length - 2}}件</text>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 商品分类 -->\r\n          <view class=\"category-info\" v-if=\"item.type !== 'coupon' && item.category_text\">\r\n            <text>{{item.category_text}}</text>\r\n          </view>\r\n          \r\n          <!-- 有效期显示 -->\r\n          <view class=\"validity\" v-if=\"item.type === 'coupon' && item.coupons && item.coupons.length > 0\">\r\n            <text>领取后{{item.coupons[0].valid_day}}天内有效</text>\r\n          </view>\r\n          \r\n          <view class=\"bottom-row\">\r\n            <view class=\"points-price\">\r\n              <text class=\"price-value\">{{item.points}}</text>\r\n              <text class=\"price-unit\">积分</text>\r\n            </view>\r\n            \r\n            <view class=\"exchange-btn\" :class=\"{disabled: item.points > userPoints || item.stock <= 0}\">\r\n              <text>{{item.stock <= 0 ? '已售罄' : (item.points > userPoints ? '积分不足' : '立即兑换')}}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        \r\n        <!-- 商品类型标签 -->\r\n        <view class=\"type-tag\" :class=\"{'coupon-tag': item.type === 'coupon', 'product-tag': item.type !== 'coupon'}\">\r\n          <text>{{item.type === 'coupon' ? '券' : '品'}}</text>\r\n        </view>\r\n        \r\n        <!-- 库存标签 -->\r\n        <view class=\"stock-tag\" v-if=\"item.stock !== undefined && item.stock <= 10 && item.stock > 0\">\r\n          <text>仅剩{{item.stock}}{{item.type === 'coupon' ? '张' : '件'}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 加载中状态 -->\r\n    <view v-if=\"loading\" class=\"loading-container\">\r\n      <view class=\"loading-spinner\"></view>\r\n      <text>正在加载...</text>\r\n    </view>\r\n    \r\n    <!-- 空状态 -->\r\n    <view v-if=\"!loading && filteredGoodsList.length === 0\" class=\"empty-container\">\r\n      <image class=\"empty-image\" src=\"/static/my/c04474c2444f5692feea25109359ae0f.png\" mode=\"aspectFit\" />\r\n      <text>暂无商品</text>\r\n      <text class=\"empty-tips\">敬请期待更多商品上架</text>\r\n    </view>\r\n    \r\n    <!-- 加载更多 -->\r\n    <view v-if=\"!loading && filteredGoodsList.length > 0\" class=\"load-more\" @tap=\"loadMore\">\r\n      <text v-if=\"hasMore\">点击加载更多</text>\r\n      <text v-else>—— 已经到底了 ——</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getUserProfile } from '@/api/user';\r\nimport { getPointMallList, exchangePointMallItem } from '@/api/mall';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      userPoints: 0,\r\n      goodsList: [],\r\n      activeTab: 'all', // 默认显示全部商品\r\n      page: 1,\r\n      limit: 10,\r\n      loading: true,\r\n      hasMore: true,\r\n      isRefreshing: false\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 根据当前选中的标签过滤商品列表\r\n    filteredGoodsList() {\r\n      if (this.activeTab === 'all') {\r\n        return this.goodsList;\r\n      } else if (this.activeTab === 'coupon') {\r\n        return this.goodsList.filter(item => item.type === 'coupon');\r\n      } else if (this.activeTab === 'product') {\r\n        return this.goodsList.filter(item => item.type !== 'coupon');\r\n      }\r\n      return this.goodsList;\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    this.getUserInfo();\r\n    this.getGoodsList();\r\n  },\r\n  \r\n  // 添加下拉刷新\r\n  onPullDownRefresh() {\r\n    this.handlePullRefresh();\r\n  },\r\n  \r\n  methods: {\r\n    // 切换标签\r\n    switchTab(tab) {\r\n      this.activeTab = tab;\r\n    },\r\n    \r\n    // 跳转到兑换记录页面\r\n    goToPointsDetail() {\r\n      uni.navigateTo({\r\n        url: '/pages/points/record'\r\n      });\r\n    },\r\n    \r\n    handleBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 处理下拉刷新\r\n    async handlePullRefresh() {\r\n      if (this.isRefreshing) return;\r\n      \r\n      this.isRefreshing = true;\r\n      \r\n      try {\r\n        // 重置页码\r\n        this.page = 1;\r\n        \r\n        // 并行请求用户信息和商品列表\r\n        await Promise.all([\r\n          this.getUserInfo(),\r\n          this.getGoodsList()\r\n        ]);\r\n        \r\n        uni.showToast({\r\n          title: '刷新成功',\r\n          icon: 'none',\r\n          duration: 1000\r\n        });\r\n      } catch (error) {\r\n        console.error('下拉刷新异常:', error);\r\n        uni.showToast({\r\n          title: '刷新失败，请重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.isRefreshing = false;\r\n        uni.stopPullDownRefresh();\r\n      }\r\n    },\r\n    \r\n    // 获取用户信息包含积分\r\n    async getUserInfo() {\r\n      try {\r\n        const res = await getUserProfile();\r\n        console.log('用户信息完整数据:', JSON.stringify(res));\r\n        \r\n        if (res.code === 1) {\r\n          // 打印出用户信息数据结构\r\n          console.log('用户信息数据结构:', JSON.stringify(res.data));\r\n          \r\n          // 从返回的数据中获取积分值\r\n          // 根据my.vue页面的代码，应该使用score字段而不是points\r\n          let points = 0;\r\n          if (res.data && res.data.score !== undefined) {\r\n            points = parseInt(res.data.score || 0);\r\n            console.log('从data.score获取积分:', points);\r\n          } else if (res.data && res.data.points !== undefined) {\r\n            points = parseInt(res.data.points || 0);\r\n            console.log('从data.points获取积分:', points);\r\n          } else if (res.data && res.data.user && res.data.user.score !== undefined) {\r\n            points = parseInt(res.data.user.score || 0);\r\n            console.log('从data.user.score获取积分:', points);\r\n          } else if (res.data && res.data.assets && res.data.assets.points !== undefined) {\r\n            points = parseInt(res.data.assets.points || 0);\r\n            console.log('从data.assets.points获取积分:', points);\r\n          }\r\n          \r\n          this.userPoints = points || 0;\r\n          \r\n          // 同时更新本地存储\r\n          uni.setStorageSync('points', this.userPoints);\r\n        } else {\r\n          console.error('获取用户信息失败:', res.msg);\r\n          \r\n          // 如果无法获取用户信息，尝试从本地存储获取\r\n          const points = uni.getStorageSync('points');\r\n          if (points !== '' && points !== undefined) {\r\n            this.userPoints = parseInt(points);\r\n            console.log('从本地存储获取积分:', this.userPoints);\r\n          } else {\r\n            // 尝试从用户信息中获取\r\n            const userInfo = uni.getStorageSync('userInfo');\r\n            console.log('从本地存储获取用户信息:', JSON.stringify(userInfo));\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('获取用户信息异常:', error);\r\n        \r\n        // 如果请求出错，尝试从本地存储获取\r\n        const points = uni.getStorageSync('points');\r\n        if (points !== '' && points !== undefined) {\r\n          this.userPoints = parseInt(points);\r\n          console.log('从本地存储获取积分:', this.userPoints);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取商品列表\r\n    async getGoodsList() {\r\n      try {\r\n        this.loading = true;\r\n        const params = {\r\n          page: this.page,\r\n          limit: this.limit\r\n        };\r\n        \r\n        const res = await getPointMallList(params);\r\n        if (res.code === 1) {\r\n          // 检查API返回的数据结构\r\n          console.log('积分商城数据:', res.data);\r\n          \r\n          // 根据实际返回的数据结构获取商品列表\r\n          const list = res.data.rows || res.data.list || [];\r\n          \r\n          // 处理商品数据，确保中文显示\r\n          const processedList = list.map(item => {\r\n            // 如果category_text不存在，根据type设置中文类别\r\n            if (!item.category_text) {\r\n              item.category_text = item.type === 'coupon' ? '优惠券' : '实物商品';\r\n            }\r\n            \r\n            // 处理优惠券数据，确保没有多余元素\r\n            if (item.type === 'coupon' && item.coupons && item.coupons.length > 0) {\r\n              // 只保留必要的优惠券数据，并处理无门槛优惠券\r\n              item.coupons = item.coupons.map(coupon => {\r\n                return {\r\n                  min_amount: parseFloat(coupon.min_amount || 0),\r\n                  amount: parseFloat(coupon.amount || 0),\r\n                  valid_day: parseInt(coupon.valid_day || 30),\r\n                  name: coupon.name || ''\r\n                };\r\n              });\r\n              \r\n              // 根据优惠券类型添加描述\r\n              item.couponDesc = this.getCouponDescription(item.coupons);\r\n            }\r\n            \r\n            // 处理实物商品数据\r\n            if (item.type !== 'coupon' && item.products && item.products.length > 0) {\r\n              // 处理商品数据\r\n              item.products = item.products.map(product => {\r\n                return {\r\n                  id: product.id || 0,\r\n                  name: product.name || '',\r\n                  image: product.image || ''\r\n                };\r\n              });\r\n              \r\n              // 添加商品描述\r\n              item.productDesc = this.getProductDescription(item.products);\r\n            }\r\n            \r\n            return item;\r\n          });\r\n          \r\n          // 如果是第一页，替换列表，否则追加\r\n          if (this.page === 1) {\r\n            this.goodsList = processedList;\r\n          } else {\r\n            this.goodsList = [...this.goodsList, ...processedList];\r\n          }\r\n          \r\n          this.hasMore = list.length >= this.limit;\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '获取商品列表失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('获取商品列表异常:', error);\r\n        uni.showToast({\r\n          title: '网络异常，请稍后重试',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 获取优惠券描述\r\n    getCouponDescription(coupons) {\r\n      if (!coupons || coupons.length === 0) return '';\r\n      \r\n      // 找出最大优惠的优惠券\r\n      const maxCoupon = coupons.reduce((max, current) => {\r\n        return current.amount > max.amount ? current : max;\r\n      }, coupons[0]);\r\n      \r\n      if (maxCoupon.min_amount > 0) {\r\n        return `满${maxCoupon.min_amount}减${maxCoupon.amount}`;\r\n      } else {\r\n        return `${maxCoupon.amount}元无门槛`;\r\n      }\r\n    },\r\n    \r\n    // 获取商品描述\r\n    getProductDescription(products) {\r\n      if (!products || products.length === 0) return '';\r\n      \r\n      if (products.length === 1) {\r\n        return products[0].name;\r\n      } else {\r\n        return `${products[0].name}等${products.length}件商品`;\r\n      }\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.hasMore && !this.loading) {\r\n        this.page++;\r\n        this.loading = true;\r\n        this.getGoodsList();\r\n      }\r\n    },\r\n    \r\n    // 兑换商品\r\n    exchange(item) {\r\n      if (item.points > this.userPoints) {\r\n        uni.showToast({\r\n          title: '积分不足',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      if (item.stock <= 0) {\r\n        uni.showToast({\r\n          title: '商品已售罄',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 如果是优惠券类型并且有多张优惠券，显示详情弹窗\r\n      if (item.type === 'coupon' && item.coupons && item.coupons.length > 1) {\r\n        this.showCouponDetail(item);\r\n        return;\r\n      }\r\n      \r\n      // 如果是实物商品并且有多个产品，显示详情弹窗\r\n      if (item.type !== 'coupon' && item.products && item.products.length > 1) {\r\n        this.showProductDetail(item);\r\n        return;\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '确认兑换',\r\n        content: `确定使用${item.points}积分兑换${item.name}吗？`,\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            try {\r\n              uni.showLoading({\r\n                title: '兑换中...',\r\n                mask: true\r\n              });\r\n              \r\n              const data = {\r\n                id: item.id\r\n              };\r\n              \r\n              const result = await exchangePointMallItem(data);\r\n              \r\n              if (result.code === 1) {\r\n                // 更新本地数据，无需完全重载\r\n                this.userPoints -= item.points;\r\n                \r\n                // 更新商品库存\r\n                const updatedItem = this.goodsList.find(i => i.id === item.id);\r\n                if (updatedItem) {\r\n                  updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);\r\n                }\r\n                \r\n                // 先隐藏加载框，再显示成功提示\r\n                uni.hideLoading();\r\n                uni.showToast({\r\n                  title: '兑换成功',\r\n                  icon: 'none'\r\n                });\r\n                \r\n                // 在后台更新用户信息\r\n                this.getUserInfo().then(() => {\r\n                  // 静默更新，不影响用户体验\r\n                  console.log('用户信息已更新');\r\n                });\r\n                \r\n                // 如果库存为零，可能需要刷新列表以获取最新数据\r\n                if (updatedItem && updatedItem.stock <= 0) {\r\n                  setTimeout(() => {\r\n                    // 延迟刷新列表，保证提示消息显示\r\n                    this.refreshGoodsList();\r\n                  }, 1500);\r\n                }\r\n              } else {\r\n                uni.hideLoading();\r\n                uni.showToast({\r\n                  title: result.msg || '兑换失败',\r\n                  icon: 'none'\r\n                });\r\n              }\r\n            } catch (error) {\r\n              console.error('兑换商品异常:', error);\r\n              uni.hideLoading();\r\n              uni.showToast({\r\n                title: '网络异常，请稍后重试',\r\n                icon: 'none'\r\n              });\r\n            }\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 显示优惠券详情\r\n    showCouponDetail(item) {\r\n      // 构建优惠券详情内容\r\n      let content = '';\r\n      if (item.coupons && item.coupons.length > 0) {\r\n        content = item.coupons.map((coupon, index) => {\r\n          const desc = coupon.min_amount > 0 ? \r\n            `满${coupon.min_amount}减${coupon.amount}` : \r\n            `${coupon.amount}元无门槛`;\r\n          return `${index + 1}. ${coupon.name || desc}（${coupon.valid_day}天有效）`;\r\n        }).join('\\n');\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '优惠券详情',\r\n        content: content || '暂无详情',\r\n        confirmText: '立即兑换',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 用户点击确认，继续兑换流程\r\n            uni.showModal({\r\n              title: '确认兑换',\r\n              content: `确定使用${item.points}积分兑换${item.name}吗？`,\r\n              success: async (confirmRes) => {\r\n                if (confirmRes.confirm) {\r\n                  // 执行兑换操作\r\n                  try {\r\n                    uni.showLoading({\r\n                      title: '兑换中...',\r\n                      mask: true\r\n                    });\r\n                    \r\n                    const data = {\r\n                      id: item.id\r\n                    };\r\n                    \r\n                    const result = await exchangePointMallItem(data);\r\n                    \r\n                    if (result.code === 1) {\r\n                      // 更新本地数据\r\n                      this.userPoints -= item.points;\r\n                      \r\n                      // 更新商品库存\r\n                      const updatedItem = this.goodsList.find(i => i.id === item.id);\r\n                      if (updatedItem) {\r\n                        updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);\r\n                      }\r\n                      \r\n                      uni.hideLoading();\r\n                      uni.showToast({\r\n                        title: '兑换成功',\r\n                        icon: 'none'\r\n                      });\r\n                      \r\n                      // 更新用户信息\r\n                      this.getUserInfo();\r\n                    } else {\r\n                      uni.hideLoading();\r\n                      uni.showToast({\r\n                        title: result.msg || '兑换失败',\r\n                        icon: 'none'\r\n                      });\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('兑换商品异常:', error);\r\n                    uni.hideLoading();\r\n                    uni.showToast({\r\n                      title: '网络异常，请稍后重试',\r\n                      icon: 'none'\r\n                    });\r\n                  }\r\n                }\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 显示商品详情\r\n    showProductDetail(item) {\r\n      // 构建商品详情内容\r\n      let content = '';\r\n      if (item.products && item.products.length > 0) {\r\n        content = item.products.map((product, index) => {\r\n          return `${index + 1}. ${product.name}`;\r\n        }).join('\\n');\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '商品详情',\r\n        content: content || '暂无详情',\r\n        confirmText: '立即兑换',\r\n        success: (res) => {\r\n          if (res.confirm) {\r\n            // 用户点击确认，继续兑换流程\r\n            uni.showModal({\r\n              title: '确认兑换',\r\n              content: `确定使用${item.points}积分兑换${item.name}吗？`,\r\n              success: async (confirmRes) => {\r\n                if (confirmRes.confirm) {\r\n                  // 执行兑换操作\r\n                  try {\r\n                    uni.showLoading({\r\n                      title: '兑换中...',\r\n                      mask: true\r\n                    });\r\n                    \r\n                    const data = {\r\n                      id: item.id\r\n                    };\r\n                    \r\n                    const result = await exchangePointMallItem(data);\r\n                    \r\n                    if (result.code === 1) {\r\n                      // 更新本地数据\r\n                      this.userPoints -= item.points;\r\n                      \r\n                      // 更新商品库存\r\n                      const updatedItem = this.goodsList.find(i => i.id === item.id);\r\n                      if (updatedItem) {\r\n                        updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);\r\n                      }\r\n                      \r\n                      uni.hideLoading();\r\n                      uni.showToast({\r\n                        title: '兑换成功',\r\n                        icon: 'none'\r\n                      });\r\n                      \r\n                      // 更新用户信息\r\n                      this.getUserInfo();\r\n                    } else {\r\n                      uni.hideLoading();\r\n                      uni.showToast({\r\n                        title: result.msg || '兑换失败',\r\n                        icon: 'none'\r\n                      });\r\n                    }\r\n                  } catch (error) {\r\n                    console.error('兑换商品异常:', error);\r\n                    uni.hideLoading();\r\n                    uni.showToast({\r\n                      title: '网络异常，请稍后重试',\r\n                      icon: 'none'\r\n                    });\r\n                  }\r\n                }\r\n              }\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 平滑刷新商品列表\r\n    refreshGoodsList() {\r\n      // 先保存滚动位置\r\n      const currentScrollTop = uni.pageScrollTo({\r\n        scrollTop: 0,\r\n        duration: 0\r\n      });\r\n      \r\n      // 重置页码\r\n      this.page = 1;\r\n      \r\n      // 请求新数据\r\n      const params = {\r\n        page: this.page,\r\n        limit: this.limit\r\n      };\r\n      \r\n      this.loading = true;\r\n      \r\n      getPointMallList(params).then(res => {\r\n        if (res.code === 1) {\r\n          // 获取新列表\r\n          const list = res.data.rows || res.data.list || [];\r\n          \r\n          // 处理商品数据\r\n          const processedList = list.map(item => {\r\n            if (!item.category_text) {\r\n              item.category_text = item.type === 'coupon' ? '优惠券' : '实物商品';\r\n            }\r\n            \r\n            // 处理优惠券数据\r\n            if (item.type === 'coupon' && item.coupons && item.coupons.length > 0) {\r\n              item.coupons = item.coupons.map(coupon => {\r\n                return {\r\n                  min_amount: coupon.min_amount || 0,\r\n                  amount: coupon.amount || 0\r\n                };\r\n              });\r\n            }\r\n            \r\n            return item;\r\n          });\r\n          \r\n          // 平滑更新列表\r\n          this.goodsList = processedList;\r\n          this.hasMore = list.length >= this.limit;\r\n        }\r\n      }).catch(err => {\r\n        console.error('刷新列表异常:', err);\r\n      }).finally(() => {\r\n        this.loading = false;\r\n        \r\n        // 恢复滚动位置\r\n        setTimeout(() => {\r\n          uni.pageScrollTo({\r\n            scrollTop: currentScrollTop || 0,\r\n            duration: 0\r\n          });\r\n        }, 50);\r\n      });\r\n    },\r\n    \r\n    // 处理图片加载错误\r\n    handleImageError(e) {\r\n      // 替换为默认图片\r\n      e.target.src = '/static/images/placeholder.png';\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n  padding-top: 88rpx;\r\n  padding-bottom: 30rpx;\r\n  position: relative;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 30rpx;\r\n    \r\n    .back-icon {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #fff;\r\n      font-weight: bold;\r\n    }\r\n    \r\n    .empty-placeholder {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n    }\r\n  }\r\n  \r\n  .points-card {\r\n    margin: 30rpx 40rpx 10rpx;\r\n    background: rgba(255, 255, 255, 0.2);\r\n    border-radius: 20rpx;\r\n    padding: 30rpx;\r\n    backdrop-filter: blur(5px);\r\n    \r\n    .points-info {\r\n      .label {\r\n        font-size: 28rpx;\r\n        color: rgba(255,255,255,0.9);\r\n        display: block;\r\n      }\r\n      \r\n      .value-container {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-top: 10rpx;\r\n        \r\n        .value {\r\n          font-size: 52rpx;\r\n          color: #fff;\r\n          font-weight: bold;\r\n        }\r\n        \r\n        .detail-btn {\r\n          display: flex;\r\n          align-items: center;\r\n          background: rgba(255, 255, 255, 0.3);\r\n          padding: 10rpx 20rpx;\r\n          border-radius: 100rpx;\r\n          \r\n          text {\r\n            font-size: 24rpx;\r\n            color: #fff;\r\n          }\r\n          \r\n          .arrow-right {\r\n            width: 14rpx;\r\n            height: 14rpx;\r\n            border-top: 2rpx solid #fff;\r\n            border-right: 2rpx solid #fff;\r\n            transform: rotate(45deg);\r\n            margin-left: 10rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .points-tips {\r\n      margin-top: 20rpx;\r\n      \r\n      text {\r\n        font-size: 24rpx;\r\n        color: rgba(255,255,255,0.8);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.category-tabs {\r\n  display: flex;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: -20rpx 20rpx 20rpx;\r\n  padding: 20rpx;\r\n  position: relative;\r\n  z-index: 1;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n  \r\n  .tab-item {\r\n    flex: 1;\r\n    text-align: center;\r\n    padding: 20rpx 0;\r\n    position: relative;\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      position: relative;\r\n      z-index: 1;\r\n    }\r\n    \r\n    &.active {\r\n      text {\r\n        color: #8cd548;\r\n        font-weight: bold;\r\n      }\r\n      \r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        bottom: 10rpx;\r\n        left: 50%;\r\n        transform: translateX(-50%);\r\n        width: 40rpx;\r\n        height: 6rpx;\r\n        background: #8cd548;\r\n        border-radius: 6rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.goods-list {\r\n  padding: 0 20rpx;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: space-between;\r\n  \r\n  .goods-card {\r\n    width: calc(50% - 10rpx);\r\n    margin-bottom: 20rpx;\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n    position: relative;\r\n    \r\n    .goods-image {\r\n      width: 100%;\r\n      height: 240rpx;\r\n      display: block;\r\n      object-fit: cover;\r\n    }\r\n    \r\n    .goods-info {\r\n      padding: 20rpx;\r\n      \r\n      .goods-name {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        line-height: 1.4;\r\n        height: 80rpx;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        display: -webkit-box;\r\n        -webkit-line-clamp: 2;\r\n        -webkit-box-orient: vertical;\r\n      }\r\n      \r\n      .tags-row {\r\n        margin: 16rpx 0;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        min-height: 40rpx;\r\n        \r\n        .tag {\r\n          background: rgba(255, 68, 68, 0.1);\r\n          border-radius: 8rpx;\r\n          padding: 0 12rpx;\r\n          margin-right: 10rpx;\r\n          margin-bottom: 10rpx;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          height: 32rpx;\r\n          box-sizing: border-box;\r\n          min-width: 60rpx;\r\n          \r\n          text {\r\n            font-size: 20rpx;\r\n            color: #ff4444;\r\n            line-height: 1;\r\n            padding: 0;\r\n            margin: 0;\r\n            white-space: nowrap;\r\n          }\r\n          \r\n          &.product-tag {\r\n            background: rgba(106, 181, 46, 0.1);\r\n            \r\n            text {\r\n              color: #6ab52e;\r\n            }\r\n          }\r\n          \r\n          &.more-tag {\r\n            background: rgba(106, 181, 46, 0.1);\r\n            \r\n            text {\r\n              color: #6ab52e;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      \r\n      .category-info {\r\n        margin-top: 8rpx;\r\n        \r\n        text {\r\n          font-size: 22rpx;\r\n          color: #6ab52e;\r\n        }\r\n      }\r\n      \r\n      .validity {\r\n        margin-top: 8rpx;\r\n        \r\n        text {\r\n          font-size: 22rpx;\r\n          color: #999;\r\n        }\r\n      }\r\n      \r\n      .bottom-row {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-top: 16rpx;\r\n        \r\n        .points-price {\r\n          display: flex;\r\n          align-items: baseline;\r\n          \r\n          .price-value {\r\n            font-size: 32rpx;\r\n            color: #ff4444;\r\n            font-weight: bold;\r\n          }\r\n          \r\n          .price-unit {\r\n            font-size: 24rpx;\r\n            color: #ff4444;\r\n            margin-left: 4rpx;\r\n          }\r\n        }\r\n        \r\n        .exchange-btn {\r\n          padding: 0 20rpx;\r\n          background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n          border-radius: 100rpx;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          height: 48rpx;\r\n          box-sizing: border-box;\r\n          min-width: 120rpx;\r\n          \r\n          text {\r\n            font-size: 24rpx;\r\n            color: #fff;\r\n            line-height: 1;\r\n            padding: 0;\r\n            margin: 0;\r\n          }\r\n          \r\n          &.disabled {\r\n            background: linear-gradient(135deg, #ccc 0%, #999 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .type-tag {\r\n      position: absolute;\r\n      top: 20rpx;\r\n      left: 20rpx;\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      \r\n      &.coupon-tag {\r\n        background: #ff4444;\r\n      }\r\n      \r\n      &.product-tag {\r\n        background: #6ab52e;\r\n      }\r\n      \r\n      text {\r\n        font-size: 22rpx;\r\n        color: #fff;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n    \r\n    .stock-tag {\r\n      position: absolute;\r\n      top: 20rpx;\r\n      right: 20rpx;\r\n      padding: 4rpx 12rpx;\r\n      background: rgba(255, 68, 68, 0.8);\r\n      border-radius: 8rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      height: 32rpx;\r\n      box-sizing: border-box;\r\n      \r\n      text {\r\n        font-size: 20rpx;\r\n        color: #fff;\r\n        line-height: 1;\r\n        padding: 0;\r\n        margin: 0;\r\n        white-space: nowrap;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.loading-container {\r\n  padding: 40rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  .loading-spinner {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    border: 4rpx solid rgba(140, 213, 72, 0.2);\r\n    border-left: 4rpx solid #8cd548;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.empty-container {\r\n  padding: 100rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  .empty-image {\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 30rpx;\r\n    color: #666;\r\n    \r\n    &.empty-tips {\r\n      font-size: 24rpx;\r\n      color: #999;\r\n      margin-top: 10rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.load-more {\r\n  padding: 40rpx 0 80rpx;\r\n  text-align: center;\r\n  \r\n  text {\r\n    font-size: 26rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=style&index=0&id=163aafd7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=style&index=0&id=163aafd7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309726\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}