@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-18f9de58 {
  min-height: 100vh;
  background: #f8f8f8;
}
.header.data-v-18f9de58 {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding-top: 88rpx;
  padding-bottom: 30rpx;
  position: relative;
}
.header .nav-bar.data-v-18f9de58 {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.header .nav-bar .back-icon.data-v-18f9de58 {
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-18f9de58 {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}
.header .nav-bar .empty-placeholder.data-v-18f9de58 {
  width: 48rpx;
  height: 48rpx;
}
.record-list.data-v-18f9de58 {
  padding: 0 20rpx;
}
.record-list .record-item.data-v-18f9de58 {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 30rpx 20rpx;
}
.record-list .record-item.data-v-18f9de58:last-child {
  border-bottom: none;
}
.record-list .record-item .item-left.data-v-18f9de58 {
  margin-right: 20rpx;
}
.record-list .record-item .item-left .item-image.data-v-18f9de58 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  object-fit: cover;
}
.record-list .record-item .item-center.data-v-18f9de58 {
  flex: 1;
  overflow: hidden;
}
.record-list .record-item .item-center .item-name.data-v-18f9de58 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.record-list .record-item .item-center .item-tag.data-v-18f9de58 {
  display: inline-flex;
  align-items: center;
  height: 32rpx;
  padding: 0 12rpx;
  border-radius: 4rpx;
  margin-bottom: 10rpx;
  min-width: 80rpx;
}
.record-list .record-item .item-center .item-tag text.data-v-18f9de58 {
  font-size: 20rpx;
  white-space: nowrap;
}
.record-list .record-item .item-center .item-tag.coupon-tag.data-v-18f9de58 {
  background: rgba(255, 68, 68, 0.1);
}
.record-list .record-item .item-center .item-tag.coupon-tag text.data-v-18f9de58 {
  color: #ff4444;
}
.record-list .record-item .item-center .item-tag.product-tag.data-v-18f9de58 {
  background: rgba(106, 181, 46, 0.1);
}
.record-list .record-item .item-center .item-tag.product-tag text.data-v-18f9de58 {
  color: #6ab52e;
}
.record-list .record-item .item-center .item-time.data-v-18f9de58 {
  font-size: 24rpx;
  color: #999;
}
.record-list .record-item .item-right.data-v-18f9de58 {
  text-align: right;
}
.record-list .record-item .item-right .item-status.data-v-18f9de58 {
  margin-bottom: 10rpx;
}
.record-list .record-item .item-right .item-status text.data-v-18f9de58 {
  font-size: 26rpx;
}
.record-list .record-item .item-right .item-status.success text.data-v-18f9de58 {
  color: #6ab52e;
}
.record-list .record-item .item-right .item-status.pending text.data-v-18f9de58 {
  color: #ff9900;
}
.record-list .record-item .item-right .item-status.failed text.data-v-18f9de58 {
  color: #ff4444;
}
.record-list .record-item .item-right .item-points text.data-v-18f9de58 {
  font-size: 26rpx;
  color: #ff4444;
}
.loading-container.data-v-18f9de58 {
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.loading-container .loading-spinner.data-v-18f9de58 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(140, 213, 72, 0.2);
  border-left: 4rpx solid #8cd548;
  border-radius: 50%;
  -webkit-animation: spin-data-v-18f9de58 1s linear infinite;
          animation: spin-data-v-18f9de58 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container text.data-v-18f9de58 {
  font-size: 28rpx;
  color: #999;
}
.empty-container.data-v-18f9de58 {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.empty-container .empty-image.data-v-18f9de58 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.empty-container text.data-v-18f9de58 {
  font-size: 30rpx;
  color: #666;
}
.empty-container text.empty-tips.data-v-18f9de58 {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.bottom-tip.data-v-18f9de58 {
  padding: 40rpx 0 80rpx;
  text-align: center;
}
.bottom-tip text.data-v-18f9de58 {
  font-size: 26rpx;
  color: #999;
}
.bottom-tip .loading-more.data-v-18f9de58 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-tip .loading-more .loading-dot.data-v-18f9de58 {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid rgba(140, 213, 72, 0.2);
  border-left: 3rpx solid #8cd548;
  border-radius: 50%;
  -webkit-animation: spin-data-v-18f9de58 1s linear infinite;
          animation: spin-data-v-18f9de58 1s linear infinite;
  margin-right: 10rpx;
}
.bottom-tip .loading-more text.data-v-18f9de58 {
  font-size: 26rpx;
  color: #999;
}
@-webkit-keyframes spin-data-v-18f9de58 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-18f9de58 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}

