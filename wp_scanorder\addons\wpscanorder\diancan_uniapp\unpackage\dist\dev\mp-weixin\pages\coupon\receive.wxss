@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-264d149a {
  min-height: 100vh;
  background: #f8f8f8;
}
.header.data-v-264d149a {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-264d149a {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-264d149a {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-264d149a {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.coupon-list.data-v-264d149a {
  padding: 20rpx;
}
.coupon-list .coupon-item.data-v-264d149a {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}
.coupon-list .coupon-item .left.data-v-264d149a {
  width: 220rpx;
  background: #8cd548;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.coupon-list .coupon-item .left .amount.data-v-264d149a {
  display: flex;
  align-items: baseline;
  color: #fff;
}
.coupon-list .coupon-item .left .amount .symbol.data-v-264d149a {
  font-size: 32rpx;
}
.coupon-list .coupon-item .left .amount .value.data-v-264d149a {
  font-size: 60rpx;
  font-weight: bold;
}
.coupon-list .coupon-item .left .limit.data-v-264d149a {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 8rpx;
}
.coupon-list .coupon-item .right.data-v-264d149a {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.coupon-list .coupon-item .right .name.data-v-264d149a {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}
.coupon-list .coupon-item .right .date.data-v-264d149a {
  font-size: 24rpx;
  color: #999;
}
.coupon-list .coupon-item .right .receive-btn.data-v-264d149a {
  align-self: flex-end;
  padding: 12rpx 32rpx;
  background: #8cd548;
  border-radius: 100rpx;
}
.coupon-list .coupon-item .right .receive-btn text.data-v-264d149a {
  font-size: 26rpx;
  color: #fff;
}
.coupon-list .coupon-item .right .receive-btn.disabled.data-v-264d149a {
  background: #ccc;
}

