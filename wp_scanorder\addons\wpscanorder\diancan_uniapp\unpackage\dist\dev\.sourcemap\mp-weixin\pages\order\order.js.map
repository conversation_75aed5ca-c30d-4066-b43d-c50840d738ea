{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/order.vue?8c73", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/order.vue?e47e", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/order.vue?63ec", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/order.vue?d1be", "uni-app:///pages/order/order.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/order.vue?1217", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/order.vue?f073"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "OrderList", "data", "statusBarHeight", "currentTab", "tabs", "name", "orderList", "originalOrders", "refreshTimer", "isLoading", "page", "pageSize", "hasMore", "isLoadingMore", "onLoad", "onShow", "onHide", "onUnload", "onReachBottom", "onPullDownRefresh", "methods", "switchTab", "filterOrders", "loadOrders", "isRefresh", "isFirstLoad", "uni", "title", "processedOrders", "order", "orderType", "status", "status_text", "products", "id", "price", "count", "specs", "totalPrice", "console", "icon", "loadMoreOrders", "refreshOrders", "startAutoRefresh", "clearAutoRefresh", "clearInterval", "goToMenu", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACwDlrB;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC,OACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;EACA;EAEAC;IACA;IACA;;IAEA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEA;EACAC;IACA;MACA;IACA;EACA;EAEA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;QACA;QACA;UAAA;QAAA;MACA;QACA;QACA;UAAA;QAAA;MACA;IACA;IAEAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBACA;kBACA;kBACA;gBACA;gBAEA;gBAAA;gBAEA;gBACAC;gBACA;kBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA,OAEA;kBACAjB;kBACAC;gBACA;cAAA;gBAAA;gBAHAV;gBAKA;gBACA;kBACA;gBACA;;gBAEA;gBACA2B;kBACA,uCACAC;oBACAC;oBACAC;oBACAC;oBACAC;sBAAA;wBACAC;wBACA7B;wBACA8B;wBACAC;wBACAC;wBACAC;sBACA;oBAAA;kBAAA;gBAEA,IAEA;gBACA;kBACA;gBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA;gBAEA;kBACAZ;gBACA;;gBAEA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAa;gBACA;gBACA;kBACAb;oBACAC;oBACAa;kBACA;gBACA;;gBAEA;gBACAd;cAAA;gBAAA;gBAEA;kBACAA;gBACA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACA/B;kBACAC;gBACA;cAAA;gBAAA;gBAHAV;gBAKA;gBACA;kBACA;gBACA;;gBAEA;gBACA2B;kBACA,uCACAC;oBACAC;oBACAC;oBACAC;oBACAC;sBAAA;wBACAC;wBACA7B;wBACA8B;wBACAC;wBACAC;wBACAC;sBACA;oBAAA;kBAAA;gBAEA,IAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;gBACAb;kBACAC;kBACAa;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACAJ;QACA;MACA;;MAEAA;IACA;IAEA;IACAK;MACA;QACAC;QACA;QACAN;MACA;IACA;IAEAO;MACApB;QACAqB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9TA;AAAA;AAAA;AAAA;AAAyvC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACA7wC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=127632e4&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/order.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=template&id=127632e4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orderList.length\n  var g1 = _vm.orderList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<!-- 使用通用导航栏 -->\n\t\t<nav-bar \n\t\t\ttitle=\"我的订单\" \n\t\t\t:isTabbar=\"true\" \n\t\t\tbackground=\"white\"\n\t\t></nav-bar>\n\t\t\n\t\t<!-- 导航栏占位 -->\n\t\t<view class=\"navbar-placeholder\" :style=\"{ height: statusBarHeight + 88 + 'px' }\"></view>\n\n\t\t<!-- 订单类型切换 -->\n\t\t<view class=\"tab-section\">\n\t\t\t<view \n\t\t\t\tv-for=\"(tab, index) in tabs\" \n\t\t\t\t:key=\"index\"\n\t\t\t\t:class=\"['tab-item', currentTab === index ? 'active' : '']\"\n\t\t\t\t@tap=\"switchTab(index)\"\n\t\t\t>\n\t\t\t\t<text class=\"tab-text\">{{tab.name}}</text>\n\t\t\t\t<view v-if=\"currentTab === index\" class=\"tab-line\"></view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 订单内容区域 -->\n\t\t<view class=\"content-container\">\n\t\t\t<!-- 有订单数据时显示订单列表组件 -->\n\t\t\t<OrderList \n\t\t\t\tv-if=\"orderList.length > 0\"\n\t\t\t\t:order-list=\"orderList\"\n\t\t\t\t:current-tab=\"currentTab\"\n\t\t\t\t@refresh=\"refreshOrders\"\n\t\t\t/>\n\n\t\t\t<!-- 使用通用空状态组件 -->\n\t\t\t<empty-state \n\t\t\t\tv-else\n\t\t\t\ttext=\"您暂时还没有订单哦～\"\n\t\t\t\timage=\"/static/order/e186e04e8774da64b58c96a8bb479840.png\"\n\t\t\t\t:showAction=\"true\"\n\t\t\t\tactionText=\"去点单\"\n\t\t\t\t@action=\"goToMenu\"\n\t\t\t></empty-state>\n\t\t\t\n\t\t\t<!-- 加载状态提示 -->\n\t\t\t<view class=\"loading-more\" v-if=\"orderList.length > 0\">\n\t\t\t\t<text v-if=\"isLoadingMore\" class=\"loading-text\">加载中...</text>\n\t\t\t\t<text v-else-if=\"!hasMore\" class=\"no-more-text\">没有更多订单了</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport OrderList from './OrderList.vue';\nimport { myOrders } from '@/api/order';\n\nexport default {\n\tcomponents: {\n\t\tOrderList\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tstatusBarHeight: 20,\n\t\t\tcurrentTab: 0,\n\t\t\ttabs: [\n\t\t\t\t{ name: '全部订单' },\n\t\t\t\t{ name: '堂食订单' },\n\t\t\t\t{ name: '外卖订单' }\n\t\t\t],\n\t\t\torderList: [],\n\t\t\toriginalOrders: [],\n\t\t\trefreshTimer: null, // 定时刷新的定时器\n\t\t\tisLoading: false, // 是否正在加载数据\n\t\t\tpage: 1, // 当前页码\n\t\t\tpageSize: 10, // 每页数量\n\t\t\thasMore: true, // 是否有更多数据\n\t\t\tisLoadingMore: false // 是否正在加载更多\n\t\t}\n\t},\n\tonLoad() {\n\t\t// 获取状态栏高度\n\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\tthis.statusBarHeight = systemInfo.statusBarHeight;\n\t\t\n\t\t// 加载订单数据\n\t\tthis.loadOrders(true);\n\t},\n\t\n\tonShow() {\n\t\t// 每次进入页面时重新加载数据\n\t\tthis.loadOrders(true);\n\t\t\n\t\t// 启动定时刷新\n\t\tthis.startAutoRefresh();\n\t},\n\t\n\tonHide() {\n\t\t// 页面隐藏时清除定时器\n\t\tthis.clearAutoRefresh();\n\t},\n\t\n\tonUnload() {\n\t\t// 页面卸载时清除定时器\n\t\tthis.clearAutoRefresh();\n\t},\n\t\n\t// 添加上拉触底事件处理函数\n\tonReachBottom() {\n\t\tif (this.hasMore && !this.isLoading && !this.isLoadingMore) {\n\t\t\tthis.loadMoreOrders();\n\t\t}\n\t},\n\t\n\t// 添加下拉刷新事件处理函数\n\tonPullDownRefresh() {\n\t\tthis.refreshOrders();\n\t},\n\tmethods: {\n\t\tswitchTab(index) {\n\t\t\tif (this.currentTab === index) return;\n\t\t\tthis.currentTab = index;\n\t\t\t// 切换标签时重置分页并重新加载\n\t\t\tthis.page = 1;\n\t\t\tthis.hasMore = true;\n\t\t\tthis.filterOrders();\n\t\t},\n\t\t\n\t\tfilterOrders() {\n\t\t\tif (this.currentTab === 0) {\n\t\t\t\t// 全部订单\n\t\t\t\tthis.orderList = [...this.originalOrders];\n\t\t\t} else if (this.currentTab === 1) {\n\t\t\t\t// 堂食订单\n\t\t\t\tthis.orderList = this.originalOrders.filter(order => order.orderType === 'dine-in');\n\t\t\t} else {\n\t\t\t\t// 外卖订单\n\t\t\t\tthis.orderList = this.originalOrders.filter(order => order.orderType === 'takeout');\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync loadOrders(isRefresh = false) {\n\t\t\t// 如果正在加载，则不重复加载\n\t\t\tif (this.isLoading) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 刷新时重置页码\n\t\t\tif (isRefresh) {\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.hasMore = true;\n\t\t\t}\n\t\t\t\n\t\t\tthis.isLoading = true;\n\t\t\ttry {\n\t\t\t\t// 静默加载，不显示loading，除非是首次加载\n\t\t\t\tconst isFirstLoad = this.originalOrders.length === 0;\n\t\t\t\tif (isFirstLoad) {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconst { data } = await myOrders({\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 检查是否还有更多数据\n\t\t\t\tif (!data.list || data.list.length < this.pageSize) {\n\t\t\t\t\tthis.hasMore = false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理订单数据\n\t\t\t\tconst processedOrders = data.list.map(order => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...order,\n\t\t\t\t\t\torderType: order.dining_type === 'dine-in' ? 'dine-in' : 'takeout',\n\t\t\t\t\t\tstatus: order.status,\n\t\t\t\t\t\tstatus_text: order.status_text,\n\t\t\t\t\t\tproducts: order.order_products ? order.order_products.map(p => ({\n\t\t\t\t\t\t\tid: p.product_id,\n\t\t\t\t\t\t\tname: p.product_name,\n\t\t\t\t\t\t\tprice: p.product_price,\n\t\t\t\t\t\t\tcount: p.quantity,\n\t\t\t\t\t\t\tspecs: p.specs || '',\n\t\t\t\t\t\t\ttotalPrice: p.total_price\n\t\t\t\t\t\t})) : [],\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 如果是刷新，则替换原有数据\n\t\t\t\tif (isRefresh) {\n\t\t\t\t\tthis.originalOrders = processedOrders;\n\t\t\t\t} else {\n\t\t\t\t\t// 否则追加数据\n\t\t\t\t\tthis.originalOrders = [...this.originalOrders, ...processedOrders];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 根据当前标签过滤数据\n\t\t\t\tthis.filterOrders();\n\t\t\t\t\n\t\t\t\tif (isFirstLoad) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 如果是下拉刷新，停止下拉刷新动画\n\t\t\t\tif (isRefresh) {\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载订单失败:', error);\n\t\t\t\t// 只有首次加载失败才显示错误提示\n\t\t\t\tif (this.originalOrders.length === 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载订单失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 停止下拉刷新动画\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t} finally {\n\t\t\t\tif (this.originalOrders.length === 0) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t\tthis.isLoading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载更多订单\n\t\tasync loadMoreOrders() {\n\t\t\tif (this.isLoadingMore || !this.hasMore) return;\n\t\t\t\n\t\t\tthis.isLoadingMore = true;\n\t\t\tthis.page++;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst { data } = await myOrders({\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 检查是否还有更多数据\n\t\t\t\tif (!data.list || data.list.length < this.pageSize) {\n\t\t\t\t\tthis.hasMore = false;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理订单数据\n\t\t\t\tconst processedOrders = data.list.map(order => {\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...order,\n\t\t\t\t\t\torderType: order.dining_type === 'dine-in' ? 'dine-in' : 'takeout',\n\t\t\t\t\t\tstatus: order.status,\n\t\t\t\t\t\tstatus_text: order.status_text,\n\t\t\t\t\t\tproducts: order.order_products ? order.order_products.map(p => ({\n\t\t\t\t\t\t\tid: p.product_id,\n\t\t\t\t\t\t\tname: p.product_name,\n\t\t\t\t\t\t\tprice: p.product_price,\n\t\t\t\t\t\t\tcount: p.quantity,\n\t\t\t\t\t\t\tspecs: p.specs || '',\n\t\t\t\t\t\t\ttotalPrice: p.total_price\n\t\t\t\t\t\t})) : [],\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 追加数据\n\t\t\t\tthis.originalOrders = [...this.originalOrders, ...processedOrders];\n\t\t\t\t\n\t\t\t\t// 根据当前标签过滤数据\n\t\t\t\tthis.filterOrders();\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载更多订单失败:', error);\n\t\t\t\tthis.page--; // 加载失败，恢复页码\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '加载更多失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.isLoadingMore = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync refreshOrders() {\n\t\t\tawait this.loadOrders(true);\n\t\t},\n\t\t\n\t\t// 启动自动刷新\n\t\tstartAutoRefresh() {\n\t\t\t// 先清除可能存在的定时器\n\t\t\tthis.clearAutoRefresh();\n\t\t\t\n\t\t\t// 设置新的定时器，每10秒刷新一次\n\t\t\tthis.refreshTimer = setInterval(() => {\n\t\t\t\tconsole.log('定时刷新订单数据');\n\t\t\t\tthis.loadOrders();\n\t\t\t}, 10000); // 10秒\n\t\t\t\n\t\t\tconsole.log('启动订单自动刷新');\n\t\t},\n\t\t\n\t\t// 清除自动刷新定时器\n\t\tclearAutoRefresh() {\n\t\t\tif (this.refreshTimer) {\n\t\t\t\tclearInterval(this.refreshTimer);\n\t\t\t\tthis.refreshTimer = null;\n\t\t\t\tconsole.log('停止订单自动刷新');\n\t\t\t}\n\t\t},\n\t\t\n\t\tgoToMenu() {\n\t\t\tuni.switchTab({\n\t\t\t\turl: '/pages/menu/menu'\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n@import '@/styles/theme.scss';\n\n.page {\n\tmin-height: 100vh;\n\tbackground-color: #f8f8f8;\n}\n\n.navbar-placeholder {\n\tbackground-color: #fff;\n}\n\n.tab-section {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\tbackground-color: #fff;\n\tpadding: 0 40rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\n\t\n\t.tab-item {\n\t\tposition: relative;\n\t\tpadding: 24rpx 0;\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\t\n\t\t.tab-text {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #999;\n\t\t\ttransition: all 0.3s;\n\t\t}\n\t\t\n\t\t.tab-line {\n\t\t\tposition: absolute;\n\t\t\tbottom: 0;\n\t\t\twidth: 60rpx;\n\t\t\theight: 4rpx;\n\t\t\tbackground-color: #8cd548;\n\t\t\tborder-radius: 2rpx;\n\t\t\ttransition: all 0.3s;\n\t\t}\n\t\t\n\t\t&.active {\n\t\t\t.tab-text {\n\t\t\t\tcolor: #8cd548;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\t\t}\n\t\t\n\t\t&:active {\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n}\n\n.content-container {\n\tflex: 1;\n\tpadding: 0 0 30rpx;\n}\n\n.loading-more {\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tpadding: 20rpx;\n}\n\n.loading-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n\tmargin-right: 10rpx;\n}\n\n.no-more-text {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./order.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309975\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}