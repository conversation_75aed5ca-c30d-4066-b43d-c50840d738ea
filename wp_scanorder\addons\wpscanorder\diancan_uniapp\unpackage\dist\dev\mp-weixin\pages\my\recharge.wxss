@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-dd0ce27e {
  min-height: 100vh;
  background: #f8f8f8;
}
.header.data-v-dd0ce27e {
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  padding-top: 88rpx;
  padding-bottom: 60rpx;
}
.header .nav-bar.data-v-dd0ce27e {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-dd0ce27e {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-dd0ce27e {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}
.header .balance-info.data-v-dd0ce27e {
  text-align: center;
  margin-top: 40rpx;
}
.header .balance-info .label.data-v-dd0ce27e {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
  display: block;
}
.header .balance-info .amount.data-v-dd0ce27e {
  display: flex;
  align-items: baseline;
  justify-content: center;
}
.header .balance-info .amount .symbol.data-v-dd0ce27e {
  font-size: 32rpx;
  color: #fff;
  margin-right: 8rpx;
}
.header .balance-info .amount .value.data-v-dd0ce27e {
  font-size: 60rpx;
  color: #fff;
  font-weight: bold;
  font-family: 'DIN';
}
.content.data-v-dd0ce27e {
  margin-top: -40rpx;
  padding: 0 30rpx;
  padding-bottom: 120rpx;
}
.content .recharge-card.data-v-dd0ce27e {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.content .recharge-card .card-header.data-v-dd0ce27e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.content .recharge-card .card-header .card-title.data-v-dd0ce27e {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.content .recharge-card .card-header .record-link.data-v-dd0ce27e {
  display: flex;
  align-items: center;
  padding: 6rpx 12rpx;
}
.content .recharge-card .card-header .record-link text.data-v-dd0ce27e {
  font-size: 26rpx;
  color: #333;
}
.content .recharge-card .card-header .record-link .arrow.data-v-dd0ce27e {
  margin-left: 6rpx;
  font-size: 22rpx;
}
.content .recharge-card .card-header .record-link.data-v-dd0ce27e:active {
  opacity: 0.7;
}
.content .recharge-card .amount-grid.data-v-dd0ce27e {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-top: 10rpx;
}
.content .recharge-card .amount-grid .amount-item.data-v-dd0ce27e {
  background: #f8f8f8;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  border: 2rpx solid transparent;
}
.content .recharge-card .amount-grid .amount-item .amount.data-v-dd0ce27e {
  margin-bottom: 12rpx;
}
.content .recharge-card .amount-grid .amount-item .amount .symbol.data-v-dd0ce27e {
  font-size: 24rpx;
  color: #333;
}
.content .recharge-card .amount-grid .amount-item .amount .value.data-v-dd0ce27e {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}
.content .recharge-card .amount-grid .amount-item .gift.data-v-dd0ce27e {
  font-size: 24rpx;
  color: #999;
}
.content .recharge-card .amount-grid .amount-item.active.data-v-dd0ce27e {
  background: rgba(140, 213, 72, 0.1);
  border-color: #8cd548;
}
.content .recharge-card .amount-grid .amount-item.active .symbol.data-v-dd0ce27e, .content .recharge-card .amount-grid .amount-item.active .value.data-v-dd0ce27e {
  color: #8cd548;
}
.content .recharge-card .amount-grid .amount-item.active .gift.data-v-dd0ce27e {
  color: #8cd548;
}
.notice.data-v-dd0ce27e {
  padding: 0 20rpx;
}
.notice .notice-title.data-v-dd0ce27e {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.notice .notice-item.data-v-dd0ce27e {
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
  display: block;
}
.bottom-bar.data-v-dd0ce27e {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  background: #fff;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.bottom-bar .recharge-btn.data-v-dd0ce27e {
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-bar .recharge-btn text.data-v-dd0ce27e {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.bottom-bar .recharge-btn.data-v-dd0ce27e:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.bottom-bar .recharge-btn.disabled.data-v-dd0ce27e {
  opacity: 0.7;
  pointer-events: none;
}

