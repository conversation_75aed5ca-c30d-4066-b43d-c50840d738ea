<template>
  <view class="page">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="nav-bar">
        <image 
          class="back-icon" 
          src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" 
          @tap="handleBack"
        />
        <text class="title">收货地址</text>
      </view>
    </view>

    <!-- 地址列表 -->
    <scroll-view class="address-list" scroll-y>
      <view
        class="address-item"
        v-for="(item, index) in addressList"
        :key="item.id ? item.id : index"
        @tap="($event) => selectAddress($event, item)"
        v-if="item"
      >
        <view class="info">
          <view class="user-info">
            <text class="name">{{item.name || ''}}</text>
            <text class="phone">{{item.phone || ''}}</text>
            <text class="tag" v-if="item.tag">{{item.tag}}</text>
          </view>
          <view class="address">{{item.province || ''}} {{item.city || ''}} {{item.district || ''}} {{item.address || ''}}</view>
        </view>
        <view class="actions">
          <view class="default" @tap.stop="($event) => handleSetDefaultClick($event, item)">
            <view class="radio" :class="{ active: item.status == '1' }">
              <view class="inner" v-if="item.status == '1'"></view>
            </view>
            <text>设为默认</text>
          </view>
          <view class="edit-delete">
            <view class="edit" @tap.stop="($event) => editAddress($event, item)">
              <u-icon name="edit-pen" size="32" color="#999"></u-icon>
              <text>编辑</text>
            </view>
            <view class="delete" @tap.stop="($event) => handleDeleteClick($event, item)">
              <u-icon name="trash" size="32" color="#999"></u-icon>
              <text>删除</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <empty-state 
      v-if="addressList.length === 0"
      text="暂无收货地址"
      image="/static/order/e186e04e8774da64b58c96a8bb479840.png"
      :showAction="false"
    ></empty-state>

    <!-- 底部添加按钮 -->
    <view class="add-btn" @tap="addAddress">
      <text>新增收货地址</text>
    </view>
  </view>
</template>

<script>
import { getAddressList, setDefaultAddress, deleteAddress } from '@/api/address.js'

export default {
  data() {
    return {
      addressList: [],
      loading: false
    }
  },
  
  onShow() {
    this.loadAddressList()
  },
  
  methods: {
    handleBack() {
      uni.navigateBack()
    },
    
    async loadAddressList() {
      try {
        this.loading = true
        uni.showLoading({
          title: '加载中...'
        });
        
        const res = await getAddressList()
        console.log('获取地址列表原始响应:', JSON.stringify(res));
        
        if(res.code === 1 && Array.isArray(res.data)) {
          // 增强数据过滤和验证
          this.addressList = res.data
            .filter(item => item && typeof item === 'object')  // 确保是对象
            .filter(item => item.id)  // 确保有id
            .map(item => {
              // 确保status字段的一致性
              if(item.status === undefined && item.is_default !== undefined) {
                item.status = item.is_default ? '1' : '0';
              }
              // 确保所有必要字段都有默认值
              return {
                id: item.id || '',
                name: item.name || '',
                phone: item.phone || '',
                province: item.province || '',
                city: item.city || '',
                district: item.district || '',
                address: item.address || '',
                tag: item.tag || '',
                status: item.status || '0'
              };
            });
          
          console.log('过滤和标准化后的地址列表:', JSON.stringify(this.addressList));
        } else {
          this.addressList = []
          console.error('获取地址列表失败:', res.msg || '未知错误')
        }
      } catch(e) {
        console.error('获取地址列表异常:', e)
        uni.showToast({
          title: '获取地址列表失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        uni.hideLoading()
      }
    },
    
    addAddress() {
      uni.navigateTo({
        url: '/pages/address/edit'
      })
    },
    
    editAddress(event, item) {
      console.log('编辑地址点击 - 事件:', event.type);
      console.log('编辑地址点击 - 完整item数据:', JSON.stringify(item));
      
      if (!item || !item.id) {
        console.error('编辑地址失败: 无效的地址或ID');
        uni.showToast({
          title: '操作失败: 无效的地址',
          icon: 'none'
        });
        return;
      }
      
      // 防止事件冒泡引起的参数错误
      if (typeof item !== 'object') {
        console.error('编辑地址失败: 参数不是对象');
        uni.showToast({
          title: '操作失败: 参数错误',
          icon: 'none'
        });
        return;
      }
      
      // 确保id参数是字符串类型
      const safeId = String(item.id || '');
      if (!safeId) {
        console.error('编辑地址失败: ID转换后为空');
        uni.showToast({
          title: '操作失败: 无效的地址ID',
          icon: 'none'
        });
        return;
      }
      
      console.log('准备跳转到编辑页面, ID:', safeId);
      uni.navigateTo({
        url: `/pages/address/edit?id=${safeId}`
      })
    },
    
    async setDefault(id) {
      console.log('准备设置默认地址，ID:', id);
      // 再次验证ID，确保类型安全
      const safeId = String(id || '');
      if (!safeId) {
        console.error('设置默认地址失败: ID为空');
        uni.showToast({
          title: '操作失败: 无效的地址ID',
          icon: 'none'
        });
        return;
      }
      
      try {
        uni.showLoading({
          title: '设置中...'
        });
        
        console.log('调用setDefaultAddress API，参数:', { id: safeId });
        const res = await setDefaultAddress(safeId);
        
        console.log('设置默认地址结果:', res);
        
        if(res.code === 1) {
          uni.showToast({
            title: '设置成功',
            icon: 'none'
          });
          
          // 重新加载地址列表
          this.loadAddressList();
        } else {
          uni.showToast({
            title: res.msg || '设置失败',
            icon: 'none'
          });
        }
      } catch(e) {
        console.error('设置默认地址异常:', JSON.stringify(e));
        uni.showToast({
          title: '设置失败',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },
    
    selectAddress(event, address) {
      console.log('选择地址点击 - 事件:', event.type);
      console.log('选择地址点击 - 完整address数据:', JSON.stringify(address));
      
      if (!address || !address.id) {
        console.error('选择地址失败: 无效的地址或ID');
        uni.showToast({
          title: '操作失败: 无效的地址',
          icon: 'none'
        });
        return;
      }
      
      // 防止事件冒泡引起的参数错误
      if (typeof address !== 'object') {
        console.error('选择地址失败: 参数不是对象');
        uni.showToast({
          title: '操作失败: 参数错误',
          icon: 'none'
        });
        return;
      }
      
      // 保存选中的地址到缓存
      const tempOrderData = uni.getStorageSync('tempOrderData') || {};
      tempOrderData.selectedAddress = address;
      uni.setStorageSync('tempOrderData', tempOrderData);
      
      // 触发事件，将选中的地址传递回订单页面
      uni.$emit('addressSelected', address);
      
      console.log('地址选择完成，准备返回上一页');
      // 返回上一页
      uni.navigateBack();
    },
    
    async deleteAddress(id) {
      console.log('准备删除地址，ID:', id);
      // 再次验证ID，确保类型安全
      const safeId = String(id || '');
      if (!safeId) {
        console.error('删除地址失败: ID为空');
        uni.showToast({
          title: '操作失败: 无效的地址ID',
          icon: 'none'
        });
        return;
      }
      
      uni.showModal({
        title: '提示',
        content: '确定要删除该地址吗？',
        success: async (res) => {
          if(res.confirm) {
            try {
              uni.showLoading({
                title: '删除中...'
              });
              
              console.log('调用deleteAddress API，参数:', { id: safeId });
              const res = await deleteAddress(safeId);
              
              console.log('删除地址结果:', res);
              
              if(res.code === 1) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'none'
                });
                
                // 重新加载地址列表
                this.loadAddressList();
              } else {
                uni.showToast({
                  title: res.msg || '删除失败',
                  icon: 'none'
                });
              }
            } catch(e) {
              console.error('删除地址异常:', JSON.stringify(e));
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              });
            } finally {
              uni.hideLoading();
            }
          }
        }
      });
    },
    
    // 处理设置默认地址按钮点击
    handleSetDefaultClick(event, item) {
      console.log('设置默认地址点击 - 事件:', event.type);
      console.log('设置默认地址点击 - 完整item数据:', JSON.stringify(item));
      
      if (!item || !item.id) {
        console.error('设置默认地址失败: 无效的地址或ID');
        uni.showToast({
          title: '操作失败: 无效的地址',
          icon: 'none'
        });
        return;
      }
      
      // 防止事件冒泡引起的参数错误
      if (typeof item !== 'object') {
        console.error('设置默认地址失败: 参数不是对象');
        uni.showToast({
          title: '操作失败: 参数错误',
          icon: 'none'
        });
        return;
      }
      
      // 如果已经是默认地址，不做任何操作
      if (item.status === '1') {
        console.log('该地址已经是默认地址，无需设置');
        return;
      }
      
      // 确保id参数是字符串类型
      const safeId = String(item.id || '');
      if (!safeId) {
        console.error('设置默认地址失败: ID转换后为空');
        uni.showToast({
          title: '操作失败: 无效的地址ID',
          icon: 'none'
        });
        return;
      }
      
      console.log('准备调用setDefault, ID:', safeId);
      this.setDefault(safeId);
    },
    
    // 处理删除地址按钮点击
    handleDeleteClick(event, item) {
      console.log('删除地址点击 - 事件:', event.type);
      console.log('删除地址点击 - 完整item数据:', JSON.stringify(item));
      
      if (!item || !item.id) {
        console.error('删除地址失败: 无效的地址或ID');
        uni.showToast({
          title: '操作失败: 无效的地址',
          icon: 'none'
        });
        return;
      }
      
      // 防止事件冒泡引起的参数错误
      if (typeof item !== 'object') {
        console.error('删除地址失败: 参数不是对象');
        uni.showToast({
          title: '操作失败: 参数错误',
          icon: 'none'
        });
        return;
      }
      
      // 确保id参数是字符串类型
      const safeId = String(item.id || '');
      if (!safeId) {
        console.error('删除地址失败: ID转换后为空');
        uni.showToast({
          title: '操作失败: 无效的地址ID',
          icon: 'none'
        });
        return;
      }
      
      console.log('准备调用deleteAddress, ID:', safeId);
      this.deleteAddress(safeId);
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.header {
  background: #fff;
  padding-top: 88rpx;
  
  .nav-bar {
    position: relative;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .back-icon {
      position: absolute;
      left: 30rpx;
      width: 48rpx;
      height: 48rpx;
      padding: 10rpx;
    }
    
    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
  }
}

.address-list {
  padding: 20rpx;
  
  .address-item {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    
    .info {
      .user-info {
        margin-bottom: 16rpx;
        
        .name {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          margin-right: 20rpx;
        }
        
        .phone {
          font-size: 28rpx;
          color: #666;
        }
        
        .tag {
          font-size: 22rpx;
          color: #8cd548;
          background: rgba(140, 213, 72, 0.1);
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          margin-left: 16rpx;
        }
      }
      
      .address {
        font-size: 28rpx;
        color: #333;
        line-height: 1.4;
      }
    }
    
    .actions {
      margin-top: 24rpx;
      padding-top: 24rpx;
      border-top: 1px solid #f5f5f5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .default {
        display: flex;
        align-items: center;
        
        .radio {
          width: 36rpx;
          height: 36rpx;
          border-radius: 50%;
          border: 2rpx solid #ddd;
          margin-right: 8rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;
          
          &.active {
            border-color: #8cd548;
            
            .inner {
              width: 24rpx;
              height: 24rpx;
              border-radius: 50%;
              background: #8cd548;
            }
          }
        }
        
        text {
          font-size: 26rpx;
          color: #666;
        }
      }
      
      .edit-delete {
        display: flex;
        align-items: center;
        
        .edit, .delete {
          display: flex;
          align-items: center;
          margin-left: 32rpx;
          
          text {
            font-size: 26rpx;
            color: #666;
            margin-left: 8rpx;
          }
        }
      }
    }
  }
}

.add-btn {
  position: fixed;
  left: 40rpx;
  right: 40rpx;
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 500;
  }
  
  &:active {
    transform: scale(0.98);
  }
}
</style> 