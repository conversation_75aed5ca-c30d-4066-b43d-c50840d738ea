<view class="page data-v-db675620"><view class="header data-v-db675620"><view class="nav-bar data-v-db675620"><image class="back-icon data-v-db675620" src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" bindtap="__e"></image><text class="title data-v-db675620">收货地址</text></view></view><scroll-view class="address-list data-v-db675620" scroll-y="{{true}}"><block wx:for="{{addressList}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item})}}" class="address-item data-v-db675620" bindtap="__e"><view class="info data-v-db675620"><view class="user-info data-v-db675620"><text class="name data-v-db675620">{{item.name||''}}</text><text class="phone data-v-db675620">{{item.phone||''}}</text><block wx:if="{{item.tag}}"><text class="tag data-v-db675620">{{item.tag}}</text></block></view><view class="address data-v-db675620">{{(item.province||'')+" "+(item.city||'')+" "+(item.district||'')+" "+(item.address||'')}}</view></view><view class="actions data-v-db675620"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item})}}" class="default data-v-db675620" catchtap="__e"><view class="{{['radio','data-v-db675620',(item.status=='1')?'active':'']}}"><block wx:if="{{item.status=='1'}}"><view class="inner data-v-db675620"></view></block></view><text class="data-v-db675620">设为默认</text></view><view class="edit-delete data-v-db675620"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({item})}}" class="edit data-v-db675620" catchtap="__e"><u-icon vue-id="{{'91826bd4-1-'+index}}" name="edit-pen" size="32" color="#999" class="data-v-db675620" bind:__l="__l"></u-icon><text class="data-v-db675620">编辑</text></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({item})}}" class="delete data-v-db675620" catchtap="__e"><u-icon vue-id="{{'91826bd4-2-'+index}}" name="trash" size="32" color="#999" class="data-v-db675620" bind:__l="__l"></u-icon><text class="data-v-db675620">删除</text></view></view></view></view></block></block></scroll-view><block wx:if="{{$root.g0===0}}"><empty-state vue-id="91826bd4-3" text="暂无收货地址" image="/static/order/e186e04e8774da64b58c96a8bb479840.png" showAction="{{false}}" class="data-v-db675620" bind:__l="__l"></empty-state></block><view data-event-opts="{{[['tap',[['addAddress',['$event']]]]]}}" class="add-btn data-v-db675620" bindtap="__e"><text class="data-v-db675620">新增收货地址</text></view></view>