@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-6c4d65be {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}
.header.data-v-6c4d65be {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-6c4d65be {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-6c4d65be {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-6c4d65be {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.form.data-v-6c4d65be {
  margin-top: 20rpx;
  background: #fff;
  padding: 0 30rpx;
}
.form .form-item.data-v-6c4d65be {
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: flex-start;
}
.form .form-item .label.data-v-6c4d65be {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  padding-top: 6rpx;
}
.form .form-item .input.data-v-6c4d65be {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.form .form-item .textarea.data-v-6c4d65be {
  flex: 1;
  height: 160rpx;
  font-size: 28rpx;
  color: #333;
}
.form .form-item .region-picker.data-v-6c4d65be {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.form .form-item .region-picker .region-text.data-v-6c4d65be {
  font-size: 28rpx;
  color: #333;
}
.form .form-item .region-picker .region-text.placeholder.data-v-6c4d65be {
  color: #999;
}
.form .form-item .tag-list.data-v-6c4d65be {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.form .form-item .tag-list .tag.data-v-6c4d65be {
  padding: 12rpx 32rpx;
  background: #f8f8f8;
  border-radius: 100rpx;
  font-size: 26rpx;
  color: #666;
}
.form .form-item .tag-list .tag.active.data-v-6c4d65be {
  background: rgba(140, 213, 72, 0.1);
  color: #8cd548;
}
.form .form-item.switch.data-v-6c4d65be {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.form .form-item.switch .label.data-v-6c4d65be {
  padding-top: 0;
}
.placeholder.data-v-6c4d65be {
  color: #999;
}
.save-btn.data-v-6c4d65be {
  position: fixed;
  left: 40rpx;
  right: 40rpx;
  bottom: calc(40rpx + env(safe-area-inset-bottom));
  height: 88rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.save-btn text.data-v-6c4d65be {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.save-btn.data-v-6c4d65be:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}

