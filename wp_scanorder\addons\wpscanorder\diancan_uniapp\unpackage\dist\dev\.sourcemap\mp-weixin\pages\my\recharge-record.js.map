{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge-record.vue?e236", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge-record.vue?cbd6", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge-record.vue?0c7f", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge-record.vue?7e4a", "uni-app:///pages/my/recharge-record.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge-record.vue?e4f7", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/recharge-record.vue?4b7d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "recordList", "page", "limit", "total", "loading", "hasMore", "onLoad", "methods", "loadRechargeRecords", "isLoadMore", "res", "list", "processedList", "item", "status", "status_text", "amount", "send_amount", "create_time_text", "uni", "title", "icon", "console", "loadMore", "formatTime", "handleBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrCA;AAAA;AAAA;AAAA;AAAwqB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACkD5rB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGA;gBAEA;kBACA;gBACA;gBAAA;gBAAA,OAEA;kBACAR;kBACAC;gBACA;cAAA;gBAHAQ;gBAKA;kBACA;kBACAC;kBACA;;kBAEA;kBACAC;oBACA,uCACAC;sBACA;sBACAC;sBACA;sBACAC;sBACA;sBACAC;sBACAC;sBACA;sBACAC;oBAAA;kBAEA;kBAEA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;;kBAEA;kBACA;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAH;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEAC;MACAN;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAA2xC,CAAgB,goCAAG,EAAC,C;;;;;;;;;;;ACA/yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/recharge-record.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/recharge-record.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./recharge-record.vue?vue&type=template&id=0c2463e6&scoped=true&\"\nvar renderjs\nimport script from \"./recharge-record.vue?vue&type=script&lang=js&\"\nexport * from \"./recharge-record.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recharge-record.vue?vue&type=style&index=0&id=0c2463e6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c2463e6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/recharge-record.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge-record.vue?vue&type=template&id=0c2463e6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.recordList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.recordList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = item.send_amount && Number(item.send_amount) > 0\n          var m1 = item.create_time_text || _vm.formatTime(item.createtime)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var g1 = _vm.recordList.length > 0 && _vm.hasMore\n  var g2 = _vm.recordList.length === 0 && !_vm.loading\n  var g3 = _vm.loading && _vm.recordList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge-record.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge-record.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">充值记录</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 记录列表 -->\r\n    <view class=\"record-list\" v-if=\"recordList.length > 0\">\r\n      <view class=\"record-item\" v-for=\"(item, index) in recordList\" :key=\"index\">\r\n        <view class=\"left\">\r\n          <text class=\"amount\">+¥{{item.amount}}</text>\r\n          <text v-if=\"item.send_amount && Number(item.send_amount) > 0\" class=\"gift\">赠送¥{{item.send_amount}}</text>\r\n          <text class=\"time\">{{item.create_time_text || formatTime(item.createtime)}}</text>\r\n        </view>\r\n        <view class=\"right\">\r\n          <text class=\"status\" :class=\"item.status === '1' ? 'success' : 'failed'\">\r\n            {{item.status_text || (item.status === '1' ? '充值成功' : '充值失败')}}\r\n          </text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载更多 -->\r\n    <view class=\"load-more\" v-if=\"recordList.length > 0 && hasMore\">\r\n      <text @tap=\"loadMore\" v-if=\"!loading\">点击加载更多</text>\r\n      <text v-else>加载中...</text>\r\n    </view>\r\n\r\n    <!-- 空状态 -->\r\n    <view class=\"empty-state\" v-if=\"recordList.length === 0 && !loading\">\r\n      <image class=\"empty-image\" src=\"/static/order/e186e04e8774da64b58c96a8bb479840.png\" />\r\n      <text class=\"empty-text\">暂无充值记录～</text>\r\n    </view>\r\n\r\n    <!-- 加载中 -->\r\n    <view class=\"loading\" v-if=\"loading && recordList.length === 0\">\r\n      <text>加载中...</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getRechargeList } from '@/api/recharge.js';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      recordList: [],\r\n      page: 1,\r\n      limit: 10,\r\n      total: 0,\r\n      loading: false,\r\n      hasMore: false\r\n    }\r\n  },\r\n\r\n  onLoad() {\r\n    this.loadRechargeRecords();\r\n  },\r\n\r\n  methods: {\r\n    // 加载充值记录\r\n    async loadRechargeRecords(isLoadMore = false) {\r\n      if (this.loading) return;\r\n      \r\n      try {\r\n        this.loading = true;\r\n        \r\n        if (!isLoadMore) {\r\n          this.page = 1;\r\n        }\r\n        \r\n        const res = await getRechargeList({\r\n          page: this.page,\r\n          limit: this.limit\r\n        });\r\n        \r\n        if (res.code === 1 && res.data) {\r\n          // 处理数据\r\n          const list = res.data.list || [];\r\n          this.total = res.data.total || 0;\r\n          \r\n          // 处理返回的数据，确保格式统一\r\n          const processedList = list.map(item => {\r\n            return {\r\n              ...item,\r\n              // 确保状态字段格式一致\r\n              status: String(item.status),\r\n              // 如果没有状态文本，根据状态生成\r\n              status_text: item.status_text || (String(item.status) === '1' ? '充值成功' : '充值失败'),\r\n              // 确保金额格式一致\r\n              amount: item.amount ? parseFloat(item.amount).toFixed(2) : '0.00',\r\n              send_amount: item.send_amount ? parseFloat(item.send_amount).toFixed(2) : '0.00',\r\n              // 使用格式化后的时间或者格式化时间戳\r\n              create_time_text: item.create_time_text || this.formatTime(item.createtime)\r\n            };\r\n          });\r\n          \r\n          if (isLoadMore) {\r\n            // 加载更多时，追加数据\r\n            this.recordList = [...this.recordList, ...processedList];\r\n          } else {\r\n            // 首次加载，直接赋值\r\n            this.recordList = processedList;\r\n          }\r\n          \r\n          // 判断是否还有更多数据\r\n          this.hasMore = this.recordList.length < this.total;\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '获取充值记录失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('获取充值记录失败:', error);\r\n        uni.showToast({\r\n          title: '获取充值记录失败',\r\n          icon: 'none'\r\n        });\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      if (this.hasMore && !this.loading) {\r\n        this.page++;\r\n        this.loadRechargeRecords(true);\r\n      }\r\n    },\r\n    \r\n    // 格式化时间戳\r\n    formatTime(timestamp) {\r\n      if (!timestamp) return '--';\r\n      \r\n      const date = new Date(timestamp * 1000);\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hour = String(date.getHours()).padStart(2, '0');\r\n      const minute = String(date.getMinutes()).padStart(2, '0');\r\n      const second = String(date.getSeconds()).padStart(2, '0');\r\n      \r\n      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;\r\n    },\r\n    \r\n    handleBack() {\r\n      uni.navigateBack()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  background: #fff;\r\n  padding-top: 88rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .back-icon {\r\n      position: absolute;\r\n      left: 30rpx;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n\r\n.record-list {\r\n  padding: 20rpx;\r\n  \r\n  .record-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    padding: 30rpx;\r\n    margin-bottom: 20rpx;\r\n    \r\n    .left {\r\n      .amount {\r\n        font-size: 36rpx;\r\n        color: #333;\r\n        font-weight: bold;\r\n        display: block;\r\n        margin-bottom: 8rpx;\r\n      }\r\n      \r\n      .gift {\r\n        font-size: 24rpx;\r\n        color: #8cd548;\r\n        display: block;\r\n        margin-bottom: 8rpx;\r\n      }\r\n      \r\n      .time {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n    \r\n    .right {\r\n      .status {\r\n        font-size: 26rpx;\r\n        padding: 4rpx 12rpx;\r\n        border-radius: 8rpx;\r\n        \r\n        &.success {\r\n          color: #8cd548;\r\n          background: rgba(140, 213, 72, 0.1);\r\n        }\r\n        \r\n        &.failed {\r\n          color: #ff4444;\r\n          background: rgba(255, 68, 68, 0.1);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.load-more {\r\n  text-align: center;\r\n  padding: 30rpx 0;\r\n  \r\n  text {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200rpx;\r\n  \r\n  text {\r\n    font-size: 26rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.empty-state {\r\n  padding-top: 200rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  \r\n  .empty-image {\r\n    width: 400rpx;\r\n    height: 300rpx;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge-record.vue?vue&type=style&index=0&id=0c2463e6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recharge-record.vue?vue&type=style&index=0&id=0c2463e6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309378\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}