<template>
  <view class="page">
    <!-- 使用通用导航栏 -->
    <nav-bar 
      title="图标使用指南" 
      :isTabbar="false" 
      background="primary"
      textColor="#fff"
    ></nav-bar>
    
    <!-- 导航栏占位 -->
    <view class="navbar-placeholder" :style="{ height: statusBarHeight + 88 + 'px' }"></view>

    <!-- 内容区域 -->
    <scroll-view class="content-container" scroll-y>
      <!-- 基本图标说明 -->
      <ui-card title="图标使用说明">
        <view class="description">
          <text>应用使用两种图标系统：</text>
          <view class="list-item">
            <text>1. uView 内置图标库（推荐使用）</text>
          </view>
          <view class="list-item">
            <text>2. 自定义图标（位于 static/icons 目录）</text>
          </view>
          <text class="tip">使用统一图标组件 custom-icon 可以同时支持这两种图标</text>
        </view>
      </ui-card>
      
      <!-- uView图标演示 -->
      <ui-card title="uView 内置图标（常用）">
        <view class="icon-grid">
          <view class="icon-grid-item" v-for="(icon, index) in commonIcons" :key="index">
            <custom-icon :name="icon" :size="40" color="#333"></custom-icon>
            <text class="icon-text">{{icon}}</text>
          </view>
        </view>
      </ui-card>
      
      <!-- 自定义图标演示 -->
      <ui-card title="自定义图标">
        <view class="icon-grid">
          <view class="icon-grid-item" v-for="(icon, index) in customIcons" :key="index">
            <custom-icon :name="icon" :size="40" :custom="true"></custom-icon>
            <text class="icon-text">{{icon}}</text>
          </view>
        </view>
      </ui-card>
      
      <!-- 使用示例 -->
      <ui-card title="图标使用示例">
        <view class="example-section">
          <view class="example-title">基本使用</view>
          <view class="example-content">
            <custom-icon name="home" size="32" color="#8cd548"></custom-icon>
            <view class="code-block">
              <text class="code">&lt;custom-icon name="home" size="32" color="#8cd548"&gt;&lt;/custom-icon&gt;</text>
            </view>
          </view>
        </view>
        
        <view class="example-section">
          <view class="example-title">图标 + 文字</view>
          <view class="example-content">
            <view class="icon-text">
              <custom-icon name="home" size="28" color="#8cd548" class="icon"></custom-icon>
              <text class="text">首页</text>
            </view>
            <view class="code-block">
              <text class="code">&lt;view class="icon-text"&gt;
  &lt;custom-icon name="home" size="28" color="#8cd548" class="icon"&gt;&lt;/custom-icon&gt;
  &lt;text class="text"&gt;首页&lt;/text&gt;
&lt;/view&gt;</text>
            </view>
          </view>
        </view>
        
        <view class="example-section">
          <view class="example-title">按钮中的图标</view>
          <view class="example-content">
            <custom-button type="primary" icon="plus">添加</custom-button>
            <view class="code-block">
              <text class="code">&lt;custom-button type="primary" icon="plus"&gt;添加&lt;/custom-button&gt;</text>
            </view>
          </view>
        </view>
      </ui-card>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 20,
      commonIcons: [
        'home',
        'photo',
        'level',
        'star',
        'star-fill',
        'plus',
        'minus',
        'close',
        'checkmark',
        'search',
        'setting',
        'bell',
        'chat',
        'heart',
        'heart-fill',
        'shopping-cart',
        'trash',
        'edit',
        'arrow-left',
        'arrow-right',
        'arrow-up',
        'arrow-down',
        'map',
        'calendar',
        'time'
      ],
      customIcons: [
        'logo',
        'coupon',
        'member',
        'wallet',
        'location',
        'order'
      ]
    };
  },
  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight;
  }
};
</script>

<style lang="scss">
@import '@/styles/theme.scss';
@import '@/styles/icons.scss';

.description {
  .list-item {
    margin: $spacing-xs 0;
    padding-left: $spacing-md;
  }
  
  .tip {
    display: block;
    margin-top: $spacing-md;
    color: $primary-color;
    font-weight: bold;
  }
}

.example-section {
  margin-bottom: $spacing-lg;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .example-title {
    font-size: $font-size-md;
    color: $text-color-primary;
    margin-bottom: $spacing-sm;
  }
  
  .example-content {
    .code-block {
      margin-top: $spacing-sm;
      background-color: #f7f7f7;
      padding: $spacing-sm;
      border-radius: $border-radius-sm;
      
      .code {
        font-family: monospace;
        font-size: $font-size-xs;
        color: $text-color-secondary;
        word-break: break-all;
      }
    }
  }
}
</style> 