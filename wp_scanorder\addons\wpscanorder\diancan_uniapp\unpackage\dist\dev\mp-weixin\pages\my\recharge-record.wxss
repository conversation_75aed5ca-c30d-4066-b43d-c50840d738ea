@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-0c2463e6 {
  min-height: 100vh;
  background: #f8f8f8;
}
.header.data-v-0c2463e6 {
  background: #fff;
  padding-top: 88rpx;
}
.header .nav-bar.data-v-0c2463e6 {
  position: relative;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header .nav-bar .back-icon.data-v-0c2463e6 {
  position: absolute;
  left: 30rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 10rpx;
}
.header .nav-bar .title.data-v-0c2463e6 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.record-list.data-v-0c2463e6 {
  padding: 20rpx;
}
.record-list .record-item.data-v-0c2463e6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.record-list .record-item .left .amount.data-v-0c2463e6 {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}
.record-list .record-item .left .gift.data-v-0c2463e6 {
  font-size: 24rpx;
  color: #8cd548;
  display: block;
  margin-bottom: 8rpx;
}
.record-list .record-item .left .time.data-v-0c2463e6 {
  font-size: 24rpx;
  color: #999;
}
.record-list .record-item .right .status.data-v-0c2463e6 {
  font-size: 26rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}
.record-list .record-item .right .status.success.data-v-0c2463e6 {
  color: #8cd548;
  background: rgba(140, 213, 72, 0.1);
}
.record-list .record-item .right .status.failed.data-v-0c2463e6 {
  color: #ff4444;
  background: rgba(255, 68, 68, 0.1);
}
.load-more.data-v-0c2463e6 {
  text-align: center;
  padding: 30rpx 0;
}
.load-more text.data-v-0c2463e6 {
  font-size: 24rpx;
  color: #999;
}
.loading.data-v-0c2463e6 {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}
.loading text.data-v-0c2463e6 {
  font-size: 26rpx;
  color: #999;
}
.empty-state.data-v-0c2463e6 {
  padding-top: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.empty-state .empty-image.data-v-0c2463e6 {
  width: 400rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
}
.empty-state .empty-text.data-v-0c2463e6 {
  font-size: 28rpx;
  color: #999;
}

