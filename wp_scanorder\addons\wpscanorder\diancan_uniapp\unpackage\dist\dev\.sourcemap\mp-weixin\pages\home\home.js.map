{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/home/<USER>", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/home/<USER>", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/home/<USER>", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/home/<USER>", "uni-app:///pages/home/<USER>", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/home/<USER>", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bannerList", "image", "userInfo", "points", "balance", "is<PERSON>ogin", "statusBarHeight", "topSellingProducts", "iconsConfig", "onLoad", "console", "methods", "getIconsConfig", "res", "preloadImage", "uni", "url", "success", "fail", "getBannerList", "bannerData", "getUserData", "token", "profileRes", "userData", "id", "userId", "username", "nick<PERSON><PERSON>", "avatarUrl", "phone", "getFallbackUserData", "assetsRes", "getUserPointsAndBalance", "pointsRes", "balanceRes", "handleBannerChange", "handleBannerClick", "handleMemberCode", "animationType", "animationDuration", "handleStoreOrder", "handleDeliveryOrder", "handleGroupBuy", "title", "icon", "goToRecharge", "handlePointsMall", "goToCoupon", "goToLogin", "goToProductDetail", "categoryId", "productId", "addToCart", "name", "price", "count", "totalPrice", "spec_type", "specs", "props_text", "list", "total", "cartData", "getTopSellingProducts", "limit", "productsData", "item", "is_new", "onShow", "storedPoints", "storedBalance"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgJjrB;AACA;AACA;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC,aACA;QACAC;MACA,EACA;MAAA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEAC;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACAH;gBAEA;kBACA;kBACA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAAA;MACAJ;MACAK;QACAC;QACAC;UACA;YACAP;YACA;YACA;YACA;YACAK;UACA;YACAL;UACA;QACA;QACAQ;UACAR;QACA;MACA;IACA;IACA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAN;gBAEA;gBACAO;gBAEA;kBACAA;gBACA;kBACAA;gBACA;kBACAA;gBACA;gBAEA;kBACA;kBACA;oBACA;oBACA;sBACA;wBACAnB;sBACA;oBACA;oBACA;oBAAA,KACA;sBACA;wBACAA;sBACA;oBACA;oBACA;oBAAA,KACA;sBACA;sBACA;sBACA;sBACA;wBACA;0BACAA;wBACA;sBACA;;sBACA;wBACAA;sBACA;oBACA;oBACA;kBACA;oBAAA;kBAAA;gBACA;;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAS;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMA;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC,4BAEA;gBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;gBACA;;gBAEA;gBACAf;gBACAA;gBACAA;gBAEAL;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBAEAjB;kBACAA;gBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;kBACA;kBACAnB;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAoB;gBACA;kBACA;kBACApB;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA0B;MACA1B;IACA;IACA2B;MACA3B;IACA;IACA4B;MACAvB;QACAC;QACAuB;QACAC;MACA;IACA;IACAC;MACA;MACA1B;MAEAA;QACAC;MACA;IACA;IACA0B;MACA;MACA3B;;MAEA;MACAA;QACAC;MACA;IACA;IACA2B;MACA5B;QACA6B;QACAC;MACA;IACA;IAEAC;MACA/B;QACAC;MACA;IACA;IACA+B;MACAhC;QACAC;QACAuB;QACAC;MACA;IACA;IACAQ;MACAjC;QACAC;MACA;IACA;IACA;IACAiC;MACAlC;QACAC;MACA;IACA;IACAkC;MACA;MACA;QACA;QACAnC;;QAEA;QACAA;UACAC;UACAC;YACA;YACAF;cACAoC;cACAC;YACA;UACA;QACA;MACA;QACArC;UACAC;QACA;MACA;IACA;IACAqC;MACA;MACA;QACA;QACA;;QAEA;QACA;UACA5B;UACA6B;UACAC;UACAtD;UACAuD;UACAC;UACAC;UACAC;UACAC;QACA;;QAEA;QACA;UACAC;UACAC;UACAP;QACA;;QAEA;QACA;UAAA;QAAA;QAEA;UACA;UACAQ;UACAA;QACA;UACA;UACAA;QACA;;QAEA;QACAA;UAAA;QAAA;QACAA;UAAA;QAAA;;QAEA;QACAhD;;QAEA;QACAA;UACA6B;UACAC;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBAAAC;gBAAA;cAAA;gBAAApD;gBACAH;gBAEAwD;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;;gBAEA;gBACA;kBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;kBACA;oBACAjE;kBACA;kBAEA,uCACAkE;oBACAZ;oBACAa;oBACAnE;kBAAA;gBAEA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAS;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACA2D;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA/C;cACA;;cAEA;cACApB;cACA;;cAEA;cACAoE;cACAC;cAEA;cACA;;cAEA;cACA7D;cACA;;cAEA;cACA;;cAEA;cACA;gBACA;cACA;;cAEA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxkBA;AAAA;AAAA;AAAA;AAAgxC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;;ACApyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./home.vue?vue&type=template&id=92bb8f34&scoped=true&\"\nvar renderjs\nimport script from \"./home.vue?vue&type=script&lang=js&\"\nexport * from \"./home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./home.vue?vue&type=style&index=0&id=92bb8f34&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92bb8f34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=template&id=92bb8f34&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.bannerList && _vm.bannerList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"wp_-flex-col page\">\r\n\t\t<!-- 使用原生swiper组件替代uView的u-swiper -->\r\n\t\t<swiper \r\n\t\t\tv-if=\"bannerList && bannerList.length > 0\"\r\n\t\t\tclass=\"swiper\" \r\n\t\t\tcircular \r\n\t\t\tautoplay \r\n\t\t\tindicator-dots \r\n\t\t\tindicator-color=\"rgba(255, 255, 255, 0.6)\"\r\n\t\t\tindicator-active-color=\"#ffffff\"\r\n\t\t\t:interval=\"3000\" \r\n\t\t\t:duration=\"500\"\r\n\t\t\tstyle=\"height: 600rpx; width: 100%;\"\r\n\t\t>\r\n\t\t\t<swiper-item v-for=\"(item, index) in bannerList\" :key=\"index\" class=\"swiper-item\">\r\n\t\t\t\t<image \r\n\t\t\t\t\t:src=\"item.image\" \r\n\t\t\t\t\tmode=\"aspectFill\" \r\n\t\t\t\t\tclass=\"swiper-image\"\r\n\t\t\t\t></image>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t\r\n\t\t<!-- 会员卡片 -->\r\n\t\t<view class=\"member-card\">\r\n\t\t\t<view class=\"member-info\" v-if=\"isLogin\">\r\n\t\t\t\t<view class=\"info-content\">\r\n\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t<view class=\"avatar-wrap\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"userInfo.avatarUrl || '/static/home/<USER>'\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t\t<view class=\"vip-badge\">\r\n\t\t\t\t\t\t\t\t<u-icon name=\"star-fill\" size=\"16\" color=\"#fff\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"user-detail\">\r\n\t\t\t\t\t\t\t<text class=\"name\">{{userInfo.nickName || ''}}</text>\r\n\t\t\t\t\t\t\t<text class=\"level\">默认会员</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"asset-info\">\r\n\t\t\t\t\t\t<view class=\"asset-item\" @tap=\"handlePointsMall\">\r\n\t\t\t\t\t\t\t<text class=\"value\">{{points}}</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">积分</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"divider\"></view>\r\n\t\t\t\t\t\t<view class=\"asset-item\" @tap=\"goToRecharge\">\r\n\t\t\t\t\t\t\t<text class=\"value\">{{balance}}</text>\r\n\t\t\t\t\t\t\t<text class=\"label\">余额 (元)</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"qr-code\" @tap=\"handleMemberCode\" v-if=\"userInfo.is_member == 1\">\r\n\t\t\t\t\t<view class=\"code-wrap\">\r\n\t\t\t\t\t\t<image class=\"code-icon\" src=\"/static/home/<USER>\" mode=\"aspectFit\" />\r\n\t\t\t\t\t\t<view class=\"code-light\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text>邀请好友</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 未登录状态显示登录按钮 -->\r\n\t\t\t<view class=\"member-info not-login\" v-else>\r\n\t\t\t\t<view class=\"info-content\">\r\n\t\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t\t<view class=\"avatar-wrap\">\r\n\t\t\t\t\t\t\t<image class=\"avatar\" src=\"/static/my/default-avatar.png\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"user-detail\">\r\n\t\t\t\t\t\t\t<text class=\"name\">立即登录</text>\r\n\t\t\t\t\t\t\t<text class=\"login-tip\">登录后享受更多会员权益</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"login-btn\" @tap=\"goToLogin\">\r\n\t\t\t\t\t<text>立即登录</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 统一白色背景区域 -->\r\n\t\t<view class=\"unified-white-box\">\r\n\t\t\t<!-- 点餐方式 -->\r\n\t\t\t<view class=\"order-section\">\r\n\t\t\t\t<view class=\"order-item\" @tap=\"handleStoreOrder\">\r\n\t\t\t\t\t<image class=\"order-icon\" :src=\"iconsConfig.icon_dine_in || '/static/home/<USER>'\" mode=\"aspectFit\"/>\r\n\t\t\t\t\t<view class=\"order-content\">\r\n\t\t\t\t\t\t<text class=\"title\">堂食点餐</text>\r\n\t\t\t\t\t\t<text class=\"desc\">小程序下单免排队</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"order-item\" @tap=\"handleDeliveryOrder\">\r\n\t\t\t\t\t<image class=\"order-icon\" :src=\"iconsConfig.icon_takeout || '/static/home/<USER>'\" mode=\"aspectFit\"/>\r\n\t\t\t\t\t<view class=\"order-content\">\r\n\t\t\t\t\t\t<text class=\"title\">外卖配送</text>\r\n\t\t\t\t\t\t<text class=\"desc\">美味送到家</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 分隔线 -->\r\n\t\t\t<view class=\"divider-line\"></view>\r\n\r\n\t\t\t<!-- 功能区域 -->\r\n\t\t\t<view class=\"feature-section\">\r\n\t\t\t\t<view class=\"feature-item\" @tap=\"goToCoupon\">\r\n\t\t\t\t\t<image class=\"feature-icon\" :src=\"iconsConfig.icon_my_coupon || '/static/home/<USER>'\" mode=\"aspectFit\" />\r\n\t\t\t\t\t<text>我的优惠</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"feature-item\" @tap=\"goToRecharge\">\r\n\t\t\t\t\t<image class=\"feature-icon\" :src=\"iconsConfig.icon_member_recharge || '/static/home/<USER>'\" mode=\"aspectFit\" />\r\n\t\t\t\t\t<text>会员充值</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"feature-item\" @tap=\"handlePointsMall\">\r\n\t\t\t\t\t<image class=\"feature-icon\" :src=\"iconsConfig.icon_points_mall || '/static/home/<USER>'\" mode=\"aspectFit\" />\r\n\t\t\t\t\t<text>积分商城</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 热销商品区域 -->\r\n\t\t<view class=\"hot-selling-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">大家都在喝</text>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view scroll-x class=\"product-scroll\" enable-flex show-scrollbar=\"false\">\r\n\t\t\t\t<view class=\"product-card\" v-for=\"(item, index) in topSellingProducts\" :key=\"index\" @tap=\"goToProductDetail(item)\">\r\n\t\t\t\t\t<view class=\"product-tag\" v-if=\"item.is_new\"><text>新</text></view>\r\n\t\t\t\t\t<image class=\"product-image\" :src=\"item.image || '/static/home/<USER>'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t<text class=\"product-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t<view class=\"product-price-row\">\r\n\t\t\t\t\t\t\t<text class=\"product-price\">¥ {{item.price}}</text>\r\n\t\t\t\t\t\t\t<view class=\"add-btn\" @tap.stop=\"addToCart(item)\">\r\n\t\t\t\t\t\t\t\t<text class=\"add-icon\">+</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport request from '@/utils/request.js'\r\nimport { getUserProfile, getUserAssets, getUserPoints, getUserBalance } from '@/api/user.js'\r\nimport { getBanners, getTopSellingProducts } from '@/api/home.js'\r\nimport { getIconsConfig } from '@/api/config.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tbannerList: [\r\n\t\t\t\t{\r\n\t\t\t\t\timage: '/static/home/<USER>'\r\n\t\t\t\t}\r\n\t\t\t], // 不设置默认值，只从API获取\r\n\t\t\tuserInfo: {},\r\n\t\t\tpoints: 0,\r\n\t\t\tbalance: '0.00',\r\n\t\t\tisLogin: false, // 登录状态\r\n\t\t\tstatusBarHeight: 20, // 默认状态栏高度\r\n\t\t\ttopSellingProducts: [], // 热销商品数据\r\n\t\t\ticonsConfig: {} // 图标配置\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 获取系统信息设置状态栏高度\r\n\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\tthis.statusBarHeight = systemInfo.statusBarHeight\r\n\t\t\r\n\t\t// 页面加载时获取本地缓存数据\r\n\t\tconst userInfo = uni.getStorageSync('userInfo') || {}\r\n\t\tconst storedPoints = uni.getStorageSync('points')\r\n\t\tconst storedBalance = uni.getStorageSync('balance')\r\n\t\t\r\n\t\tthis.userInfo = userInfo\r\n\t\tif (storedPoints) this.points = storedPoints\r\n\t\tif (storedBalance) this.balance = storedBalance\r\n\t\t\r\n\t\tconsole.log('onLoad 初始化轮播图数据:', this.bannerList)\r\n\t\t\r\n\t\t// 获取热销商品数据\r\n\t\tthis.getTopSellingProducts()\r\n\t\t\r\n\t\t// 获取图标配置\r\n\t\tthis.getIconsConfig()\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取图标配置\r\n\t\tasync getIconsConfig() {\r\n\t\t\ttry {\r\n\t\t\t\t// 如果缓存中没有，则从API获取\r\n\t\t\t\tconst res = await getIconsConfig()\r\n\t\t\t\tconsole.log('API返回图标数据:', JSON.stringify(res))\r\n\t\t\t\t\r\n\t\t\t\tif (res && res.code === 1 && res.data) {\r\n\t\t\t\t\t// 将图标配置存入本地缓存\r\n\t\t\t\t\tthis.iconsConfig = res.data\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('获取图标配置失败或数据为空')\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取图标配置出错:', e)\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 预下载单个图片\r\n\t\tpreloadImage(key, url) {\r\n\t\t\tconsole.log(`开始预下载图片[${key}]: ${url}`)\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: url,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\tconsole.log(`图片[${key}]下载成功，临时路径: ${res.tempFilePath}`)\r\n\t\t\t\t\t\t// 将远程图片URL替换为本地临时路径\r\n\t\t\t\t\t\tthis.iconsConfig[key] = res.tempFilePath\r\n\t\t\t\t\t\t// 更新缓存\r\n\t\t\t\t\t\tuni.setStorageSync('iconsConfig', JSON.stringify(this.iconsConfig))\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error(`图片[${key}]下载失败，状态码: ${res.statusCode}`)\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error(`图片[${key}]下载出错:`, err)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 获取轮播图数据\r\n\t\tasync getBannerList() {\r\n\t\t\ttry {\r\n\t\t\t\t// 使用专门的API函数获取轮播图\r\n\t\t\t\tconst res = await getBanners()\r\n\t\t\t\t\r\n\t\t\t\t// 增加数据验证\r\n\t\t\t\tlet bannerData = []\r\n\t\t\t\t\r\n\t\t\t\tif (res && res.code === 1 && res.list && Array.isArray(res.list)) {\r\n\t\t\t\t\tbannerData = res.list\r\n\t\t\t\t} else if (res && res.data && Array.isArray(res.data.list)) {\r\n\t\t\t\t\tbannerData = res.data.list\r\n\t\t\t\t} else if (res && Array.isArray(res.data)) {\r\n\t\t\t\t\tbannerData = res.data\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (bannerData.length > 0) {\r\n\t\t\t\t\t// 确保格式符合swiper要求 - 需要包含image字段\r\n\t\t\t\t\tthis.bannerList = bannerData.map(item => {\r\n\t\t\t\t\t\t// 如果已经是正确格式的对象\r\n\t\t\t\t\t\tif (item && typeof item === 'object' && item.image) {\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\timage: item.image\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 如果是字符串URL\r\n\t\t\t\t\t\telse if (typeof item === 'string') {\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\timage: item\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 其他情况，尝试提取image属性\r\n\t\t\t\t\t\telse if (item && typeof item === 'object') {\r\n\t\t\t\t\t\t\t// 尝试查找可能的图片URL字段\r\n\t\t\t\t\t\t\tconst imageUrl = item.image || item.img || item.url || item.src || item.imageUrl || item.imgUrl || item.path || ''\r\n\t\t\t\t\t\t\t// 检查URL是否为相对路径，如果是则添加域名\r\n\t\t\t\t\t\t\tif (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('/')) {\r\n\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\timage: '/static/home/<USER>' // 如果是相对路径，添加静态文件夹前缀\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\timage: imageUrl\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn null\r\n\t\t\t\t\t}).filter(item => item && item.image)\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果API没有返回数据，则保持轮播图为空，而不是显示默认图片\r\n\t\t\t} catch(e) {\r\n\t\t\t\tconsole.error('获取轮播图失败:', e)\r\n\t\t\t\t// 出错时也不设置默认图片，保持轮播图为空\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取用户信息和资产\r\n\t\tasync getUserData() {\r\n\t\t\t// 检查是否登录\r\n\t\t\tconst token = uni.getStorageSync('token')\r\n\t\t\tif (!token) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\t// 使用统一接口获取用户完整信息\r\n\t\t\t\tconst profileRes = await getUserProfile()\r\n\t\t\t\tif (profileRes && profileRes.code === 1 && profileRes.data) {\r\n\t\t\t\t\tconst userData = profileRes.data\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新用户信息\r\n\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\tid: userData.id,\r\n\t\t\t\t\t\tuserId: userData.user_id,\r\n\t\t\t\t\t\tusername: userData.username,\r\n\t\t\t\t\t\tnickName: userData.nickname,\r\n\t\t\t\t\t\tavatarUrl: userData.avatar,\r\n\t\t\t\t\t\tphone: userData.mobile || ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新资产信息\r\n\t\t\t\t\tthis.points = parseInt(userData.score || 0)\r\n\t\t\t\t\tthis.balance = parseFloat(userData.money || 0).toFixed(2)\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新本地存储\r\n\t\t\t\t\tuni.setStorageSync('userInfo', this.userInfo)\r\n\t\t\t\t\tuni.setStorageSync('points', this.points)\r\n\t\t\t\t\tuni.setStorageSync('balance', this.balance)\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('用户数据获取成功:', userData.nickname)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果统一接口失败，尝试分别获取\r\n\t\t\t\tthis.getFallbackUserData()\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取用户数据失败:', error)\r\n\t\t\t\tthis.getFallbackUserData()\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 备用方案：分别获取用户信息和资产\r\n\t\tasync getFallbackUserData() {\r\n\t\t\ttry {\r\n\t\t\t\t// 获取资产信息\r\n\t\t\t\tconst assetsRes = await getUserAssets()\r\n\t\t\t\tif (assetsRes && assetsRes.code === 1 && assetsRes.data) {\r\n\t\t\t\t\tthis.points = parseInt(assetsRes.data.points || 0)\r\n\t\t\t\t\tthis.balance = parseFloat(assetsRes.data.balance || 0).toFixed(2)\r\n\t\t\t\t\t\r\n\t\t\t\t\tuni.setStorageSync('points', this.points)\r\n\t\t\t\t\tuni.setStorageSync('balance', this.balance)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果获取资产信息失败，分别获取积分和余额\r\n\t\t\t\t\tthis.getUserPointsAndBalance()\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取资产信息失败，尝试分别获取:', e)\r\n\t\t\t\tthis.getUserPointsAndBalance()\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 分别获取积分和余额\r\n\t\tasync getUserPointsAndBalance() {\r\n\t\t\ttry {\r\n\t\t\t\t// 获取积分\r\n\t\t\t\tconst pointsRes = await getUserPoints()\r\n\t\t\t\tif (pointsRes && pointsRes.code === 1) {\r\n\t\t\t\t\tthis.points = parseInt(pointsRes.data.points || 0)\r\n\t\t\t\t\tuni.setStorageSync('points', this.points)\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取余额\r\n\t\t\t\tconst balanceRes = await getUserBalance()\r\n\t\t\t\tif (balanceRes && balanceRes.code === 1) {\r\n\t\t\t\t\tthis.balance = parseFloat(balanceRes.data.balance || 0).toFixed(2)\r\n\t\t\t\t\tuni.setStorageSync('balance', this.balance)\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取积分或余额失败:', e)\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\thandleBannerChange(index) {\r\n\t\t\tconsole.log('当前轮播图索引：', index)\r\n\t\t},\r\n\t\thandleBannerClick(index) {\r\n\t\t\tconsole.log('点击了第' + (index + 1) + '张轮播图')\r\n\t\t},\r\n\t\thandleMemberCode() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/invite/poster',\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\thandleStoreOrder() {\r\n\t\t\t// 保持堂食的选择状态\r\n\t\t\tuni.setStorageSync('diningType', 'dine-in')\r\n\t\t\t\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/menu/menu'\r\n\t\t\t})\r\n\t\t},\r\n\t\thandleDeliveryOrder() {\r\n\t\t\t// 保存外卖选择状态\r\n\t\t\tuni.setStorageSync('diningType', 'takeout')\r\n\t\t\t\r\n\t\t\t// 跳转到点餐页面\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/menu/menu'\r\n\t\t\t})\r\n\t\t},\r\n\t\thandleGroupBuy() {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '团购功能开发中',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\tgoToRecharge() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/my/recharge',\r\n\t\t\t})\r\n\t\t},\r\n\t\thandlePointsMall() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/points/mall',\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\tgoToCoupon() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/coupon/coupon',\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 跳转到登录页面\r\n\t\tgoToLogin() {\r\n\t\t\tuni.redirectTo({\r\n\t\t\t\turl: '/pages/login/login'\r\n\t\t\t})\r\n\t\t},\r\n\t\tgoToProductDetail(item) {\r\n\t\t\t// 跳转到菜单页面，并传递分类ID\r\n\t\t\tif (item.category_id) {\r\n\t\t\t\t// 储存要滚动到的商品ID\r\n\t\t\t\tuni.setStorageSync('scrollToProductId', item.id)\r\n\t\t\t\t\r\n\t\t\t\t// 跳转到菜单页面并传递分类ID\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/menu/menu',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t// 通过事件总线触发菜单页面切换到对应分类\r\n\t\t\t\t\t\tuni.$emit('switchCategory', {\r\n\t\t\t\t\t\t\tcategoryId: item.category_id,\r\n\t\t\t\t\t\t\tproductId: item.id\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/menu/menu'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\taddToCart(item) {\r\n\t\t\t// 判断是否为单规格商品\r\n\t\t\tif (item.spec_type === 'single') {\r\n\t\t\t\t// 单规格商品直接加入购物车\r\n\t\t\t\tconst price = parseFloat(item.price || 0).toFixed(2);\r\n\t\t\t\t\r\n\t\t\t\t// 构建购物车商品数据\r\n\t\t\t\tconst cartItem = {\r\n\t\t\t\t\tid: item.id,\r\n\t\t\t\t\tname: item.name,\r\n\t\t\t\t\tprice: price,\r\n\t\t\t\t\timage: item.image,\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\ttotalPrice: price,\r\n\t\t\t\t\tspec_type: 'single',\r\n\t\t\t\t\tspecs: '默认规格',\r\n\t\t\t\t\tprops_text: '默认规格'\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 获取当前购物车数据\r\n\t\t\t\tconst cartData = uni.getStorageSync('cartData') || {\r\n\t\t\t\t\tlist: [],\r\n\t\t\t\t\ttotal: 0,\r\n\t\t\t\t\tprice: 0\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 检查购物车是否已有相同商品\r\n\t\t\t\tconst existingItemIndex = cartData.list.findIndex(i => i.id === item.id)\r\n\t\t\t\t\r\n\t\t\t\tif (existingItemIndex > -1) {\r\n\t\t\t\t\t// 已存在则数量+1\r\n\t\t\t\t\tcartData.list[existingItemIndex].count += 1\r\n\t\t\t\t\tcartData.list[existingItemIndex].totalPrice = Number(cartData.list[existingItemIndex].price) * cartData.list[existingItemIndex].count\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 不存在则添加\r\n\t\t\t\t\tcartData.list.push(cartItem)\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 重新计算总数和总价\r\n\t\t\t\tcartData.total = cartData.list.reduce((sum, item) => sum + item.count, 0)\r\n\t\t\t\tcartData.price = cartData.list.reduce((sum, item) => sum + Number(item.totalPrice), 0)\r\n\t\t\t\t\r\n\t\t\t\t// 更新购物车数据\r\n\t\t\t\tuni.setStorageSync('cartData', cartData)\r\n\t\t\t\t\r\n\t\t\t\t// 显示添加成功提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已加入购物车',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\t// 多规格商品，跳转到商品详情让用户选择规格\r\n\t\t\t\tthis.goToProductDetail(item)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取热销商品数据\r\n\t\tasync getTopSellingProducts() {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await getTopSellingProducts({ limit: 10 })\r\n\t\t\t\tconsole.log('获取热销商品数据:', res)\r\n\t\t\t\t\r\n\t\t\t\tlet productsData = []\r\n\t\t\t\tif (res && res.code === 1) {\r\n\t\t\t\t\tif (res.data && Array.isArray(res.data.list)) {\r\n\t\t\t\t\t\tproductsData = res.data.list\r\n\t\t\t\t\t} else if (Array.isArray(res.data)) {\r\n\t\t\t\t\t\tproductsData = res.data\r\n\t\t\t\t\t} else if (Array.isArray(res.list)) {\r\n\t\t\t\t\t\tproductsData = res.list\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 处理数据并设置is_new标识\r\n\t\t\t\tthis.topSellingProducts = productsData.map(item => {\r\n\t\t\t\t\t// 确保价格为字符串\r\n\t\t\t\t\tconst price = typeof item.price === 'number' ? item.price.toFixed(2) : item.price\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 随机设置20%的商品为新品\r\n\t\t\t\t\tconst isNew = Math.random() > 0.8\r\n\r\n\t\t\t\t\t// 确保图片路径正确\r\n\t\t\t\t\tlet image = item.image || ''\r\n\t\t\t\t\tif (image && !image.startsWith('http') && !image.startsWith('/')) {\r\n\t\t\t\t\t\timage = '/' + image\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tprice,\r\n\t\t\t\t\t\tis_new: isNew,\r\n\t\t\t\t\t\timage\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取热销商品失败:', error)\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tasync onShow() {\r\n\t\t// 检查登录状态\r\n\t\tconst token = uni.getStorageSync('token')\r\n\t\tthis.isLogin = !!token\r\n\t\t\r\n\t\t// 获取本地存储的用户信息\r\n\t\tconst userInfo = uni.getStorageSync('userInfo') || {}\r\n\t\tthis.userInfo = userInfo\r\n\t\t\r\n\t\t// 从本地存储获取最近的积分和余额数据（用于快速显示）\r\n\t\tconst storedPoints = uni.getStorageSync('points')\r\n\t\tconst storedBalance = uni.getStorageSync('balance')\r\n\t\t\r\n\t\tif (storedPoints) this.points = storedPoints\r\n\t\tif (storedBalance) this.balance = storedBalance\r\n\t\t\r\n\t\t// 获取轮播图数据\r\n\t\tconsole.log('onShow 开始获取轮播图')\r\n\t\tthis.getBannerList()\r\n\t\t\r\n\t\t// 获取热销商品数据\r\n\t\tthis.getTopSellingProducts()\r\n\t\t\r\n\t\t// 获取最新的用户数据\r\n\t\tif (this.isLogin) {\r\n\t\t\tthis.getUserData()\r\n\t\t}\r\n\t\t\r\n\t\t// 重新获取图标配置\r\n\t\tthis.getIconsConfig()\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f8f8;\r\n\tpadding-bottom: env(safe-area-inset-bottom);\r\n\t\r\n\t.status-bar-placeholder {\r\n\t\tdisplay: none; /* 隐藏额外空间 */\r\n\t}\r\n\t\r\n\t.swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 380rpx; /* 增加轮播图高度 */\r\n\t\tmargin-top: 0; /* 移除轮播图顶部外边距 */\r\n\t\tmargin-bottom: 10rpx; /* 轮播图底部外边距 */\r\n\t\tpadding-top: env(safe-area-inset-top); /* 添加安全区域适配 */\r\n\t\t\r\n\t\t.swiper-item {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t\t\r\n\t\t.swiper-image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t\tobject-fit: cover; /* 确保图片填充整个容器且保持纵横比 */\r\n\t\t}\r\n\t}\r\n\t\r\n\t.banner {\r\n\t\twidth: 100%;\r\n\t\theight: 320rpx;\r\n\t\t\r\n\t\t::v-deep .u-swiper__wrapper {\r\n\t\t\theight: 320rpx !important;\r\n\t\t}\r\n\t\t\r\n\t\t::v-deep .u-swiper__wrapper__item__wrapper__image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tborder-radius: 0;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.member-card {\r\n\t\tmargin: 20rpx 20rpx 0rpx; /* 增加上下边距 */\r\n\t\t\r\n\t\t.member-info {\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\r\n\t\t\t&.not-login {\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.login-tip {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #666;\r\n\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.login-btn {\r\n\t\t\t\t\tbackground-color: #78c238;\r\n\t\t\t\t\tpadding: 16rpx 40rpx;\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.info-content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t\r\n\t\t\t\t.user-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.avatar-wrap {\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.avatar {\r\n\t\t\t\t\t\t\twidth: 100rpx;\r\n\t\t\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.vip-badge {\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tright: -6rpx;\r\n\t\t\t\t\t\t\tbottom: -6rpx;\r\n\t\t\t\t\t\t\twidth: 32rpx;\r\n\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\tbackground: #8cd548;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.user-detail {\r\n\t\t\t\t\t\t.name {\r\n\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.level {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #8cd548;\r\n\t\t\t\t\t\t\tbackground: rgba(140, 213, 72, 0.1);\r\n\t\t\t\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.asset-info {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.asset-item {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.value {\r\n\t\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.label {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.divider {\r\n\t\t\t\t\t\twidth: 1rpx;\r\n\t\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\t\tbackground: #eee;\r\n\t\t\t\t\t\tmargin: 0 30rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.qr-code {\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\t\r\n\t\t\t\t.code-wrap {\r\n\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.code-icon {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.unified-white-box {\r\n\t\tmargin: 20rpx 20rpx 0rpx; /* 增加上下边距 */\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.order-section {\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tgap: 15rpx;\r\n\t\t\t\r\n\t\t\t.order-item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmin-width: 0;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.order-icon {\r\n\t\t\t\t\twidth: 300rpx;\r\n\t\t\t\t\theight: 220rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.order-content {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.title {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tmargin-bottom: 8rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.desc {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t\twhite-space: normal;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.divider-line {\r\n\t\t\theight: 1rpx;\r\n\t\t\tbackground: #eee;\r\n\t\t\tmargin: 30rpx 0;\r\n\t\t}\r\n\t\t\r\n\t\t.feature-section {\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t\t\tgap: 20rpx;\r\n\t\t\t\r\n\t\t\t.feature-item {\r\n\t\t\t\tbackground: transparent;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: column;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t.feature-icon {\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.hot-selling-section {\r\n\t\tmargin: 20rpx 20rpx 40rpx; /* 增加底部边距 */\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.section-header {\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\tpadding: 0 10rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\t\r\n\t\t\t.section-title {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-left: 16rpx;\r\n\t\t\t\t\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tleft: 0;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\tbackground: linear-gradient(to bottom, #8cd548, #6ab52e);\r\n\t\t\t\t\tborder-radius: 3rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.product-scroll {\r\n\t\t\twhite-space: nowrap;\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\t/* 隐藏滚动条但保留滚动功能 */\r\n\t\t\t&::-webkit-scrollbar {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.product-card {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 200rpx;\r\n\t\t\t\tmargin-right: 50rpx;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\t\t\tvertical-align: top;\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.product-tag {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 10rpx;\r\n\t\t\t\t\tleft: 10rpx;\r\n\t\t\t\t\tbackground: #ff5722;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\tz-index: 2;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.product-image {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 180rpx;\r\n\t\t\t\t\tobject-fit: cover;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.product-info {\r\n\t\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\tbackground: #fff;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.product-name {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\twhite-space: normal;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t\t-webkit-line-clamp: 1;\r\n\t\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t\theight: 34rpx;\r\n\t\t\t\t\t\tline-height: 1.4;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.product-price-row {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.product-price {\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.add-btn {\r\n\t\t\t\t\t\t\twidth: 48rpx;\r\n\t\t\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\tbox-shadow: 0 4rpx 8rpx rgba(140, 213, 72, 0.2);\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.add-icon {\r\n\t\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\t\t\tline-height: 1;\r\n\t\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\t\t\t\t\topacity: 0.9;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@keyframes shine {\r\n\t0% {\r\n\t\ttransform: translate(-50%, -50%) rotate(0deg);\r\n\t}\r\n\t100% {\r\n\t\ttransform: translate(50%, 50%) rotate(0deg);\r\n\t}\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&id=92bb8f34&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./home.vue?vue&type=style&index=0&id=92bb8f34&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948310027\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}