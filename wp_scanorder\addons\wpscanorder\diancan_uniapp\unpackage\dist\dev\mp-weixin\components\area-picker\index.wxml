<view class="area-picker data-v-36ea85b4"><u-popup vue-id="a6652ec0-1" show="{{show}}" mode="bottom" data-event-opts="{{[['^close',[['onClose']]]]}}" bind:close="__e" class="data-v-36ea85b4" bind:__l="__l" vue-slots="{{['default']}}"><view class="area-picker-header data-v-36ea85b4"><text data-event-opts="{{[['tap',[['onCancel',['$event']]]]]}}" class="cancel-btn data-v-36ea85b4" bindtap="__e">取消</text><text class="title data-v-36ea85b4">{{title}}</text><text data-event-opts="{{[['tap',[['onConfirm',['$event']]]]]}}" class="confirm-btn data-v-36ea85b4" bindtap="__e">确定</text></view><view class="area-picker-body data-v-36ea85b4"><view class="area-column data-v-36ea85b4"><scroll-view class="area-scroll data-v-36ea85b4" scroll-y="{{true}}" scroll-top="{{provinceScrollTop}}" scroll-with-animation="{{true}}" id="province-scroll"><block wx:if="{{$root.g0}}"><view class="area-loading data-v-36ea85b4"><text class="data-v-36ea85b4">加载中...</text></view></block><block wx:for="{{provinces}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="{{['data-v-36ea85b4','area-item',provinceIndex===index?'active':'']}}" id="{{'province-item-'+index}}" data-event-opts="{{[['tap',[['selectProvince',[index]]]]]}}" bindtap="__e">{{''+item.name+''}}</view></block><block wx:if="{{$root.g1}}"><view class="area-empty data-v-36ea85b4"><text class="data-v-36ea85b4">暂无数据</text></view></block></scroll-view></view><view class="area-column data-v-36ea85b4"><scroll-view class="area-scroll data-v-36ea85b4" scroll-y="{{true}}" scroll-top="{{cityScrollTop}}" scroll-with-animation="{{true}}" id="city-scroll"><block wx:if="{{$root.g2}}"><view class="area-loading data-v-36ea85b4"><text class="data-v-36ea85b4">加载中...</text></view></block><block wx:for="{{cities}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="{{['data-v-36ea85b4','area-item',cityIndex===index?'active':'']}}" id="{{'city-item-'+index}}" data-event-opts="{{[['tap',[['selectCity',[index]]]]]}}" bindtap="__e">{{''+item.name+''}}</view></block><block wx:if="{{$root.g3}}"><view class="area-empty data-v-36ea85b4"><text class="data-v-36ea85b4">暂无数据</text></view></block></scroll-view></view><view class="area-column data-v-36ea85b4"><scroll-view class="area-scroll data-v-36ea85b4" scroll-y="{{true}}" scroll-top="{{districtScrollTop}}" scroll-with-animation="{{true}}" id="district-scroll"><block wx:if="{{$root.g4}}"><view class="area-loading data-v-36ea85b4"><text class="data-v-36ea85b4">加载中...</text></view></block><block wx:for="{{districts}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="{{['data-v-36ea85b4','area-item',districtIndex===index?'active':'']}}" id="{{'district-item-'+index}}" data-event-opts="{{[['tap',[['selectDistrict',[index]]]]]}}" bindtap="__e">{{''+item.name+''}}</view></block><block wx:if="{{$root.g5}}"><view class="area-empty data-v-36ea85b4"><text class="data-v-36ea85b4">暂无数据</text></view></block></scroll-view></view></view></u-popup></view>