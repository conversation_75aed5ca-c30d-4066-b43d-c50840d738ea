{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/my.vue?db5d", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/my.vue?5c07", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/my.vue?27a7", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/my.vue?dad9", "uni-app:///pages/my/my.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/my.vue?5527", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/my/my.vue?8517"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "statusBarHeight", "is<PERSON>ogin", "userInfo", "userAssets", "points", "coupons", "balance", "features", "name", "icon", "type", "onLoad", "onShow", "methods", "handleLogin", "uni", "url", "handleLoginSuccess", "console", "openid", "session_key", "avatar", "nickname", "avatarUrl", "nick<PERSON><PERSON>", "phone", "userId", "createTime", "title", "duration", "getUserData", "profileRes", "userData", "id", "username", "gender", "birthday", "getCouponsCount", "couponsRes", "parseInt", "getFallbackUserData", "assetsRes", "getPointsInfo", "pointsRes", "getCouponsInfo", "oldCouponsRes", "getBalanceInfo", "balanceRes", "goToSetting", "animationType", "animationDuration", "handleFeatureClick", "goToPoints", "goToCoupons", "goToRecharge"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACsD;AACL;AACsC;;;AAGvF;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2pB,CAAgB,4mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8F/qB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC,WACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA;MACA;MACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA;IAEA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;MACA;MACA;IACA;;IAEA;IACA;IACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;MACA;;MAEA;MACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MAEA;QACAC;MACA;;MAEA;MACAA;QACAC;MACA;IACA;IAEA;IACAC;MACA;QACAC;QAEA;UACA;QACA;QAEA;QACA;QAEA;UACA;QACA;;QAEA;QACAH;;QAEA;QACA;UACAA;YACAI;YACAC;UACA;QACA;;QAEA;QACA;QACA;UACAC;QACA;UACAA;QACA;UACAA;QACA;;QAEA;QACA;QACA;UACAC;QACA;UACAA;QACA;UACAA;QACA;;QAEA;QACA;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QAEAZ;;QAEA;QACA;QACA;;QAEA;QACAA;UACAa;UACAnB;UACAoB;QACA;;QAEA;QACA;MAEA;QACAX;QACAH;UACAa;UACAnB;UACAoB;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;gBACAd;;gBAEA;gBACA;kBACAe;kBACAP;kBACAQ;kBACAV;kBACAD;kBACAE;kBACAU;kBACAC;gBACA;;gBAEA;gBACA;kBACAhC;kBACAC;kBAAA;kBACAC;gBACA;gBAEAY;gBACA;gBACAH;gBACAA;gBACAA;;gBAEA;gBACA;gBAEAG;gBAAA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA,6DACAA,yBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;kBACA;oBACArC;oBACAC;oBACAC;kBACA;;kBAEA;kBACAS;kBACAA;gBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAG;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAwB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA5B;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAG;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA0B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAN;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA,6DACAA,yBACAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAM;gBACA;kBACA,gEACAA,4BACAN;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGArB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACAhC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAG;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA8B;MACAjC;QACAC;QACAiC;QACAC;MACA;IACA;IACAC;MACA;QACA;UACApC;YACAC;UACA;UACA;QACA;UACAD;YACAC;UACA;UACA;QACA;UACAD;YACAC;UACA;UACA;QACA;UACAD;YACAC;UACA;UACA;QACA;UACAD;YACAC;UACA;UACA;QACA;UACAD;YACAC;UACA;UACA;MAAA;IAEA;IACAoC;MACArC;QACAC;QACAiC;QACAC;MACA;IACA;IACAG;MACAtC;QACAC;QACAiC;QACAC;MACA;IACA;IACAI;MACAvC;QACAC;QACAiC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxcA;AAAA;AAAA;AAAA;AAA8wC,CAAgB,mnCAAG,EAAC,C;;;;;;;;;;;ACAlyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"\nvar renderjs\nimport script from \"./my.vue?vue&type=script&lang=js&\"\nexport * from \"./my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my.vue?vue&type=style&index=0&id=0be17cc6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0be17cc6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/my.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"wp_-flex-col page\">\r\n\t\t<!-- 顶部用户信息 -->\r\n\t\t<view class=\"header-section\">\r\n\t\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n\t\t\t<view class=\"section\">\r\n\t\t\t\t<view class=\"user-info\">\r\n\t\t\t\t\t<!-- 未登录状态 -->\r\n\t\t\t\t\t<template v-if=\"!isLogin\">\r\n\t\t\t\t\t\t<view class=\"user-content\" @tap=\"handleLogin\">\r\n\t\t\t\t\t\t\t<view class=\"user-left\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar\" src=\"/static/my/default-avatar.png\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t\t\t<view class=\"info-wrap\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"nickname\">点击授权登录</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"desc\">登录后享受更多权益</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 已登录状态 -->\r\n\t\t\t\t\t<template v-else>\r\n\t\t\t\t\t\t<view class=\"user-content\">\r\n\t\t\t\t\t\t\t<view class=\"user-left\">\r\n\t\t\t\t\t\t\t\t<image class=\"avatar\" :src=\"userInfo.avatarUrl || '/static/my/default-avatar.png'\" mode=\"aspectFill\" />\r\n\t\t\t\t\t\t\t\t<view class=\"info-wrap\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"nickname\">{{userInfo.nickName}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"desc\">{{userInfo.phone}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"setting-btn\" @tap=\"goToSetting\">\r\n\t\t\t\t\t\t\t\t<custom-icon name=\"setting-fill\" size=\"32\" color=\"#a3a3a3\"></custom-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 登录后显示资产信息 -->\r\n\t\t<template v-if=\"isLogin\">\r\n\t\t\t<view class=\"assets-box\">\r\n\t\t\t\t<view class=\"asset-item\" @tap=\"goToPoints\">\r\n\t\t\t\t\t<text class=\"asset-value\">{{userAssets.points}}</text>\r\n\t\t\t\t\t<text class=\"asset-label\">积分</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"asset-item\" @tap=\"goToCoupons\">\r\n\t\t\t\t\t<text class=\"asset-value\">{{userAssets.coupons}}</text>\r\n\t\t\t\t\t<text class=\"asset-label\">优惠券</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"asset-item\" @tap=\"goToRecharge\">\r\n\t\t\t\t\t<text class=\"asset-value\">¥{{userAssets.balance}}</text>\r\n\t\t\t\t\t<text class=\"asset-label\">余额</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\r\n\t\t<!-- 功能列表 -->\r\n\t\t<view class=\"feature-box\">\r\n\t\t\t<view class=\"feature-title\">我的服务</view>\r\n\t\t\t<view class=\"feature-list\">\r\n\t\t\t\t<block v-for=\"(item, index) in features\" :key=\"index\">\r\n\t\t\t\t\t<!-- 普通功能项 -->\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tv-if=\"item.type !== 'service'\"\r\n\t\t\t\t\t\tclass=\"feature-item\" \r\n\t\t\t\t\t\t@tap=\"handleFeatureClick(item)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"item-left\">\r\n\t\t\t\t\t\t\t<u-icon :name=\"item.icon\" size=\"30\" color=\"#333\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"feature-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999\" size=\"15\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 客服功能项 - 使用button实现，但样式保持一致 -->\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tv-else\r\n\t\t\t\t\t\topen-type=\"contact\"\r\n\t\t\t\t\t\tclass=\"feature-item contact-feature-btn\" \r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"item-left\">\r\n\t\t\t\t\t\t\t<u-icon :name=\"item.icon\" size=\"30\" color=\"#333\"></u-icon>\r\n\t\t\t\t\t\t\t<text class=\"feature-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999\" size=\"15\"></u-icon>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { thirdLogin, thirdLoginFullPath, getUserProfile, getUserAssets, getUserPoints, getUserCoupons, getUserBalance, getMyCoupons } from '@/api/user.js';\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tstatusBarHeight: 0,\r\n\t\t\tisLogin: false,\r\n\t\t\tuserInfo: {},\r\n\t\t\tuserAssets: {\r\n\t\t\t\tpoints: 0,\r\n\t\t\t\tcoupons: 0,\r\n\t\t\t\tbalance: '0.00'\r\n\t\t\t},\r\n\t\t\tfeatures: [\r\n\t\t\t\t{ name: '我的订单', icon: 'order', type: 'order' },\r\n\t\t\t\t{ name: '收货地址', icon: 'map', type: 'address' },\r\n\t\t\t\t{ name: '联系客服', icon: 'phone', type: 'service' },\r\n\t\t\t\t{ name: '充值记录', icon: 'file-text', type: 'recharge-record' },\r\n\t\t\t\t// { name: '领取优惠', icon: 'gift', type: 'receive' },\r\n\t\t\t\t{ name: '资料设置', icon: 'account', type: 'settings' },\r\n\t\t\t\t{ name: '版权声明', icon: 'info-circle', type: 'copyright' }\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 获取状态栏高度\r\n\t\tconst systemInfo = uni.getSystemInfoSync()\r\n\t\tthis.statusBarHeight = systemInfo.statusBarHeight\r\n\t},\r\n\tonShow() {\r\n\t\t// 检查登录状态\r\n\t\tconst token = uni.getStorageSync('token')\r\n\t\tif (!token) {\r\n\t\t\tthis.isLogin = false\r\n\t\t\tthis.userInfo = {}\r\n\t\t\treturn\r\n\t\t}\r\n\t\t\r\n\t\t// 已登录则更新用户信息\r\n\t\tconst userInfo = uni.getStorageSync('userInfo')\r\n\t\tif (userInfo) {\r\n\t\t\tthis.isLogin = true \r\n\t\t\tthis.userInfo = userInfo\r\n\t\t\t\r\n\t\t\t// 从本地存储获取最近的资产数据（用于快速显示）\r\n\t\t\tconst storedPoints = uni.getStorageSync('points')\r\n\t\t\tconst storedBalance = uni.getStorageSync('balance')\r\n\t\t\t\r\n\t\t\tif (storedPoints) this.userAssets.points = storedPoints\r\n\t\t\tif (storedBalance) this.userAssets.balance = storedBalance\r\n\t\t\t\r\n\t\t\t// 获取最新用户信息和资产  \r\n\t\t\tthis.getUserData()\r\n\t\t} else {\r\n\t\t\tthis.isLogin = false\r\n\t\t\tthis.userInfo = {}\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 处理登录点击\r\n\t\thandleLogin() {\r\n\t\t\t// 保存当前页面路径\r\n\t\t\tconst pages = getCurrentPages();\r\n\t\t\tconst currentPage = pages[pages.length - 1];\r\n\t\t\tconst url = currentPage ? `/${currentPage.route}` : '';\r\n\t\t\t\r\n\t\t\tif (url) {\r\n\t\t\t\tuni.setStorageSync('redirect_url', url);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 跳转到登录页面\r\n\t\t\tuni.redirectTo({\r\n\t\t\t\turl: '/pages/login/login'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\t// 处理登录成功后的数据\r\n\t\thandleLoginSuccess(data, originalUserInfo) {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('处理登录成功数据');\r\n\t\t\t\t\r\n\t\t\t\tif (!data) {\r\n\t\t\t\t\tthrow new Error('返回数据为空');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst userData = data.userinfo || {};\r\n\t\t\t\tconst thirdData = data.thirdinfo || {};\r\n\t\t\t\t\r\n\t\t\t\tif (!userData || !userData.token) {\r\n\t\t\t\t\tthrow new Error('用户数据异常');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 保存登录凭证\r\n\t\t\t\tuni.setStorageSync('token', userData.token);\r\n\t\t\t\t\r\n\t\t\t\t// 保存第三方登录数据（可选）\r\n\t\t\t\tif (thirdData && (thirdData.session_key || thirdData.openid)) {\r\n\t\t\t\t\tuni.setStorageSync('thirdData', {\r\n\t\t\t\t\t\topenid: thirdData.openid || '',\r\n\t\t\t\t\t\tsession_key: thirdData.session_key || ''\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 头像处理\r\n\t\t\t\tlet avatar = '/static/my/default-avatar.png';\r\n\t\t\t\tif (userData.avatar) {\r\n\t\t\t\t\tavatar = userData.avatar;\r\n\t\t\t\t} else if (thirdData && thirdData.avatarUrl) {\r\n\t\t\t\t\tavatar = thirdData.avatarUrl;\r\n\t\t\t\t} else if (originalUserInfo && originalUserInfo.avatarUrl) {\r\n\t\t\t\t\tavatar = originalUserInfo.avatarUrl;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 昵称处理\r\n\t\t\t\tlet nickname = '用户';\r\n\t\t\t\tif (userData.nickname) {\r\n\t\t\t\t\tnickname = userData.nickname;\r\n\t\t\t\t} else if (thirdData && thirdData.nickName) {\r\n\t\t\t\t\tnickname = thirdData.nickName;\r\n\t\t\t\t} else if (originalUserInfo && originalUserInfo.nickName) {\r\n\t\t\t\t\tnickname = originalUserInfo.nickName;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 保存用户信息 - 优先使用服务器返回数据\r\n\t\t\t\tconst userInfoToSave = {\r\n\t\t\t\t\tavatarUrl: avatar,\r\n\t\t\t\t\tnickName: nickname,\r\n\t\t\t\t\tphone: userData.mobile || '',\r\n\t\t\t\t\tuserId: userData.id || userData.user_id || '',\r\n\t\t\t\t\tcreateTime: userData.createtime || ''\r\n\t\t\t\t};\r\n\t\t\t\t\r\n\t\t\t\tuni.setStorageSync('userInfo', userInfoToSave);\r\n\t\t\t\t\r\n\t\t\t\t// 更新页面状态\r\n\t\t\t\tthis.isLogin = true;\r\n\t\t\t\tthis.userInfo = userInfoToSave;\r\n\t\t\t\t\r\n\t\t\t\t// 显示成功提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 获取最新用户信息和资产\r\n\t\t\t\tthis.getUserData();\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('处理登录数据出错:', error.message);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '登录数据处理失败',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取用户数据（包括资产信息）\r\n\t\tasync getUserData() {\r\n\t\t\ttry {\r\n\t\t\t\t// 使用统一接口获取用户完整信息\r\n\t\t\t\tconst profileRes = await getUserProfile()\r\n\t\t\t\tif (profileRes && profileRes.code === 1 && profileRes.data) {\r\n\t\t\t\t\tconst userData = profileRes.data\r\n\t\t\t\t\tconsole.log('新增用户数据:', userData)\r\n\r\n\t\t\t\t\t// 更新用户信息\r\n\t\t\t\t\tthis.userInfo = {\r\n\t\t\t\t\t\tid: userData.id,\r\n\t\t\t\t\t\tuserId: userData.user_id,\r\n\t\t\t\t\t\tusername: userData.username,\r\n\t\t\t\t\t\tnickName: userData.nickname,\r\n\t\t\t\t\t\tavatarUrl: userData.avatar,\r\n\t\t\t\t\t\tphone: userData.mobile,\r\n\t\t\t\t\t\tgender: userData.gender,\r\n\t\t\t\t\t\tbirthday: userData.birthday\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新资产信息\r\n\t\t\t\t\tthis.userAssets = {\r\n\t\t\t\t\t\tpoints: parseInt(userData.score || 0),\r\n\t\t\t\t\t\tcoupons: parseInt(userData.coupons || 0), // 可能需要额外获取\r\n\t\t\t\t\t\tbalance: parseFloat(userData.money || 0).toFixed(2)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('新增用户:', this.userInfo.gender)\r\n\t\t\t\t\t// 更新本地存储\r\n\t\t\t\t\tuni.setStorageSync('userInfo', this.userInfo)\r\n\t\t\t\t\tuni.setStorageSync('points', this.userAssets.points)\r\n\t\t\t\t\tuni.setStorageSync('balance', this.userAssets.balance)\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 单独获取优惠券数量\r\n\t\t\t\t\tthis.getCouponsCount()\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('用户数据获取成功:', userData.nickname)\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果统一接口失败，使用分开的方式获取\r\n\t\t\t\tthis.getFallbackUserData()\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('获取用户数据失败:', error)\r\n\t\t\t\tthis.getFallbackUserData()\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取优惠券数量\r\n\t\tasync getCouponsCount() {\r\n\t\t\ttry {\r\n\t\t\t\tconst couponsRes = await getUserCoupons()\r\n\t\t\t\tif (couponsRes && couponsRes.code === 1) {\r\n\t\t\t\t\tthis.userAssets.coupons = Array.isArray(couponsRes.data) \r\n\t\t\t\t\t\t? couponsRes.data.length \r\n\t\t\t\t\t\t: parseInt(couponsRes.data || 0)\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取优惠券数量失败:', e)\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 备用方案：分别获取用户资产\r\n\t\tasync getFallbackUserData() {\r\n\t\t\ttry {\r\n\t\t\t\t// 获取资产信息\r\n\t\t\t\tconst assetsRes = await getUserAssets()\r\n\t\t\t\tif (assetsRes && assetsRes.code === 1 && assetsRes.data) {\r\n\t\t\t\t\tthis.userAssets = {\r\n\t\t\t\t\t\tpoints: parseInt(assetsRes.data.points || 0),\r\n\t\t\t\t\t\tcoupons: parseInt(assetsRes.data.coupons || 0),\r\n\t\t\t\t\t\tbalance: parseFloat(assetsRes.data.balance || 0).toFixed(2)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新本地存储\r\n\t\t\t\t\tuni.setStorageSync('points', this.userAssets.points)\r\n\t\t\t\t\tuni.setStorageSync('balance', this.userAssets.balance)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果获取资产信息失败，分别获取各项资产\r\n\t\t\t\t\tthis.getPointsInfo()\r\n\t\t\t\t\tthis.getCouponsInfo()\r\n\t\t\t\t\tthis.getBalanceInfo()\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取资产信息失败，尝试分别获取:', e)\r\n\t\t\t\tthis.getPointsInfo()\r\n\t\t\t\tthis.getCouponsInfo()\r\n\t\t\t\tthis.getBalanceInfo()\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取积分信息\r\n\t\tasync getPointsInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst pointsRes = await getUserPoints()\r\n\t\t\t\tif (pointsRes && pointsRes.code === 1) {\r\n\t\t\t\t\tthis.userAssets.points = parseInt(pointsRes.data.points || 0)\r\n\t\t\t\t\tuni.setStorageSync('points', this.userAssets.points)\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取积分失败:', e.message)\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取优惠券信息\r\n\t\tasync getCouponsInfo() {\r\n\t\t\ttry {\r\n\t\t\t\t// 使用新的getMyCoupons接口\r\n\t\t\t\tconst couponsRes = await getMyCoupons()\r\n\t\t\t\tif (couponsRes && couponsRes.code === 1) {\r\n\t\t\t\t\tthis.userAssets.coupons = Array.isArray(couponsRes.data) \r\n\t\t\t\t\t\t? couponsRes.data.length \r\n\t\t\t\t\t\t: parseInt(couponsRes.data || 0)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果新接口失败，尝试使用旧接口\r\n\t\t\t\t\tconst oldCouponsRes = await getUserCoupons()\r\n\t\t\t\t\tif (oldCouponsRes && oldCouponsRes.code === 1) {\r\n\t\t\t\t\t\tthis.userAssets.coupons = Array.isArray(oldCouponsRes.data) \r\n\t\t\t\t\t\t\t? oldCouponsRes.data.length \r\n\t\t\t\t\t\t\t: parseInt(oldCouponsRes.data || 0)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取优惠券失败:', e.message)\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 获取余额信息\r\n\t\tasync getBalanceInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst balanceRes = await getUserBalance()\r\n\t\t\t\tif (balanceRes && balanceRes.code === 1) {\r\n\t\t\t\t\tthis.userAssets.balance = parseFloat(balanceRes.data.balance || 0).toFixed(2)\r\n\t\t\t\t\tuni.setStorageSync('balance', this.userAssets.balance)\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取余额失败:', e.message)\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tgoToSetting() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/my/settings',\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\thandleFeatureClick(item) {\r\n\t\t\tswitch(item.type) {\r\n\t\t\t\tcase 'order':\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: '/pages/order/order'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'address':\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/address/address'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'recharge-record':\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/my/recharge-record'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'receive':\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/coupon/receive'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'settings':\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/my/settings'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'copyright':\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/copyright/copyright'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t},\r\n\t\tgoToPoints() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/points/mall',\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\tgoToCoupons() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/coupon/coupon',\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\tgoToRecharge() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/my/recharge',\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f8f8f8;\r\n\t\r\n\t.header-section {\r\n\t\tpadding: 0 30rpx 60rpx;\r\n\t\t\r\n\t\t.status-bar {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t\t\r\n\t\t.section {\r\n\t\t\tpadding-top: 88rpx;\r\n\t\t\t\r\n\t\t\t.user-info {\r\n\t\t\t\t.user-content {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.user-left {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.avatar {\r\n\t\t\t\t\t\t\twidth: 140rpx;\r\n\t\t\t\t\t\t\theight: 140rpx;\r\n\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\tborder: 4rpx solid rgba(255, 255, 255, 0.3);\r\n\t\t\t\t\t\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.info-wrap {\r\n\t\t\t\t\t\t\tmargin-left: 32rpx;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.nickname {\r\n\t\t\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t\t\tmargin-bottom: 12rpx;\r\n\t\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.desc {\r\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.setting-btn {\r\n\t\t\t\t\t\twidth: 88rpx;\r\n\t\t\t\t\t\theight: 88rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.login-btn {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tbackground: rgba(255, 255, 255, 0.2);\r\n\t\t\t\t\t\tborder-radius: 100rpx;\r\n\t\t\t\t\t\tpadding: 20rpx 40rpx;\r\n\t\t\t\t\t\tborder: 1px solid rgba(255, 255, 255, 0.25);\r\n\t\t\t\t\t\ttransition: all 0.3s;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t\tmargin-right: 12rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t&:active {\r\n\t\t\t\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\t\t\t\tbackground: rgba(255, 255, 255, 0.25);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.feature-box {\r\n\t\tmargin: 20rpx 40rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 24rpx;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.feature-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t\tpadding: 0 8rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.feature-list {\r\n\t\t\t.feature-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 30rpx 20rpx;\r\n\t\t\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t\t\t\t\r\n\t\t\t\t.item-left {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.feature-name {\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground: #f8f8f8;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tborder-bottom: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.contact-feature-btn {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbackground-color: transparent;\r\n\t\t\t\tborder: none;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tline-height: normal;\r\n\t\t\t\tborder-radius: 0;\r\n\t\t\t\tpadding: 30rpx 20rpx;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\tbackground: #f8f8f8;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.assets-box {\r\n\t\tmargin: 20rpx 40rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\t\r\n\t\t.asset-item {\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: 12rpx 0;\r\n\t\t\t\r\n\t\t\t&:not(:last-child)::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 0;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\twidth: 1rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tbackground: #eee;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.asset-value {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tline-height: 1.4;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.asset-label {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309866\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}