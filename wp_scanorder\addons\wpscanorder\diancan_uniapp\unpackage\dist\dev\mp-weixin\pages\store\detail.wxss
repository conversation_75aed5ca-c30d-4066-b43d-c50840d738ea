@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-7b3dceb8 {
  min-height: 100vh;
  background: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}
.custom-navbar.data-v-7b3dceb8 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
}
.custom-navbar .navbar-content.data-v-7b3dceb8 {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
  position: relative;
}
.custom-navbar .navbar-content .back-btn.data-v-7b3dceb8 {
  width: 88rpx;
  height: 44px;
  display: flex;
  align-items: center;
}
.custom-navbar .navbar-content .back-btn.data-v-7b3dceb8:active {
  opacity: 0.7;
}
.custom-navbar .navbar-content .page-title.data-v-7b3dceb8 {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.custom-navbar .navbar-content .placeholder.data-v-7b3dceb8 {
  width: 88rpx;
  height: 44px;
}
.navbar-placeholder.data-v-7b3dceb8 {
  width: 100%;
}
.store-header.data-v-7b3dceb8 {
  background: #fff;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.store-header .store-info-wrap.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
  padding: 0 4rpx;
}
.store-header .store-info-wrap .store-logo.data-v-7b3dceb8 {
  width: 140rpx;
  height: 140rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  background: #f8f8f8;
}
.store-header .store-info-wrap .store-brief.data-v-7b3dceb8 {
  flex: 1;
  margin-left: 24rpx;
}
.store-header .store-info-wrap .store-brief .store-name.data-v-7b3dceb8 {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}
.store-header .store-info-wrap .store-brief .store-stats.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
}
.store-header .store-info-wrap .store-brief .store-stats .rating-wrap.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
}
.store-header .store-info-wrap .store-brief .store-stats .rating-wrap .rating.data-v-7b3dceb8 {
  font-size: 28rpx;
  color: #ff9900;
  font-weight: 600;
  margin-right: 8rpx;
}
.store-header .store-info-wrap .store-brief .store-stats .rating-wrap .rating-stars.data-v-7b3dceb8 {
  display: flex;
  gap: 4rpx;
}
.store-header .store-info-wrap .store-brief .store-stats .divider.data-v-7b3dceb8 {
  margin: 0 16rpx;
  font-size: 24rpx;
  color: #ddd;
}
.store-header .store-info-wrap .store-brief .store-stats .sales.data-v-7b3dceb8 {
  font-size: 26rpx;
  color: #666;
}
.store-info.data-v-7b3dceb8 {
  padding: 24rpx;
}
.store-info .info-card.data-v-7b3dceb8 {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.02);
}
.store-info .info-card.data-v-7b3dceb8:last-child {
  margin-bottom: 0;
}
.store-info .business-card .business-info .time-item.data-v-7b3dceb8 {
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  background: linear-gradient(to bottom, #f9f9f9, #f5f5f5);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}
.store-info .business-card .business-info .time-item .time-header.data-v-7b3dceb8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.store-info .business-card .business-info .time-item .time-header .header-left.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
}
.store-info .business-card .business-info .time-item .time-header .header-left .u-icon.data-v-7b3dceb8 {
  margin-right: 8rpx;
}
.store-info .business-card .business-info .time-item .time-header .header-left .time-label.data-v-7b3dceb8 {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}
.store-info .business-card .business-info .time-item .time-header .status-tag.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  background: rgba(140, 213, 72, 0.1);
  border-radius: 24rpx;
}
.store-info .business-card .business-info .time-item .time-header .status-tag .u-icon.data-v-7b3dceb8 {
  margin-right: 4rpx;
}
.store-info .business-card .business-info .time-item .time-header .status-tag text.data-v-7b3dceb8 {
  font-size: 24rpx;
  color: #8cd548;
  font-weight: 500;
}
.store-info .business-card .business-info .time-item .time-header .status-tag.closed.data-v-7b3dceb8 {
  background: rgba(255, 107, 107, 0.1);
}
.store-info .business-card .business-info .time-item .time-header .status-tag.closed text.data-v-7b3dceb8 {
  color: #ff6b6b;
}
.store-info .business-card .business-info .time-item .time-periods.data-v-7b3dceb8 {
  padding: 16rpx;
  background: #fff;
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}
.store-info .business-card .business-info .time-item .time-periods .time-value.data-v-7b3dceb8 {
  display: block;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.store-info .business-card .business-info .time-item .time-periods .time-tips.data-v-7b3dceb8 {
  display: block;
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}
.store-info .business-card .business-info .notice-item.data-v-7b3dceb8 {
  margin-top: 16rpx;
  padding: 20rpx;
  background: rgba(255, 153, 0, 0.05);
  border-radius: 12rpx;
  display: flex;
  align-items: flex-start;
}
.store-info .business-card .business-info .notice-item .u-icon.data-v-7b3dceb8 {
  margin-top: 4rpx;
  flex-shrink: 0;
  margin-right: 12rpx;
}
.store-info .business-card .business-info .notice-item .notice-content.data-v-7b3dceb8 {
  flex: 1;
}
.store-info .business-card .business-info .notice-item .notice-content .notice-label.data-v-7b3dceb8 {
  display: block;
  font-size: 24rpx;
  color: #ff9900;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.store-info .business-card .business-info .notice-item .notice-content .notice-text.data-v-7b3dceb8 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.store-info .business-card .business-info .notice-item.data-v-7b3dceb8:active {
  background: rgba(255, 153, 0, 0.08);
}
.store-info .card-header.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 4rpx;
}
.store-info .card-header .title.data-v-7b3dceb8 {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-left: 12rpx;
}
.store-info .address-info .address-content.data-v-7b3dceb8 {
  background: #f9f9f9;
  border-radius: 12rpx;
  padding: 24rpx;
}
.store-info .address-info .address-content .location-info .address-header.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.store-info .address-info .address-content .location-info .distance-tag.data-v-7b3dceb8 {
  display: inline-flex;
  align-items: center;
  background: rgba(255, 107, 107, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 24rpx;
}
.store-info .address-info .address-content .location-info .distance-tag .u-icon.data-v-7b3dceb8 {
  margin-right: 6rpx;
}
.store-info .address-info .address-content .location-info .distance-tag text.data-v-7b3dceb8 {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: 500;
}
.store-info .address-info .address-content .location-info .nav-btn.data-v-7b3dceb8 {
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  background: #fff;
  border-radius: 24rpx;
  border: 1rpx solid rgba(255, 107, 107, 0.2);
}
.store-info .address-info .address-content .location-info .nav-btn .u-icon.data-v-7b3dceb8 {
  margin-right: 4rpx;
}
.store-info .address-info .address-content .location-info .nav-btn text.data-v-7b3dceb8 {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: 500;
}
.store-info .address-info .address-content .location-info .nav-btn.data-v-7b3dceb8:active {
  opacity: 0.8;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.store-info .address-info .address-content .address-detail.data-v-7b3dceb8 {
  display: flex;
  align-items: flex-start;
  background: #fff;
  padding: 16rpx;
  border-radius: 12rpx;
}
.store-info .address-info .address-content .address-detail .u-icon.data-v-7b3dceb8 {
  margin-right: 8rpx;
  margin-top: 4rpx;
}
.store-info .address-info .address-content .address-detail .address-text.data-v-7b3dceb8 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.store-info .qualification .qual-content.data-v-7b3dceb8 {
  position: relative;
}
.store-info .qualification .qual-content .qual-scroll.data-v-7b3dceb8 {
  width: 100%;
  white-space: nowrap;
  padding: 12rpx 0;
}
.store-info .qualification .qual-content .qual-scroll .qual-item.data-v-7b3dceb8 {
  display: inline-block;
  width: 240rpx;
  margin-right: 20rpx;
  position: relative;
}
.store-info .qualification .qual-content .qual-scroll .qual-item.data-v-7b3dceb8:last-child {
  margin-right: 0;
}
.store-info .qualification .qual-content .qual-scroll .qual-item .qual-image.data-v-7b3dceb8 {
  width: 100%;
  height: 320rpx;
  border-radius: 12rpx;
  background: #f8f8f8;
}
.store-info .qualification .qual-content .qual-scroll .qual-item .qual-tips.data-v-7b3dceb8 {
  position: absolute;
  left: 50%;
  bottom: 16rpx;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  font-size: 24rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}
.store-info .qualification .qual-content .qual-scroll .qual-item.data-v-7b3dceb8:active {
  opacity: 0.9;
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.bottom-bar.data-v-7b3dceb8 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.08);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}
.bottom-bar .contact.data-v-7b3dceb8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 32rpx;
  position: relative;
}
.bottom-bar .contact.data-v-7b3dceb8::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 1rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.1);
}
.bottom-bar .contact text.data-v-7b3dceb8 {
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}
.bottom-bar .order-btn.data-v-7b3dceb8 {
  flex: 1;
  height: 88rpx;
  margin-left: 32rpx;
  background: linear-gradient(135deg, #8cd548 0%, #7bc438 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(140, 213, 72, 0.25);
}
.bottom-bar .order-btn .cart-icon.data-v-7b3dceb8 {
  margin-right: 8rpx;
}
.bottom-bar .order-btn text.data-v-7b3dceb8 {
  font-size: 32rpx;
  color: #fff;
  font-weight: 500;
}
.bottom-bar .order-btn.data-v-7b3dceb8:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(140, 213, 72, 0.2);
}

