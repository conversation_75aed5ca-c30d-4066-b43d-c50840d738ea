{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/store/detail.vue?5445", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/store/detail.vue?f4ef", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/store/detail.vue?4ef1", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/store/detail.vue?cef7", "uni-app:///pages/store/detail.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/store/detail.vue?f6c5", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/store/detail.vue?3030"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "navigationBarTitleText", "navigationBarBackgroundColor", "navigationBarTextStyle", "data", "storeInfo", "name", "image", "rating", "monthlySales", "businessHours", "notice", "address", "distance", "phone", "qualifications", "merchantId", "statusBarHeight", "navbarHeight", "isOpen", "currentTime", "onLoad", "methods", "initNavBarHeight", "handleBack", "uni", "delta", "getMerchantDetail", "res", "console", "merchantData", "title", "icon", "openMap", "latitude", "longitude", "success", "fail", "contactStore", "phoneNumber", "goToMenu", "url", "checkBusinessStatus", "previewImage", "current", "urls", "indicator", "loop"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgKnrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;EACAC;EAEAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;;IAEA;IACA;;IAEA;IACA;MACA;IACA;MACA;MACA;IACA;;IAEA;IACA;EACA;;EAEAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACAC;QACAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAC;;gBAEA;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACAE,yBAEA;gBACA;kBACAxB;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC,oDACAe;oBAAA;sBACAxB;sBACAC;oBACA;kBAAA,KACA;gBACA;gBAEAsB;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAJ;kBACAM;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAC;MACA;MACA;MACA;MACA;;MAEAR;QACAS;QACAC;QACA7B;QACAM;QACAwB;UACAP;QACA;QACAQ;UACAR;UACAJ;YACAM;YACAC;UACA;QACA;MACA;IACA;IAEAM;MACAb;QACAc;MACA;IACA;IAEAC;MACAf;QACAgB;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;YACA;YACA;;YAEA;YACA;YACA;;YAEA;YACA;YACA;UACA;QACA;;QAEA;QACA;MACA;QACAb;QACA;QACA;MACA;IACA;IAEAc;MACA;QAAA;MAAA;MACAlB;QACAmB;QACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3VA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/store/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/store/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=7b3dceb8&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=7b3dceb8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7b3dceb8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/store/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=7b3dceb8&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = Math.floor(_vm.storeInfo.rating)\n  var g1 = Math.floor(_vm.storeInfo.rating)\n  var g2 =\n    _vm.storeInfo.qualifications && _vm.storeInfo.qualifications.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <!-- 自定义导航栏 -->\n    <view class=\"custom-navbar\" :style=\"{ paddingTop: statusBarHeight + 'px' }\">\n      <view class=\"navbar-content\">\n        <view class=\"back-btn\" @tap=\"handleBack\">\n          <u-icon name=\"arrow-left\" color=\"#333\" size=\"20\"></u-icon>\n        </view>\n        <text class=\"page-title\">商户详情</text>\n        <view class=\"placeholder\"></view>\n      </view>\n    </view>\n    \n    <!-- 占位元素，防止内容被导航栏遮挡 -->\n    <view class=\"navbar-placeholder\" :style=\"{ height: navbarHeight + 'px' }\"></view>\n\n    <!-- 商户信息头部 -->\n    <view class=\"store-header\">\n      <view class=\"store-info-wrap\">\n        <image \n          class=\"store-logo\" \n          :src=\"storeInfo.image || '/static/store/default.png'\" \n          mode=\"aspectFill\" \n        />\n        <view class=\"store-brief\">\n          <text class=\"store-name\">{{storeInfo.name}}</text>\n          <view class=\"store-stats\">\n            <view class=\"rating-wrap\">\n              <text class=\"rating\">{{storeInfo.rating}}分</text>\n              <view class=\"rating-stars\">\n                <u-icon \n                  v-for=\"i in 5\" \n                  :key=\"i\"\n                  :name=\"i <= Math.floor(storeInfo.rating) ? 'star-fill' : 'star'\"\n                  :color=\"i <= Math.floor(storeInfo.rating) ? '#ff9900' : '#ddd'\"\n                  size=\"24\"\n                ></u-icon>\n              </view>\n            </view>\n            <text class=\"divider\">|</text>\n            <text class=\"sales\">月售{{storeInfo.monthlySales}}单</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 商户信息 -->\n    <view class=\"store-info\">\n      <!-- 营业信息卡片 -->\n      <view class=\"info-card business-card\">\n        <view class=\"card-header\">\n          <u-icon name=\"info-circle\" size=\"32\" color=\"#333\"></u-icon>\n          <text class=\"title\">营业信息</text>\n        </view>\n        <view class=\"business-info\">\n          <view class=\"time-item\">\n            <view class=\"time-header\">\n              <view class=\"header-left\">\n                <u-icon \n                  name=\"clock\" \n                  size=\"28\" \n                  color=\"#8cd548\"\n                ></u-icon>\n                <text class=\"time-label\">营业时间</text>\n              </view>\n              <view \n                class=\"status-tag\" \n                :class=\"{ closed: !isOpen }\"\n              >\n                <u-icon \n                  :name=\"isOpen ? 'checkmark-circle' : 'close-circle'\" \n                  size=\"24\" \n                  :color=\"isOpen ? '#8cd548' : '#ff6b6b'\"\n                ></u-icon>\n                <text>{{ isOpen ? '营业中' : '已打烊' }}</text>\n              </view>\n            </view>\n            <view class=\"time-periods\">\n              <text class=\"time-value\">{{ storeInfo.businessHours }}</text>\n              <text class=\"time-tips\">*最后下单时间为打烊前30分钟</text>\n            </view>\n          </view>\n          <view class=\"notice-item\" v-if=\"storeInfo.notice\">\n            <u-icon name=\"volume\" size=\"28\" color=\"#ff9900\"></u-icon>\n            <view class=\"notice-content\">\n              <text class=\"notice-label\">商家公告</text>\n              <text class=\"notice-text\">{{storeInfo.notice}}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 地址信息卡片 -->\n      <view class=\"info-card address-info\">\n        <view class=\"card-header\">\n          <u-icon name=\"map-fill\" size=\"32\" color=\"#ff6b6b\"></u-icon>\n          <text class=\"title\">商家地址</text>\n        </view>\n        <view class=\"address-content\">\n          <view class=\"location-info\">\n            <view class=\"address-header\">\n              <view class=\"distance-tag\">\n                <u-icon name=\"map\" size=\"24\" color=\"#ff6b6b\"></u-icon>\n                <text>距您{{storeInfo.distance}}km</text>\n              </view>\n              <view class=\"nav-btn\" @tap=\"openMap\">\n                <u-icon size=\"28\" color=\"#ff6b6b\"></u-icon>\n                <text>去导航</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"address-detail\">\n            <u-icon name=\"home\" size=\"28\" color=\"#666\"></u-icon>\n            <text class=\"address-text\">{{storeInfo.address}}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 资质信息卡片 -->\n      <view class=\"info-card qualification\" v-if=\"storeInfo.qualifications && storeInfo.qualifications.length > 0\">\n        <view class=\"card-header\">\n          <u-icon name=\"file-text\" size=\"32\" color=\"#333\"></u-icon>\n          <text class=\"title\">商家资质</text>\n        </view>\n        <view class=\"qual-content\">\n          <scroll-view \n            class=\"qual-scroll\" \n            scroll-x \n            show-scrollbar=\"false\"\n            enhanced\n          >\n            <view \n              class=\"qual-item\" \n              v-for=\"(item, index) in storeInfo.qualifications\" \n              :key=\"index\" \n              @tap=\"previewImage(index)\"\n            >\n              <image class=\"qual-image\" :src=\"item.image\" mode=\"aspectFill\" />\n              <text class=\"qual-tips\">点击查看</text>\n            </view>\n          </scroll-view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部按钮 -->\n    <view class=\"bottom-bar\">\n      <view class=\"contact\" @tap=\"contactStore\">\n        <u-icon name=\"phone-fill\" size=\"32\" color=\"#666\"></u-icon>\n        <text>联系商家</text>\n      </view>\n      <view class=\"order-btn\" @tap=\"goToMenu\">\n        <u-icon name=\"shopping-cart\" size=\"32\" color=\"#fff\" class=\"cart-icon\"></u-icon>\n        <text>去点餐</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getMerchantInfo } from '@/api/merchant'\n\nexport default {\n  navigationBarTitleText: '商户详情',\n  navigationBarBackgroundColor: '#ffffff',\n  navigationBarTextStyle: 'black',\n\n  data() {\n    return {\n      storeInfo: {\n        name: '',\n        image: '',\n        rating: 5.0,\n        monthlySales: 0,\n        businessHours: '',\n        notice: '',\n        address: '',\n        distance: 0,\n        phone: '',\n        qualifications: []\n      },\n      merchantId: 0,\n      statusBarHeight: 0,\n      navbarHeight: 0,\n      isOpen: true,\n      currentTime: ''\n    }\n  },\n\n  onLoad(options) {\n    // 获取传递过来的商户ID\n    this.merchantId = options.id || 0\n    \n    // 初始化导航栏高度\n    this.initNavBarHeight()\n    \n    // 获取商户信息\n    if (this.merchantId) {\n      this.getMerchantDetail()\n    } else {\n      // 无商户ID时获取默认商户\n      this.getMerchantDetail()\n    }\n    \n    // 不在这里检查营业状态，而是在获取数据后检查\n    // this.checkBusinessStatus()\n  },\n\n  methods: {\n    initNavBarHeight() {\n      // 获取状态栏高度\n      const systemInfo = uni.getSystemInfoSync()\n      this.statusBarHeight = systemInfo.statusBarHeight\n      // 计算导航栏总高度 (状态栏 + 导航内容区)\n      this.navbarHeight = this.statusBarHeight + 44\n    },\n    \n    handleBack() {\n      uni.navigateBack({\n        delta: 1\n      })\n    },\n\n    async getMerchantDetail() {\n      try {\n        const res = await getMerchantInfo()\n        console.log('获取到的商户信息:', res)\n\n        // 检查API返回的数据结构\n        if (res && res.code === 1 && res.data) {\n          const merchantData = res.data\n          \n          // 从data中提取商户信息\n          this.storeInfo = {\n            name: merchantData.name || '',\n            image: merchantData.logo_image || '',\n            rating: Number(merchantData.rating) || 5.0,\n            monthlySales: merchantData.monthly_sales || 0,\n            businessHours: merchantData.business_hours || '',\n            notice: merchantData.announcement || '',\n            address: merchantData.address || '',\n            distance: 0,\n            phone: merchantData.phone || '',\n            qualifications: merchantData.qualification_images \n              ? merchantData.qualification_images.map(image => ({\n                  name: '商家资质',\n                  image: image\n                }))\n              : []\n          }\n          \n          console.log('处理后的商户信息:', this.storeInfo)\n          \n          // 获取到商户信息后再检查营业状态\n          this.checkBusinessStatus()\n        } else {\n          throw new Error('API返回数据格式错误')\n        }\n      } catch (e) {\n        console.error('获取商户详情失败:', e)\n        uni.showToast({\n          title: '获取商户详情失败',\n          icon: 'none'\n        })\n      }\n    },\n\n    openMap() {\n      // 打开地图导航\n      // 注意：这里使用的是固定坐标，实际应用中应该从API获取商户的经纬度\n      const latitude = 22.544925  // 默认纬度\n      const longitude = 114.109078  // 默认经度\n      \n      uni.openLocation({\n        latitude: latitude,\n        longitude: longitude,\n        name: this.storeInfo.name,\n        address: this.storeInfo.address,\n        success: () => {\n          console.log('打开地图成功')\n        },\n        fail: (err) => {\n          console.error('打开地图失败:', err)\n          uni.showToast({\n            title: '导航失败，请手动导航',\n            icon: 'none'\n          })\n        }\n      })\n    },\n\n    contactStore() {\n      uni.makePhoneCall({\n        phoneNumber: this.storeInfo.phone\n      })\n    },\n\n    goToMenu() {\n      uni.switchTab({\n        url: '/pages/menu/menu'\n      })\n    },\n\n    checkBusinessStatus() {\n      try {\n        // 获取当前时间\n        const now = new Date()\n        const hours = now.getHours()\n        const minutes = now.getMinutes()\n        const currentTime = hours * 100 + minutes // 例如：10:30 => 1030\n        \n        // 解析营业时间格式（假设格式为 \"HH:MM-HH:MM\"）\n        if (this.storeInfo.businessHours) {\n          const timeRange = this.storeInfo.businessHours.split('-')\n          if (timeRange.length === 2) {\n            const startTimeStr = timeRange[0].trim()\n            const endTimeStr = timeRange[1].trim()\n            \n            // 转换为数字比较（例如：\"10:00\" => 1000）\n            const startTime = parseInt(startTimeStr.replace(':', ''))\n            const endTime = parseInt(endTimeStr.replace(':', ''))\n            \n            // 判断当前时间是否在营业时间范围内\n            this.isOpen = currentTime >= startTime && currentTime <= endTime\n            return\n          }\n        }\n        \n        // 如果解析失败，使用默认值\n        this.isOpen = hours >= 9 && hours < 22\n      } catch (e) {\n        console.error('检查营业状态失败:', e)\n        // 出错时使用默认值\n        this.isOpen = true\n      }\n    },\n\n    previewImage(index) {\n      const images = this.storeInfo.qualifications.map(item => item.image)\n      uni.previewImage({\n        current: index,\n        urls: images,\n        indicator: 'number',\n        loop: true\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page {\n  min-height: 100vh;\n  background: #f8f8f8;\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));\n}\n\n.custom-navbar {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 999;\n  background: #fff;\n  \n  .navbar-content {\n    height: 44px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 16rpx;\n    position: relative;\n    \n    .back-btn {\n      width: 88rpx;\n      height: 44px;\n      display: flex;\n      align-items: center;\n      \n      &:active {\n        opacity: 0.7;\n      }\n    }\n    \n    .page-title {\n      position: absolute;\n      left: 50%;\n      top: 50%;\n      transform: translate(-50%, -50%);\n      font-size: 32rpx;\n      font-weight: 500;\n      color: #333;\n    }\n    \n    .placeholder {\n      width: 88rpx;\n      height: 44px;\n    }\n  }\n}\n\n.navbar-placeholder {\n  width: 100%;\n}\n\n.store-header {\n  background: #fff;\n  padding: 24rpx;\n  margin-bottom: 24rpx;\n  \n  .store-info-wrap {\n    display: flex;\n    align-items: center;\n    padding: 0 4rpx;\n    \n    .store-logo {\n      width: 140rpx;\n      height: 140rpx;\n      border-radius: 12rpx;\n      border: 1rpx solid rgba(0, 0, 0, 0.05);\n      background: #f8f8f8;\n    }\n    \n    .store-brief {\n      flex: 1;\n      margin-left: 24rpx;\n      \n      .store-name {\n        font-size: 36rpx;\n        color: #333;\n        font-weight: 600;\n        margin-bottom: 12rpx;\n      }\n      \n      .store-stats {\n        display: flex;\n        align-items: center;\n        \n        .rating-wrap {\n          display: flex;\n          align-items: center;\n          \n          .rating {\n            font-size: 28rpx;\n            color: #ff9900;\n            font-weight: 600;\n            margin-right: 8rpx;\n          }\n          \n          .rating-stars {\n            display: flex;\n            gap: 4rpx;\n          }\n        }\n        \n        .divider {\n          margin: 0 16rpx;\n          font-size: 24rpx;\n          color: #ddd;\n        }\n        \n        .sales {\n          font-size: 26rpx;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n\n.store-info {\n  padding: 24rpx;\n  \n  .info-card {\n    background: #fff;\n    border-radius: 16rpx;\n    padding: 24rpx;\n    margin-bottom: 24rpx;\n    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\n    border: 1rpx solid rgba(0, 0, 0, 0.02);\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .business-card {\n    .business-info {\n      .time-item {\n        display: flex;\n        flex-direction: column;\n        padding: 24rpx;\n        background: linear-gradient(to bottom, #f9f9f9, #f5f5f5);\n        border-radius: 16rpx;\n        margin-bottom: 24rpx;\n        border: 1rpx solid rgba(0, 0, 0, 0.05);\n        \n        .time-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16rpx;\n          \n          .header-left {\n            display: flex;\n            align-items: center;\n            \n            .u-icon {\n              margin-right: 8rpx;\n            }\n            \n            .time-label {\n              font-size: 28rpx;\n              color: #333;\n              font-weight: 600;\n            }\n          }\n          \n          .status-tag {\n            display: flex;\n            align-items: center;\n            padding: 6rpx 16rpx;\n            background: rgba(140, 213, 72, 0.1);\n            border-radius: 24rpx;\n            \n            .u-icon {\n              margin-right: 4rpx;\n            }\n            \n            text {\n              font-size: 24rpx;\n              color: #8cd548;\n              font-weight: 500;\n            }\n            \n            &.closed {\n              background: rgba(255, 107, 107, 0.1);\n              \n              text {\n                color: #ff6b6b;\n              }\n            }\n          }\n        }\n        \n        .time-periods {\n          padding: 16rpx;\n          background: #fff;\n          border-radius: 12rpx;\n          border: 1rpx solid rgba(0, 0, 0, 0.03);\n          \n          .time-value {\n            display: block;\n            font-size: 30rpx;\n            color: #333;\n            font-weight: 500;\n            margin-bottom: 8rpx;\n          }\n          \n          .time-tips {\n            display: block;\n            font-size: 24rpx;\n            color: #999;\n            font-style: italic;\n          }\n        }\n      }\n\n      .notice-item {\n        margin-top: 16rpx;\n        padding: 20rpx;\n        background: rgba(255, 153, 0, 0.05);\n        border-radius: 12rpx;\n        display: flex;\n        align-items: flex-start;\n        \n        .u-icon {\n          margin-top: 4rpx;\n          flex-shrink: 0;\n          margin-right: 12rpx;\n        }\n        \n        .notice-content {\n          flex: 1;\n          \n          .notice-label {\n            display: block;\n            font-size: 24rpx;\n            color: #ff9900;\n            font-weight: 500;\n            margin-bottom: 8rpx;\n          }\n          \n          .notice-text {\n            display: block;\n            font-size: 26rpx;\n            color: #666;\n            line-height: 1.6;\n            display: -webkit-box;\n            -webkit-line-clamp: 3;\n            -webkit-box-orient: vertical;\n            overflow: hidden;\n          }\n        }\n        \n        &:active {\n          background: rgba(255, 153, 0, 0.08);\n        }\n      }\n    }\n  }\n\n  .card-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20rpx;\n    padding: 0 4rpx;\n    \n    .title {\n      font-size: 28rpx;\n      color: #333;\n      font-weight: 600;\n      margin-left: 12rpx;\n    }\n  }\n  \n  .address-info {\n    .address-content {\n      background: #f9f9f9;\n      border-radius: 12rpx;\n      padding: 24rpx;\n      \n      .location-info {\n        .address-header {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          margin-bottom: 20rpx;\n        }\n        .distance-tag {\n          display: inline-flex;\n          align-items: center;\n          background: rgba(255, 107, 107, 0.1);\n          padding: 6rpx 16rpx;\n          border-radius: 24rpx;\n          .u-icon {\n            margin-right: 6rpx;\n          }\n          text {\n            font-size: 24rpx;\n            color: #ff6b6b;\n            font-weight: 500;\n          }\n        }\n        .nav-btn {\n          display: flex;\n          align-items: center;\n          padding: 6rpx 16rpx;\n          background: #fff;\n          border-radius: 24rpx;\n          border: 1rpx solid rgba(255, 107, 107, 0.2);\n          .u-icon {\n            margin-right: 4rpx;\n          }\n          text {\n            font-size: 24rpx;\n            color: #ff6b6b;\n            font-weight: 500;\n          }\n          &:active {\n            opacity: 0.8;\n            transform: scale(0.98);\n          }\n        }\n      }\n      .address-detail {\n        display: flex;\n        align-items: flex-start;\n        background: #fff;\n        padding: 16rpx;\n        border-radius: 12rpx;\n        .u-icon {\n          margin-right: 8rpx;\n          margin-top: 4rpx;\n        }\n        .address-text {\n          flex: 1;\n          font-size: 28rpx;\n          color: #333;\n          line-height: 1.6;\n          display: -webkit-box;\n          -webkit-line-clamp: 2;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n        }\n      }\n    }\n  }\n  \n  .qualification {\n    .qual-content {\n      position: relative;\n      .qual-scroll {\n        width: 100%;\n        white-space: nowrap;\n        padding: 12rpx 0;\n        \n        .qual-item {\n          display: inline-block;\n          width: 240rpx;\n          margin-right: 20rpx;\n          position: relative;\n          \n          &:last-child {\n            margin-right: 0;\n          }\n          \n          .qual-image {\n            width: 100%;\n            height: 320rpx;\n            border-radius: 12rpx;\n            background: #f8f8f8;\n          }\n          \n          .qual-tips {\n            position: absolute;\n            left: 50%;\n            bottom: 16rpx;\n            transform: translateX(-50%);\n            font-size: 24rpx;\n            color: #fff;\n            background: rgba(0, 0, 0, 0.6);\n            padding: 4rpx 16rpx;\n            border-radius: 20rpx;\n            white-space: nowrap;\n          }\n          \n          &:active {\n            opacity: 0.9;\n            transform: scale(0.98);\n          }\n        }\n      }\n    }\n  }\n}\n\n.bottom-bar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(20px);\n  padding: 16rpx 24rpx;\n  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n  display: flex;\n  align-items: center;\n  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.08);\n  border-top: 1rpx solid rgba(0, 0, 0, 0.05);\n  \n  .contact {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 12rpx 32rpx;\n    position: relative;\n    \n    &::after {\n      content: '';\n      position: absolute;\n      right: 0;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 1rpx;\n      height: 32rpx;\n      background: rgba(0, 0, 0, 0.1);\n    }\n    \n    text {\n      font-size: 24rpx;\n      color: #666;\n      margin-top: 4rpx;\n    }\n  }\n  \n  .order-btn {\n    flex: 1;\n    height: 88rpx;\n    margin-left: 32rpx;\n    background: linear-gradient(135deg, #8cd548 0%, #7bc438 100%);\n    border-radius: 44rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: 0 6rpx 16rpx rgba(140, 213, 72, 0.25);\n    \n    .cart-icon {\n      margin-right: 8rpx;\n    }\n    \n    text {\n      font-size: 32rpx;\n      color: #fff;\n      font-weight: 500;\n    }\n    \n    &:active {\n      transform: scale(0.98);\n      box-shadow: 0 2rpx 8rpx rgba(140, 213, 72, 0.2);\n    }\n  }\n}\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7b3dceb8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=7b3dceb8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309664\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}