{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/invite/poster.vue?b7e2", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/invite/poster.vue?b710", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/invite/poster.vue?329f", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/invite/poster.vue?3c09", "uni-app:///pages/invite/poster.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/invite/poster.vue?86a0", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/invite/poster.vue?3827"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "onLoad", "methods", "handleBack", "uni", "saveImage", "title", "setTimeout", "icon", "shareToFriend", "withShareTicket", "menus"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgDnrB;EACAC;IACA;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IAEA;IACAC;MACAD;QACAE;MACA;MAEAC;QACAH;QACAA;UACAE;UACAE;QACA;MACA;IACA;IAEA;IACAC;MACAL;QACAM;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/invite/poster.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/invite/poster.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./poster.vue?vue&type=template&id=28b3842c&scoped=true&\"\nvar renderjs\nimport script from \"./poster.vue?vue&type=script&lang=js&\"\nexport * from \"./poster.vue?vue&type=script&lang=js&\"\nimport style0 from \"./poster.vue?vue&type=style&index=0&id=28b3842c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"28b3842c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/invite/poster.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=template&id=28b3842c&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">邀请好友</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 海报内容 -->\r\n    <view class=\"poster-content\">\r\n      <view class=\"poster-card\">\r\n        <image class=\"user-avatar\" :src=\"userInfo.avatarUrl || '/static/my/default-avatar.png'\" mode=\"aspectFill\" />\r\n        <text class=\"user-name\">{{userInfo.nickName || '用户昵称'}}</text>\r\n        <text class=\"invite-text\">诚邀您体验美食</text>\r\n        <image class=\"qr-code\" src=\"/static/invite/qrcode.png\" mode=\"aspectFill\" />\r\n        <text class=\"tip\">扫码立即注册</text>\r\n      </view>\r\n\r\n      <!-- 分享按钮组 -->\r\n      <view class=\"share-btns\">\r\n        <view class=\"btn-item\" @tap=\"saveImage\">\r\n          <u-icon name=\"download\" size=\"40\" color=\"#333\"></u-icon>\r\n          <text>保存图片</text>\r\n        </view>\r\n        <view class=\"btn-item\" @tap=\"shareToFriend\">\r\n          <u-icon name=\"weixin-fill\" size=\"40\" color=\"#333\"></u-icon>\r\n          <text>分享好友</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 邀请说明 -->\r\n      <view class=\"invite-rules\">\r\n        <view class=\"rule-title\">活动规则</view>\r\n        <view class=\"rule-item\">1. 邀请好友注册并完成首单</view>\r\n        <view class=\"rule-item\">2. 好友获得10元新人券</view>\r\n        <view class=\"rule-item\">3. 您获得5元现金奖励</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      userInfo: {}\r\n    }\r\n  },\r\n  \r\n  onLoad() {\r\n    this.userInfo = uni.getStorageSync('userInfo') || {}\r\n  },\r\n  \r\n  methods: {\r\n    handleBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 保存海报图片\r\n    saveImage() {\r\n      uni.showLoading({\r\n        title: '保存中...'\r\n      })\r\n      \r\n      setTimeout(() => {\r\n        uni.hideLoading()\r\n        uni.showToast({\r\n          title: '保存成功',\r\n          icon: 'none'\r\n        })\r\n      }, 1500)\r\n    },\r\n    \r\n    // 分享给好友\r\n    shareToFriend() {\r\n      uni.showShareMenu({\r\n        withShareTicket: true,\r\n        menus: ['shareAppMessage', 'shareTimeline']\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  background: #fff;\r\n  padding-top: 88rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .back-icon {\r\n      position: absolute;\r\n      left: 30rpx;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n\r\n.poster-content {\r\n  padding: 30rpx;\r\n  \r\n  .poster-card {\r\n    background: #fff;\r\n    border-radius: 20rpx;\r\n    padding: 40rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n    \r\n    .user-avatar {\r\n      width: 120rpx;\r\n      height: 120rpx;\r\n      border-radius: 50%;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .user-name {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n      margin-bottom: 12rpx;\r\n    }\r\n    \r\n    .invite-text {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      margin-bottom: 40rpx;\r\n    }\r\n    \r\n    .qr-code {\r\n      width: 300rpx;\r\n      height: 300rpx;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .tip {\r\n      font-size: 26rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n  \r\n  .share-btns {\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 40rpx;\r\n    \r\n    .btn-item {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      margin: 0 40rpx;\r\n      \r\n      text {\r\n        font-size: 26rpx;\r\n        color: #333;\r\n        margin-top: 12rpx;\r\n      }\r\n      \r\n      &:active {\r\n        opacity: 0.8;\r\n      }\r\n    }\r\n  }\r\n  \r\n  .invite-rules {\r\n    margin-top: 60rpx;\r\n    background: #fff;\r\n    border-radius: 20rpx;\r\n    padding: 30rpx;\r\n    \r\n    .rule-title {\r\n      font-size: 30rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n      margin-bottom: 20rpx;\r\n    }\r\n    \r\n    .rule-item {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n      line-height: 1.8;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=style&index=0&id=28b3842c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./poster.vue?vue&type=style&index=0&id=28b3842c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309764\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}