{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/address.vue?8b1b", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/address.vue?dbdf", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/address.vue?b6f7", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/address.vue?eeb8", "uni-app:///pages/address/address.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/address.vue?8535", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/address.vue?0710"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "addressList", "loading", "onShow", "methods", "handleBack", "uni", "loadAddressList", "title", "res", "console", "filter", "map", "item", "id", "name", "phone", "province", "city", "district", "address", "tag", "status", "icon", "addAddress", "url", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "safeId", "selectAddress", "tempOrderData", "deleteAddress", "content", "success", "handleSetDefaultClick", "handleDeleteClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrFA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoEprB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAD;kBACAE;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAC;gBAEA;kBACA;kBACA,6BACAC;oBAAA;kBAAA;kBAAA,CACAA;oBAAA;kBAAA;kBAAA,CACAC;oBACA;oBACA;sBACAC;oBACA;oBACA;oBACA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBACA;kBAEAZ;gBACA;kBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAJ;kBACAE;kBACAe;gBACA;cAAA;gBAAA;gBAEA;gBACAjB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAkB;MACAlB;QACAmB;MACA;IACA;IAEAC;MACAhB;MACAA;MAEA;QACAA;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;QACAb;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAb;QACAJ;UACAE;UACAe;QACA;QACA;MACA;MAEAb;MACAJ;QACAmB;MACA;IACA;IAEAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjB;gBACA;gBACAkB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAlB;gBACAJ;kBACAE;kBACAe;gBACA;gBAAA;cAAA;gBAAA;gBAKAjB;kBACAE;gBACA;gBAEAE;kBAAAI;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAL;gBAEAC;gBAEA;kBACAJ;oBACAE;oBACAe;kBACA;;kBAEA;kBACA;gBACA;kBACAjB;oBACAE;oBACAe;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAb;gBACAJ;kBACAE;kBACAe;gBACA;cAAA;gBAAA;gBAEAjB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MACAnB;MACAA;MAEA;QACAA;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;QACAb;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;MACAO;MACAxB;;MAEA;MACAA;MAEAI;MACA;MACAJ;IACA;IAEAyB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACArB;gBACA;gBACAkB;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAlB;gBACAJ;kBACAE;kBACAe;gBACA;gBAAA;cAAA;gBAIAjB;kBACAE;kBACAwB;kBACAC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA,KACAxB;gCAAA;gCAAA;8BAAA;8BAAA;8BAEAH;gCACAE;8BACA;8BAEAE;gCAAAI;8BAAA;8BAAA;8BAAA,OACA;4BAAA;8BAAAL;8BAEAC;8BAEA;gCACAJ;kCACAE;kCACAe;gCACA;;gCAEA;gCACA;8BACA;gCACAjB;kCACAE;kCACAe;gCACA;8BACA;8BAAA;8BAAA;4BAAA;8BAAA;8BAAA;8BAEAb;8BACAJ;gCACAE;gCACAe;8BACA;4BAAA;8BAAA;8BAEAjB;8BAAA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CAGA;oBAAA;sBAAA;oBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA4B;MACAxB;MACAA;MAEA;QACAA;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;QACAb;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;QACAb;QACA;MACA;;MAEA;MACA;MACA;QACAA;QACAJ;UACAE;UACAe;QACA;QACA;MACA;MAEAb;MACA;IACA;IAEA;IACAyB;MACAzB;MACAA;MAEA;QACAA;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;QACAb;QACAJ;UACAE;UACAe;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAb;QACAJ;UACAE;UACAe;QACA;QACA;MACA;MAEAb;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxZA;AAAA;AAAA;AAAA;AAAmxC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAvyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/address/address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/address/address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./address.vue?vue&type=template&id=db675620&scoped=true&\"\nvar renderjs\nimport script from \"./address.vue?vue&type=script&lang=js&\"\nexport * from \"./address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./address.vue?vue&type=style&index=0&id=db675620&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"db675620\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/address/address.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=template&id=db675620&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.addressList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var args = [],\n        len = arguments.length - 2\n      while (len-- > 0) args[len] = arguments[len + 2]\n\n      var _temp = args[args.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      return _vm.selectAddress($event, item)\n    }\n    _vm.e1 = function ($event, item) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        item = _temp4.item\n      var _temp3, _temp4\n      $event.stopPropagation()\n      return (function ($event) {\n        return _vm.handleSetDefaultClick($event, item)\n      })($event)\n    }\n    _vm.e2 = function ($event, item) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        item = _temp6.item\n      var _temp5, _temp6\n      $event.stopPropagation()\n      return (function ($event) {\n        return _vm.editAddress($event, item)\n      })($event)\n    }\n    _vm.e3 = function ($event, item) {\n      var _temp7 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        item = _temp8.item\n      var _temp7, _temp8\n      $event.stopPropagation()\n      return (function ($event) {\n        return _vm.handleDeleteClick($event, item)\n      })($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <!-- 顶部导航 -->\n    <view class=\"header\">\n      <view class=\"nav-bar\">\n        <image \n          class=\"back-icon\" \n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \n          @tap=\"handleBack\"\n        />\n        <text class=\"title\">收货地址</text>\n      </view>\n    </view>\n\n    <!-- 地址列表 -->\n    <scroll-view class=\"address-list\" scroll-y>\n      <view\n        class=\"address-item\"\n        v-for=\"(item, index) in addressList\"\n        :key=\"item.id ? item.id : index\"\n        @tap=\"($event) => selectAddress($event, item)\"\n        v-if=\"item\"\n      >\n        <view class=\"info\">\n          <view class=\"user-info\">\n            <text class=\"name\">{{item.name || ''}}</text>\n            <text class=\"phone\">{{item.phone || ''}}</text>\n            <text class=\"tag\" v-if=\"item.tag\">{{item.tag}}</text>\n          </view>\n          <view class=\"address\">{{item.province || ''}} {{item.city || ''}} {{item.district || ''}} {{item.address || ''}}</view>\n        </view>\n        <view class=\"actions\">\n          <view class=\"default\" @tap.stop=\"($event) => handleSetDefaultClick($event, item)\">\n            <view class=\"radio\" :class=\"{ active: item.status == '1' }\">\n              <view class=\"inner\" v-if=\"item.status == '1'\"></view>\n            </view>\n            <text>设为默认</text>\n          </view>\n          <view class=\"edit-delete\">\n            <view class=\"edit\" @tap.stop=\"($event) => editAddress($event, item)\">\n              <u-icon name=\"edit-pen\" size=\"32\" color=\"#999\"></u-icon>\n              <text>编辑</text>\n            </view>\n            <view class=\"delete\" @tap.stop=\"($event) => handleDeleteClick($event, item)\">\n              <u-icon name=\"trash\" size=\"32\" color=\"#999\"></u-icon>\n              <text>删除</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 空状态 -->\n    <empty-state \n      v-if=\"addressList.length === 0\"\n      text=\"暂无收货地址\"\n      image=\"/static/order/e186e04e8774da64b58c96a8bb479840.png\"\n      :showAction=\"false\"\n    ></empty-state>\n\n    <!-- 底部添加按钮 -->\n    <view class=\"add-btn\" @tap=\"addAddress\">\n      <text>新增收货地址</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getAddressList, setDefaultAddress, deleteAddress } from '@/api/address.js'\n\nexport default {\n  data() {\n    return {\n      addressList: [],\n      loading: false\n    }\n  },\n  \n  onShow() {\n    this.loadAddressList()\n  },\n  \n  methods: {\n    handleBack() {\n      uni.navigateBack()\n    },\n    \n    async loadAddressList() {\n      try {\n        this.loading = true\n        uni.showLoading({\n          title: '加载中...'\n        });\n        \n        const res = await getAddressList()\n        console.log('获取地址列表原始响应:', JSON.stringify(res));\n        \n        if(res.code === 1 && Array.isArray(res.data)) {\n          // 增强数据过滤和验证\n          this.addressList = res.data\n            .filter(item => item && typeof item === 'object')  // 确保是对象\n            .filter(item => item.id)  // 确保有id\n            .map(item => {\n              // 确保status字段的一致性\n              if(item.status === undefined && item.is_default !== undefined) {\n                item.status = item.is_default ? '1' : '0';\n              }\n              // 确保所有必要字段都有默认值\n              return {\n                id: item.id || '',\n                name: item.name || '',\n                phone: item.phone || '',\n                province: item.province || '',\n                city: item.city || '',\n                district: item.district || '',\n                address: item.address || '',\n                tag: item.tag || '',\n                status: item.status || '0'\n              };\n            });\n          \n          console.log('过滤和标准化后的地址列表:', JSON.stringify(this.addressList));\n        } else {\n          this.addressList = []\n          console.error('获取地址列表失败:', res.msg || '未知错误')\n        }\n      } catch(e) {\n        console.error('获取地址列表异常:', e)\n        uni.showToast({\n          title: '获取地址列表失败',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n        uni.hideLoading()\n      }\n    },\n    \n    addAddress() {\n      uni.navigateTo({\n        url: '/pages/address/edit'\n      })\n    },\n    \n    editAddress(event, item) {\n      console.log('编辑地址点击 - 事件:', event.type);\n      console.log('编辑地址点击 - 完整item数据:', JSON.stringify(item));\n      \n      if (!item || !item.id) {\n        console.error('编辑地址失败: 无效的地址或ID');\n        uni.showToast({\n          title: '操作失败: 无效的地址',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 防止事件冒泡引起的参数错误\n      if (typeof item !== 'object') {\n        console.error('编辑地址失败: 参数不是对象');\n        uni.showToast({\n          title: '操作失败: 参数错误',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 确保id参数是字符串类型\n      const safeId = String(item.id || '');\n      if (!safeId) {\n        console.error('编辑地址失败: ID转换后为空');\n        uni.showToast({\n          title: '操作失败: 无效的地址ID',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      console.log('准备跳转到编辑页面, ID:', safeId);\n      uni.navigateTo({\n        url: `/pages/address/edit?id=${safeId}`\n      })\n    },\n    \n    async setDefault(id) {\n      console.log('准备设置默认地址，ID:', id);\n      // 再次验证ID，确保类型安全\n      const safeId = String(id || '');\n      if (!safeId) {\n        console.error('设置默认地址失败: ID为空');\n        uni.showToast({\n          title: '操作失败: 无效的地址ID',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      try {\n        uni.showLoading({\n          title: '设置中...'\n        });\n        \n        console.log('调用setDefaultAddress API，参数:', { id: safeId });\n        const res = await setDefaultAddress(safeId);\n        \n        console.log('设置默认地址结果:', res);\n        \n        if(res.code === 1) {\n          uni.showToast({\n            title: '设置成功',\n            icon: 'none'\n          });\n          \n          // 重新加载地址列表\n          this.loadAddressList();\n        } else {\n          uni.showToast({\n            title: res.msg || '设置失败',\n            icon: 'none'\n          });\n        }\n      } catch(e) {\n        console.error('设置默认地址异常:', JSON.stringify(e));\n        uni.showToast({\n          title: '设置失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n    \n    selectAddress(event, address) {\n      console.log('选择地址点击 - 事件:', event.type);\n      console.log('选择地址点击 - 完整address数据:', JSON.stringify(address));\n      \n      if (!address || !address.id) {\n        console.error('选择地址失败: 无效的地址或ID');\n        uni.showToast({\n          title: '操作失败: 无效的地址',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 防止事件冒泡引起的参数错误\n      if (typeof address !== 'object') {\n        console.error('选择地址失败: 参数不是对象');\n        uni.showToast({\n          title: '操作失败: 参数错误',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 保存选中的地址到缓存\n      const tempOrderData = uni.getStorageSync('tempOrderData') || {};\n      tempOrderData.selectedAddress = address;\n      uni.setStorageSync('tempOrderData', tempOrderData);\n      \n      // 触发事件，将选中的地址传递回订单页面\n      uni.$emit('addressSelected', address);\n      \n      console.log('地址选择完成，准备返回上一页');\n      // 返回上一页\n      uni.navigateBack();\n    },\n    \n    async deleteAddress(id) {\n      console.log('准备删除地址，ID:', id);\n      // 再次验证ID，确保类型安全\n      const safeId = String(id || '');\n      if (!safeId) {\n        console.error('删除地址失败: ID为空');\n        uni.showToast({\n          title: '操作失败: 无效的地址ID',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      uni.showModal({\n        title: '提示',\n        content: '确定要删除该地址吗？',\n        success: async (res) => {\n          if(res.confirm) {\n            try {\n              uni.showLoading({\n                title: '删除中...'\n              });\n              \n              console.log('调用deleteAddress API，参数:', { id: safeId });\n              const res = await deleteAddress(safeId);\n              \n              console.log('删除地址结果:', res);\n              \n              if(res.code === 1) {\n                uni.showToast({\n                  title: '删除成功',\n                  icon: 'none'\n                });\n                \n                // 重新加载地址列表\n                this.loadAddressList();\n              } else {\n                uni.showToast({\n                  title: res.msg || '删除失败',\n                  icon: 'none'\n                });\n              }\n            } catch(e) {\n              console.error('删除地址异常:', JSON.stringify(e));\n              uni.showToast({\n                title: '删除失败',\n                icon: 'none'\n              });\n            } finally {\n              uni.hideLoading();\n            }\n          }\n        }\n      });\n    },\n    \n    // 处理设置默认地址按钮点击\n    handleSetDefaultClick(event, item) {\n      console.log('设置默认地址点击 - 事件:', event.type);\n      console.log('设置默认地址点击 - 完整item数据:', JSON.stringify(item));\n      \n      if (!item || !item.id) {\n        console.error('设置默认地址失败: 无效的地址或ID');\n        uni.showToast({\n          title: '操作失败: 无效的地址',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 防止事件冒泡引起的参数错误\n      if (typeof item !== 'object') {\n        console.error('设置默认地址失败: 参数不是对象');\n        uni.showToast({\n          title: '操作失败: 参数错误',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 如果已经是默认地址，不做任何操作\n      if (item.status === '1') {\n        console.log('该地址已经是默认地址，无需设置');\n        return;\n      }\n      \n      // 确保id参数是字符串类型\n      const safeId = String(item.id || '');\n      if (!safeId) {\n        console.error('设置默认地址失败: ID转换后为空');\n        uni.showToast({\n          title: '操作失败: 无效的地址ID',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      console.log('准备调用setDefault, ID:', safeId);\n      this.setDefault(safeId);\n    },\n    \n    // 处理删除地址按钮点击\n    handleDeleteClick(event, item) {\n      console.log('删除地址点击 - 事件:', event.type);\n      console.log('删除地址点击 - 完整item数据:', JSON.stringify(item));\n      \n      if (!item || !item.id) {\n        console.error('删除地址失败: 无效的地址或ID');\n        uni.showToast({\n          title: '操作失败: 无效的地址',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 防止事件冒泡引起的参数错误\n      if (typeof item !== 'object') {\n        console.error('删除地址失败: 参数不是对象');\n        uni.showToast({\n          title: '操作失败: 参数错误',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      // 确保id参数是字符串类型\n      const safeId = String(item.id || '');\n      if (!safeId) {\n        console.error('删除地址失败: ID转换后为空');\n        uni.showToast({\n          title: '操作失败: 无效的地址ID',\n          icon: 'none'\n        });\n        return;\n      }\n      \n      console.log('准备调用deleteAddress, ID:', safeId);\n      this.deleteAddress(safeId);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.page {\n  min-height: 100vh;\n  background: #f8f8f8;\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));\n}\n\n.header {\n  background: #fff;\n  padding-top: 88rpx;\n  \n  .nav-bar {\n    position: relative;\n    height: 88rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    \n    .back-icon {\n      position: absolute;\n      left: 30rpx;\n      width: 48rpx;\n      height: 48rpx;\n      padding: 10rpx;\n    }\n    \n    .title {\n      font-size: 32rpx;\n      color: #333;\n      font-weight: bold;\n    }\n  }\n}\n\n.address-list {\n  padding: 20rpx;\n  \n  .address-item {\n    background: #fff;\n    border-radius: 16rpx;\n    padding: 30rpx;\n    margin-bottom: 20rpx;\n    \n    .info {\n      .user-info {\n        margin-bottom: 16rpx;\n        \n        .name {\n          font-size: 30rpx;\n          color: #333;\n          font-weight: 500;\n          margin-right: 20rpx;\n        }\n        \n        .phone {\n          font-size: 28rpx;\n          color: #666;\n        }\n        \n        .tag {\n          font-size: 22rpx;\n          color: #8cd548;\n          background: rgba(140, 213, 72, 0.1);\n          padding: 4rpx 12rpx;\n          border-radius: 8rpx;\n          margin-left: 16rpx;\n        }\n      }\n      \n      .address {\n        font-size: 28rpx;\n        color: #333;\n        line-height: 1.4;\n      }\n    }\n    \n    .actions {\n      margin-top: 24rpx;\n      padding-top: 24rpx;\n      border-top: 1px solid #f5f5f5;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      \n      .default {\n        display: flex;\n        align-items: center;\n        \n        .radio {\n          width: 36rpx;\n          height: 36rpx;\n          border-radius: 50%;\n          border: 2rpx solid #ddd;\n          margin-right: 8rpx;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          transition: all 0.2s;\n          \n          &.active {\n            border-color: #8cd548;\n            \n            .inner {\n              width: 24rpx;\n              height: 24rpx;\n              border-radius: 50%;\n              background: #8cd548;\n            }\n          }\n        }\n        \n        text {\n          font-size: 26rpx;\n          color: #666;\n        }\n      }\n      \n      .edit-delete {\n        display: flex;\n        align-items: center;\n        \n        .edit, .delete {\n          display: flex;\n          align-items: center;\n          margin-left: 32rpx;\n          \n          text {\n            font-size: 26rpx;\n            color: #666;\n            margin-left: 8rpx;\n          }\n        }\n      }\n    }\n  }\n}\n\n.add-btn {\n  position: fixed;\n  left: 40rpx;\n  right: 40rpx;\n  bottom: calc(40rpx + env(safe-area-inset-bottom));\n  height: 88rpx;\n  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\n  border-radius: 44rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  \n  text {\n    font-size: 32rpx;\n    color: #fff;\n    font-weight: 500;\n  }\n  \n  &:active {\n    transform: scale(0.98);\n  }\n}\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=style&index=0&id=db675620&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./address.vue?vue&type=style&index=0&id=db675620&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309908\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}