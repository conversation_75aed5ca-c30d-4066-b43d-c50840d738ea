<view class="wp_-flex-col page data-v-368aef34"><view class="wp_-flex-col header data-v-368aef34"><view data-event-opts="{{[['tap',[['goToSearch',['$event']]]]]}}" class="wp_-flex-row search-wrapper data-v-368aef34" bindtap="__e"><view class="wp_-flex-row wp_-items-center wp_-flex-1 search-input data-v-368aef34"><image class="search-icon data-v-368aef34" src="/static/menu/117dfdcfc62a8651f86aa56c1db6c74a.png"></image><text class="search-placeholder data-v-368aef34">请输入商品名称</text></view></view><view class="wp_-flex-row wp_-justify-between store-info data-v-368aef34"><view data-event-opts="{{[['tap',[['goToStoreDetail',['$event']]]]]}}" class="wp_-flex-col wp_-self-center data-v-368aef34" bindtap="__e"><view class="wp_-flex-row wp_-items-center data-v-368aef34"><text class="store-name data-v-368aef34">{{merchant.name}}</text></view><view class="wp_-mt-16 wp_-flex-row wp_-items-center data-v-368aef34"><image class="distance-icon data-v-368aef34" src="/static/menu/82a617e96551e74fc96528efc85baa41.png"></image><text class="distance-text data-v-368aef34">{{"月售"+merchant.monthly_sales+"单"}}</text></view></view><view class="dining-type data-v-368aef34"><view data-event-opts="{{[['tap',[['switchDiningType',['dine-in']]]]]}}" class="{{['dining-type-item','data-v-368aef34',(diningType==='dine-in')?'dining-type-active':'']}}" bindtap="__e"><text class="data-v-368aef34">堂食</text></view><view data-event-opts="{{[['tap',[['switchDiningType',['takeout']]]]]}}" class="{{['dining-type-item','data-v-368aef34',(diningType==='takeout')?'dining-type-active':'']}}" bindtap="__e"><text class="data-v-368aef34">外卖</text></view></view></view></view><view class="menu-content data-v-368aef34"><scroll-view class="menu-sidebar data-v-368aef34" scroll-y="{{true}}"><block wx:for="{{categories}}" wx:for-item="category" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['switchCategory',[index]]]]]}}" class="{{['data-v-368aef34','sidebar-item',currentCategory===index?'active':'']}}" bindtap="__e"><text class="data-v-368aef34">{{category.name}}</text></view></block></scroll-view><scroll-view class="product-list-container data-v-368aef34" scroll-y="{{true}}"><text class="category-title data-v-368aef34">{{categories[currentCategory].name}}</text><view class="product-list data-v-368aef34"><block wx:for="{{currentProducts}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="product-item data-v-368aef34"><image class="product-image data-v-368aef34" src="{{item.image||'/static/home/<USER>'}}"></image><view class="product-info data-v-368aef34"><text class="product-name data-v-368aef34">{{item.name}}</text><view class="product-bottom data-v-368aef34"><view class="price data-v-368aef34"><text class="price-symbol data-v-368aef34">¥</text><text class="price-value data-v-368aef34">{{item.price||'0.00'}}</text></view><view data-event-opts="{{[['tap',[['selectSpec',['$0'],[[['currentProducts','',index]]]]]]]}}" class="select-btn data-v-368aef34" bindtap="__e"><block wx:if="{{item.spec_type==='multi'}}"><text class="spec-btn data-v-368aef34">选规格</text></block><block wx:else><view class="add-btn data-v-368aef34"><image class="add-icon data-v-368aef34" src="/static/menu/add.png"></image></view></block></view></view></view></view></block></view></scroll-view></view><block wx:if="{{showSpecSelector}}"><menu-open vue-id="9ad1f540-1" product="{{selectedProduct}}" data-event-opts="{{[['^close',[['closeSpecSelector']]],['^addToCart',[['handleAddToCart']]]]}}" bind:close="__e" bind:addToCart="__e" class="data-v-368aef34" bind:__l="__l"></menu-open></block><block wx:if="{{cartTotal>0}}"><view class="cart-bar data-v-368aef34"><view class="wp_-flex-row wp_-items-center cart-content data-v-368aef34"><view data-event-opts="{{[['tap',[['openCart',['$event']]]]]}}" class="cart-left data-v-368aef34" bindtap="__e"><image class="cart-icon data-v-368aef34" src="/static/menu/cart.png"></image><view class="cart-badge data-v-368aef34">{{cartTotal}}</view></view><view class="wp_-flex-1 cart-center data-v-368aef34"><text class="price-symbol data-v-368aef34">¥</text><text class="price-value data-v-368aef34">{{$root.g0}}</text></view><view data-event-opts="{{[['tap',[['goToCheckout',['$event']]]]]}}" class="checkout-btn data-v-368aef34" bindtap="__e"><text class="btn-text data-v-368aef34">去结算</text></view></view></view></block><block wx:if="{{showCart}}"><shopping-cart vue-id="9ad1f540-2" cart-list="{{cartList}}" total-price="{{cartPrice}}" data-event-opts="{{[['^close',[['closeCart']]],['^update',[['updateCart']]]]}}" bind:close="__e" bind:update="__e" class="data-v-368aef34" bind:__l="__l"></shopping-cart></block></view>