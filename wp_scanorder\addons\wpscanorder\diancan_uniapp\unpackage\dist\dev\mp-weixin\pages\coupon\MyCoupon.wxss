@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-2aa2dbf2 {
  background-color: #f8fafb;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100%;
}
.page .section_4.data-v-2aa2dbf2 {
  padding: 28rpx 56rpx 36rpx;
  background-color: #ffffff;
  position: relative;
}
.page .section_4 .text-wrapper.data-v-2aa2dbf2 {
  width: 90rpx;
}
.page .section_4 .text-wrapper.active .font.data-v-2aa2dbf2 {
  color: #77d431;
}
.page .section_4 .font.data-v-2aa2dbf2 {
  font-size: 30rpx;
  font-family: MiSans;
  line-height: 27.6rpx;
  color: #a2a2a2;
}
.page .section_4 .font.active-text.data-v-2aa2dbf2 {
  color: #77d431;
}
.page .section_4 .section_5.data-v-2aa2dbf2 {
  background-color: #8cd548;
  border-radius: 322rpx;
  width: 40rpx;
  height: 4rpx;
  position: absolute;
  bottom: 20rpx;
  transition: left 0.3s;
}
.page .loading-container.data-v-2aa2dbf2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.page .loading-container .loading-circle.data-v-2aa2dbf2 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #8cd548;
  border-radius: 50%;
  -webkit-animation: spin-data-v-2aa2dbf2 1s linear infinite;
          animation: spin-data-v-2aa2dbf2 1s linear infinite;
  margin-bottom: 20rpx;
}
.page .loading-container .loading-text.data-v-2aa2dbf2 {
  font-size: 28rpx;
  color: #999;
}
.page .error-container.data-v-2aa2dbf2 {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page .error-container .error-text.data-v-2aa2dbf2 {
  font-size: 28rpx;
  color: #ff5b5b;
  margin-bottom: 30rpx;
}
.page .error-container .retry-btn.data-v-2aa2dbf2 {
  padding: 16rpx 60rpx;
  background-color: #8cd548;
  border-radius: 100rpx;
}
.page .error-container .retry-btn .retry-text.data-v-2aa2dbf2 {
  font-size: 28rpx;
  color: #ffffff;
}
.page .empty-state.data-v-2aa2dbf2 {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page .empty-state .empty-image.data-v-2aa2dbf2 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}
.page .empty-state .empty-text.data-v-2aa2dbf2 {
  font-size: 28rpx;
  color: #999;
}
.page .list.data-v-2aa2dbf2 {
  padding: 30rpx 12rpx 0;
}
.page .list .section_6.data-v-2aa2dbf2 {
  margin: 10rpx 8rpx 0;
  padding: 36rpx 12rpx 24rpx 48rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
}
.page .list .section_6 .group.data-v-2aa2dbf2 {
  padding-left: 16rpx;
}
.page .list .section_6 .group .group_2.data-v-2aa2dbf2 {
  margin-top: 16rpx;
}
.page .list .section_6 .group .group_2 .font_3.data-v-2aa2dbf2 {
  font-size: 40rpx;
  color: #000000;
}
.page .list .section_6 .group .group_2 .font_4.data-v-2aa2dbf2 {
  font-size: 24rpx;
  color: #000000;
}
.page .list .section_6 .group .font_2.data-v-2aa2dbf2 {
  font-size: 32rpx;
  color: #000000;
}
.page .list .section_6 .group .text-wrapper_3.data-v-2aa2dbf2 {
  margin-top: 52rpx;
  padding: 8rpx 0;
  background-color: #77d431;
  border-radius: 6rpx;
  width: 100rpx;
  height: 40rpx;
  text-align: center;
}
.page .list .section_6 .group .text-wrapper_3 .text_9.data-v-2aa2dbf2 {
  color: #ffffff;
}
.page .list .section_6 .group .text-wrapper_3.disabled.data-v-2aa2dbf2 {
  background-color: #cccccc;
}
.page .list .section_6 .font_5.data-v-2aa2dbf2 {
  font-size: 28rpx;
  color: #b3b3b3;
}
.page .list .section_6 .font_6.data-v-2aa2dbf2 {
  font-size: 28rpx;
  color: #000000;
}
@-webkit-keyframes spin-data-v-2aa2dbf2 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-2aa2dbf2 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.mt-17.data-v-2aa2dbf2 {
  margin-top: 34rpx;
}
.mt-3.data-v-2aa2dbf2 {
  margin-top: 6rpx;
}
.ml-1.data-v-2aa2dbf2 {
  margin-left: 2rpx;
}

