<view class="page data-v-6c4d65be"><view class="header data-v-6c4d65be"><view class="nav-bar data-v-6c4d65be"><image class="back-icon data-v-6c4d65be" src="/static/recharge/d2d56def1b7e4daac7adad19dc740323.png" data-event-opts="{{[['tap',[['handleBack',['$event']]]]]}}" bindtap="__e"></image><text class="title data-v-6c4d65be">{{isEdit?'编辑地址':'新增地址'}}</text></view></view><view class="form data-v-6c4d65be"><view class="form-item data-v-6c4d65be"><text class="label data-v-6c4d65be">收货人</text><input class="input data-v-6c4d65be" placeholder="请输入收货人姓名" placeholder-class="placeholder" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" value="{{form.name}}" bindinput="__e"/></view><view class="form-item data-v-6c4d65be"><text class="label data-v-6c4d65be">手机号码</text><input class="input data-v-6c4d65be" type="number" placeholder="请输入手机号码" placeholder-class="placeholder" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" value="{{form.phone}}" bindinput="__e"/></view><view class="form-item data-v-6c4d65be"><text class="label data-v-6c4d65be">所在地区</text><view data-event-opts="{{[['tap',[['showRegionPicker',['$event']]]]]}}" class="region-picker data-v-6c4d65be" bindtap="__e"><text class="{{['data-v-6c4d65be','region-text',regionSelected?'':'placeholder']}}">{{''+(regionSelected?form.province+' '+form.city+' '+form.district:'请选择所在地区')+''}}</text><u-icon vue-id="1d5fd308-1" name="arrow-right" color="#999" size="32" class="data-v-6c4d65be" bind:__l="__l"></u-icon></view></view><view class="form-item data-v-6c4d65be"><text class="label data-v-6c4d65be">详细地址</text><textarea class="textarea data-v-6c4d65be" placeholder="请输入详细地址" placeholder-class="placeholder" data-event-opts="{{[['input',[['__set_model',['$0','address','$event',[]],['form']]]]]}}" value="{{form.address}}" bindinput="__e"></textarea></view><view class="form-item data-v-6c4d65be"><text class="label data-v-6c4d65be">标签</text><view class="tag-list data-v-6c4d65be"><block wx:for="{{tags}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectTag',['$0'],[[['tags','',index]]]]]]]}}" class="{{['data-v-6c4d65be','tag',form.tag===tag?'active':'']}}" bindtap="__e">{{''+tag+''}}</view></block></view></view><view class="form-item switch data-v-6c4d65be"><text class="label data-v-6c4d65be">设为默认地址</text><switch checked="{{form.status=='1'}}" color="#8cd548" data-event-opts="{{[['change',[['switchDefault',['$event']]]]]}}" bindchange="__e" class="data-v-6c4d65be"></switch></view></view><view data-event-opts="{{[['tap',[['saveAddress',['$event']]]]]}}" class="save-btn data-v-6c4d65be" bindtap="__e"><text class="data-v-6c4d65be">保存</text></view><area-picker vue-id="1d5fd308-2" show="{{showPicker}}" title="选择地区" data-event-opts="{{[['^updateShow',[['__set_sync',['$0','showPicker','$event'],['']]]],['^confirm',[['confirmRegion']]],['^cancel',[['cancelRegion']]]]}}" bind:updateShow="__e" bind:confirm="__e" bind:cancel="__e" class="data-v-6c4d65be" bind:__l="__l"></area-picker></view>