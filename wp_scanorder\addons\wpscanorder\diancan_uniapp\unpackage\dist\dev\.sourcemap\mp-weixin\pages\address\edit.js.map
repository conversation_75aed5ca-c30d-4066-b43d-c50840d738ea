{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/edit.vue?2c89", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/edit.vue?3175", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/edit.vue?486f", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/edit.vue?1a02", "uni-app:///pages/address/edit.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/edit.vue?6bb5", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/address/edit.vue?1997"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "AreaPicker", "data", "id", "isEdit", "regionSelected", "form", "name", "phone", "province", "city", "district", "address", "tag", "status", "tags", "showPicker", "onLoad", "console", "uni", "title", "icon", "setTimeout", "methods", "handleBack", "getAddressDetail", "res", "showRegionPicker", "selectTag", "switchDefault", "confirmRegion", "cancelRegion", "save<PERSON><PERSON>ress", "is_default", "success", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkGjrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;IAEA;MACA;MACA;MACA;QACAA;QACAC;UACAC;UACAC;QACA;QACAC;UAAA;QAAA;QACA;MACA;MAEAJ;MACA;MACA;MACA;IACA;MACAA;IACA;EACA;EAEAK;IACAC;MACAL;IACA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAP;;gBAEA;gBAAA,IACAf;kBAAA;kBAAA;gBAAA;gBACAe;gBACAC;kBACAC;kBACAC;gBACA;gBACAC;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAKAH;kBACAC;gBACA;;gBAEA;gBACAF;kBAAAf;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAuB;gBAEA;kBACA;oBACAnB;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;gBACA;kBACAK;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAQ;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACAZ;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEAa;MACAb;IACA;IAEAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAb;kBACAC;kBACAC;gBACA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA,kCACAF;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAIAF;kBACAC;gBACA;;gBAEA;gBACAlB;kBACAK;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAoB;gBACA,GAEA;gBACAf;gBAAA,KAGA;kBAAA;kBAAA;gBAAA;gBACA;gBACAhB;gBAAA;gBAAA,OACA;cAAA;gBAAAwB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAA;cAAA;gBAGA;gBACAR;gBAEA;kBACAC;oBACAC;oBACAC;oBACAa;sBACAZ;wBACAH;sBACA;oBACA;kBACA;gBACA;kBACA;kBACAA;oBACAC;oBACAC;oBACAc;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAjB;gBACAC;kBACAC;kBACAC;kBACAc;gBACA;cAAA;gBAAA;gBAEAhB;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7UA;AAAA;AAAA;AAAA;AAAgxC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;;ACApyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/address/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/address/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=6c4d65be&scoped=true&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&id=6c4d65be&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6c4d65be\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/address/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=6c4d65be&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">{{isEdit ? '编辑地址' : '新增地址'}}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 表单内容 -->\r\n    <view class=\"form\">\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">收货人</text>\r\n        <input \r\n          class=\"input\"\r\n          v-model=\"form.name\"\r\n          placeholder=\"请输入收货人姓名\"\r\n          placeholder-class=\"placeholder\"\r\n        />\r\n      </view>\r\n      \r\n      <view class=\"form-item\">\r\n        <text class=\"label\">手机号码</text>\r\n        <input \r\n          class=\"input\"\r\n          type=\"number\"\r\n          v-model=\"form.phone\"\r\n          placeholder=\"请输入手机号码\"\r\n          placeholder-class=\"placeholder\"\r\n          maxlength=\"11\"\r\n        />\r\n      </view>\r\n      \r\n      <view class=\"form-item\">\r\n        <text class=\"label\">所在地区</text>\r\n        <view class=\"region-picker\" @tap=\"showRegionPicker\">\r\n          <text :class=\"['region-text', regionSelected ? '' : 'placeholder']\">\r\n            {{ regionSelected ? `${form.province} ${form.city} ${form.district}` : '请选择所在地区' }}\r\n          </text>\r\n          <u-icon name=\"arrow-right\" color=\"#999\" size=\"32\"></u-icon>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"form-item\">\r\n        <text class=\"label\">详细地址</text>\r\n        <textarea \r\n          class=\"textarea\"\r\n          v-model=\"form.address\"\r\n          placeholder=\"请输入详细地址\"\r\n          placeholder-class=\"placeholder\"\r\n        />\r\n      </view>\r\n      \r\n      <view class=\"form-item\">\r\n        <text class=\"label\">标签</text>\r\n        <view class=\"tag-list\">\r\n          <view \r\n            v-for=\"(tag, index) in tags\"\r\n            :key=\"index\"\r\n            :class=\"['tag', form.tag === tag ? 'active' : '']\"\r\n            @tap=\"selectTag(tag)\"\r\n          >\r\n            {{tag}}\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"form-item switch\">\r\n        <text class=\"label\">设为默认地址</text>\r\n        <switch \r\n          :checked=\"form.status == '1'\"\r\n          color=\"#8cd548\"\r\n          @change=\"switchDefault\"\r\n        />\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部保存按钮 -->\r\n    <view class=\"save-btn\" @tap=\"saveAddress\">\r\n      <text>保存</text>\r\n    </view>\r\n\r\n    <!-- 地区选择器 -->\r\n    <area-picker\r\n      :show.sync=\"showPicker\"\r\n      title=\"选择地区\"\r\n      @confirm=\"confirmRegion\"\r\n      @cancel=\"cancelRegion\"\r\n    ></area-picker>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { addAddress, editAddress, getAddressDetail } from '@/api/address.js'\r\nimport AreaPicker from '@/components/area-picker/index.vue'\r\n\r\nexport default {\r\n  components: {\r\n    AreaPicker\r\n  },\r\n  data() {\r\n    return {\r\n      id: 0,\r\n      isEdit: false,\r\n      regionSelected: false,\r\n      form: {\r\n        name: '',\r\n        phone: '',\r\n        province: '',\r\n        city: '',\r\n        district: '',\r\n        address: '',\r\n        tag: '',\r\n        status: '0'\r\n      },\r\n      tags: ['家', '公司', '学校'],\r\n      showPicker: false\r\n    }\r\n  },\r\n  \r\n  onLoad(options) {\r\n    // 获取地址ID，如果有则是编辑模式\r\n    console.log('地址编辑页面 - 接收参数:', JSON.stringify(options));\r\n    \r\n    if(options && options.id) {\r\n      // 确保ID是字符串类型\r\n      const safeId = String(options.id || '');\r\n      if (!safeId) {\r\n        console.error('编辑地址失败: 收到的ID转换后为空');\r\n        uni.showToast({\r\n          title: '操作失败: 无效的地址ID',\r\n          icon: 'none'\r\n        });\r\n        setTimeout(() => uni.navigateBack(), 1500);\r\n        return;\r\n      }\r\n      \r\n      console.log('编辑地址 - 使用ID:', safeId);\r\n      this.id = safeId;\r\n      this.isEdit = true;\r\n      this.getAddressDetail(safeId);\r\n    } else {\r\n      console.log('新增地址模式');\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    handleBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    // 获取地址详情\r\n    async getAddressDetail(id) {\r\n      console.log('获取地址详情 - ID:', id);\r\n      \r\n      // 验证ID参数\r\n      if (!id) {\r\n        console.error('获取地址详情失败: ID为空');\r\n        uni.showToast({\r\n          title: '获取地址失败: 无效的ID',\r\n          icon: 'none'\r\n        });\r\n        setTimeout(() => uni.navigateBack(), 1500);\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        uni.showLoading({\r\n          title: '加载中...'\r\n        })\r\n        \r\n        // 使用API获取地址详情\r\n        console.log('调用getAddressDetail API，参数:', { id });\r\n        const res = await getAddressDetail(id)\r\n        \r\n        if (res.code === 1 && res.data) {\r\n          this.form = { \r\n            name: res.data.name,\r\n            phone: res.data.phone,\r\n            province: res.data.province,\r\n            city: res.data.city,\r\n            district: res.data.district,\r\n            address: res.data.address,\r\n            tag: res.data.tag || '',\r\n            status: res.data.status\r\n          }\r\n          \r\n          // 设置地区已选中标志\r\n          this.regionSelected = !!(this.form.province && this.form.city && this.form.district)\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg || '获取地址详情失败',\r\n            icon: 'none'\r\n          })\r\n        }\r\n      } catch (e) {\r\n        console.error('获取地址详情失败:', JSON.stringify(e))\r\n        uni.showToast({\r\n          title: '获取地址详情失败',\r\n          icon: 'none'\r\n        })\r\n      } finally {\r\n        uni.hideLoading()\r\n      }\r\n    },\r\n    \r\n    showRegionPicker() {\r\n      this.showPicker = true\r\n    },\r\n    \r\n    selectTag(tag) {\r\n      this.form.tag = this.form.tag === tag ? '' : tag\r\n    },\r\n    \r\n    switchDefault(e) {\r\n      this.form.status = e.detail.value ? '1' : '0'\r\n    },\r\n    \r\n    confirmRegion(result) {\r\n      console.log('地区选择结果:', result);\r\n      if (result && result.province && result.city && result.district) {\r\n        this.form.province = result.province.name;\r\n        this.form.city = result.city.name;\r\n        this.form.district = result.district.name;\r\n        this.regionSelected = true;\r\n      }\r\n    },\r\n    \r\n    cancelRegion() {\r\n      console.log('取消地区选择');\r\n    },\r\n    \r\n    async saveAddress() {\r\n      // 表单验证\r\n      if(!this.form.name) {\r\n        return uni.showToast({\r\n          title: '请输入收货人姓名',\r\n          icon: 'none'\r\n        })\r\n      }\r\n      if(!this.form.phone) {\r\n        return uni.showToast({\r\n          title: '请输入手机号码',\r\n          icon: 'none'\r\n        })\r\n      }\r\n      if(!/^1[3-9]\\d{9}$/.test(this.form.phone)) {\r\n        return uni.showToast({\r\n          title: '手机号码格式不正确',\r\n          icon: 'none'\r\n        })\r\n      }\r\n      if(!this.form.province || !this.form.city || !this.form.district) {\r\n        return uni.showToast({\r\n          title: '请选择所在地区',\r\n          icon: 'none'\r\n        })\r\n      }\r\n      if(!this.form.address) {\r\n        return uni.showToast({\r\n          title: '请输入详细地址',\r\n          icon: 'none'\r\n        })\r\n      }\r\n      \r\n      try {\r\n        uni.showLoading({\r\n          title: '保存中...'\r\n        })\r\n        \r\n        // 根据接口文档格式化参数\r\n        const data = {\r\n          name: this.form.name,\r\n          phone: this.form.phone,\r\n          province: this.form.province,\r\n          city: this.form.city,\r\n          district: this.form.district,\r\n          address: this.form.address,\r\n          tag: this.form.tag || '',\r\n          is_default: this.form.status === '1' ? 1 : 0\r\n        }\r\n        \r\n        // 打印请求数据，帮助调试\r\n        console.log('地址数据请求参数:', JSON.stringify(data))\r\n        \r\n        let res\r\n        if (this.isEdit) {\r\n          // 编辑模式\r\n          data.id = this.id\r\n          res = await editAddress(data)\r\n        } else {\r\n          // 新增模式\r\n          res = await addAddress(data)\r\n        }\r\n        \r\n        // 打印响应数据，帮助调试\r\n        console.log('地址保存响应:', JSON.stringify(res))\r\n        \r\n        if (res.code === 1) {\r\n          uni.showToast({\r\n            title: '保存成功',\r\n            icon: 'none',\r\n            success: () => {\r\n              setTimeout(() => {\r\n                uni.navigateBack()\r\n              }, 1500)\r\n            }\r\n          })\r\n        } else {\r\n          // 显示详细错误信息\r\n          uni.showToast({\r\n            title: '保存失败: ' + (res.msg || ''),\r\n            icon: 'none',\r\n            duration: 3000\r\n          })\r\n        }\r\n      } catch (e) {\r\n        console.error('保存地址异常:', JSON.stringify(e))\r\n        uni.showToast({\r\n          title: '保存失败: ' + (e.msg || e.message || ''),\r\n          icon: 'none',\r\n          duration: 3000\r\n        })\r\n      } finally {\r\n        uni.hideLoading()\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));\r\n}\r\n\r\n.header {\r\n  background: #fff;\r\n  padding-top: 88rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .back-icon {\r\n      position: absolute;\r\n      left: 30rpx;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n\r\n.form {\r\n  margin-top: 20rpx;\r\n  background: #fff;\r\n  padding: 0 30rpx;\r\n  \r\n  .form-item {\r\n    padding: 30rpx 0;\r\n    border-bottom: 1px solid #f5f5f5;\r\n    display: flex;\r\n    align-items: flex-start;\r\n    \r\n    .label {\r\n      width: 160rpx;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n      padding-top: 6rpx;\r\n    }\r\n    \r\n    .input {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n    \r\n    .textarea {\r\n      flex: 1;\r\n      height: 160rpx;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n    \r\n    .region-picker {\r\n      flex: 1;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      \r\n      .region-text {\r\n        font-size: 28rpx;\r\n        color: #333;\r\n        \r\n        &.placeholder {\r\n          color: #999;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .tag-list {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 20rpx;\r\n      \r\n      .tag {\r\n        padding: 12rpx 32rpx;\r\n        background: #f8f8f8;\r\n        border-radius: 100rpx;\r\n        font-size: 26rpx;\r\n        color: #666;\r\n        \r\n        &.active {\r\n          background: rgba(140, 213, 72, 0.1);\r\n          color: #8cd548;\r\n        }\r\n      }\r\n    }\r\n    \r\n    &.switch {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      \r\n      .label {\r\n        padding-top: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.placeholder {\r\n  color: #999;\r\n}\r\n\r\n.save-btn {\r\n  position: fixed;\r\n  left: 40rpx;\r\n  right: 40rpx;\r\n  bottom: calc(40rpx + env(safe-area-inset-bottom));\r\n  height: 88rpx;\r\n  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n  border-radius: 44rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  text {\r\n    font-size: 32rpx;\r\n    color: #fff;\r\n    font-weight: 500;\r\n  }\r\n  \r\n  &:active {\r\n    transform: scale(0.98);\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=6c4d65be&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=6c4d65be&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309939\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}