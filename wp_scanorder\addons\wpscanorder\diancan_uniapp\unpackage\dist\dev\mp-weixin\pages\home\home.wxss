@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-92bb8f34 {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: env(safe-area-inset-bottom);
}
.page .status-bar-placeholder.data-v-92bb8f34 {
  display: none;
  /* 隐藏额外空间 */
}
.page .swiper.data-v-92bb8f34 {
  width: 100%;
  height: 380rpx;
  /* 增加轮播图高度 */
  margin-top: 0;
  /* 移除轮播图顶部外边距 */
  margin-bottom: 10rpx;
  /* 轮播图底部外边距 */
  padding-top: env(safe-area-inset-top);
  /* 添加安全区域适配 */
}
.page .swiper .swiper-item.data-v-92bb8f34 {
  width: 100%;
  height: 100%;
  display: block;
}
.page .swiper .swiper-image.data-v-92bb8f34 {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  /* 确保图片填充整个容器且保持纵横比 */
}
.page .banner.data-v-92bb8f34 {
  width: 100%;
  height: 320rpx;
}
.page .banner.data-v-92bb8f34  .u-swiper__wrapper {
  height: 320rpx !important;
}
.page .banner.data-v-92bb8f34  .u-swiper__wrapper__item__wrapper__image {
  width: 100%;
  height: 100%;
  border-radius: 0;
}
.page .member-card.data-v-92bb8f34 {
  margin: 20rpx 20rpx 0rpx;
  /* 增加上下边距 */
}
.page .member-card .member-info.data-v-92bb8f34 {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.page .member-card .member-info.not-login.data-v-92bb8f34 {
  align-items: center;
}
.page .member-card .member-info.not-login .login-tip.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #666;
  margin-top: 6rpx;
}
.page .member-card .member-info.not-login .login-btn.data-v-92bb8f34 {
  background-color: #78c238;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  margin-left: 20rpx;
}
.page .member-card .member-info.not-login .login-btn text.data-v-92bb8f34 {
  color: #fff;
  font-size: 28rpx;
}
.page .member-card .member-info .info-content.data-v-92bb8f34 {
  flex: 1;
}
.page .member-card .member-info .info-content .user-info.data-v-92bb8f34 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.page .member-card .member-info .info-content .user-info .avatar-wrap.data-v-92bb8f34 {
  position: relative;
  margin-right: 20rpx;
}
.page .member-card .member-info .info-content .user-info .avatar-wrap .avatar.data-v-92bb8f34 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.page .member-card .member-info .info-content .user-info .avatar-wrap .vip-badge.data-v-92bb8f34 {
  position: absolute;
  right: -6rpx;
  bottom: -6rpx;
  width: 32rpx;
  height: 32rpx;
  background: #8cd548;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .member-card .member-info .info-content .user-info .user-detail .name.data-v-92bb8f34 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
  display: block;
}
.page .member-card .member-info .info-content .user-info .user-detail .level.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #8cd548;
  background: rgba(140, 213, 72, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}
.page .member-card .member-info .info-content .asset-info.data-v-92bb8f34 {
  display: flex;
  align-items: center;
}
.page .member-card .member-info .info-content .asset-info .asset-item.data-v-92bb8f34 {
  flex: 1;
  text-align: center;
}
.page .member-card .member-info .info-content .asset-info .asset-item .value.data-v-92bb8f34 {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}
.page .member-card .member-info .info-content .asset-info .asset-item .label.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #999;
}
.page .member-card .member-info .info-content .asset-info .divider.data-v-92bb8f34 {
  width: 1rpx;
  height: 36rpx;
  background: #eee;
  margin: 0 30rpx;
}
.page .member-card .member-info .qr-code.data-v-92bb8f34 {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.page .member-card .member-info .qr-code .code-wrap.data-v-92bb8f34 {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 8rpx;
}
.page .member-card .member-info .qr-code .code-wrap .code-icon.data-v-92bb8f34 {
  width: 100%;
  height: 100%;
}
.page .member-card .member-info .qr-code text.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #999;
}
.page .unified-white-box.data-v-92bb8f34 {
  margin: 20rpx 20rpx 0rpx;
  /* 增加上下边距 */
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.page .unified-white-box .order-section.data-v-92bb8f34 {
  margin-bottom: 30rpx;
  display: flex;
  gap: 15rpx;
}
.page .unified-white-box .order-section .order-item.data-v-92bb8f34 {
  flex: 1;
  min-width: 0;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page .unified-white-box .order-section .order-item .order-icon.data-v-92bb8f34 {
  width: 300rpx;
  height: 220rpx;
}
.page .unified-white-box .order-section .order-item .order-content.data-v-92bb8f34 {
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page .unified-white-box .order-section .order-item .order-content .title.data-v-92bb8f34 {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.page .unified-white-box .order-section .order-item .order-content .desc.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #999;
  white-space: normal;
  text-align: center;
  line-height: 1.4;
}
.page .unified-white-box .order-section .order-item.data-v-92bb8f34:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.page .unified-white-box .divider-line.data-v-92bb8f34 {
  height: 1rpx;
  background: #eee;
  margin: 30rpx 0;
}
.page .unified-white-box .feature-section.data-v-92bb8f34 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}
.page .unified-white-box .feature-section .feature-item.data-v-92bb8f34 {
  background: transparent;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.page .unified-white-box .feature-section .feature-item .feature-icon.data-v-92bb8f34 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 12rpx;
}
.page .unified-white-box .feature-section .feature-item text.data-v-92bb8f34 {
  font-size: 26rpx;
  color: #333;
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.page .hot-selling-section.data-v-92bb8f34 {
  margin: 20rpx 20rpx 40rpx;
  /* 增加底部边距 */
  padding: 20rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.page .hot-selling-section .section-header.data-v-92bb8f34 {
  margin-bottom: 20rpx;
  padding: 0 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page .hot-selling-section .section-header .section-title.data-v-92bb8f34 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  position: relative;
  padding-left: 16rpx;
}
.page .hot-selling-section .section-header .section-title.data-v-92bb8f34::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #8cd548, #6ab52e);
  border-radius: 3rpx;
}
.page .hot-selling-section .product-scroll.data-v-92bb8f34 {
  white-space: nowrap;
  width: 100%;
  padding: 20rpx;
  /* 隐藏滚动条但保留滚动功能 */
}
.page .hot-selling-section .product-scroll.data-v-92bb8f34::-webkit-scrollbar {
  display: none;
}
.page .hot-selling-section .product-scroll .product-card.data-v-92bb8f34 {
  display: inline-block;
  width: 200rpx;
  margin-right: 50rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  vertical-align: top;
}
.page .hot-selling-section .product-scroll .product-card.data-v-92bb8f34:last-child {
  margin-right: 0;
}
.page .hot-selling-section .product-scroll .product-card .product-tag.data-v-92bb8f34 {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: #ff5722;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  z-index: 2;
}
.page .hot-selling-section .product-scroll .product-card .product-image.data-v-92bb8f34 {
  width: 100%;
  height: 180rpx;
  object-fit: cover;
}
.page .hot-selling-section .product-scroll .product-card .product-info.data-v-92bb8f34 {
  padding: 10rpx;
  background: #fff;
}
.page .hot-selling-section .product-scroll .product-card .product-info .product-name.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 10rpx;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  height: 34rpx;
  line-height: 1.4;
}
.page .hot-selling-section .product-scroll .product-card .product-info .product-price-row.data-v-92bb8f34 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page .hot-selling-section .product-scroll .product-card .product-info .product-price-row .product-price.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}
.page .hot-selling-section .product-scroll .product-card .product-info .product-price-row .add-btn.data-v-92bb8f34 {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(140, 213, 72, 0.2);
}
.page .hot-selling-section .product-scroll .product-card .product-info .product-price-row .add-btn .add-icon.data-v-92bb8f34 {
  color: #fff;
  font-size: 32rpx;
  line-height: 1;
  font-weight: bold;
}
.page .hot-selling-section .product-scroll .product-card .product-info .product-price-row .add-btn.data-v-92bb8f34:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  opacity: 0.9;
}
.page .hot-selling-section .product-scroll .product-card.data-v-92bb8f34:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
@-webkit-keyframes shine-data-v-92bb8f34 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
}
100% {
    -webkit-transform: translate(50%, 50%) rotate(0deg);
            transform: translate(50%, 50%) rotate(0deg);
}
}
@keyframes shine-data-v-92bb8f34 {
0% {
    -webkit-transform: translate(-50%, -50%) rotate(0deg);
            transform: translate(-50%, -50%) rotate(0deg);
}
100% {
    -webkit-transform: translate(50%, 50%) rotate(0deg);
            transform: translate(50%, 50%) rotate(0deg);
}
}

