{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?28f2", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?a6be", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?90a5", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?724f", "uni-app:///pages/order/detail.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?ea88", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/order/detail.vue?e6b1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "order", "loading", "orderId", "refreshTimer", "lastRefreshTime", "onShow", "console", "onHide", "onUnload", "clearInterval", "computed", "isPaid", "onLoad", "uni", "title", "icon", "setTimeout", "methods", "fetchOrderDetail", "then", "catch", "finally", "handleBack", "url", "refreshOrderData", "processOrderData", "orderData", "product", "image", "specs", "count", "price", "getStatusDesc", "reorder", "list", "id", "name", "totalPrice", "specSelected", "total", "duration", "success", "checkProgress", "content", "showCancel", "confirmText", "goComment", "getProgressDesc", "goPay", "itemList", "payType", "wxPay", "payResult", "fail", "balancePay", "cancelText", "checkPaymentResult", "query<PERSON><PERSON>ult", "startAutoRefresh", "clearAutoRefresh", "cancelOrder", "res", "result"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+InrB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;IAEA;EACA;EAEAC;IACAD;IACA;EACA;EAEAE;IACAF;IACA;IACA;IACAG;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;EACA;EAEAC;IACAN;;IAEA;IACA;IAEAA;;IAEA;IACA;IAEA;MACA;MACAO;QACAC;MACA;;MAEA;MACA;QACA;QACAR;QACA;MACA;IACA;MACAO;QACAC;QACAC;MACA;MACAC;QACAH;MACA;IACA;EACA;EAEAI;IACA;IACAC;MAAA;MAAA;MACA;MACA;;MAEA;MACA;QACAZ;QACA;UACAO;UACAA;YACAC;YACAC;UACA;QACA;QACA;QACA;MACA;;MAEA;MACA;MAEAT;MACA,qCACAa;QACAb;QAEA;UACA;UACA;UACAA;;UAEA;UACAU;YACA;YACAV;;YAEA;YACA;UACA;QACA;UACAO;YACAC;YACAC;UACA;UACAC;YACAH;UACA;QACA;MACA,GACAO;QACAd;QAEAO;QAEAA;UACAC;UACAC;QACA;QACAC;UACAH;QACA;MACA,GACAQ;QACA;QACA;UACAR;QACA;MACA;IACA;IAEAS;MACA;MACAT;QACAU;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;UACAX;YACAC;YACAC;UACA;QACA;QACA;MACA;;MAEA;MACA;MACA;QAAA;QACAT;QACA;MACA;MAEA;MAEA;QACA;UACAO;YACAC;UACA;QACA;QACAR;;QAEA;QACA;QACA;MACA;QACA;UACAO;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAU;MACA;QACA;QACA;UACA;UACAC;YAAA,uCACAC;cACAC;cACAC;cACAC;cACAC;YAAA;UAAA,CACA;UACA;UACAL;QACA;UACA;UACAA;YAAA,uCACAC;cACAC;cACAC;cACAC;cACAC;YAAA;UAAA,CACA;QACA;UACAL;QACA;;QAEA;QACAA;QACAA;QACAA;;QAEA;QACA;QAEA;MACA;QACApB;QACA;MACA;IACA;IAEA0B;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;QAAA;MAEA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;UACA;YACA;QAAA;MAEA;IACA;IAEAC;MACA;MACA;MAEA;QACApB;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAmB;UACA;UACA;UACA;YACAC;YACAC;YACAL;YACAD;YACAO;YACAT;YACAC;YACAS;UACA;QACA;QACAC;UAAA;QAAA;QACAR;MACA;;MAEA;MACAlB;;MAEA;MACAA;;MAEA;MACAA;QACAC;QACAC;QACAyB;QACAC;UACA;UACAzB;YACAH;cACAU;YACA;UACA;QACA;MACA;IACA;IAEAmB;MACA;MACA;QACA7B;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAC;QACA6B;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAjC;QACAC;QACAC;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEAgC;MACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;QACA;QACA;UACA;YACA,8MAGA;UACA;YACA,yMAGA;UAEA;YACA,+MAGA;UACA;YACA;UAEA;YACA,yMAGA;QACA;MAEA;QACA;QACA;UACA;YACA,oNAGA;UACA;YACA,gNAGA;UACA;YACA,yMAGA;UACA;YACA,6GACA,2EACA,0DACA,qGACA;UACA;YACA;UAEA;YACA,2JAEA;QAAA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;QACAnC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACAF;QACAoC;QACAR;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAS;oBAEA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACAvC;gBAAA,MAEAuC;kBAAA;kBAAA;gBAAA;gBACAvC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACAF,mDACAuC;kBACAX;oBACA;kBACA;kBACAY;oBACA/C;oBACAO;sBACAC;sBACAC;oBACA;kBACA;gBAAA,GACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;kBACAC;kBACAC;gBACA;gBACAT;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAgD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAzC;kBAAAC;gBAAA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAsC;gBACAvC;gBAEA;kBACAA;oBACAC;oBACAC;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;oBACA;oBACAF;sBACAC;sBACA6B;sBACAE;sBACAU;sBACAd;wBACA;0BACA;0BACA5B;4BACAU;0BACA;wBACA;sBACA;oBACA;kBACA;kBACAV;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAA;kBACAC;kBACAC;gBACA;gBACAT;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAkD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA3C;kBAAAC;gBAAA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA2C;gBACA5C;gBAEA;kBACAA;oBACAC;oBACAC;kBACA;;kBAEA;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;gBACAA;kBACAC;kBACAC;gBACA;gBACAT;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAoD;MAAA;MACA;MACA;;MAEA;MACA;QACApD;QACA;MACA;;MAEA;MACA;MAEAA;IACA;IAEAqD;MACA;QACAlD;QACA;QACAH;MACA;MACA;MACA;QACAG;QACA;MACA;IACA;IAEA;IACAmD;MAAA;MACA;MACA;MACA;QACA/C;UACAC;UACAC;QACA;QACA;MACA;MAEAF;QACAC;QACA6B;QACAF;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAoB;sBAAA;sBAAA;oBAAA;oBAAA;oBAEAhD;sBAAAC;oBAAA;oBAAA;oBAAA,OAEA;kBAAA;oBAAAgD;oBACAjD;oBAEA;sBACAA;wBACAC;wBACAC;sBACA;;sBAEA;sBACAC;wBACA;sBACA;oBACA;sBACAH;wBACAC;wBACAC;sBACA;oBACA;oBAAA;oBAAA;kBAAA;oBAAA;oBAAA;oBAEAF;oBACAA;sBACAC;sBACAC;oBACA;oBACAT;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACj0BA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=57d42baa&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=57d42baa&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57d42baa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=57d42baa&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && Object.keys(_vm.order).length > 0\n  var m0 =\n    g0 &&\n    !(_vm.order.status === \"pending\") &&\n    !(_vm.order.status === \"cancelled\")\n      ? _vm.getStatusDesc()\n      : null\n  var g1 =\n    (_vm.order.dining_type || _vm.order.type) === \"takeout\" && _vm.order.address\n      ? _vm.order.address.replace(/,/g, \"\\n\")\n      : null\n  var g2 = (_vm.order.total_price || _vm.order.totalPrice || 0).toFixed(2)\n  var g3 = (_vm.order.discount_amount || _vm.order.discountAmount || 0).toFixed(\n    2\n  )\n  var g4 = (\n    _vm.order.final_price ||\n    _vm.order.finalPrice ||\n    _vm.order.total_price ||\n    _vm.order.totalPrice ||\n    0\n  ).toFixed(2)\n  var g5 = !_vm.loading && Object.keys(_vm.order).length > 0\n  var g6 = g5 ? [\"pending\", \"paid\"].includes(_vm.order.status) : null\n  var g7 =\n    g5 && !(_vm.order.status === \"pending\")\n      ? [\"paid\", \"cooking\", \"cooked\", \"delivering\"].includes(_vm.order.status)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <view class=\"left\" @tap=\"handleBack\">\r\n          <u-icon name=\"arrow-left\" color=\"#333\" size=\"20\"></u-icon>\r\n        </view>\r\n        <text class=\"title\">订单详情</text>\r\n        <view class=\"right\" @tap=\"refreshOrderData\">\r\n          <u-icon name=\"reload\" color=\"#333\" size=\"20\"></u-icon>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 订单状态 -->\r\n    <view class=\"status-card\" :class=\"{\r\n      'unpaid-card': !isPaid, \r\n      'paid-card': order.status === 'paid',\r\n      'cooking-card': order.status === 'cooking',\r\n      'cooked-card': order.status === 'cooked',\r\n      'delivering-card': order.status === 'delivering',\r\n      'completed-card': order.status === 'completed',\r\n      'cancelled-card': order.status === 'cancelled'\r\n    }\">\r\n      <view class=\"status-info\" v-if=\"!loading && Object.keys(order).length > 0\">\r\n        <text class=\"status\" v-if=\"order.status === 'pending'\">待支付</text>\r\n        <text class=\"status\" v-else-if=\"order.status === 'paid'\">已支付</text>\r\n        <text class=\"status\" v-else-if=\"order.status === 'cooking'\">制作中</text>\r\n        <text class=\"status\" v-else-if=\"order.status === 'cooked'\">制作完成</text>\r\n        <text class=\"status\" v-else-if=\"order.status === 'delivering'\">配送中</text>\r\n        <text class=\"status\" v-else-if=\"order.status === 'completed'\">已完成</text>\r\n        <text class=\"status\" v-else-if=\"order.status === 'cancelled'\">已取消</text>\r\n        <text class=\"status\" v-else>{{isPaid ? '已支付' : '待支付'}}</text>\r\n        \r\n        <text class=\"desc\" v-if=\"order.status === 'pending'\">请尽快完成支付，订单将在30分钟后自动取消</text>\r\n        <text class=\"desc\" v-else-if=\"order.status === 'cancelled'\">订单已取消，请重新下单</text>\r\n        <text class=\"desc\" v-else>{{getStatusDesc()}}</text>\r\n        \r\n        <view class=\"auto-refresh-tip\" v-if=\"order.status !== 'completed' && order.status !== 'cancelled'\">\r\n          <text>订单状态自动刷新中</text>\r\n        </view>\r\n      </view>\r\n      <view class=\"status-info\" v-else>\r\n        <text class=\"status\">加载中...</text>\r\n        <text class=\"desc\">正在获取订单信息</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 订单信息 -->\r\n    <view class=\"order-card\">\r\n      <view class=\"card-title\">订单信息</view>\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">取餐号</text>\r\n        <text class=\"value\">{{order.pickup_code || order.pickup_code || ''}}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">订单编号</text>\r\n        <text class=\"value\">{{order.order_no || order.orderNo}}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">下单时间</text>\r\n        <text class=\"value\">{{order.createtime_text || order.createTime}}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">用餐方式</text>\r\n        <text class=\"value\">{{(order.dining_type || order.type) === 'dine-in' ? '堂食' : '外卖'}}</text>\r\n      </view>\r\n      <view class=\"info-item\" v-if=\"(order.dining_type || order.type) === 'takeout'\">\r\n        <text class=\"label\">配送地址</text>\r\n        <text class=\"value\">{{order.address ? order.address.replace(/,/g, '\\n') : '未设置地址'}}</text>\r\n      </view>\r\n      <view class=\"info-item\">\r\n        <text class=\"label\">备注信息</text>\r\n        <text class=\"value\">{{order.remark || '无'}}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 商品列表 -->\r\n    <view class=\"product-card\">\r\n      <view class=\"card-title\">商品信息</view>\r\n      <view class=\"product-list\">\r\n        <view class=\"product-item\" v-for=\"(item, index) in order.products\" :key=\"index\">\r\n          <text class=\"product-name\">{{item.product_name || item.name}}</text>\r\n          <text class=\"product-specs\">{{item.props_text || item.specs || ''}}</text>\r\n          <view class=\"product-price\">\r\n            <text>¥{{item.price || item.product_price || 0}}</text>\r\n            <text class=\"count\">x{{item.quantity || item.count || 1}}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"price-info\">\r\n        <view class=\"price-item\">\r\n          <text class=\"label\">商品小计</text>\r\n          <text class=\"value\">¥{{(order.total_price || order.totalPrice || 0).toFixed(2)}}</text>\r\n        </view>\r\n        <view class=\"price-item\">\r\n          <text class=\"label\">优惠金额</text>\r\n          <text class=\"value\">-¥{{(order.discount_amount || order.discountAmount || 0).toFixed(2)}}</text>\r\n        </view>\r\n        <view class=\"price-item total\">\r\n          <text class=\"label\">实付金额</text>\r\n          <text class=\"value\" :class=\"{'unpaid': !isPaid}\">¥{{(order.final_price || order.finalPrice || order.total_price || order.totalPrice || 0).toFixed(2)}}</text>\r\n        </view>\r\n        <view class=\"price-item payment-status\">\r\n          <text class=\"label\">支付状态</text>\r\n          <text class=\"value\" :class=\"{'paid': isPaid, 'unpaid': !isPaid}\">{{isPaid ? '已支付' : '待支付'}}</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部按钮 -->\r\n    <view class=\"bottom-bar\">\r\n      <view class=\"btn outline\" @tap=\"reorder\">再来一单</view>\r\n      \r\n      <template v-if=\"!loading && Object.keys(order).length > 0\">\r\n        <!-- 订单取消按钮 -->\r\n        <view class=\"btn outline\" \r\n              v-if=\"['pending', 'paid'].includes(order.status)\"\r\n              @tap=\"cancelOrder\">\r\n          取消订单\r\n        </view>\r\n        \r\n        <!-- 待支付状态显示去支付按钮 -->\r\n        <view class=\"btn primary\" v-if=\"order.status === 'pending'\" @tap=\"goPay\">\r\n          去支付\r\n        </view>\r\n        \r\n        <!-- 已支付、制作中、制作完成、配送中状态显示查看进度按钮 -->\r\n        <view class=\"btn primary\" v-else-if=\"['paid', 'cooking', 'cooked', 'delivering'].includes(order.status)\" @tap=\"checkProgress\">\r\n          查看进度\r\n        </view>\r\n        \r\n        <!-- 已完成状态可以评价 -->\r\n        <view class=\"btn primary\" v-else-if=\"order.status === 'completed'\" v-show=\"false\" @tap=\"goComment\">\r\n          评价订单\r\n        </view>\r\n      </template>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getOrderDetail, getWxPayParams, queryWxPayResult, payOrderWithBalance, cancelOrder } from '@/api/order';\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      order: {},\r\n      loading: true,\r\n      orderId: '',\r\n      refreshTimer: null,\r\n      lastRefreshTime: 0\r\n    }\r\n  },\r\n  \r\n  onShow() {\r\n    console.log('页面显示');\r\n    \r\n    this.startAutoRefresh();\r\n  },\r\n  \r\n  onHide() {\r\n    console.log('页面隐藏，清除自动刷新');\r\n    this.clearAutoRefresh();\r\n  },\r\n  \r\n  onUnload() {\r\n    console.log('页面卸载，清除自动刷新');\r\n    this.clearAutoRefresh();\r\n    // 确保在页面卸载时取消所有定时器\r\n    clearInterval(this.refreshTimer);\r\n    this.refreshTimer = null;\r\n  },\r\n  \r\n  computed: {\r\n    // 判断订单是否已支付\r\n    isPaid() {\r\n      if (this.order.status == 'cancelled') {\r\n        return false\r\n      }\r\n      // 如果订单有pay_status字段，则根据pay_status判断\r\n      if (this.order.pay_status !== undefined) {\r\n        return this.order.pay_status === 1 || this.order.pay_status === '1' || this.order.pay_status === true;\r\n      }\r\n      // 如果订单有支付时间，认为已支付\r\n      if (this.order.pay_time || this.order.payTime) {\r\n        return true;\r\n      }\r\n      // 如果都没有，则根据订单状态判断（completed状态认为是已支付）\r\n      return this.order.status === 'completed';\r\n    }\r\n  },\r\n  \r\n  onLoad(options) {\r\n    console.log('接收到的参数:', options)\r\n    \r\n    // 获取订单号\r\n    const orderId = options.order_no || options.orderNo || options.id || '';\r\n    \r\n    console.log('页面接收到的订单ID:', orderId);\r\n    \r\n    // 保存订单ID，用于页面重新显示时刷新数据\r\n    this.orderId = orderId;\r\n    \r\n    if(orderId) {\r\n      // 显示加载中\r\n      uni.showLoading({\r\n        title: '加载中...'\r\n      });\r\n      \r\n      // 优先从API获取最新订单数据\r\n      if (!orderId.startsWith('temp_')) {\r\n        // 直接从API获取订单详情\r\n        console.log('直接从API获取订单详情');\r\n        this.fetchOrderDetail(orderId);\r\n      }\r\n    } else {\r\n      uni.showToast({\r\n        title: '订单ID不存在',\r\n        icon: 'none'\r\n      })\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 1500)\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 从API获取订单详情\r\n    fetchOrderDetail(orderId, silent = false) {\r\n      // 设置加载状态\r\n      this.loading = true;\r\n      \r\n      // 如果是临时ID，不进行API请求\r\n      if (orderId.startsWith('temp_')) {\r\n        console.log('临时ID不进行API请求');\r\n        if (!silent) {\r\n          uni.hideLoading();\r\n          uni.showToast({\r\n            title: '订单信息暂未同步',\r\n            icon: 'none'\r\n          });\r\n        }\r\n        this.loading = false;\r\n        return;\r\n      }\r\n      \r\n      // 直接使用订单号\r\n      const order_no = orderId;\r\n      \r\n      console.log('正在从API获取订单详情，订单号:', order_no);\r\n      getOrderDetail(order_no)\r\n        .then(res => {\r\n          console.log('API返回的订单详情:', res);\r\n          \r\n          if (res.code === 1 && res.data) {\r\n            // 处理订单数据\r\n            const orderData = res.data;\r\n            console.log('处理前的订单数据:', JSON.stringify(orderData));\r\n            \r\n            // 使用通用方法处理订单数据\r\n            setTimeout(() => {\r\n              this.processOrderData(orderData);\r\n              console.log('订单数据处理完成');\r\n              \r\n              // 初始化自动刷新（首次加载完成后）\r\n              this.startAutoRefresh();\r\n            }, 10);\r\n          } else {\r\n            uni.showToast({\r\n              title: res.msg || '订单不存在',\r\n              icon: 'none'\r\n            });\r\n            setTimeout(() => {\r\n              uni.navigateBack();\r\n            }, 1500);\r\n          }\r\n        })\r\n        .catch(err => {\r\n          console.error('获取订单详情失败:', err);\r\n          \r\n          uni.hideLoading();\r\n          \r\n          uni.showToast({\r\n            title: '获取订单详情失败',\r\n            icon: 'none'\r\n          });\r\n          setTimeout(() => {\r\n            uni.navigateBack();\r\n          }, 1500);\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n          if (!silent) {\r\n            uni.hideLoading();\r\n          }\r\n        });\r\n    },\r\n    \r\n    handleBack() {\r\n      // 返回到首页\r\n      uni.switchTab({\r\n        url: '/pages/order/order'\r\n      })\r\n    },\r\n    \r\n    // 手动刷新订单数据\r\n    refreshOrderData(silent = false) {\r\n      if (this.loading) {\r\n        if (!silent) {\r\n          uni.showToast({\r\n            title: '正在加载中...',\r\n            icon: 'none'\r\n          });\r\n        }\r\n        return;\r\n      }\r\n      \r\n      // 控制刷新频率，防止频繁请求\r\n      const now = Date.now();\r\n      if (now - this.lastRefreshTime < 3000) { // 3秒内不重复刷新\r\n        console.log('刷新过于频繁，跳过');\r\n        return;\r\n      }\r\n      \r\n      this.lastRefreshTime = now;\r\n      \r\n      if (this.orderId) {\r\n        if (!silent) {\r\n          uni.showLoading({\r\n            title: '刷新数据...'\r\n          });\r\n        }\r\n        console.log((silent ? '自动' : '手动') + '刷新订单数据:', this.orderId);\r\n        \r\n        // 设置临时loading状态但不影响UI\r\n        const tempLoading = this.loading;\r\n        this.fetchOrderDetail(this.orderId, silent);\r\n      } else {\r\n        if (!silent) {\r\n          uni.showToast({\r\n            title: '订单ID不存在',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理订单数据\r\n    processOrderData(orderData) {\r\n      try {\r\n        // 确保订单商品数据格式正确\r\n        if (orderData.order_products && Array.isArray(orderData.order_products)) {\r\n          // 使用接口返回的订单商品数据\r\n          orderData.order_products = orderData.order_products.map(product => ({\r\n            ...product,\r\n            image: product.image || '',\r\n            specs: product.props_text || product.specs || '',\r\n            count: product.quantity || product.count || 1,\r\n            price: parseFloat(product.price || product.product_price || 0)\r\n          }));\r\n          // 将order_products复制到products字段\r\n          orderData.products = [...orderData.order_products];\r\n        } else if (orderData.products && Array.isArray(orderData.products)) {\r\n          // 兼容旧数据格式\r\n          orderData.products = orderData.products.map(product => ({\r\n            ...product,\r\n            image: product.image || '',\r\n            specs: product.props_text || product.specs || '',\r\n            count: product.count || product.quantity || 1,\r\n            price: parseFloat(product.price || product.product_price || 0)\r\n          }));\r\n        } else {\r\n          orderData.products = [];\r\n        }\r\n        \r\n        // 确保价格字段为数字\r\n        orderData.total_price = parseFloat(orderData.total_price || 0);\r\n        orderData.final_price = parseFloat(orderData.final_price || orderData.totalPrice || orderData.total_price || 0);\r\n        orderData.discount_amount = parseFloat(orderData.discount_amount || 0);\r\n        \r\n        // 更新订单数据\r\n        this.order = orderData;\r\n        \r\n        return orderData;\r\n      } catch (error) {\r\n        console.error('处理订单数据失败:', error);\r\n        return orderData;\r\n      }\r\n    },\r\n    \r\n    getStatusDesc() {\r\n      const orderType = this.order.dining_type || this.order.type;\r\n      const orderStatus = this.order.status;\r\n      \r\n      // 如果订单数据不完整，返回空字符串\r\n      if (!orderType || !orderStatus) {\r\n        return '';\r\n      }\r\n      \r\n      // 根据不同状态和用餐方式返回不同描述\r\n      if (orderType === 'dine-in') {\r\n        // 堂食订单\r\n        switch (orderStatus) {\r\n          case 'paid':\r\n            return `您的订单已支付成功，正在安排制作，请耐心等待`;\r\n          case 'cooking':\r\n            return `您的订单正在制作中，预计3分钟后可取餐，取餐号：${this.order.pickup_code || ''}`;\r\n          case 'cooked':\r\n            return `您的订单已制作完成，请前往取餐区取餐，取餐号：${this.order.pickup_code || ''}`;\r\n          case 'completed':\r\n            return `感谢您的光临，期待您再次惠顾`;\r\n          default:\r\n            return `预计3分钟后可取餐，取餐号：${this.order.pickup_code || ''}`;\r\n        }\r\n      } else {\r\n        // 外卖订单\r\n        switch (orderStatus) {\r\n          case 'paid':\r\n            return `您的订单已支付成功，正在安排制作，请耐心等待`;\r\n          case 'cooking':\r\n            return `您的订单正在制作中，制作完成后将为您配送`;\r\n          case 'cooked':\r\n            return `您的订单已制作完成，正在等待配送员取餐`;\r\n          case 'delivering':\r\n            return `您的订单正在配送中，预计${this.order.estimate_time || '15'}分钟送达`;\r\n          case 'completed':\r\n            return `订单已送达，感谢您的惠顾`;\r\n          default:\r\n            return `预计15分钟送达，配送员：${this.order.delivery_name || ''}`;\r\n        }\r\n      }\r\n    },\r\n    \r\n    reorder() {\r\n      // 获取订单商品数据\r\n      const products = this.order.products || this.order.order_products || [];\r\n      \r\n      if (!this.order || !products || !Array.isArray(products) || products.length === 0) {\r\n        uni.showToast({\r\n          title: '订单数据异常',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 构建购物车数据\r\n      const cartData = {\r\n        list: products.map(item => {\r\n          const price = parseFloat(item.price || item.product_price || 0);\r\n          const count = parseInt(item.quantity || item.count || 1);\r\n          return {\r\n            id: item.product_id || item.id,\r\n            name: item.product_name || item.name,\r\n            price: price,\r\n            count: count,\r\n            totalPrice: price * count,\r\n          image: item.image || '',\r\n            specs: item.props_text || item.specs || '',\r\n          specSelected: true\r\n          };\r\n        }),\r\n        total: products.reduce((sum, item) => sum + parseInt(item.quantity || item.count || 1), 0),\r\n        price: parseFloat(this.order.total_price || this.order.totalPrice || 0)\r\n      }\r\n      \r\n      // 保存到本地存储\r\n      uni.setStorageSync('cartData', cartData)\r\n      \r\n      // 保存用餐方式\r\n      uni.setStorageSync('diningType', this.order.type)\r\n      \r\n      // 显示提示\r\n      uni.showToast({\r\n        title: '已添加到购物车',\r\n        icon: 'none',\r\n        duration: 1500,\r\n        success: () => {\r\n          // 跳转到菜单页\r\n          setTimeout(() => {\r\n            uni.switchTab({\r\n              url: '/pages/menu/menu'\r\n            })\r\n          }, 1500)\r\n        }\r\n      })\r\n    },\r\n    \r\n    checkProgress() {\r\n      // 如果正在加载数据，不允许查看进度\r\n      if (this.loading) {\r\n        uni.showToast({\r\n          title: '订单信息加载中...',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 未支付的订单不能查看进度\r\n      if (!this.isPaid) {\r\n        uni.showToast({\r\n          title: '请先完成支付',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '订单进度',\r\n        content: this.getProgressDesc(),\r\n        showCancel: false,\r\n        confirmText: '我知道了'\r\n      })\r\n    },\r\n    \r\n    // 评价订单\r\n    goComment() {\r\n      uni.showToast({\r\n        title: '评价功能开发中',\r\n        icon: 'none'\r\n      });\r\n      \r\n      // 可以跳转到评价页面，这里先用提示代替\r\n      // uni.navigateTo({\r\n      //   url: `/pages/order/comment?id=${this.order.order_no || this.order.orderNo}`\r\n      // });\r\n    },\r\n    \r\n    getProgressDesc() {\r\n      const orderType = this.order.dining_type || this.order.type;\r\n      const orderStatus = this.order.status;\r\n      \r\n      // 如果订单数据不完整，返回加载中提示\r\n      if (!orderType || !orderStatus) {\r\n        return '正在获取订单进度信息...';\r\n      }\r\n      \r\n      if(orderType === 'dine-in') {\r\n        // 堂食订单\r\n        switch(orderStatus) {\r\n          case 'paid':\r\n            return `您的堂食订单已支付成功\r\n正在安排制作\r\n预计2-3分钟后开始制作\r\n取餐号：${this.order.pickup_code || ''}`;\r\n          case 'cooking':\r\n            return `您的堂食订单正在制作中\r\n制作进度：50%\r\n预计5分钟后可以取餐\r\n取餐号：${this.order.pickup_code || ''}\r\n请前往取餐区等候`;\r\n          case 'cooked':\r\n            return `您的堂食订单已制作完成\r\n制作进度：100%\r\n请立即前往取餐区取餐\r\n取餐号：${this.order.pickup_code || ''}`;\r\n          case 'completed':\r\n            return `您的堂食订单已完成\r\n感谢您的光临，期待您再次惠顾`;\r\n          default:\r\n            return `您的堂食订单正在制作中\r\n制作进度：80%\r\n预计3分钟后可以取餐\r\n取餐号：${this.order.pickup_code || ''}\r\n请前往取餐区等候`;\r\n        }\r\n      } else {\r\n        // 外卖订单\r\n        switch(orderStatus) {\r\n          case 'paid':\r\n            return `您的外卖订单已支付成功\r\n正在安排制作\r\n预计2-3分钟后开始制作\r\n配送地址：${this.order.address || '未设置地址'}`;\r\n          case 'cooking':\r\n            return `您的外卖订单正在制作中\r\n制作进度：50%\r\n预计15分钟后开始配送\r\n配送地址：${this.order.address || '未设置地址'}`;\r\n          case 'cooked':\r\n            return `您的外卖订单已制作完成\r\n制作进度：100%\r\n正在等待骑手接单\r\n配送地址：${this.order.address || '未设置地址'}`;\r\n          case 'delivering':\r\n            return `您的外卖订单正在配送中\r\n配送员：${this.order.delivery_name || ''}\r\n联系电话：${this.order.delivery_phone || ''}\r\n预计${this.order.estimate_time || '10'}分钟送达\r\n配送地址：${this.order.address || '未设置地址'}`;\r\n          case 'completed':\r\n            return `您的外卖订单已送达\r\n感谢您的惠顾，期待您再次下单`;\r\n          default:\r\n            return `您的外卖订单正在配送中\r\n预计15分钟送达\r\n配送地址：${this.order.address || '未设置地址'}`;\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 去支付\r\n    goPay() {\r\n      // 获取订单号\r\n      const orderNo = this.order.order_no || this.order.orderNo;\r\n      if (!orderNo) {\r\n        uni.showToast({\r\n          title: '订单号不存在',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      // 弹出支付方式选择\r\n      uni.showActionSheet({\r\n        itemList: ['微信支付', '余额支付'],\r\n        success: async (res) => {\r\n          const payType = res.tapIndex;\r\n          \r\n          if (payType === 0) {\r\n            // 微信支付\r\n            this.wxPay(orderNo);\r\n          } else if (payType === 1) {\r\n            // 余额支付\r\n            this.balancePay(orderNo);\r\n          }\r\n        }\r\n      });\r\n    },\r\n    \r\n    // 微信支付\r\n    async wxPay(orderNo) {\r\n      try {\r\n        // 获取微信支付参数\r\n        const payResult = await getWxPayParams(orderNo);\r\n        uni.hideLoading();\r\n        \r\n        if (payResult.code !== 1) {\r\n          uni.showToast({\r\n            title: payResult.msg || '获取支付参数失败',\r\n            icon: 'none'\r\n          });\r\n          return;\r\n        }\r\n        \r\n        // 调用微信支付\r\n        uni.requestPayment({\r\n          ...payResult.data,\r\n          success: () => {\r\n            this.checkPaymentResult(orderNo);\r\n          },\r\n          fail: (err) => {\r\n            console.log('支付失败', err);\r\n            uni.showToast({\r\n              title: '支付已取消',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        });\r\n      } catch (error) {\r\n        uni.showToast({\r\n          title: '支付异常，请稍后再试',\r\n          icon: 'none'\r\n        });\r\n        console.error('支付出错', error);\r\n      }\r\n    },\r\n    \r\n    // 余额支付\r\n    async balancePay(orderNo) {\r\n      try {\r\n        uni.showLoading({ title: '处理中...' });\r\n        \r\n        // 调用余额支付API\r\n        const payResult = await payOrderWithBalance(orderNo);\r\n        uni.hideLoading();\r\n        \r\n        if (payResult.code === 1) {\r\n          uni.showToast({\r\n            title: '支付成功',\r\n            icon: 'none'\r\n          });\r\n          // 刷新订单数据\r\n          this.fetchOrderDetail(orderNo);\r\n        } else {\r\n          // 如果为2 跳转充值页面\r\n          if (payResult.code === 2) {\r\n            // 显示确认对话框，让用户选择是否跳转到充值页面\r\n            uni.showModal({\r\n              title: '余额不足',\r\n              content: '您的余额不足，是否前往充值？',\r\n              confirmText: '去充值',\r\n              cancelText: '取消',\r\n              success: (res) => {\r\n                if (res.confirm) {\r\n                  // 用户点击确认，跳转到充值页面\r\n                  uni.navigateTo({\r\n                    url: '/pages/my/recharge'\r\n                  });\r\n                }\r\n              }\r\n            });\r\n          }\r\n          uni.showToast({\r\n            title: payResult.msg || '余额不足',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '支付失败，请稍后再试',\r\n          icon: 'none'\r\n        });\r\n        console.error('余额支付出错', error);\r\n      }\r\n    },\r\n    \r\n    // 检查支付结果\r\n    async checkPaymentResult(orderNo) {\r\n      try {\r\n        uni.showLoading({ title: '正在查询支付结果...' });\r\n        \r\n        // 查询支付结果\r\n        const queryResult = await queryWxPayResult(orderNo);\r\n        uni.hideLoading();\r\n        \r\n        if (queryResult.code === 1 && queryResult.data && queryResult.data.pay_status === 1) {\r\n          uni.showToast({\r\n            title: '支付成功',\r\n            icon: 'none'\r\n          });\r\n          \r\n          // 刷新订单数据\r\n          this.fetchOrderDetail(orderNo);\r\n        } else {\r\n          uni.showToast({\r\n            title: queryResult.msg || '支付结果查询失败',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      } catch (error) {\r\n        uni.hideLoading();\r\n        uni.showToast({\r\n          title: '查询支付结果失败',\r\n          icon: 'none'\r\n        });\r\n        console.error('查询支付结果出错', error);\r\n      }\r\n    },\r\n    \r\n    startAutoRefresh() {\r\n      // 清除可能存在的定时器\r\n      this.clearAutoRefresh();\r\n      \r\n      // 设置新的定时器，每10秒刷新一次\r\n      this.refreshTimer = setInterval(() => {\r\n        console.log('定时刷新订单数据');\r\n        this.refreshOrderData(true); // 静默刷新\r\n      }, 10000); // 10秒\r\n      \r\n      // 保存引用以便于完全清除\r\n      this._refreshTimerId = this.refreshTimer;\r\n      \r\n      console.log('启动订单自动刷新，定时器ID:', this.refreshTimer);\r\n    },\r\n    \r\n    clearAutoRefresh() {\r\n      if (this.refreshTimer) {\r\n        clearInterval(this.refreshTimer);\r\n        this.refreshTimer = null;\r\n        console.log('停止订单自动刷新');\r\n      }\r\n      // 确保没有其他定时器在运行\r\n      if (typeof this._refreshTimerId !== 'undefined') {\r\n        clearInterval(this._refreshTimerId);\r\n        this._refreshTimerId = null;\r\n      }\r\n    },\r\n    \r\n    // 取消订单\r\n    cancelOrder() {\r\n      // 获取订单号\r\n      const orderNo = this.order.order_no || this.order.orderNo;\r\n      if (!orderNo) {\r\n        uni.showToast({\r\n          title: '订单号不存在',\r\n          icon: 'none'\r\n        });\r\n        return;\r\n      }\r\n      \r\n      uni.showModal({\r\n        title: '取消订单',\r\n        content: '确定要取消该订单吗？',\r\n        success: async (res) => {\r\n          if (res.confirm) {\r\n            try {\r\n              uni.showLoading({ title: '处理中...' });\r\n              \r\n              const result = await cancelOrder(orderNo);\r\n              uni.hideLoading();\r\n              \r\n              if (result.code === 1) {\r\n                uni.showToast({\r\n                  title: '订单已取消',\r\n                  icon: 'none'\r\n                });\r\n                \r\n                // 刷新订单数据\r\n                setTimeout(() => {\r\n                  this.fetchOrderDetail(orderNo);\r\n                }, 500);\r\n              } else {\r\n                uni.showToast({\r\n                  title: result.msg || '取消失败，请稍后再试',\r\n                  icon: 'none'\r\n                });\r\n              }\r\n            } catch (error) {\r\n              uni.hideLoading();\r\n              uni.showToast({\r\n                title: '取消订单失败',\r\n                icon: 'none'\r\n              });\r\n              console.error('取消订单出错', error);\r\n            }\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));\r\n}\r\n\r\n.header {\r\n  background: #fff;\r\n  padding-top: 88rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 0 30rpx;\r\n    \r\n    .left {\r\n      width: 88rpx;\r\n      height: 88rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      &:active {\r\n        opacity: 0.6;\r\n      }\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n      flex: 1;\r\n      text-align: center;\r\n    }\r\n    \r\n    .right {\r\n      width: 88rpx;\r\n      height: 88rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.status-card {\r\n  background: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n  padding: 40rpx 30rpx;\r\n  \r\n  &.unpaid-card {\r\n    background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);\r\n  }\r\n  &.paid-card {\r\n    background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);\r\n  }\r\n  &.cooking-card {\r\n    background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);\r\n  }\r\n  &.cooked-card {\r\n    background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);\r\n  }\r\n  &.delivering-card {\r\n    background: linear-gradient(135deg, #ff9500 0%, #ff6f00 100%);\r\n  }\r\n  &.completed-card {\r\n    background: linear-gradient(135deg, #6ab52e 0%, #8cd548 100%);\r\n  }\r\n  &.cancelled-card {\r\n    background: linear-gradient(135deg, #999999 0%, #666666 100%);\r\n  }\r\n  \r\n  .status-info {\r\n    text-align: center;\r\n    \r\n    .status {\r\n      font-size: 36rpx;\r\n      color: #fff;\r\n      font-weight: bold;\r\n      margin-bottom: 12rpx;\r\n      display: block;\r\n    }\r\n    \r\n    .desc {\r\n      font-size: 26rpx;\r\n      color: rgba(255, 255, 255, 0.9);\r\n    }\r\n    \r\n    .auto-refresh-tip {\r\n      margin-top: 16rpx;\r\n      padding: 6rpx 16rpx;\r\n      background-color: rgba(255, 255, 255, 0.2);\r\n      border-radius: 100rpx;\r\n      display: inline-block;\r\n      \r\n      text {\r\n        font-size: 22rpx;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.order-card, .product-card {\r\n  margin: 20rpx;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  \r\n  .card-title {\r\n    font-size: 30rpx;\r\n    color: #333;\r\n    font-weight: bold;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .info-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 16rpx 0;\r\n    \r\n    .label {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n    }\r\n    \r\n    .value {\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.product-list {\r\n  .product-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx 0;\r\n    border-bottom: 1px solid #f5f5f5;\r\n    \r\n    .product-name {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n    }\r\n    \r\n    .product-specs {\r\n      font-size: 24rpx;\r\n      color: #999;\r\n      margin: 0 20rpx;\r\n    }\r\n    \r\n    .product-price {\r\n      font-size: 28rpx;\r\n      color: #333;\r\n      \r\n      .count {\r\n        margin-left: 10rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.price-info {\r\n  margin-top: 20rpx;\r\n  padding-top: 20rpx;\r\n  border-top: 1px solid #f5f5f5;\r\n  \r\n  .price-item {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 12rpx 0;\r\n    \r\n    .label {\r\n      font-size: 26rpx;\r\n      color: #666;\r\n    }\r\n    \r\n            .value {\r\n          font-size: 26rpx;\r\n          color: #333;\r\n          \r\n          &.unpaid {\r\n            color: #ff8500;\r\n          }\r\n          \r\n          &.paid {\r\n            color: #6ab52e;\r\n          }\r\n        }\r\n    \r\n            &.total {\r\n          margin-top: 12rpx;\r\n          padding-top: 12rpx;\r\n          border-top: 1px dashed #eee;\r\n          \r\n          .label {\r\n            font-size: 28rpx;\r\n            color: #333;\r\n          }\r\n          \r\n          .value {\r\n            font-size: 32rpx;\r\n            color: #ff4444;\r\n            font-weight: bold;\r\n            \r\n            &.unpaid {\r\n              color: #ff8500;\r\n            }\r\n          }\r\n        }\r\n        \r\n        &.payment-status {\r\n          margin-top: 12rpx;\r\n          padding-top: 12rpx;\r\n          border-top: 1px dashed #eee;\r\n        }\r\n  }\r\n}\r\n\r\n.bottom-bar {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  padding: 20rpx 40rpx;\r\n  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\r\n  background: #fff;\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 20rpx;\r\n  \r\n      .btn {\r\n      padding: 20rpx 40rpx;\r\n      border-radius: 100rpx;\r\n      font-size: 28rpx;\r\n      \r\n      &.outline {\r\n        border: 1px solid #ddd;\r\n      }\r\n    \r\n    &.primary {\r\n      background: #8cd548;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=57d42baa&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=57d42baa&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309951\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}