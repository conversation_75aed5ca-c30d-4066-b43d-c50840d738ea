import request from '@/utils/request.js';

/**
 * 获取充值记录列表
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码，默认为1
 * @param {Number} params.limit - 每页记录数，默认为10
 * @param {String} params.status - 状态：0=失败，1=成功
 */
export function getRechargeList(params = {}) {
  return request({
    url: '/recharge/orders',
    method: 'GET',
    params
  });
}

/**
 * 创建充值记录
 * @param {Object} data - 充值数据
 * @param {Number} data.amount - 充值金额
 */
export function createRecharge(data) {
  return request({
    url: '/recharge/create',
    method: 'POST',
    data
  });
}

/**
 * 获取充值记录详情
 * @param {Number} id - 记录ID
 */
export function getRechargeDetail(id) {
  return request({
    url: '/recharge/detail',
    method: 'GET',
    params: { id }
  });
}

/**
 * 获取充值统计信息
 */
export function getRechargeStatistics() {
  return request({
    url: '/recharge/statistics',
    method: 'GET'
  });
}

/**
 * 获取充值套餐配置
 */
export function getRechargeConfigs() {
  return request({
    url: '/recharge/config',
    method: 'GET'
  });
} 