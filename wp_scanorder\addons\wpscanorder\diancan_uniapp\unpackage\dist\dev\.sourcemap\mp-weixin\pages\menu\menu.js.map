{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?1e40", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?8d8e", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?dffc", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?016a", "uni-app:///pages/menu/menu.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?b285", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/menu/menu.vue?0471"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MenuOpen", "ShoppingCart", "data", "showSpecSelector", "selectedProduct", "productList", "name", "spec", "price", "image", "storeInfo", "distance", "currentCategory", "categories", "diningType", "cartTotal", "cartPrice", "cartList", "showCart", "currentProducts", "loading", "merchant", "logo_image", "address", "phone", "monthly_sales", "business_hours", "rating", "announcement", "pendingProductInfo", "allProducts", "onLoad", "uni", "onUnload", "methods", "getCategories", "res", "categoriesData", "console", "categoryIndex", "title", "icon", "onSearch", "switchDiningType", "selectSpec", "id", "count", "totalPrice", "spec_type", "specs", "props_text", "properties", "values", "value", "is_default", "product", "number", "property", "prices", "prop", "closeSpecSelector", "switchCategory", "mask", "setTimeout", "openCart", "closeCart", "updateCart", "addToCart", "goToCheckout", "list", "total", "url", "handleAddToCart", "existingItem", "goToSearch", "animationType", "animationDuration", "loadCartData", "item", "hasFixed", "goToStoreDetail", "getProductList", "category_id", "categoryIdNum", "productsData", "tryOpenPendingProduct", "productToOpen", "handleAddToCartInModal", "merchantData", "categoryId", "productId", "onShow", "savedDiningType", "scrollToProductId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC6HjrB;AACA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC;QACAJ;QACAK;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAf;QACAgB;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACAC;;IAEA;IACAA;;IAEA;IACA;IACA;EACA;EACAC;IACA;IACAD;IACAA;EACA;EACAE;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAEA;gBACAC;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBAEA;gBACAC;;gBAEA;gBACA;kBACAC;oBAAA;kBAAA;kBACA;oBACAD;oBACA;kBACA;gBACA;;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAA;gBACAN;kBACAQ;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACAJ;IACA;IACA;IACAK;MACA;MACA;MACAX;IACA;IACA;IACAY;MACA;QACA;QACA;QACA;QACA;QACAN;QAEA;UACAO;UACAvC;UACAE;UACAC;UACAqC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;UACAC;YACA7C;YACA8C;cAAA;gBACAC;gBACAC;gBACA9C;cACA;YAAA;UACA;QACA;;QAEA;QACA;UACA;UACA2C;YACA7C;YACA8C;cAAA;gBACAC;gBACAC;cACA;YAAA;UACA;QACA;;QAEA;QACA;UACA;UACAH;YACA7C;YACA8C;cAAA;gBACAC;gBACAC;cACA;YAAA;UACA;QACA;;QAEA;QACA,uDACAC;UACAC;UACAC;UACAC;QAAA,EACA;;QAEA;QACA;UACA;YACAC;UACA;QACA;;QAEA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;;gBAEA;gBACA;gBACA7B;kBACAQ;kBACAsB;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxB;gBACA;gBACAyB;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA;0BAAA;0BAAA,OAEA;wBAAA;0BAAA;0BAAA;wBAAA;0BAAA;0BAAA;0BAEAzB;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA,CAEA;cAAA;gBAAA;gBAEAN;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAgC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACAlC;IACA;IAEA;IACAmC;MACA;MACA,2CACAZ;QACAhD;QACAuC;MAAA,EACA;MACA;MACA;MACA;IACA;IAEA;IACAsB;MACA;QACA;QACApC;UACAqC;UACAC;UACA9D;QACA;;QAEA;QACAwB;;QAEA;QACAA;UACAuC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;MAEA;QACA;QACAC;QACAA;MACA;QACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA1C;QACAuC;QACAI;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAvC;QACA;QACA;QACA;;QAEA;QACA;UACA;UACA;YACA;cACAwC;cACAxC;cACAyC;YACA;cACAD;cACAA;cACAxC;cACAyC;YACA;UACA;;UAEA;UACA;YACAzC;YACAN;cACAqC;cACAC;cACA9D;YACA;UACA;QACA;MACA;IACA;IAEA;IACAwE;MACAhD;QACAuC;QACAI;QACAC;MACA;IACA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;gBAEA7C;gBACAA;gBAAA;gBAAA,OAEA;kBAAA4C;gBAAA;cAAA;gBAAA9C;gBAEA;gBACAgD;gBACA;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;gBACA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA9C;gBACA;gBAAA;cAAA;gBAIA;gBACA;kBACA;kBACA;;kBAEA;kBACA;kBACA;oBACA/B;kBACA;oBACAA;kBACA;oBACAA;kBACA;oBACAA;kBACA;kBAEA,uCACAgD;oBACAhD;oBACAC;kBAAA;gBAEA;gBAEA8B;;gBAEA;gBACA;kBAAA;gBAAA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAN;kBACAQ;kBACAC;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACA4C;MAAA;MACA;;MAEA;MACA;QAAA;MAAA;MAEA;QACA/C;;QAEA;QACA;;QAEA;QACAyB;UACA;UACA;UACA;UACA;QACA;MACA;QACAzB;;QAEA;QACAgD;UAAA;QAAA;QAEA;UACAhD;UACA;UACA;YAAA;UAAA;UACA;YACAA;YACA;YACA;YACA;cACAyB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAwB;MAAA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAjF;UACA+C;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;;MAEA;MACA;QACAR;QACAvC;QACAE;QACAC;QACAqC;QACAC;QACAC;QACAC;UAAA;QAAA;QACAC;UAAA;QAAA;MACA;MAEA;MACA;IACA;EAAA,uFAGA4B;IACA;IACAxC;;IAEA;IACA;MACAwC;MACAxC;IACA;;IAEA;IACA;MACA;MACA;MACA;IACA;IAEA;MACA;MACAmC;MACAA;MACA;MACA;QACAA;MACA;MACAnC;IACA;MACA;MACA;MACAA;IACA;;IAEA;IACA;EACA,0FAGA;IACA;IACA;MACA;QACAwC;QACAxC;MACA;QACA;QACAwC;QACAA;QACAxC;MACA;IACA;;IAEA;IACA;MAAA;IAAA;IACA;MAAA;IAAA;;IAEA;IACA;MACA+B;MACAC;MACA9D;IACA;;IAEA;IACA8B;;IAEA;IACAN;;IAEA;IACA;IACA;;IAEA;IACAA;MACAQ;MACAC;IACA;EACA,0FAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAAL;cACAE;;cAEA;cAAA,MACAF;gBAAA;gBAAA;cAAA;cACAoD,yBAEA;cACA;gBACA3C;gBACAvC;gBACAgB;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;cACA;cAEAU;cAAA;cAAA;YAAA;cAAA,MAEA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAGAA;cACAN;gBACAQ;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,0FAEA;IACA;IACA;IACA;IACA;IACA;MACAc;IACA;EACA,kGAEArD;IACA;IAEAoC;;IAEA;IACA;MACA;QACAmD;QACAC;MACA;;MAEA;MACA;IACA;;IAEA;IACA;MAAA;IAAA;IACA;MACA;MACA;IACA;MACA;MACApD;MACA;IACA;EACA,aACA;EACAqD;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACAC;cACA;gBACA;gBACA;gBACA5D;cACA;;cAEA;cACA;;cAEA;cACA6D;cACA;gBACAvD;gBACA;gBACA;kBACAoD;gBACA;gBACA;gBACA;gBACA;gBACA1D;cACA;;cAEA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OAGA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;AC50BA;AAAA;AAAA;AAAA;AAAgxC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;;ACApyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/menu/menu.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/menu/menu.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./menu.vue?vue&type=template&id=368aef34&scoped=true&\"\nvar renderjs\nimport script from \"./menu.vue?vue&type=script&lang=js&\"\nexport * from \"./menu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./menu.vue?vue&type=style&index=0&id=368aef34&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"368aef34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/menu/menu.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=template&id=368aef34&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.cartTotal > 0 ? _vm.cartPrice.toFixed(2) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"wp_-flex-col page\">\r\n\t\t<!-- 顶部搜索和店铺信息 -->\r\n\t\t<view class=\"wp_-flex-col header\">\r\n\t\t\t<!-- 搜索框 -->\r\n\t\t\t<view class=\"wp_-flex-row search-wrapper\" @tap=\"goToSearch\">\r\n\t\t\t\t<view class=\"wp_-flex-row wp_-items-center wp_-flex-1 search-input\">\r\n\t\t\t\t\t<image class=\"search-icon\" src=\"/static/menu/117dfdcfc62a8651f86aa56c1db6c74a.png\" />\r\n\t\t\t\t\t<text class=\"search-placeholder\">请输入商品名称</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 店铺信息 -->\r\n\t\t\t<view class=\"wp_-flex-row wp_-justify-between store-info\">\r\n\t\t\t\t<view class=\"wp_-flex-col wp_-self-center\" @tap=\"goToStoreDetail\">\r\n\t\t\t\t\t<view class=\"wp_-flex-row wp_-items-center\">\r\n\t\t\t\t\t\t<text class=\"store-name\">{{merchant.name}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"wp_-mt-16 wp_-flex-row wp_-items-center\">\r\n\t\t\t\t\t\t<image class=\"distance-icon\" src=\"/static/menu/82a617e96551e74fc96528efc85baa41.png\" />\r\n\t\t\t\t\t\t<text class=\"distance-text\">月售{{merchant.monthly_sales}}单</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 堂食/外卖切换 -->\r\n\t\t\t\t<view class=\"dining-type\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"dining-type-item\" \r\n\t\t\t\t\t\t:class=\"{'dining-type-active': diningType === 'dine-in'}\"\r\n\t\t\t\t\t\t@click=\"switchDiningType('dine-in')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text>堂食</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"dining-type-item\"\r\n\t\t\t\t\t\t:class=\"{'dining-type-active': diningType === 'takeout'}\"\r\n\t\t\t\t\t\t@click=\"switchDiningType('takeout')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text>外卖</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 商品列表区域 -->\r\n\t\t<view class=\"menu-content\">\r\n\t\t\t<!-- 左侧分类菜单 -->\r\n\t\t\t<scroll-view scroll-y class=\"menu-sidebar\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"(category, index) in categories\" \r\n\t\t\t\t\t:key=\"category.id\"\r\n\t\t\t\t\t:class=\"['sidebar-item', currentCategory === index ? 'active' : '']\"\r\n\t\t\t\t\t@click=\"switchCategory(index)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text>{{category.name}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t\r\n\t\t\t<!-- 右侧商品列表 -->\r\n\t\t\t<scroll-view scroll-y class=\"product-list-container\">\r\n\t\t\t\t<!-- 分类标题 -->\r\n\t\t\t\t<text class=\"category-title\">{{categories[currentCategory].name}}</text>\r\n\t\t\t\t\r\n\t\t\t\t<!-- 商品列 -->\r\n\t\t\t\t<view class=\"product-list\">\r\n\t\t\t\t\t<view class=\"product-item\" v-for=\"(item, index) in currentProducts\" :key=\"index\">\r\n\t\t\t\t\t\t<image class=\"product-image\" :src=\"item.image || '/static/home/<USER>'\" />\r\n\t\t\t\t\t\t<view class=\"product-info\">\r\n\t\t\t\t\t\t\t<text class=\"product-name\">{{item.name}}</text>\r\n\t\t\t\t\t\t\t<view class=\"product-bottom\">\r\n\t\t\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"price-value\">{{item.price || '0.00'}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"select-btn\" @tap=\"selectSpec(item)\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"item.spec_type === 'multi'\" class=\"spec-btn\">选规格</text>\r\n\t\t\t\t\t\t\t\t\t<view v-else class=\"add-btn\">\r\n\t\t\t\t\t\t\t\t\t\t<image class=\"add-icon\" src=\"/static/menu/add.png\" />\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 规格选择弹窗 -->\r\n\t\t<menu-open \r\n\t\t\tv-if=\"showSpecSelector\" \r\n\t\t\t:product=\"selectedProduct\"\r\n\t\t\t@close=\"closeSpecSelector\"\r\n\t\t\t@add-to-cart=\"handleAddToCart\"\r\n\t\t></menu-open>\r\n\r\n\t\t<!-- 底部购物车栏 -->\r\n\t\t<view class=\"cart-bar\" v-if=\"cartTotal > 0\">\r\n\t\t\t<view class=\"wp_-flex-row wp_-items-center cart-content\">\r\n\t\t\t\t<view class=\"cart-left\" @tap=\"openCart\">\r\n\t\t\t\t\t<image class=\"cart-icon\" src=\"/static/menu/cart.png\" />\r\n\t\t\t\t\t<view class=\"cart-badge\">{{cartTotal}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"wp_-flex-1 cart-center\">\r\n\t\t\t\t\t<text class=\"price-symbol\">¥</text>\r\n\t\t\t\t\t<text class=\"price-value\">{{cartPrice.toFixed(2)}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"checkout-btn\" @tap=\"goToCheckout\">\r\n\t\t\t\t\t<text class=\"btn-text\">去结算</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 购物车弹窗 -->\r\n\t\t<shopping-cart \r\n\t\t\tv-if=\"showCart\"\r\n\t\t\t:cart-list=\"cartList\"\r\n\t\t\t:total-price=\"cartPrice\"\r\n\t\t\t@close=\"closeCart\"\r\n\t\t\t@update=\"updateCart\"\r\n\t\t/>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport MenuOpen from '../menu/menu_open.vue'\r\nimport ShoppingCart from './ShoppingCart.vue'\r\nimport { getCategories, getProducts } from '@/api/menu'\r\nimport { getMerchantInfo } from '@/api/merchant'\r\n\r\nexport default {\r\n\tcomponents: {\r\n\t\tMenuOpen,\r\n\t\tShoppingCart\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tshowSpecSelector: false, // 控制规格选择器显示\r\n\t\t\tselectedProduct: null, // 当前选中的商品\r\n\t\t\tproductList: [\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '暴打凤梨鸭屎香柠檬茶',\r\n\t\t\t\t\tspec: '600ml',\r\n\t\t\t\t\tprice: 18,\r\n\t\t\t\t\timage: '/static/menu/595f1342438e860b2989fb7a6b4614bc.png'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '招牌暴打渣男柠檬茶',\r\n\t\t\t\t\tspec: '600ml',\r\n\t\t\t\t\tprice: 18,\r\n\t\t\t\t\timage: '/static/menu/ade3ff4d605f82d824aedfaef156d804.png'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '芒果柠檬茶',\r\n\t\t\t\t\tspec: '600ml',\r\n\t\t\t\t\tprice: 15,\r\n\t\t\t\t\timage: '/static/menu/51a6a8b5890fe982ce6e69b5bcc9499e.png'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tstoreInfo: {\r\n\t\t\t\tname: 'KKmall京基店2楼188号',\r\n\t\t\t\tdistance: '300m'\r\n\t\t\t},\r\n\t\t\tcurrentCategory: 0, // 当前选中的分类\r\n\t\t\tcategories: [], // 分类列表\r\n\t\t\tdiningType: 'dine-in', // 用餐方式：dine-in(堂食) / takeout(外卖)\r\n\t\t\tcartTotal: 0, // 购物车商品总数\r\n\t\t\tcartPrice: 0, // 购物车总价\r\n\t\t\tcartList: [], // 购物车商品列表\r\n\t\t\tshowCart: false, // 控制物弹窗显示\r\n\t\t\tcurrentProducts: [], // 当前分类的商品列表\r\n\t\t\tloading: false, // 加载状态\r\n\t\t\tmerchant: {\r\n\t\t\t\tname: '',\r\n\t\t\t\tlogo_image: '',\r\n\t\t\t\taddress: '',\r\n\t\t\t\tphone: '',\r\n\t\t\t\tmonthly_sales: 0,\r\n\t\t\t\tbusiness_hours: '',\r\n\t\t\t\trating: 5.0,\r\n\t\t\t\tannouncement: ''\r\n\t\t\t},\r\n\t\t\tpendingProductInfo: null, // 存储待打开的商品信息\r\n\t\t\tallProducts: [] // 所有分类的商品缓存\r\n\t\t}\r\n\t},\r\n\tonLoad() {\r\n\t\t// 监听清空购物车事件\r\n\t\tuni.$on('clearCart', this.handleClearCart)\r\n\t\t\r\n\t\t// 监听从首页跳转过来时切换分类的事件\r\n\t\tuni.$on('switchCategory', this.handleSwitchCategory)\r\n\t\t\r\n\t\t// 初始化时就获取商户信息和分类数据\r\n\t\tthis.getMerchantInfo()\r\n\t\tthis.getCategories()\r\n\t},\r\n\tonUnload() {\r\n\t\t// 移除事件监听\r\n\t\tuni.$off('clearCart', this.handleClearCart)\r\n\t\tuni.$off('switchCategory', this.handleSwitchCategory)\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取分类列表\r\n\t\tasync getCategories() {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await getCategories()\r\n\t\t\t\t\r\n\t\t\t\t// 根据接口返回的实际数据结构调整取值路径\r\n\t\t\t\tlet categoriesData = []\r\n\t\t\t\tif (res && res.code === 1) {\r\n\t\t\t\t\tif (res.data && Array.isArray(res.data.list)) {\r\n\t\t\t\t\t\tcategoriesData = res.data.list\r\n\t\t\t\t\t} else if (res.data && Array.isArray(res.data)) {\r\n\t\t\t\t\t\tcategoriesData = res.data\r\n\t\t\t\t\t} else if (Array.isArray(res.list)) {\r\n\t\t\t\t\t\tcategoriesData = res.list\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.categories = categoriesData\r\n\t\t\t\tconsole.log('获取到的分类数据:', this.categories)\r\n\t\t\t\t\r\n\t\t\t\t// 如果有待处理的商品信息，检查对应的分类\r\n\t\t\t\tif (this.pendingProductInfo && this.pendingProductInfo.categoryId) {\r\n\t\t\t\t\tconst categoryIndex = this.categories.findIndex(cat => cat.id === this.pendingProductInfo.categoryId)\r\n\t\t\t\t\tif (categoryIndex > -1 && categoryIndex !== this.currentCategory) {\r\n\t\t\t\t\t\tconsole.log('处理待打开商品的分类切换:', categoryIndex)\r\n\t\t\t\t\t\tthis.currentCategory = categoryIndex\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 如果有分类数据，自动获取第一个分类的商品\r\n\t\t\t\tif (this.categories.length > 0) {\r\n\t\t\t\t\tawait this.getProductList()\r\n\t\t\t\t}\r\n\t\t\t} catch(e) {\r\n\t\t\t\tconsole.error('获取分类失败:', e)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取分类失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 搜索商品\r\n\t\tonSearch(keyword) {\r\n\t\t\tconsole.log('搜索:', keyword)\r\n\t\t},\r\n\t\t// 切换堂食/外卖\r\n\t\tswitchDiningType(type) {\r\n\t\t\tthis.diningType = type\r\n\t\t\t// 将当前选择的用餐方式保存到本地存储\r\n\t\t\tuni.setStorageSync('diningType', type)\r\n\t\t},\r\n\t\t// 选择商品规格\r\n\t\tselectSpec(product) {\r\n\t\t\tif (product.spec_type === 'single') {\r\n\t\t\t\t// 单规格商品直接加入购物车\r\n\t\t\t\tconst price = parseFloat(product.price || 0);\r\n\t\t\t\t// 确保规格信息正确\r\n\t\t\t\tconst specText = product.spec || product.props_text || '默认规格';\r\n\t\t\t\tconsole.log('单规格商品添加购物车，规格信息:', specText);\r\n\t\t\t\t\r\n\t\t\t\tthis.handleAddToCart({\r\n\t\t\t\t\tid: product.id,\r\n\t\t\t\t\tname: product.name,\r\n\t\t\t\t\tprice: price,\r\n\t\t\t\t\timage: product.image,\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\ttotalPrice: price,\r\n\t\t\t\t\tspec_type: 'single',\r\n\t\t\t\t\tspecs: specText,\r\n\t\t\t\t\tprops_text: specText\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\t// 多规格商品，打开选择器并传递规格数据\r\n\t\t\t\tconst specConfig = product.spec_config || {}\r\n\t\t\t\t\r\n\t\t\t\t// 构建规格属性数据\r\n\t\t\t\tconst properties = []\r\n\t\t\t\t\r\n\t\t\t\t// 添加规格选项\r\n\t\t\t\tif (specConfig.规格 && (Array.isArray(specConfig.规格) ? specConfig.规格.length > 0 : true)) {\r\n\t\t\t\t\tconst specs = Array.isArray(specConfig.规格) ? specConfig.规格 : [specConfig.规格];\r\n\t\t\t\t\tproperties.push({\r\n\t\t\t\t\t\tname: '规格',\r\n\t\t\t\t\t\tvalues: specs.map(item => ({\r\n\t\t\t\t\t\t\tvalue: item,\r\n\t\t\t\t\t\t\tis_default: false,\r\n\t\t\t\t\t\t\tprice: (specConfig.价格 && specConfig.价格[item]) ? parseFloat(specConfig.价格[item]) : parseFloat(product.price || 0)\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 添加温度选项\r\n\t\t\t\tif (specConfig.温度 && (Array.isArray(specConfig.温度) ? specConfig.温度.length > 0 : true)) {\r\n\t\t\t\t\tconst temps = Array.isArray(specConfig.温度) ? specConfig.温度 : [specConfig.温度];\r\n\t\t\t\t\tproperties.push({\r\n\t\t\t\t\t\tname: '温度',\r\n\t\t\t\t\t\tvalues: temps.map(item => ({\r\n\t\t\t\t\t\t\tvalue: item,\r\n\t\t\t\t\t\t\tis_default: false\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 添加甜度选项\r\n\t\t\t\tif (specConfig.甜度 && (Array.isArray(specConfig.甜度) ? specConfig.甜度.length > 0 : true)) {\r\n\t\t\t\t\tconst sweetness = Array.isArray(specConfig.甜度) ? specConfig.甜度 : [specConfig.甜度];\r\n\t\t\t\t\tproperties.push({\r\n\t\t\t\t\t\tname: '甜度',\r\n\t\t\t\t\t\tvalues: sweetness.map(item => ({\r\n\t\t\t\t\t\t\tvalue: item,\r\n\t\t\t\t\t\t\tis_default: false\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 设置选中商品数据\r\n\t\t\t\tthis.selectedProduct = {\r\n\t\t\t\t\t...product,\r\n\t\t\t\t\tnumber: 1,\r\n\t\t\t\t\tproperty: properties,\r\n\t\t\t\t\tprices: specConfig.价格 || {}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 设置每个属性的第一个选项为默认选中\r\n\t\t\t\tthis.selectedProduct.property.forEach(prop => {\r\n\t\t\t\t\tif (prop.values.length > 0) {\r\n\t\t\t\t\t\tprop.values[0].is_default = true\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t// 打开规格选择器\r\n\t\t\t\tthis.showSpecSelector = true\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 关闭规格择器\r\n\t\tcloseSpecSelector() {\r\n\t\t\tthis.showSpecSelector = false\r\n\t\t\tthis.selectedProduct = null\r\n\t\t},\r\n\t\t// 切换分类\r\n\t\tasync switchCategory(index) {\r\n\t\t\tif (this.currentCategory === index) return\r\n\t\t\tthis.currentCategory = index\r\n\t\t\t\r\n\t\t\t// 清空当前商品列表，显示加载中\r\n\t\t\tthis.currentProducts = []\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中...',\r\n\t\t\t\tmask: true\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tawait this.getProductList()\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('切换分类加载商品失败:', e)\r\n\t\t\t\t// 如果失败，尝试重新加载一次\r\n\t\t\t\tsetTimeout(async () => {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tawait this.getProductList()\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error('重试加载商品失败:', error)\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000)\r\n\t\t\t} finally {\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 打开购物车\r\n\t\topenCart() {\r\n\t\t\tthis.showCart = true\r\n\t\t},\r\n\t\t\r\n\t\t// 关闭购物车\r\n\t\tcloseCart() {\r\n\t\t\tthis.showCart = false\r\n\t\t},\r\n\t\t\r\n\t\t// 更新购物车\r\n\t\tupdateCart(data) {\r\n\t\t\tthis.cartList = data.list\r\n\t\t\tthis.cartTotal = data.total\r\n\t\t\tthis.cartPrice = data.price\r\n\t\t\t// 同步到本地存储\r\n\t\t\tuni.setStorageSync('cartData', data)\r\n\t\t},\r\n\t\t\r\n\t\t// 添加到购物车\r\n\t\taddToCart(product, spec) {\r\n\t\t\t// 添加商品到购物车\r\n\t\t\tconst item = {\r\n\t\t\t\t...product,\r\n\t\t\t\tspec,\r\n\t\t\t\tcount: 1\r\n\t\t\t}\r\n\t\t\tthis.cartList.push(item)\r\n\t\t\tthis.cartTotal++\r\n\t\t\tthis.cartPrice += Number(product.price)\r\n\t\t},\r\n\t\t\r\n\t\t// 去结算\r\n\t\tgoToCheckout() {\r\n\t\t\tif (this.cartTotal > 0) {\r\n\t\t\t\t// 保存购车数据\r\n\t\t\t\tuni.setStorageSync('cartData', {\r\n\t\t\t\t\tlist: this.cartList,\r\n\t\t\t\t\ttotal: this.cartTotal,\r\n\t\t\t\t\tprice: this.cartPrice\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t\t// 保存当前的用餐方式\r\n\t\t\t\tuni.setStorageSync('diningType', this.diningType)\r\n\t\t\t\t\r\n\t\t\t\t// 跳转到订单提交页面\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order/orderSubmit'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 处理添加购物车\r\n\t\thandleAddToCart(item) {\r\n\t\t\t// 检查购物车是否已有相同商品（包括规格）\r\n\t\t\tconst existingItem = this.cartList.find(cartItem => {\r\n\t\t\t\tif (cartItem.id !== item.id) return false\r\n\t\t\t\tif (item.spec_type === 'single') return true\r\n\t\t\t\treturn cartItem.props_text === item.props_text\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\tif (existingItem) {\r\n\t\t\t\t// 如果已存在相同规格的商品，增加数量\r\n\t\t\t\texistingItem.count += item.count\r\n\t\t\t\texistingItem.totalPrice = Number(existingItem.price) * existingItem.count\r\n\t\t\t} else {\r\n\t\t\t\t// 如果不存在，添加新商品\r\n\t\t\t\tthis.cartList.push(item)\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 计算总数和总价\r\n\t\t\tthis.updateCartTotal()\r\n\t\t},\r\n\t\t\r\n\t\t// 跳转到搜索页面\r\n\t\tgoToSearch() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/search/search',\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 加载购物车数据\r\n\t\tloadCartData() {\r\n\t\t\tconst cartData = uni.getStorageSync('cartData')\r\n\t\t\tif (cartData) {\r\n\t\t\t\tconsole.log('从本地加载购物车数据:', JSON.stringify(cartData));\r\n\t\t\t\tthis.cartList = cartData.list || []\r\n\t\t\t\tthis.cartTotal = cartData.total || 0\r\n\t\t\t\tthis.cartPrice = cartData.price || 0\r\n\t\t\t\t\r\n\t\t\t\t// 检查并修复从本地加载的购物车数据中的规格信息\r\n\t\t\t\tif (this.cartList.length > 0) {\r\n\t\t\t\t\tlet hasFixed = false;\r\n\t\t\t\t\tthis.cartList.forEach(item => {\r\n\t\t\t\t\t\tif (!item.props_text && item.specs) {\r\n\t\t\t\t\t\t\titem.props_text = item.specs;\r\n\t\t\t\t\t\t\tconsole.log('修复加载的购物车商品规格:', item.name, item.props_text);\r\n\t\t\t\t\t\t\thasFixed = true;\r\n\t\t\t\t\t\t} else if (!item.props_text) {\r\n\t\t\t\t\t\t\titem.props_text = '默认规格';\r\n\t\t\t\t\t\t\titem.specs = '默认规格';\r\n\t\t\t\t\t\t\tconsole.log('为加载的购物车商品设置默认规格:', item.name);\r\n\t\t\t\t\t\t\thasFixed = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果有修复，更新到本地存储\r\n\t\t\t\t\tif (hasFixed) {\r\n\t\t\t\t\t\tconsole.log('更新修复后的购物车数据到本地存储');\r\n\t\t\t\t\t\tuni.setStorageSync('cartData', {\r\n\t\t\t\t\t\t\tlist: this.cartList,\r\n\t\t\t\t\t\t\ttotal: this.cartTotal,\r\n\t\t\t\t\t\t\tprice: this.cartPrice\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 添加跳转到店铺详情的方法\r\n\t\tgoToStoreDetail() {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: `/pages/store/detail?id=${this.merchant.id}`,\r\n\t\t\t\tanimationType: 'slide-in-right',\r\n\t\t\t\tanimationDuration: 300\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\t// 获取商品列表\r\n\t\tasync getProductList() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tconst category_id = this.categories[this.currentCategory]?.id\r\n\t\t\t\tif (!category_id) return\r\n\t\t\t\t\r\n\t\t\t\t// 确保category_id是整数类型，与后端接口要求匹配\r\n\t\t\t\tconst categoryIdNum = parseInt(category_id) || 0\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('获取商品列表，原始分类ID:', category_id, '类型:', typeof category_id)\r\n\t\t\t\tconsole.log('获取商品列表，转换后分类ID:', categoryIdNum, '类型:', typeof categoryIdNum)\r\n\t\t\t\t\r\n\t\t\t\tconst res = await getProducts({ category_id: categoryIdNum })\r\n\t\t\t\t\r\n\t\t\t\t// 根据接口返回的实际数据结构调整取值路径\r\n\t\t\t\tlet productsData = []\r\n\t\t\t\tif (res && res.code === 1) {\r\n\t\t\t\t\tif (res.data && Array.isArray(res.data.list)) {\r\n\t\t\t\t\t\tproductsData = res.data.list\r\n\t\t\t\t\t} else if (res.data && Array.isArray(res.data)) {\r\n\t\t\t\t\t\tproductsData = res.data\r\n\t\t\t\t\t} else if (Array.isArray(res.list)) {\r\n\t\t\t\t\t\tproductsData = res.list\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (productsData.length === 0) {\r\n\t\t\t\t\tconsole.log('当前分类没有商品数据')\r\n\t\t\t\t\tthis.currentProducts = []\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 处理商品数据，只处理价格信息\r\n\t\t\t\tthis.currentProducts = productsData.map(product => {\r\n\t\t\t\t\t// 确保价格为数字\r\n\t\t\t\t\tconst price = parseFloat(product.price || 0).toFixed(2);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 保留规格信息但不显示\r\n\t\t\t\t\tlet spec = '';\r\n\t\t\t\t\tif (product.spec) {\r\n\t\t\t\t\t\tspec = product.spec;\r\n\t\t\t\t\t} else if (product.props_text) {\r\n\t\t\t\t\t\tspec = product.props_text;\r\n\t\t\t\t\t} else if (product.spec_config && product.spec_config.规格) {\r\n\t\t\t\t\t\tspec = Array.isArray(product.spec_config.规格) ? product.spec_config.规格[0] : product.spec_config.规格;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tspec = '默认规格';\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\t...product,\r\n\t\t\t\t\t\tspec,\r\n\t\t\t\t\t\tprice\r\n\t\t\t\t\t};\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('处理后的商品数据:', this.currentProducts)\r\n\t\t\t\t\r\n\t\t\t\t// 存储到所有商品缓存中\r\n\t\t\t\tthis.allProducts = [...this.allProducts.filter(p => p.category_id !== categoryIdNum), ...this.currentProducts]\r\n\t\t\t\t\r\n\t\t\t\t// 如果有待打开的商品，检查是否在当前分类中\r\n\t\t\t\tthis.tryOpenPendingProduct()\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取商品列表失败:', e)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取商品列表失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\t// 网络错误时清空商品列表\r\n\t\t\t\tthis.currentProducts = []\r\n\t\t\t} finally {\r\n\t\t\t\tthis.loading = false\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 尝试打开待处理的商品\r\n\t\ttryOpenPendingProduct() {\r\n\t\t\tif (!this.pendingProductInfo || !this.pendingProductInfo.productId) return\r\n\t\t\t\r\n\t\t\t// 当前分类的商品中查找\r\n\t\t\tlet productToOpen = this.currentProducts.find(p => p.id == this.pendingProductInfo.productId)\r\n\t\t\t\r\n\t\t\tif (productToOpen) {\r\n\t\t\t\tconsole.log('在当前分类中找到待打开的商品:', productToOpen.name)\r\n\t\t\t\t\r\n\t\t\t\t// 确保购物车弹窗已关闭\r\n\t\t\t\tthis.showCart = false\r\n\t\t\t\t\r\n\t\t\t\t// 延迟一点打开商品详情，确保UI渲染完成\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t// 打开商品详情\r\n\t\t\t\t\tthis.selectSpec(productToOpen)\r\n\t\t\t\t\t// 清空待处理信息\r\n\t\t\t\t\tthis.pendingProductInfo = null\r\n\t\t\t\t}, 200)\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('当前分类未找到商品ID:', this.pendingProductInfo.productId)\r\n\t\t\t\t\r\n\t\t\t\t// 尝试在所有已加载的商品中查找\r\n\t\t\t\tproductToOpen = this.allProducts.find(p => p.id == this.pendingProductInfo.productId)\r\n\t\t\t\t\r\n\t\t\t\tif (productToOpen) {\r\n\t\t\t\t\tconsole.log('在其他分类中找到待打开的商品:', productToOpen.name)\r\n\t\t\t\t\t// 切换到对应分类\r\n\t\t\t\t\tconst categoryIndex = this.categories.findIndex(cat => cat.id === productToOpen.category_id)\r\n\t\t\t\t\tif (categoryIndex > -1 && categoryIndex !== this.currentCategory) {\r\n\t\t\t\t\t\tconsole.log('切换到商品所在分类:', categoryIndex)\r\n\t\t\t\t\t\tthis.currentCategory = categoryIndex\r\n\t\t\t\t\t\t// 获取分类商品并在回调中打开商品详情\r\n\t\t\t\t\t\tthis.getProductList().then(() => {\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t// 确保购物车弹窗关闭\r\n\t\t\t\t\t\t\t\tthis.showCart = false\r\n\t\t\t\t\t\t\t\t// 打开商品详情\r\n\t\t\t\t\t\t\t\tthis.selectSpec(productToOpen)\r\n\t\t\t\t\t\t\t\t// 清空待处理信息\r\n\t\t\t\t\t\t\t\tthis.pendingProductInfo = null\r\n\t\t\t\t\t\t\t}, 200)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\t// 修改模态框添加到购物车的方法\r\n\t\thandleAddToCartInModal() {\r\n\t\t\t// 获取选中的规格\r\n\t\t\tconst selectedProps = this.selectedProduct.property.map(prop => {\r\n\t\t\t\tconst selected = prop.values.find(v => v.is_default)\r\n\t\t\t\treturn {\r\n\t\t\t\t\tname: prop.name,\r\n\t\t\t\t\tvalue: selected ? selected.value : ''\r\n\t\t\t\t}\r\n\t\t\t})\r\n\r\n\t\t\t// 获取规格对应的价格\r\n\t\t\tconst size = selectedProps.find(p => p.name === '规格')?.value\r\n\t\t\tconst price = this.selectedProduct.prices[size] || this.selectedProduct.price\r\n\r\n\t\t\t// 构建购物车商品数据\r\n\t\t\tconst cartItem = {\r\n\t\t\t\tid: this.selectedProduct.id,\r\n\t\t\t\tname: this.selectedProduct.name,\r\n\t\t\t\tprice: Number(price),\r\n\t\t\t\timage: this.selectedProduct.image,\r\n\t\t\t\tcount: this.selectedProduct.number,\r\n\t\t\t\ttotalPrice: Number(price) * this.selectedProduct.number,\r\n\t\t\t\tspec_type: 'multi',\r\n\t\t\t\tspecs: selectedProps.map(p => `${p.name}:${p.value}`).join('，'),\r\n\t\t\t\tprops_text: selectedProps.map(p => `${p.name}:${p.value}`).join('，')\r\n\t\t\t}\r\n\r\n\t\t\tthis.handleAddToCart(cartItem)\r\n\t\t\tthis.closeSpecSelector()\r\n\t\t},\r\n\t\t\r\n\t\t// 修改购物车商品匹配逻辑\r\n\t\thandleAddToCart(item) {\r\n\t\t\t// 确保规格信息正确设置\r\n\t\t\tconsole.log('添加购物车，商品数据:', JSON.stringify(item));\r\n\t\t\t\r\n\t\t\t// 确保props_text字段存在\r\n\t\t\tif (!item.props_text && item.specs) {\r\n\t\t\t\titem.props_text = item.specs;\r\n\t\t\t\tconsole.log('补充props_text字段:', item.props_text);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查购物车是否已有相同商品（包括规格）\r\n\t\t\tconst existingItem = this.cartList.find(cartItem => {\r\n\t\t\t\tif (cartItem.id !== item.id) return false\r\n\t\t\t\tif (item.spec_type === 'single') return true\r\n\t\t\t\treturn cartItem.props_text === item.props_text\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\tif (existingItem) {\r\n\t\t\t\t// 如果已存在相同规格的商品，增加数量\r\n\t\t\t\texistingItem.count += item.count\r\n\t\t\t\texistingItem.totalPrice = Number(existingItem.price) * existingItem.count\r\n\t\t\t\t// 确保existingItem的props_text字段\r\n\t\t\t\tif (!existingItem.props_text && existingItem.specs) {\r\n\t\t\t\t\texistingItem.props_text = existingItem.specs;\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log('更新购物车已有商品:', JSON.stringify(existingItem));\r\n\t\t\t} else {\r\n\t\t\t\t// 如果不存在，添加新商品\r\n\t\t\t\tthis.cartList.push(item)\r\n\t\t\t\tconsole.log('添加新商品到购物车:', JSON.stringify(item));\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 计算总数和总价\r\n\t\t\tthis.updateCartTotal()\r\n\t\t},\r\n\t\t\r\n\t\t// 修改购物车商品匹配逻辑\r\n\t\tupdateCartTotal() {\r\n\t\t\t// 先检查并修复购物车中的每个商品是否都有props_text字段\r\n\t\t\tthis.cartList.forEach(item => {\r\n\t\t\t\tif (!item.props_text && item.specs) {\r\n\t\t\t\t\titem.props_text = item.specs;\r\n\t\t\t\t\tconsole.log('修复购物车商品缺失的props_text字段:', item.id, item.props_text);\r\n\t\t\t\t} else if (!item.props_text) {\r\n\t\t\t\t\t// 如果没有规格信息，设置为默认值\r\n\t\t\t\t\titem.props_text = '默认规格';\r\n\t\t\t\t\titem.specs = '默认规格';\r\n\t\t\t\t\tconsole.log('设置商品默认规格:', item.id);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 计算总数和总价\r\n\t\t\tconst total = this.cartList.reduce((sum, item) => sum + item.count, 0)\r\n\t\t\tconst price = this.cartList.reduce((sum, item) => sum + item.totalPrice, 0)\r\n\t\t\t\r\n\t\t\t// 更新购物车数据\r\n\t\t\tconst newCartData = {\r\n\t\t\t\tlist: this.cartList,\r\n\t\t\t\ttotal,\r\n\t\t\t\tprice\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 输出完整的购物车数据用于调试\r\n\t\t\tconsole.log('保存到本地存储的购物车数据:', JSON.stringify(newCartData));\r\n\t\t\t\r\n\t\t\t// 更新本地存储\r\n\t\t\tuni.setStorageSync('cartData', newCartData)\r\n\t\t\t\r\n\t\t\t// 更新页面数据\r\n\t\t\tthis.cartTotal = total\r\n\t\t\tthis.cartPrice = price\r\n\t\t\t\r\n\t\t\t// 显示添加成功提示\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '已加入购物车',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\tasync getMerchantInfo() {\r\n\t\t\ttry {\r\n\t\t\t\tconst res = await getMerchantInfo()\r\n\t\t\t\tconsole.log('获取到的商户信息:', res)\r\n\t\t\t\t\r\n\t\t\t\t// 检查API返回的数据结构\r\n\t\t\t\tif (res && res.code === 1 && res.data) {\r\n\t\t\t\t\tconst merchantData = res.data\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 从data中提取商户信息\r\n\t\t\t\t\tthis.merchant = {\r\n\t\t\t\t\t\tid: merchantData.id || 0,\r\n\t\t\t\t\t\tname: merchantData.name || '',\r\n\t\t\t\t\t\tlogo_image: merchantData.logo_image || '',\r\n\t\t\t\t\t\taddress: merchantData.address || '',\r\n\t\t\t\t\t\tphone: merchantData.phone || '',\r\n\t\t\t\t\t\tmonthly_sales: merchantData.monthly_sales || 0,\r\n\t\t\t\t\t\tbusiness_hours: merchantData.business_hours || '',\r\n\t\t\t\t\t\trating: Number(merchantData.rating) || 5.0,\r\n\t\t\t\t\t\tannouncement: merchantData.announcement || ''\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tconsole.log('处理后的商户信息:', this.merchant)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error('API返回数据格式错误')\r\n\t\t\t\t}\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('获取商户信息失败:', e)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取商户信息失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 清空购物车处理方法\r\n\t\thandleClearCart() {\r\n\t\t\tthis.cartList = []\r\n\t\t\tthis.cartTotal = 0\r\n\t\t\tthis.cartPrice = 0\r\n\t\t\t// 更新商品数量状态\r\n\t\t\tthis.productList.forEach(product => {\r\n\t\t\t\tproduct.number = 0\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 处理从首页跳转切换分类的事件\r\n\t\thandleSwitchCategory(data) {\r\n\t\t\tif (!data || !data.categoryId) return\r\n\t\t\t\r\n\t\t\tconsole.log('接收到切换分类事件:', data)\r\n\t\t\t\r\n\t\t\t// 存储待打开的商品信息\r\n\t\t\tif (data.productId) {\r\n\t\t\t\tthis.pendingProductInfo = {\r\n\t\t\t\t\tcategoryId: data.categoryId,\r\n\t\t\t\t\tproductId: data.productId\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 先关闭可能打开的购物车弹窗\r\n\t\t\t\tthis.showCart = false\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 找到对应分类的索引\r\n\t\t\tconst categoryIndex = this.categories.findIndex(cat => cat.id === data.categoryId)\r\n\t\t\tif (categoryIndex > -1) {\r\n\t\t\t\t// 切换到对应分类\r\n\t\t\t\tthis.switchCategory(categoryIndex)\r\n\t\t\t} else {\r\n\t\t\t\t// 如果还没有获取到分类数据，先保存要切换的分类ID\r\n\t\t\t\tconsole.log('分类数据未加载，记录待切换的分类')\r\n\t\t\t\t// 在getCategories完成后会处理pendingProductInfo\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tasync onShow() {\r\n\t\t// 检查是否有外卖选择状态\r\n\t\tconst savedDiningType = uni.getStorageSync('diningType')\r\n\t\tif (savedDiningType) {\r\n\t\t\tthis.diningType = savedDiningType\r\n\t\t\t// 清除保存的状态\r\n\t\t\tuni.removeStorageSync('diningType')\r\n\t\t}\r\n\r\n\t\t// 加载购物车数据\r\n\t\tthis.loadCartData()\r\n\t\t\r\n\t\t// 检查本地存储中是否有待打开的商品ID\r\n\t\tconst scrollToProductId = uni.getStorageSync('scrollToProductId')\r\n\t\tif (scrollToProductId) {\r\n\t\t\tconsole.log('从本地存储获取到待打开商品ID:', scrollToProductId)\r\n\t\t\t// 保存待打开的商品信息\r\n\t\t\tthis.pendingProductInfo = {\r\n\t\t\t\tproductId: scrollToProductId\r\n\t\t\t}\r\n\t\t\t// 关闭购物车弹窗，避免冲突\r\n\t\t\tthis.showCart = false\r\n\t\t\t// 清除本地存储\r\n\t\t\tuni.removeStorageSync('scrollToProductId')\r\n\t\t}\r\n\t\t\r\n\t\t// 每次进入页面时都重新获取最新数据\r\n\t\t// 先获取商户信息\r\n\t\tawait this.getMerchantInfo()\r\n\t\t\r\n\t\t// 获取分类数据\r\n\t\tawait this.getCategories()\r\n\t\t\r\n\t\t// 如果有分类数据，不需要再次请求商品列表，因为getCategories方法中已经调用了getProductList\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.page {\r\n\tposition: relative;\r\n\tz-index: 1;\r\n\tbackground-color: #f8fafb;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\toverflow-y: auto;\r\n\t\r\n\t// 顶部区域\r\n\t.header {\r\n\t\tpadding-top: 180rpx;\r\n\t\tbackground-image: url('/static/menu/a704b1fd76a14df640e66c4ad009de43.png');\r\n\t\tbackground-size: 100% 100%;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\t\r\n\t\t// 搜索框\r\n\t\t.search-wrapper {\r\n\t\t\tpadding: 20rpx 30rpx;\r\n\t\t\t\r\n\t\t\t.search-input {\r\n\t\t\t\tpadding: 16rpx 24rpx;\r\n\t\t\t\tbackground-color: #ffffff;\r\n\t\t\t\tborder-radius: 308rpx;\r\n\t\t\t\theight: 72rpx;\r\n\t\t\t\t\r\n\t\t\t\t.search-icon {\r\n\t\t\t\t\twidth: 28rpx;\r\n\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.search-placeholder {\r\n\t\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t\t\tcolor: #cccccc;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 店铺信息\r\n\t\t.store-info {\r\n\t\t\tpadding: 56rpx 40rpx 38rpx;\r\n\t\t\t\r\n\t\t\t.store-name {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding-right: 40rpx;\r\n\t\t\t\t\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tright: 12rpx;\r\n\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\twidth: 12rpx;\r\n\t\t\t\t\theight: 12rpx;\r\n\t\t\t\t\tborder-top: 2rpx solid #999;\r\n\t\t\t\t\tborder-right: 2rpx solid #999;\r\n\t\t\t\t\ttransform: rotate(45deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.distance-icon {\r\n\t\t\t\twidth: 36rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.distance-text {\r\n\t\t\t\tmargin-left: 14rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #b3b3b3;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 堂食/外卖切换\r\n\t\t.dining-type {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tbackground-color: #f6f6f6;\r\n\t\t\tborder-radius: 308rpx;\r\n\t\t\twidth: 186rpx;\r\n\t\t\theight: 56rpx;\r\n\t\t\t\r\n\t\t\t&-item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #b3b3b3;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\ttransition: all 0.3s;\r\n\t\t\t\t\r\n\t\t\t\t&.dining-type-active {\r\n\t\t\t\t\tbackground-color: #8cd548;\r\n\t\t\t\t\tborder-radius: 308rpx;\r\n\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t// 商品列表区域\r\n\t.menu-content {\r\n\t\tdisplay: flex;\r\n\t\theight: calc(100vh - 300rpx);\r\n\t\tpadding-bottom: 128rpx;\r\n\t\t\r\n\t\t// 左侧分类菜单\r\n\t\t.menu-sidebar {\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: #f6f6f6;\r\n\t\t\t\r\n\t\t\t.sidebar-item {\r\n\t\t\t\tpadding: 30rpx 20rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\tcolor: #8cd548;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\r\n\t\t\t\t\t&::before {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\t\twidth: 6rpx;\r\n\t\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\t\tbackground-color: #8cd548;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 右侧商品列表\r\n\t\t.product-list-container {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 100%;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t\r\n\t\t\t// 分类标题\r\n\t\t\t.category-title {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #3b3b3b;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.product-list {\r\n\t\t\t\t.product-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\t\t\r\n\t\t\t\t\t.product-image {\r\n\t\t\t\t\t\twidth: 148rpx;\r\n\t\t\t\t\t\theight: 160rpx;\r\n\t\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\t\tbackground-color: #f1f6f3;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.product-info {\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tmargin-left: 24rpx;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.product-name {\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #3b3b3b;\r\n\t\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t.product-bottom {\r\n\t\t\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.price {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\talign-items: baseline;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t&-symbol {\r\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t&-value {\r\n\t\t\t\t\t\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\t\t\t\t\t\tmargin-left: 6rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t.select-btn {\r\n\t\t\t\t\t\t\t\tpadding: 12rpx 0;\r\n\t\t\t\t\t\t\t\tborder-radius: 308rpx;\r\n\t\t\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\tcolor: #ffffff;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// 底部购物车栏\r\n\t.cart-bar {\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 20rpx 40rpx;\r\n\t\tbox-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.05);\r\n\t\tz-index: 100;\r\n\t\t\r\n\t\t.cart-content {\r\n\t\t\theight: 88rpx;\r\n\t\t\t\r\n\t\t\t.cart-left {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tpadding: 10rpx;\r\n\t\t\t\t\r\n\t\t\t\t.cart-icon {\r\n\t\t\t\t\twidth: 64rpx;\r\n\t\t\t\t\theight: 64rpx;\r\n\t\t\t\t\ttransition: all 0.3s;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.cart-badge {\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: -6rpx;\r\n\t\t\t\t\tright: -6rpx;\r\n\t\t\t\t\tmin-width: 36rpx;\r\n\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\tpadding: 0 8rpx;\r\n\t\t\t\t\tbackground: linear-gradient(135deg, #ff5722 0%, #ff7043 100%);\r\n\t\t\t\t\tborder-radius: 18rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tline-height: 36rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tbox-shadow: 0 2rpx 6rpx rgba(255, 87, 34, 0.3);\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tborder: 2rpx solid #fff;\r\n\t\t\t\t\tanimation: bounce 0.5s;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\t.cart-icon {\r\n\t\t\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\t\t\topacity: 0.8;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.cart-center {\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t.empty-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #999;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.price-symbol {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #ff5722;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.price-value {\r\n\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\tcolor: #ff5722;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.checkout-btn {\r\n\t\t\t\tpadding: 0 40rpx;\r\n\t\t\t\theight: 88rpx;\r\n\t\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\t\tborder-radius: 44rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbox-shadow: 0 6rpx 16rpx rgba(106, 181, 46, 0.3);\r\n\t\t\t\ttransition: all 0.2s;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t\r\n\t\t\t\t// 添加闪光效果\r\n\t\t\t\t&::before {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: 0;\r\n\t\t\t\t\tleft: -100%;\r\n\t\t\t\t\twidth: 50%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t\tbackground: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);\r\n\t\t\t\t\ttransform: skewX(-25deg);\r\n\t\t\t\t\tanimation: shine 3s infinite;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&.disabled {\r\n\t\t\t\t\tbackground-color: #ccc;\r\n\t\t\t\t\tpointer-events: none; // 禁用点击\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.btn-text {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t\tz-index: 2;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t&:active {\r\n\t\t\t\t\ttransform: scale(0.98);\r\n\t\t\t\t\topacity: 0.9;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.product-bottom {\r\n\t.select-btn {\r\n\t\t// 多规格按钮样式\r\n\t\t.spec-btn {\r\n\t\t\tdisplay: block;\r\n\t\t\tpadding: 12rpx 24rpx;\r\n\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\tborder-radius: 308rpx;\r\n\t\t\tcolor: #ffffff;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(140, 213, 72, 0.2);\r\n\t\t\ttransition: all 0.2s;\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.95);\r\n\t\t\t\topacity: 0.9;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t// 单规格加号按钮样式\r\n\t\t.add-btn {\r\n\t\t\twidth: 48rpx;\r\n\t\t\theight: 48rpx;\r\n\t\t\tbackground: linear-gradient(135deg, #8cd548 0%, #6ab52e 100%);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-left: 40rpx;\r\n\t\t\tbox-shadow: 0 4rpx 8rpx rgba(140, 213, 72, 0.2);\r\n\t\t\ttransition: all 0.2s;\r\n\t\t\tposition: relative;\r\n\t\t\t\r\n\t\t\t.add-icon {\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tline-height: 1;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\t// 优化加号显示\r\n\t\t\t\twidth: 32rpx;\r\n\t\t\t\theight: 32rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t\r\n\t\t\t\t&::before,\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tcontent: '';\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\tbackground-color: #ffffff;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 横线\r\n\t\t\t\t&::before {\r\n\t\t\t\t\twidth: 20rpx;\r\n\t\t\t\t\theight: 2rpx;\r\n\t\t\t\t\tleft: 6rpx;\r\n\t\t\t\t\ttop: 15rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 竖线\r\n\t\t\t\t&::after {\r\n\t\t\t\t\twidth: 2rpx;\r\n\t\t\t\t\theight: 20rpx;\r\n\t\t\t\t\tleft: 15rpx;\r\n\t\t\t\t\ttop: 6rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&:active {\r\n\t\t\t\ttransform: scale(0.9);\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@keyframes shine {\r\n\t0% {\r\n\t\tleft: -100%;\r\n\t}\r\n\t20% {\r\n\t\tleft: 100%;\r\n\t}\r\n\t100% {\r\n\t\tleft: 100%;\r\n\t}\r\n}\r\n\r\n@keyframes bounce {\r\n\t0%, 20%, 50%, 80%, 100% {\r\n\t\ttransform: translateY(0);\r\n\t}\r\n\t40% {\r\n\t\ttransform: translateY(-6rpx);\r\n\t}\r\n\t60% {\r\n\t\ttransform: translateY(-3rpx);\r\n\t}\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=style&index=0&id=368aef34&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./menu.vue?vue&type=style&index=0&id=368aef34&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309501\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}