(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/points/mall"],{

/***/ 272:
/*!***************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/main.js?{"page":"pages%2Fpoints%2Fmall"} ***!
  \***************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _mall = _interopRequireDefault(__webpack_require__(/*! ./pages/points/mall.vue */ 273));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_mall.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 273:
/*!********************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue ***!
  \********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mall.vue?vue&type=template&id=163aafd7&scoped=true& */ 274);
/* harmony import */ var _mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mall.vue?vue&type=script&lang=js& */ 276);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _mall_vue_vue_type_style_index_0_id_163aafd7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mall.vue?vue&type=style&index=0&id=163aafd7&lang=scss&scoped=true& */ 279);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 32);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "163aafd7",
  null,
  false,
  _mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/points/mall.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 274:
/*!***************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?vue&type=template&id=163aafd7&scoped=true& ***!
  \***************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=template&id=163aafd7&scoped=true& */ 275);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_template_id_163aafd7_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 275:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?vue&type=template&id=163aafd7&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = !_vm.loading && _vm.filteredGoodsList.length > 0
  var l0 = g0
    ? _vm.__map(_vm.filteredGoodsList, function (item, index) {
        var $orig = _vm.__get_orig(item)
        var g1 =
          item.type === "coupon" && item.coupons && item.coupons.length > 0
        var g2 = g1 ? item.coupons.length : null
        var g3 = g1 && g2 > 2 ? item.coupons.length : null
        var g4 =
          item.type !== "coupon" && item.products && item.products.length > 0
        var g5 = g4 ? item.products && item.products.length > 2 : null
        var g6 = g4 && g5 ? item.products.length : null
        var g7 =
          item.type === "coupon" && item.coupons && item.coupons.length > 0
        return {
          $orig: $orig,
          g1: g1,
          g2: g2,
          g3: g3,
          g4: g4,
          g5: g5,
          g6: g6,
          g7: g7,
        }
      })
    : null
  var g8 = !_vm.loading && _vm.filteredGoodsList.length === 0
  var g9 = !_vm.loading && _vm.filteredGoodsList.length > 0
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g8: g8,
        g9: g9,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 276:
/*!*********************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=script&lang=js& */ 277);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 277:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 55));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 57));
var _user = __webpack_require__(/*! @/api/user */ 166);
var _mall = __webpack_require__(/*! @/api/mall */ 278);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      userPoints: 0,
      goodsList: [],
      activeTab: 'all',
      // 默认显示全部商品
      page: 1,
      limit: 10,
      loading: true,
      hasMore: true,
      isRefreshing: false
    };
  },
  computed: {
    // 根据当前选中的标签过滤商品列表
    filteredGoodsList: function filteredGoodsList() {
      if (this.activeTab === 'all') {
        return this.goodsList;
      } else if (this.activeTab === 'coupon') {
        return this.goodsList.filter(function (item) {
          return item.type === 'coupon';
        });
      } else if (this.activeTab === 'product') {
        return this.goodsList.filter(function (item) {
          return item.type !== 'coupon';
        });
      }
      return this.goodsList;
    }
  },
  onLoad: function onLoad() {
    this.getUserInfo();
    this.getGoodsList();
  },
  // 添加下拉刷新
  onPullDownRefresh: function onPullDownRefresh() {
    this.handlePullRefresh();
  },
  methods: {
    // 切换标签
    switchTab: function switchTab(tab) {
      this.activeTab = tab;
    },
    // 跳转到兑换记录页面
    goToPointsDetail: function goToPointsDetail() {
      uni.navigateTo({
        url: '/pages/points/record'
      });
    },
    handleBack: function handleBack() {
      uni.navigateBack();
    },
    // 处理下拉刷新
    handlePullRefresh: function handlePullRefresh() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                if (!_this.isRefreshing) {
                  _context.next = 2;
                  break;
                }
                return _context.abrupt("return");
              case 2:
                _this.isRefreshing = true;
                _context.prev = 3;
                // 重置页码
                _this.page = 1;

                // 并行请求用户信息和商品列表
                _context.next = 7;
                return Promise.all([_this.getUserInfo(), _this.getGoodsList()]);
              case 7:
                uni.showToast({
                  title: '刷新成功',
                  icon: 'none',
                  duration: 1000
                });
                _context.next = 14;
                break;
              case 10:
                _context.prev = 10;
                _context.t0 = _context["catch"](3);
                console.error('下拉刷新异常:', _context.t0);
                uni.showToast({
                  title: '刷新失败，请重试',
                  icon: 'none'
                });
              case 14:
                _context.prev = 14;
                _this.isRefreshing = false;
                uni.stopPullDownRefresh();
                return _context.finish(14);
              case 18:
              case "end":
                return _context.stop();
            }
          }
        }, _callee, null, [[3, 10, 14, 18]]);
      }))();
    },
    // 获取用户信息包含积分
    getUserInfo: function getUserInfo() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res, points, _points, userInfo, _points2;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.prev = 0;
                _context2.next = 3;
                return (0, _user.getUserProfile)();
              case 3:
                res = _context2.sent;
                console.log('用户信息完整数据:', JSON.stringify(res));
                if (res.code === 1) {
                  // 打印出用户信息数据结构
                  console.log('用户信息数据结构:', JSON.stringify(res.data));

                  // 从返回的数据中获取积分值
                  // 根据my.vue页面的代码，应该使用score字段而不是points
                  points = 0;
                  if (res.data && res.data.score !== undefined) {
                    points = parseInt(res.data.score || 0);
                    console.log('从data.score获取积分:', points);
                  } else if (res.data && res.data.points !== undefined) {
                    points = parseInt(res.data.points || 0);
                    console.log('从data.points获取积分:', points);
                  } else if (res.data && res.data.user && res.data.user.score !== undefined) {
                    points = parseInt(res.data.user.score || 0);
                    console.log('从data.user.score获取积分:', points);
                  } else if (res.data && res.data.assets && res.data.assets.points !== undefined) {
                    points = parseInt(res.data.assets.points || 0);
                    console.log('从data.assets.points获取积分:', points);
                  }
                  _this2.userPoints = points || 0;

                  // 同时更新本地存储
                  uni.setStorageSync('points', _this2.userPoints);
                } else {
                  console.error('获取用户信息失败:', res.msg);

                  // 如果无法获取用户信息，尝试从本地存储获取
                  _points = uni.getStorageSync('points');
                  if (_points !== '' && _points !== undefined) {
                    _this2.userPoints = parseInt(_points);
                    console.log('从本地存储获取积分:', _this2.userPoints);
                  } else {
                    // 尝试从用户信息中获取
                    userInfo = uni.getStorageSync('userInfo');
                    console.log('从本地存储获取用户信息:', JSON.stringify(userInfo));
                  }
                }
                _context2.next = 13;
                break;
              case 8:
                _context2.prev = 8;
                _context2.t0 = _context2["catch"](0);
                console.error('获取用户信息异常:', _context2.t0);

                // 如果请求出错，尝试从本地存储获取
                _points2 = uni.getStorageSync('points');
                if (_points2 !== '' && _points2 !== undefined) {
                  _this2.userPoints = parseInt(_points2);
                  console.log('从本地存储获取积分:', _this2.userPoints);
                }
              case 13:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2, null, [[0, 8]]);
      }))();
    },
    // 获取商品列表
    getGoodsList: function getGoodsList() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var params, res, list, processedList;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.prev = 0;
                _this3.loading = true;
                params = {
                  page: _this3.page,
                  limit: _this3.limit
                };
                _context3.next = 5;
                return (0, _mall.getPointMallList)(params);
              case 5:
                res = _context3.sent;
                if (res.code === 1) {
                  // 检查API返回的数据结构
                  console.log('积分商城数据:', res.data);

                  // 根据实际返回的数据结构获取商品列表
                  list = res.data.rows || res.data.list || []; // 处理商品数据，确保中文显示
                  processedList = list.map(function (item) {
                    // 如果category_text不存在，根据type设置中文类别
                    if (!item.category_text) {
                      item.category_text = item.type === 'coupon' ? '优惠券' : '实物商品';
                    }

                    // 处理优惠券数据，确保没有多余元素
                    if (item.type === 'coupon' && item.coupons && item.coupons.length > 0) {
                      // 只保留必要的优惠券数据，并处理无门槛优惠券
                      item.coupons = item.coupons.map(function (coupon) {
                        return {
                          min_amount: parseFloat(coupon.min_amount || 0),
                          amount: parseFloat(coupon.amount || 0),
                          valid_day: parseInt(coupon.valid_day || 30),
                          name: coupon.name || ''
                        };
                      });

                      // 根据优惠券类型添加描述
                      item.couponDesc = _this3.getCouponDescription(item.coupons);
                    }

                    // 处理实物商品数据
                    if (item.type !== 'coupon' && item.products && item.products.length > 0) {
                      // 处理商品数据
                      item.products = item.products.map(function (product) {
                        return {
                          id: product.id || 0,
                          name: product.name || '',
                          image: product.image || ''
                        };
                      });

                      // 添加商品描述
                      item.productDesc = _this3.getProductDescription(item.products);
                    }
                    return item;
                  }); // 如果是第一页，替换列表，否则追加
                  if (_this3.page === 1) {
                    _this3.goodsList = processedList;
                  } else {
                    _this3.goodsList = [].concat((0, _toConsumableArray2.default)(_this3.goodsList), (0, _toConsumableArray2.default)(processedList));
                  }
                  _this3.hasMore = list.length >= _this3.limit;
                } else {
                  uni.showToast({
                    title: res.msg || '获取商品列表失败',
                    icon: 'none'
                  });
                }
                _context3.next = 13;
                break;
              case 9:
                _context3.prev = 9;
                _context3.t0 = _context3["catch"](0);
                console.error('获取商品列表异常:', _context3.t0);
                uni.showToast({
                  title: '网络异常，请稍后重试',
                  icon: 'none'
                });
              case 13:
                _context3.prev = 13;
                _this3.loading = false;
                return _context3.finish(13);
              case 16:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3, null, [[0, 9, 13, 16]]);
      }))();
    },
    // 获取优惠券描述
    getCouponDescription: function getCouponDescription(coupons) {
      if (!coupons || coupons.length === 0) return '';

      // 找出最大优惠的优惠券
      var maxCoupon = coupons.reduce(function (max, current) {
        return current.amount > max.amount ? current : max;
      }, coupons[0]);
      if (maxCoupon.min_amount > 0) {
        return "\u6EE1".concat(maxCoupon.min_amount, "\u51CF").concat(maxCoupon.amount);
      } else {
        return "".concat(maxCoupon.amount, "\u5143\u65E0\u95E8\u69DB");
      }
    },
    // 获取商品描述
    getProductDescription: function getProductDescription(products) {
      if (!products || products.length === 0) return '';
      if (products.length === 1) {
        return products[0].name;
      } else {
        return "".concat(products[0].name, "\u7B49").concat(products.length, "\u4EF6\u5546\u54C1");
      }
    },
    // 加载更多
    loadMore: function loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++;
        this.loading = true;
        this.getGoodsList();
      }
    },
    // 兑换商品
    exchange: function exchange(item) {
      var _this4 = this;
      if (item.points > this.userPoints) {
        uni.showToast({
          title: '积分不足',
          icon: 'none'
        });
        return;
      }
      if (item.stock <= 0) {
        uni.showToast({
          title: '商品已售罄',
          icon: 'none'
        });
        return;
      }

      // 如果是优惠券类型并且有多张优惠券，显示详情弹窗
      if (item.type === 'coupon' && item.coupons && item.coupons.length > 1) {
        this.showCouponDetail(item);
        return;
      }

      // 如果是实物商品并且有多个产品，显示详情弹窗
      if (item.type !== 'coupon' && item.products && item.products.length > 1) {
        this.showProductDetail(item);
        return;
      }
      uni.showModal({
        title: '确认兑换',
        content: "\u786E\u5B9A\u4F7F\u7528".concat(item.points, "\u79EF\u5206\u5151\u6362").concat(item.name, "\u5417\uFF1F"),
        success: function () {
          var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4(res) {
            var data, result, updatedItem;
            return _regenerator.default.wrap(function _callee4$(_context4) {
              while (1) {
                switch (_context4.prev = _context4.next) {
                  case 0:
                    if (!res.confirm) {
                      _context4.next = 15;
                      break;
                    }
                    _context4.prev = 1;
                    uni.showLoading({
                      title: '兑换中...',
                      mask: true
                    });
                    data = {
                      id: item.id
                    };
                    _context4.next = 6;
                    return (0, _mall.exchangePointMallItem)(data);
                  case 6:
                    result = _context4.sent;
                    if (result.code === 1) {
                      // 更新本地数据，无需完全重载
                      _this4.userPoints -= item.points;

                      // 更新商品库存
                      updatedItem = _this4.goodsList.find(function (i) {
                        return i.id === item.id;
                      });
                      if (updatedItem) {
                        updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);
                      }

                      // 先隐藏加载框，再显示成功提示
                      uni.hideLoading();
                      uni.showToast({
                        title: '兑换成功',
                        icon: 'none'
                      });

                      // 在后台更新用户信息
                      _this4.getUserInfo().then(function () {
                        // 静默更新，不影响用户体验
                        console.log('用户信息已更新');
                      });

                      // 如果库存为零，可能需要刷新列表以获取最新数据
                      if (updatedItem && updatedItem.stock <= 0) {
                        setTimeout(function () {
                          // 延迟刷新列表，保证提示消息显示
                          _this4.refreshGoodsList();
                        }, 1500);
                      }
                    } else {
                      uni.hideLoading();
                      uni.showToast({
                        title: result.msg || '兑换失败',
                        icon: 'none'
                      });
                    }
                    _context4.next = 15;
                    break;
                  case 10:
                    _context4.prev = 10;
                    _context4.t0 = _context4["catch"](1);
                    console.error('兑换商品异常:', _context4.t0);
                    uni.hideLoading();
                    uni.showToast({
                      title: '网络异常，请稍后重试',
                      icon: 'none'
                    });
                  case 15:
                  case "end":
                    return _context4.stop();
                }
              }
            }, _callee4, null, [[1, 10]]);
          }));
          function success(_x) {
            return _success.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 显示优惠券详情
    showCouponDetail: function showCouponDetail(item) {
      var _this5 = this;
      // 构建优惠券详情内容
      var content = '';
      if (item.coupons && item.coupons.length > 0) {
        content = item.coupons.map(function (coupon, index) {
          var desc = coupon.min_amount > 0 ? "\u6EE1".concat(coupon.min_amount, "\u51CF").concat(coupon.amount) : "".concat(coupon.amount, "\u5143\u65E0\u95E8\u69DB");
          return "".concat(index + 1, ". ").concat(coupon.name || desc, "\uFF08").concat(coupon.valid_day, "\u5929\u6709\u6548\uFF09");
        }).join('\n');
      }
      uni.showModal({
        title: '优惠券详情',
        content: content || '暂无详情',
        confirmText: '立即兑换',
        success: function success(res) {
          if (res.confirm) {
            // 用户点击确认，继续兑换流程
            uni.showModal({
              title: '确认兑换',
              content: "\u786E\u5B9A\u4F7F\u7528".concat(item.points, "\u79EF\u5206\u5151\u6362").concat(item.name, "\u5417\uFF1F"),
              success: function () {
                var _success2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5(confirmRes) {
                  var data, result, updatedItem;
                  return _regenerator.default.wrap(function _callee5$(_context5) {
                    while (1) {
                      switch (_context5.prev = _context5.next) {
                        case 0:
                          if (!confirmRes.confirm) {
                            _context5.next = 15;
                            break;
                          }
                          _context5.prev = 1;
                          uni.showLoading({
                            title: '兑换中...',
                            mask: true
                          });
                          data = {
                            id: item.id
                          };
                          _context5.next = 6;
                          return (0, _mall.exchangePointMallItem)(data);
                        case 6:
                          result = _context5.sent;
                          if (result.code === 1) {
                            // 更新本地数据
                            _this5.userPoints -= item.points;

                            // 更新商品库存
                            updatedItem = _this5.goodsList.find(function (i) {
                              return i.id === item.id;
                            });
                            if (updatedItem) {
                              updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);
                            }
                            uni.hideLoading();
                            uni.showToast({
                              title: '兑换成功',
                              icon: 'none'
                            });

                            // 更新用户信息
                            _this5.getUserInfo();
                          } else {
                            uni.hideLoading();
                            uni.showToast({
                              title: result.msg || '兑换失败',
                              icon: 'none'
                            });
                          }
                          _context5.next = 15;
                          break;
                        case 10:
                          _context5.prev = 10;
                          _context5.t0 = _context5["catch"](1);
                          console.error('兑换商品异常:', _context5.t0);
                          uni.hideLoading();
                          uni.showToast({
                            title: '网络异常，请稍后重试',
                            icon: 'none'
                          });
                        case 15:
                        case "end":
                          return _context5.stop();
                      }
                    }
                  }, _callee5, null, [[1, 10]]);
                }));
                function success(_x2) {
                  return _success2.apply(this, arguments);
                }
                return success;
              }()
            });
          }
        }
      });
    },
    // 显示商品详情
    showProductDetail: function showProductDetail(item) {
      var _this6 = this;
      // 构建商品详情内容
      var content = '';
      if (item.products && item.products.length > 0) {
        content = item.products.map(function (product, index) {
          return "".concat(index + 1, ". ").concat(product.name);
        }).join('\n');
      }
      uni.showModal({
        title: '商品详情',
        content: content || '暂无详情',
        confirmText: '立即兑换',
        success: function success(res) {
          if (res.confirm) {
            // 用户点击确认，继续兑换流程
            uni.showModal({
              title: '确认兑换',
              content: "\u786E\u5B9A\u4F7F\u7528".concat(item.points, "\u79EF\u5206\u5151\u6362").concat(item.name, "\u5417\uFF1F"),
              success: function () {
                var _success3 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6(confirmRes) {
                  var data, result, updatedItem;
                  return _regenerator.default.wrap(function _callee6$(_context6) {
                    while (1) {
                      switch (_context6.prev = _context6.next) {
                        case 0:
                          if (!confirmRes.confirm) {
                            _context6.next = 15;
                            break;
                          }
                          _context6.prev = 1;
                          uni.showLoading({
                            title: '兑换中...',
                            mask: true
                          });
                          data = {
                            id: item.id
                          };
                          _context6.next = 6;
                          return (0, _mall.exchangePointMallItem)(data);
                        case 6:
                          result = _context6.sent;
                          if (result.code === 1) {
                            // 更新本地数据
                            _this6.userPoints -= item.points;

                            // 更新商品库存
                            updatedItem = _this6.goodsList.find(function (i) {
                              return i.id === item.id;
                            });
                            if (updatedItem) {
                              updatedItem.stock = Math.max(0, (updatedItem.stock || 1) - 1);
                            }
                            uni.hideLoading();
                            uni.showToast({
                              title: '兑换成功',
                              icon: 'none'
                            });

                            // 更新用户信息
                            _this6.getUserInfo();
                          } else {
                            uni.hideLoading();
                            uni.showToast({
                              title: result.msg || '兑换失败',
                              icon: 'none'
                            });
                          }
                          _context6.next = 15;
                          break;
                        case 10:
                          _context6.prev = 10;
                          _context6.t0 = _context6["catch"](1);
                          console.error('兑换商品异常:', _context6.t0);
                          uni.hideLoading();
                          uni.showToast({
                            title: '网络异常，请稍后重试',
                            icon: 'none'
                          });
                        case 15:
                        case "end":
                          return _context6.stop();
                      }
                    }
                  }, _callee6, null, [[1, 10]]);
                }));
                function success(_x3) {
                  return _success3.apply(this, arguments);
                }
                return success;
              }()
            });
          }
        }
      });
    },
    // 平滑刷新商品列表
    refreshGoodsList: function refreshGoodsList() {
      var _this7 = this;
      // 先保存滚动位置
      var currentScrollTop = uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });

      // 重置页码
      this.page = 1;

      // 请求新数据
      var params = {
        page: this.page,
        limit: this.limit
      };
      this.loading = true;
      (0, _mall.getPointMallList)(params).then(function (res) {
        if (res.code === 1) {
          // 获取新列表
          var list = res.data.rows || res.data.list || [];

          // 处理商品数据
          var processedList = list.map(function (item) {
            if (!item.category_text) {
              item.category_text = item.type === 'coupon' ? '优惠券' : '实物商品';
            }

            // 处理优惠券数据
            if (item.type === 'coupon' && item.coupons && item.coupons.length > 0) {
              item.coupons = item.coupons.map(function (coupon) {
                return {
                  min_amount: coupon.min_amount || 0,
                  amount: coupon.amount || 0
                };
              });
            }
            return item;
          });

          // 平滑更新列表
          _this7.goodsList = processedList;
          _this7.hasMore = list.length >= _this7.limit;
        }
      }).catch(function (err) {
        console.error('刷新列表异常:', err);
      }).finally(function () {
        _this7.loading = false;

        // 恢复滚动位置
        setTimeout(function () {
          uni.pageScrollTo({
            scrollTop: currentScrollTop || 0,
            duration: 0
          });
        }, 50);
      });
    },
    // 处理图片加载错误
    handleImageError: function handleImageError(e) {
      // 替换为默认图片
      e.target.src = '/static/images/placeholder.png';
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 279:
/*!******************************************************************************************************************************************************************!*\
  !*** E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?vue&type=style&index=0&id=163aafd7&lang=scss&scoped=true& ***!
  \******************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_style_index_0_id_163aafd7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mall.vue?vue&type=style&index=0&id=163aafd7&lang=scss&scoped=true& */ 280);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_style_index_0_id_163aafd7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_style_index_0_id_163aafd7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_style_index_0_id_163aafd7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_style_index_0_id_163aafd7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_mall_vue_vue_type_style_index_0_id_163aafd7_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 280:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/points/mall.vue?vue&type=style&index=0&id=163aafd7&lang=scss&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[272,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/points/mall.js.map