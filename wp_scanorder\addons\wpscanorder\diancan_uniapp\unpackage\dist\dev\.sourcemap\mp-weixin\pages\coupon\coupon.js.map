{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/coupon.vue?7bb6", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/coupon.vue?1181", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/coupon.vue?13b0", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/coupon.vue?b773", "uni-app:///pages/coupon/coupon.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/coupon.vue?28c2", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/pages/coupon/coupon.vue?78f4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MyCoupon", "data", "currentTab", "tabs", "name", "couponList", "loading", "error", "errorMsg", "orderAmount", "scrollHeight", "page", "limit", "hasMore", "loadingMore", "refreshing", "onLoad", "console", "onReady", "onShow", "onPullDownRefresh", "onReachBottom", "methods", "calcScrollHeight", "query", "refreshCoupons", "uni", "loadMore", "fetchCoupons", "isLoadMore", "params", "res", "couponsData", "total", "possibleArrayProps", "Array", "processedCoupons", "couponStatus", "couponAmount", "couponLimit", "couponName", "item", "amount", "desc", "endDate", "status", "handleBack", "switchTab", "goToMenu", "url", "filterCoupons", "filteredList", "formatDate", "selectCoupon", "title", "icon", "id", "coupon_id", "expire_time"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC4HnrB;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;EACA;EAEAC;IACA;MACAC;MACAC,OACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;MACA;MACAC;IACA;IAEA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;MACAC;MACAA;MACAA;MAEAA;QACA;UACA;UACA;UACA;;UAEA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAT;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAV;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBACA;gBAAA;gBAGAC;kBACAnB;kBACAC;gBACA;gBAAA;gBAAA,OAEA;cAAA;gBAAAmB;gBACAd;gBAEA;kBACA;kBACAe;kBACAC;kBAEA;oBACA;oBACA;sBACAA;oBACA;oBAEA;sBACA;sBACAD;oBACA;sBACA;sBACA;sBACAE;wBAAA,OACAC;sBAAA,EACA;sBAEA;wBACA;wBACAH;sBACA;wBACA;wBACAA;sBACA;wBACA;wBACAA;sBACA;wBACA;wBACAA;sBACA;wBACA;wBACAA;sBACA;wBACA;wBACAA;sBACA;oBACA;kBACA;;kBAEA;kBACAI;oBACA;oBACA;oBACA;;oBAEA;oBACA;oBACA;sBACA;wBACAC;sBACA;wBACAA;sBACA;oBACA;sBACAA;oBACA;sBACAA;oBACA;;oBAEA;oBACA;oBACA;oBACA;;oBAEA;oBACA;sBACAC;sBACAC;sBACAC;oBACA;sBACA;sBACAF;sBACAC;sBACAC;oBACA;oBAEA,uCACAC;sBACA;sBACAC;sBACA9B;sBACAR;sBACAuC;sBACAC;sBACA;sBACAC;oBAAA;kBAEA,IAEA;kBACA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBAEA5B;gBACA;kBACA;kBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACAA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA6B;MACApB;IACA;IAEAqB;MACA;QACA;QACA;MACA;IACA;IAEAC;MACAtB;QACAuB;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEA;QACAC;MACA;QACA;QACAA;UACA;UACA;YACA;UACA;UACA;UACA;QACA;MACA;QACA;QACAA;UACA;UACA;YACA;UACA;UACA;UACA;QACA;MACA;;MAEA;MACA;QACAA;UACA;UACAV;QACA;MACA;MAEA;IACA;IAEA;IACAW;MACA;MAEA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;QACA;QACA;QAEA;MACA;QACAnC;QACA;MACA;IACA;IAEA;IACAoC;MACA;MACA;MACA;;MAEA;MACA;QACA3B;UACA4B;UACAC;QACA;QACA;MACA;MAEA;QACA7B;UACA4B;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAC;QACAC;QACAf;QACA9B;QACAR;QACAuC;QACAe;MACA;;MAEA;MACAhC;;MAEA;MACAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtfA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/coupon/coupon.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/coupon/coupon.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coupon.vue?vue&type=template&id=032f11f4&scoped=true&\"\nvar renderjs\nimport script from \"./coupon.vue?vue&type=script&lang=js&\"\nexport * from \"./coupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coupon.vue?vue&type=style&index=0&id=032f11f4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"032f11f4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/coupon/coupon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=template&id=032f11f4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    !_vm.loading && !_vm.error\n      ? _vm.__map(_vm.filterCoupons(), function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = item.expire_time ? _vm.formatDate(item.expire_time) : null\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var g0 = !_vm.loading && !_vm.error ? _vm.filterCoupons().length : null\n  var g1 =\n    !_vm.loading && !_vm.error\n      ? !_vm.hasMore && _vm.couponList.length > 0\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      return _vm.selectCoupon(item)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <!-- 顶部导航 -->\r\n    <view class=\"header\">\r\n      <view class=\"nav-bar\">\r\n        <image \r\n          class=\"back-icon\" \r\n          src=\"/static/recharge/d2d56def1b7e4daac7adad19dc740323.png\" \r\n          @tap=\"handleBack\"\r\n        />\r\n        <text class=\"title\">我的优惠券</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 状态切换 -->\r\n    <view class=\"tab-container\">\r\n      <view \r\n        v-for=\"(tab, index) in tabs\" \r\n        :key=\"index\"\r\n        :class=\"['tab-item', { active: currentTab === index }]\"\r\n        @tap=\"switchTab(index)\"\r\n      >\r\n        <text class=\"tab-text\">{{tab.name}}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 加载中 -->\r\n    <view class=\"loading\" v-if=\"loading\">\r\n      <view class=\"loading-circle\"></view>\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n    \r\n    <!-- 错误提示 -->\r\n    <view class=\"error-state\" v-else-if=\"error\">\r\n      <text class=\"error-text\">{{errorMsg}}</text>\r\n      <view class=\"retry-btn\" @tap=\"refreshCoupons\">\r\n        <text>重新加载</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 优惠券列表 -->\r\n    <scroll-view \r\n      class=\"coupon-scroll\" \r\n      scroll-y \r\n      v-else\r\n      :style=\"{ height: scrollHeight + 'px' }\"\r\n      @scrolltolower=\"loadMore\"\r\n      refresher-enabled\r\n      :refresher-triggered=\"refreshing\"\r\n      @refresherrefresh=\"refreshCoupons\"\r\n    >\r\n      <view class=\"coupon-list\">\r\n        <view \r\n          class=\"coupon-item\" \r\n          v-for=\"(item, index) in filterCoupons()\" \r\n          :key=\"index\"\r\n          :class=\"{\r\n            'disabled': this.orderAmount > 0 && !item.isAvailable,\r\n            'expired': item.status === 'expired' || item.status === 'used'\r\n          }\"\r\n          @tap=\"selectCoupon(item)\"\r\n        >\r\n          <!-- 金额区域 -->\r\n          <view class=\"coupon-amount\">\r\n            <text class=\"symbol\">¥</text>\r\n            <text class=\"value\">{{item.coupon ? item.coupon.amount : item.amount}}</text>\r\n            <text class=\"limit\">满{{item.coupon ? item.coupon.min_amount : item.limit}}元可用</text>\r\n          </view>\r\n          \r\n          <!-- 内容区域 -->\r\n          <view class=\"coupon-info\">\r\n            <view class=\"info-top\">\r\n              <text class=\"name\">{{item.coupon ? item.coupon.name : item.name}}</text>\r\n              <view class=\"status-tag\" :class=\"item.status\">\r\n                {{item.status_text || (item.status === 'unused' || item.status === 'valid' ? '未使用' : '已过期')}}\r\n              </view>\r\n            </view>\r\n            \r\n            <text class=\"desc\">{{item.desc || '全场通用'}}</text>\r\n            \r\n            <view class=\"info-bottom\">\r\n              <text class=\"date\">有效期至 {{item.expire_time ? formatDate(item.expire_time) : (item.endDate || item.expireDate || '暂无期限')}}</text>\r\n              <view class=\"use-btn\" v-if=\"item.status === 'unused' || item.status === 'valid'\">\r\n                <text>立即使用</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 不可用标签 -->\r\n          <view class=\"unavailable-tag\" v-if=\"this.orderAmount > 0 && !item.isAvailable && (item.status === 'unused' || item.status === 'valid')\">\r\n            <text>不满足使用条件</text>\r\n          </view>\r\n          \r\n          <!-- 过期水印 -->\r\n          <view class=\"expired-watermark\" v-if=\"item.status === 'expired' || item.status === 'used'\">\r\n            <text>已过期</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 空状态 -->\r\n      <view class=\"empty-state\" v-if=\"filterCoupons().length === 0\">\r\n        <image class=\"empty-image\" src=\"/static/order/e186e04e8774da64b58c96a8bb479840.png\" />\r\n        <text class=\"empty-text\">暂无优惠券～</text>\r\n        <view class=\"action-btn\" @tap=\"goToMenu\">\r\n          <text>去领券</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 加载更多提示 -->\r\n      <view class=\"loading-more\" v-if=\"loadingMore\">\r\n        <view class=\"loading-dot\"></view>\r\n        <text>加载中...</text>\r\n      </view>\r\n      \r\n      <!-- 底部提示 -->\r\n      <view class=\"bottom-tip\" v-if=\"!hasMore && couponList.length > 0\">\r\n        <text>— 已经到底了 —</text>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getMyCoupons } from '@/api/user.js'\r\nimport MyCoupon from './MyCoupon.vue'\r\n\r\nexport default {\r\n  components: {\r\n    MyCoupon\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      currentTab: 0,\r\n      tabs: [\r\n        { name: '全部' },\r\n        { name: '未使用' },\r\n        { name: '已过期' }\r\n      ],\r\n      couponList: [],\r\n      loading: false,\r\n      error: false,\r\n      errorMsg: '获取优惠券失败，请重试',\r\n      orderAmount: 0,\r\n      scrollHeight: 500, // 默认高度，将在onReady中计算\r\n      // 分页相关\r\n      page: 1,\r\n      limit: 10,\r\n      hasMore: true,\r\n      loadingMore: false,\r\n      refreshing: false\r\n    }\r\n  },\r\n  \r\n  onLoad(options) {\r\n    if (options.amount) {\r\n      this.orderAmount = parseFloat(options.amount)\r\n      console.log('订单金额:', this.orderAmount)\r\n    }\r\n    \r\n    this.fetchCoupons()\r\n  },\r\n  \r\n  onReady() {\r\n    // 计算列表区域高度\r\n    this.calcScrollHeight()\r\n  },\r\n  \r\n  onShow() {\r\n    // 每次显示页面时刷新数据\r\n    this.refreshCoupons()\r\n  },\r\n\r\n  // 下拉刷新\r\n  onPullDownRefresh() {\r\n    this.refreshCoupons()\r\n  },\r\n\r\n  // 上拉加载更多\r\n  onReachBottom() {\r\n    this.loadMore()\r\n  },\r\n\r\n  methods: {\r\n    // 计算滚动区域高度\r\n    calcScrollHeight() {\r\n      const query = uni.createSelectorQuery().in(this)\r\n      query.select('.header').boundingClientRect()\r\n      query.select('.tab-container').boundingClientRect()\r\n      query.selectViewport().boundingClientRect()\r\n      \r\n      query.exec(res => {\r\n        if (res[0] && res[1] && res[2]) {\r\n          const headerHeight = res[0].height\r\n          const tabHeight = res[1].height\r\n          const windowHeight = res[2].height\r\n          \r\n          // 计算列表可用高度 = 窗口高度 - 头部高度 - tab高度 - 底部安全区域(20px)\r\n          this.scrollHeight = windowHeight - headerHeight - tabHeight - 20\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 刷新优惠券列表\r\n    async refreshCoupons() {\r\n      if (this.refreshing) return\r\n      \r\n      this.refreshing = true\r\n      this.page = 1\r\n      this.hasMore = true\r\n      \r\n      try {\r\n        await this.fetchCoupons()\r\n        uni.stopPullDownRefresh()\r\n      } catch (error) {\r\n        console.error('刷新优惠券异常:', error)\r\n      } finally {\r\n        this.refreshing = false\r\n      }\r\n    },\r\n    \r\n    // 加载更多优惠券\r\n    async loadMore() {\r\n      if (!this.hasMore || this.loading || this.loadingMore) return\r\n      \r\n      this.loadingMore = true\r\n      this.page++\r\n      \r\n      try {\r\n        await this.fetchCoupons(true)\r\n      } catch (error) {\r\n        console.error('加载更多优惠券异常:', error)\r\n        // 加载失败时恢复页码\r\n        this.page--\r\n      } finally {\r\n        this.loadingMore = false\r\n      }\r\n    },\r\n    \r\n    // 获取优惠券列表\r\n    async fetchCoupons(isLoadMore = false) {\r\n      if (!isLoadMore) {\r\n        this.loading = true\r\n      }\r\n      this.error = false\r\n      \r\n      try {\r\n        const params = {\r\n          page: this.page,\r\n          limit: this.limit\r\n        }\r\n        \r\n        const res = await getMyCoupons(params)\r\n        console.log('优惠券接口返回数据:', res)\r\n        \r\n        if (res.code === 1) {\r\n          // 检查data的类型并正确处理\r\n          let couponsData = []\r\n          let total = 0\r\n          \r\n          if (res.data) {\r\n            // 尝试获取总数\r\n            if (typeof res.data === 'object' && res.data !== null) {\r\n              total = res.data.total || res.data.count || 0\r\n            }\r\n            \r\n            if (Array.isArray(res.data)) {\r\n              // data直接是数组\r\n              couponsData = res.data\r\n            } else if (typeof res.data === 'object') {\r\n              // data是对象，尝试从对象中找到数组\r\n              // 查找对象中可能的数组属性\r\n              const possibleArrayProps = Object.keys(res.data).filter(key => \r\n                Array.isArray(res.data[key])\r\n              )\r\n              \r\n              if (possibleArrayProps.length > 0) {\r\n                // 使用找到的第一个数组\r\n                couponsData = res.data[possibleArrayProps[0]]\r\n              } else if (res.data.list) {\r\n                // 常见的列表字段名\r\n                couponsData = res.data.list\r\n              } else if (res.data.items) {\r\n                // 常见的列表字段名\r\n                couponsData = res.data.items\r\n              } else if (res.data.coupons) {\r\n                // 常见的列表字段名\r\n                couponsData = res.data.coupons\r\n              } else if (res.data.rows) {\r\n                // 常见的列表字段名\r\n                couponsData = res.data.rows\r\n              } else {\r\n                // 如果没有找到数组，将对象转换为单项数组\r\n                couponsData = [res.data]\r\n              }\r\n            }\r\n          }\r\n          \r\n          // 处理每个优惠券数据\r\n          const processedCoupons = couponsData.map(item => {\r\n            // 检查优惠券是否已过期\r\n            const expireDate = item.endDate || item.expireDate\r\n            const isExpired = expireDate ? new Date(expireDate) < new Date() : false\r\n            \r\n            // 优先使用服务器返回的status_text字段判断状态\r\n            let couponStatus = 'unused';\r\n            if (item.status_text) {\r\n              if (item.status_text === '已过期' || item.status_text === '已使用') {\r\n                couponStatus = 'expired';\r\n              } else if (item.status_text === '未使用' || item.status_text === '有效') {\r\n                couponStatus = 'unused';\r\n              }\r\n            } else if (isExpired) {\r\n              couponStatus = 'expired';\r\n            } else if (item.status) {\r\n              couponStatus = item.status;\r\n            }\r\n            \r\n            // 处理优惠券金额和最低消费，优先从coupon嵌套对象获取\r\n            let couponAmount = 0;\r\n            let couponLimit = 0;\r\n            let couponName = '';\r\n            \r\n            // 检查是否有coupon子对象\r\n            if (item.coupon) {\r\n              couponAmount = parseFloat(item.coupon.amount || 0);\r\n              couponLimit = parseFloat(item.coupon.min_amount || 0);\r\n              couponName = item.coupon.name || '';\r\n            } else {\r\n              // 直接从item对象获取\r\n              couponAmount = parseFloat(item.amount || 0);\r\n              couponLimit = parseFloat(item.min_amount || item.limit || 0);\r\n              couponName = item.name || '';\r\n            }\r\n            \r\n            return {\r\n              ...item,\r\n              // 确保必要字段存在\r\n              amount: couponAmount,\r\n              limit: couponLimit,\r\n              name: couponName || '优惠券',\r\n              desc: item.desc || '全场通用',\r\n              endDate: expireDate || '暂无期限',\r\n              // 设置状态\r\n              status: couponStatus\r\n            }\r\n          })\r\n          \r\n          // 更新列表数据\r\n          if (isLoadMore) {\r\n            this.couponList = [...this.couponList, ...processedCoupons]\r\n          } else {\r\n            this.couponList = processedCoupons\r\n          }\r\n          \r\n          // 判断是否还有更多数据\r\n          if (total > 0) {\r\n            this.hasMore = this.couponList.length < total\r\n          } else {\r\n            // 如果接口没有返回总数，则根据返回数据判断\r\n            this.hasMore = processedCoupons.length >= this.limit\r\n          }\r\n          \r\n          console.log('处理后的优惠券数据:', this.couponList)\r\n        } else {\r\n          this.error = true\r\n          this.errorMsg = res.msg || '获取优惠券失败'\r\n          console.error('获取优惠券失败:', res.msg)\r\n        }\r\n      } catch (error) {\r\n        this.error = true\r\n        this.errorMsg = '网络异常，请稍后重试'\r\n        console.error('获取优惠券异常:', error)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    handleBack() {\r\n      uni.navigateBack()\r\n    },\r\n    \r\n    switchTab(index) {\r\n      if (this.currentTab !== index) {\r\n        this.currentTab = index\r\n        this.refreshCoupons()\r\n      }\r\n    },\r\n    \r\n    goToMenu() {\r\n      uni.switchTab({\r\n        url: '/pages/menu/menu'\r\n      })\r\n    },\r\n    \r\n    // 根据tab筛选优惠券\r\n    filterCoupons() {\r\n      // 筛选基于tab的优惠券\r\n      let filteredList = []\r\n      \r\n      if(this.currentTab === 0) {\r\n        filteredList = this.couponList\r\n      } else if(this.currentTab === 1) {\r\n        // 未使用标签：显示status为unused或valid的，或status_text为\"未使用\"/\"有效\"的\r\n        filteredList = this.couponList.filter(item => {\r\n          // 优先使用status_text字段\r\n          if (item.status_text) {\r\n            return item.status_text === '未使用' || item.status_text === '有效';\r\n          }\r\n          // 否则使用status字段\r\n          return item.status === 'unused' || item.status === 'valid';\r\n        });\r\n      } else {\r\n        // 已过期标签：显示status为expired的，或status_text为\"已过期\"/\"已使用\"的\r\n        filteredList = this.couponList.filter(item => {\r\n          // 优先使用status_text字段\r\n          if (item.status_text) {\r\n            return item.status_text === '已过期' || item.status_text === '已使用';\r\n          }\r\n          // 否则使用status字段\r\n          return item.status === 'expired' || item.status === 'used';\r\n        });\r\n      }\r\n      \r\n      // 如果有订单金额，标记优惠券是否可用\r\n      if (this.orderAmount > 0) {\r\n        filteredList.forEach(item => {\r\n          const minAmount = parseFloat(item.coupon ? item.coupon.min_amount : item.limit) || 0\r\n          item.isAvailable = this.orderAmount >= minAmount && (item.status === 'unused' || item.status === 'valid')\r\n        })\r\n      }\r\n      \r\n      return filteredList\r\n    },\r\n    \r\n    // 格式化日期时间戳\r\n    formatDate(timestamp) {\r\n      if (!timestamp) return '暂无期限';\r\n      \r\n      try {\r\n        // 检查时间戳格式\r\n        const date = new Date(Number(timestamp) * 1000);\r\n        \r\n        // 检查日期是否有效\r\n        if (isNaN(date.getTime())) {\r\n          return '暂无期限';\r\n        }\r\n        \r\n        // 格式化为 YYYY-MM-DD\r\n        const year = date.getFullYear();\r\n        const month = String(date.getMonth() + 1).padStart(2, '0');\r\n        const day = String(date.getDate()).padStart(2, '0');\r\n        \r\n        return `${year}-${month}-${day}`;\r\n      } catch (e) {\r\n        console.error('日期格式化错误:', e);\r\n        return '暂无期限';\r\n      }\r\n    },\r\n    \r\n    // 选择优惠券\r\n    selectCoupon(item) {\r\n      // 判断优惠券是否可用\r\n      const minAmount = parseFloat(item.coupon ? item.coupon.min_amount : item.limit) || 0\r\n      const status = item.status\r\n      \r\n      // 如果优惠券已过期或订单金额不满足条件，则提示但不选择\r\n      if (status === 'expired' || status === 'used') {\r\n        uni.showToast({\r\n          title: '该优惠券已过期或已使用',\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      \r\n      if (this.orderAmount > 0 && this.orderAmount < minAmount) {\r\n        uni.showToast({\r\n          title: `订单金额需满${minAmount}元才能使用`,\r\n          icon: 'none'\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 整理优惠券数据\r\n      const couponData = {\r\n        id: item.id,\r\n        coupon_id: item.coupon ? item.coupon.id : item.id,\r\n        amount: parseFloat(item.coupon ? item.coupon.amount : item.amount),\r\n        limit: parseFloat(item.coupon ? item.coupon.min_amount : item.limit),\r\n        name: item.coupon ? item.coupon.name : item.name,\r\n        desc: item.desc || '全场通用',\r\n        expire_time: item.expire_time || item.endDate || item.expireDate\r\n      }\r\n      \r\n      // 将选择的优惠券通过事件传递回订单页\r\n      uni.$emit('couponSelected', couponData)\r\n      \r\n      // 返回上一页\r\n      uni.navigateBack()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  min-height: 100vh;\r\n  background: #f8f8f8;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.header {\r\n  background: #fff;\r\n  padding-top: 88rpx;\r\n  \r\n  .nav-bar {\r\n    position: relative;\r\n    height: 88rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    \r\n    .back-icon {\r\n      position: absolute;\r\n      left: 30rpx;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      padding: 10rpx;\r\n    }\r\n    \r\n    .title {\r\n      font-size: 32rpx;\r\n      color: #333;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n}\r\n\r\n.tab-container {\r\n  display: flex;\r\n  background: #fff;\r\n  padding: 0 40rpx 16rpx;\r\n  position: relative;\r\n  \r\n  &::after {\r\n    content: '';\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    height: 1px;\r\n    background: #f5f5f5;\r\n    transform: scaleY(0.5);\r\n  }\r\n  \r\n  .tab-item {\r\n    flex: 1;\r\n    display: flex;\r\n    justify-content: center;\r\n    padding: 20rpx 0;\r\n    position: relative;\r\n    \r\n    .tab-text {\r\n      font-size: 28rpx;\r\n      color: #666;\r\n      position: relative;\r\n      padding: 0 6rpx 10rpx;\r\n      line-height: 1.4;\r\n      \r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        left: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        height: 4rpx;\r\n        border-radius: 2rpx;\r\n        background: transparent;\r\n        transition: background-color 0.3s;\r\n      }\r\n    }\r\n    \r\n    &.active {\r\n      .tab-text {\r\n        color: #8cd548;\r\n        font-weight: 500;\r\n        \r\n        &::after {\r\n          background: #8cd548;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.coupon-scroll {\r\n  flex: 1;\r\n}\r\n\r\n.coupon-list {\r\n  padding: 30rpx 20rpx;\r\n  \r\n  .coupon-item {\r\n    position: relative;\r\n    margin-bottom: 30rpx;\r\n    background: #fff;\r\n    border-radius: 16rpx;\r\n    overflow: hidden;\r\n    box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);\r\n    display: flex;\r\n    \r\n    &.disabled {\r\n      opacity: 0.8;\r\n      \r\n      .coupon-amount {\r\n        background: #f0f0f0;\r\n        \r\n        .symbol, .value {\r\n          color: #999;\r\n        }\r\n      }\r\n    }\r\n    \r\n    &.expired {\r\n      .coupon-amount {\r\n        background: #f0f0f0;\r\n        \r\n        .symbol, .value, .limit {\r\n          color: #999;\r\n        }\r\n      }\r\n      \r\n      .coupon-info {\r\n        opacity: 0.7;\r\n      }\r\n    }\r\n    \r\n    .coupon-amount {\r\n      width: 200rpx;\r\n      background: #8cd548;\r\n      padding: 30rpx 0;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      position: relative;\r\n      \r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        bottom: 0;\r\n        width: 16rpx;\r\n        background-image: \r\n          radial-gradient(circle at 0 16rpx, transparent 0, transparent 8rpx, #fff 8rpx, #fff 16rpx),\r\n          radial-gradient(circle at 0 16rpx, transparent 0, transparent 8rpx, #fff 8rpx, #fff 16rpx);\r\n        background-size: 16rpx 32rpx;\r\n        background-position: 0 0, 0 16rpx;\r\n        background-repeat: repeat-y;\r\n      }\r\n      \r\n      .symbol {\r\n        font-size: 28rpx;\r\n        color: #fff;\r\n      }\r\n      \r\n      .value {\r\n        font-size: 60rpx;\r\n        line-height: 1;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        margin: 6rpx 0;\r\n      }\r\n      \r\n      .limit {\r\n        font-size: 22rpx;\r\n        color: rgba(255, 255, 255, 0.9);\r\n        margin-top: 8rpx;\r\n      }\r\n    }\r\n    \r\n    .coupon-info {\r\n      flex: 1;\r\n      padding: 24rpx;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: space-between;\r\n      \r\n      .info-top {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-bottom: 12rpx;\r\n        \r\n        .name {\r\n          font-size: 30rpx;\r\n          color: #333;\r\n          font-weight: 500;\r\n          flex: 1;\r\n          padding-right: 20rpx;\r\n          line-height: 1.4;\r\n        }\r\n        \r\n        .status-tag {\r\n          font-size: 22rpx;\r\n          padding: 2rpx 12rpx;\r\n          border-radius: 20rpx;\r\n          white-space: nowrap;\r\n          display: flex;\r\n          align-items: center;\r\n          height: 32rpx;\r\n          \r\n          &.unused, &.valid {\r\n            color: #8cd548;\r\n            background: rgba(140, 213, 72, 0.1);\r\n          }\r\n          \r\n          &.expired {\r\n            color: #999;\r\n            background: #f5f5f5;\r\n          }\r\n        }\r\n      }\r\n      \r\n      .desc {\r\n        font-size: 24rpx;\r\n        color: #999;\r\n        margin-bottom: auto;\r\n        line-height: 1.4;\r\n      }\r\n      \r\n      .info-bottom {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        margin-top: 20rpx;\r\n        \r\n        .date {\r\n          font-size: 22rpx;\r\n          color: #999;\r\n          line-height: 1.4;\r\n        }\r\n        \r\n        .use-btn {\r\n          background: #8cd548;\r\n          border-radius: 30rpx;\r\n          padding: 6rpx 20rpx;\r\n          height: 40rpx;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          \r\n          text {\r\n            font-size: 22rpx;\r\n            color: #fff;\r\n            line-height: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    .unavailable-tag {\r\n      position: absolute;\r\n      right: 24rpx;\r\n      top: 24rpx;\r\n      background: #ff5b5b;\r\n      border-radius: 20rpx;\r\n      padding: 4rpx 12rpx;\r\n      z-index: 1;\r\n      height: 32rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      \r\n      text {\r\n        font-size: 22rpx;\r\n        color: #fff;\r\n        line-height: 1;\r\n      }\r\n    }\r\n    \r\n    .expired-watermark {\r\n      position: absolute;\r\n      right: 30rpx;\r\n      top: 50%;\r\n      transform: translateY(-50%) rotate(-30deg);\r\n      \r\n      text {\r\n        font-size: 80rpx;\r\n        color: rgba(153, 153, 153, 0.2);\r\n        font-weight: bold;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.loading {\r\n  padding: 100rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  \r\n  .loading-circle {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    border: 4rpx solid #f3f3f3;\r\n    border-top: 4rpx solid #8cd548;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  \r\n  .loading-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.error-state {\r\n  padding: 100rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  \r\n  .error-text {\r\n    font-size: 28rpx;\r\n    color: #ff5b5b;\r\n    margin-bottom: 30rpx;\r\n  }\r\n  \r\n  .retry-btn {\r\n    padding: 16rpx 60rpx;\r\n    background: #8cd548;\r\n    border-radius: 100rpx;\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n}\r\n\r\n.empty-state {\r\n  padding-top: 100rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  \r\n  .empty-image {\r\n    width: 300rpx;\r\n    height: 225rpx;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  .empty-text {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  .action-btn {\r\n    padding: 20rpx 60rpx;\r\n    background: #8cd548;\r\n    border-radius: 100rpx;\r\n    \r\n    text {\r\n      font-size: 28rpx;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n.loading-more {\r\n  padding: 20rpx 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  \r\n  .loading-dot {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    border: 3rpx solid rgba(140, 213, 72, 0.2);\r\n    border-top: 3rpx solid #8cd548;\r\n    border-radius: 50%;\r\n    animation: spin 1s linear infinite;\r\n    margin-right: 10rpx;\r\n  }\r\n  \r\n  text {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n.bottom-tip {\r\n  padding: 30rpx 0 50rpx;\r\n  text-align: center;\r\n  \r\n  text {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n  }\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=style&index=0&id=032f11f4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./coupon.vue?vue&type=style&index=0&id=032f11f4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309752\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}