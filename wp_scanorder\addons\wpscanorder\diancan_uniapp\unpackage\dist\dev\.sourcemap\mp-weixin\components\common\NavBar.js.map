{"version": 3, "sources": ["webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/common/NavBar.vue?78c0", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/common/NavBar.vue?d8c7", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/common/NavBar.vue?c41b", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/common/NavBar.vue?fbf4", "uni-app:///components/common/NavBar.vue", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/common/NavBar.vue?c4b4", "webpack:///E:/wwwroot/fastadmin_addons/wp_scanorder/addons/wpscanorder/diancan_uniapp/components/common/NavBar.vue?d00a"], "names": ["name", "props", "title", "type", "default", "isTabbar", "textColor", "background", "data", "statusBarHeight", "computed", "bgClass", "created", "methods", "goBack", "uni", "delta", "fail", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC+K;AAC/K,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsBnrB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;QACAC;UACAF;YACAG;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAA0vC,CAAgB,+lCAAG,EAAC,C;;;;;;;;;;;ACA9wC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/common/NavBar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./NavBar.vue?vue&type=template&id=7088dd89&\"\nvar renderjs\nimport script from \"./NavBar.vue?vue&type=script&lang=js&\"\nexport * from \"./NavBar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./NavBar.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/common/NavBar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NavBar.vue?vue&type=template&id=7088dd89&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NavBar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NavBar.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"navbar\">\r\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\r\n    <view class=\"navbar-content\" :class=\"[bgClass]\">\r\n      <!-- 返回按钮（非tabbar页面显示） -->\r\n      <view v-if=\"!isTabbar\" class=\"navbar-left\" @tap=\"goBack\">\r\n        <custom-icon name=\"arrow-left\" :color=\"textColor\" :size=\"36\"></custom-icon>\r\n      </view>\r\n      <view v-else class=\"navbar-left\"></view>\r\n      \r\n      <!-- 页面标题 -->\r\n      <text class=\"navbar-title\" :style=\"{color: textColor}\">{{title}}</text>\r\n      \r\n      <!-- 右侧操作区域（可自定义） -->\r\n      <view class=\"navbar-right\">\r\n        <slot name=\"right\"></slot>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'NavBar',\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    isTabbar: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    textColor: {\r\n      type: String,\r\n      default: '#333'\r\n    },\r\n    background: {\r\n      type: String,\r\n      default: '' // 可选：primary, transparent, white\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      statusBarHeight: 20\r\n    }\r\n  },\r\n  computed: {\r\n    bgClass() {\r\n      if (this.background === 'primary') {\r\n        return 'bg-primary';\r\n      } else if (this.background === 'transparent') {\r\n        return 'bg-transparent';\r\n      } else if (this.background === 'white') {\r\n        return 'bg-white';\r\n      }\r\n      return 'bg-white';\r\n    }\r\n  },\r\n  created() {\r\n    // 获取状态栏高度\r\n    const systemInfo = uni.getSystemInfoSync();\r\n    this.statusBarHeight = systemInfo.statusBarHeight;\r\n  },\r\n  methods: {\r\n    goBack() {\r\n      uni.navigateBack({\r\n        delta: 1,\r\n        fail: () => {\r\n          uni.switchTab({\r\n            url: '/pages/home/<USER>'\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import '@/styles/theme.scss';\r\n\r\n.navbar {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 9999;\r\n  \r\n  .navbar-content {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    height: 88rpx;\r\n    padding: 0 30rpx;\r\n    \r\n    .navbar-left, .navbar-right {\r\n      width: 88rpx;\r\n      height: 88rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n    \r\n    .navbar-title {\r\n      font-size: $font-size-lg;\r\n      font-weight: bold;\r\n      flex: 1;\r\n      text-align: center;\r\n    }\r\n    \r\n    &.bg-primary {\r\n      background: $gradient-primary;\r\n    }\r\n    \r\n    &.bg-transparent {\r\n      background-color: transparent;\r\n    }\r\n    \r\n    &.bg-white {\r\n      background-color: #fff;\r\n      border-bottom: 1px solid $border-color;\r\n    }\r\n  }\r\n}\r\n</style> ", "import mod from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NavBar.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./NavBar.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753948309624\n      var cssReload = require(\"E:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}